{"name": "club-m-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "typecheck": "tsc --noEmit", "build": "node -e \"process.platform === 'win32' ? require('child_process').spawn('scripts/build.bat', [], {stdio: 'inherit'}) : require('child_process').spawn('scripts/build.sh', [], {stdio: 'inherit'})\"", "build:android": "node -e \"process.platform === 'win32' ? require('child_process').spawn('scripts/build-android.bat', [], {stdio: 'inherit'}) : require('child_process').spawn('scripts/build-android.sh', [], {stdio: 'inherit'})\"", "build:ios": "scripts/build-ios.sh", "validate": "node scripts/validate-config.js", "check:security": "node scripts/check-security.js"}, "dependencies": {"@dev-plugins/react-query": "~0.2.0", "@jsquash/webp": "^1.5.0", "@microsoft/signalr": "^8.0.7", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@tanstack/react-query": "^5.80.3", "axios": "^1.9.0", "expo": "53.0.20", "expo-clipboard": "^7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-image": "~2.4.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-local-authentication": "~16.0.5", "expo-localization": "~16.1.6", "expo-network": "~7.1.5", "expo-notifications": "~0.31.4", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "i18next": "^25.0.0", "react": "19.0.0", "react-error-boundary": "^6.0.0", "react-i18next": "^15.4.1", "react-native": "0.79.5", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.3", "react-native-web": "^0.20.0", "react-qr-code": "^2.0.18", "rxjs": "^7.8.2", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@mswjs/data": "^0.16.2", "@react-native-community/cli": "^19.1.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "cross-env": "^10.0.0", "jest": "~29.7.0", "jest-expo": "~53.0.9", "msw": "^2.10.4", "react-query-external-sync": "^2.2.0", "typescript": "^5.8.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@sentry/react-native|native-base|react-native-svg|expo-image-manipulator|msw|@mswjs)"], "moduleNameMapper": {"^expo-image-manipulator$": "<rootDir>/__mocks__/expo-image-manipulator.js", "^@/(.*)$": "<rootDir>/$1", "^@/app/(.*)$": "<rootDir>/app/$1", "^@/components/(.*)$": "<rootDir>/components/$1", "^@/contexts/(.*)$": "<rootDir>/contexts/$1", "^@/hooks/(.*)$": "<rootDir>/hooks/$1", "^@/services/(.*)$": "<rootDir>/services/$1", "^@/models/(.*)$": "<rootDir>/models/$1", "^@/styles/(.*)$": "<rootDir>/styles/$1", "^@/utils/(.*)$": "<rootDir>/utils/$1", "^@/assets/(.*)$": "<rootDir>/assets/$1", "^@/locales/(.*)$": "<rootDir>/locales/$1", "^@/tests/(.*)$": "<rootDir>/tests/$1"}}, "private": true}