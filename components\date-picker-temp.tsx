import React from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet } from "react-native";

export interface DateTimePickerDrawerProps {
  visible: boolean;
  selectedDate?: Date;
  onDateTimeSelect: (date: Date) => void;
  onClose: () => void;
  title?: string;
  hideTimeSection?: boolean;
}

const DateTimePickerDrawer: React.FC<DateTimePickerDrawerProps> = ({
  visible,
  selectedDate,
  onDateTimeSelect,
  onClose,
  title = "Selecionar data",
  hideTimeSection = false
}) => {
  const handleSelectDate = () => {
    const date = selectedDate || new Date();
    onDateTimeSelect(date);
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={styles.drawer}>
          <Text style={styles.title}>{title}</Text>
          
          <View style={styles.content}>
            <Text style={styles.placeholder}>
              Calendário temporário - implementação em progresso
            </Text>
            
            <TouchableOpacity style={styles.button} onPress={handleSelectDate}>
              <Text style={styles.buttonText}>
                {hideTimeSection ? "Aplicar data" : "Aplicar data e hora"}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>
                {hideTimeSection ? "Voltar" : "Voltar aos filtros"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  drawer: {
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    minHeight: 300,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholder: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 30,
  },
  button: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  cancelButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
  },
});

export default DateTimePickerDrawer;
