import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image
} from "react-native";
import {useLocalSearchParams, router} from "expo-router";
import {useTranslation} from "react-i18next";
import styles from "@/styles/events/ticket-details.style";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import {useTicket} from "@/hooks/api/use-tickets";
import {useTicketQRCode} from "@/hooks/api/use-qr-codes";
import QRCode from "@/components/qr-code/qr-code.component";
import stylesConstants from "@/styles/styles-constants";

const TicketDetails: React.FC = () => {
  const {t} = useTranslation();
  const {id} = useLocalSearchParams();
  const [showQRModal, setShowQRModal] = useState(false);

  const ticketId = Array.isArray(id) ? id[0] : id;

  console.log("🎯 TicketDetails: ID recebido:", id);
  console.log("🎯 TicketDetails: ticketId processado:", ticketId);

  const {
    data: ticket,
    isLoading,
    error,
    refetch
  } = useTicket(ticketId || "", {
    enabled: !!ticketId
  } as any);

  // Hook para buscar QR code do ticket
  const {
    data: qrCodeValue,
    isLoading: isLoadingQRCode,
    error: qrCodeError
  } = useTicketQRCode(ticketId || "", {
    enabled: !!ticketId && !!ticket
  } as any);

  console.log(
    "🎯 TicketDetails: Hook useTicket - isLoading:",
    isLoading,
    "error:",
    error,
    "ticketData:",
    ticket
  );

  const handleCopyTicketCode = (): void => {
    if (ticket?.code) {
      // For now, just show an alert since Clipboard is deprecated
      Alert.alert(
        t("ticketDetails.codeCopied"),
        t("ticketDetails.codeCopiedMessage")
      );
    }
  };

  const handleClose = (): void => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{t("ticketDetails.title")}</Text>
          <View style={styles.headerSpacer} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={stylesConstants.colors.fullWhite}
          />
          <Text style={styles.loadingText}>{t("ticketDetails.loading")}</Text>
        </View>
      </View>
    );
  }

  if (error || !ticket) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{t("ticketDetails.title")}</Text>
          <View style={styles.headerSpacer} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{t("ticketDetails.error")}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => refetch()}
          >
            <Text style={styles.retryButtonText}>
              {t("ticketDetails.retry")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalhes do ingresso</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Content Area */}
      <View style={styles.contentArea}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {/* Main Ticket Card with Image Background */}
          <View style={styles.ticketCard}>
            {/* Background Image */}
            <Image
              source={require("@/assets/images/ticket-background-empty.png")}
              style={styles.ticketBackgroundImage}
              resizeMode="cover"
            />

            {/* Ticket Content Overlay */}
            <View style={styles.ticketContentOverlay}>
              {/* Event Title */}
              <Text style={styles.eventTitle}>{ticket.title}</Text>

              {/* Organizer */}
              <View style={styles.organizerContainer}>
                <Text style={styles.organizerLabel}>
                  {t("ticketDetails.organizedBy")}
                </Text>
                <Text style={styles.organizerName}>{ticket.organizer}</Text>
              </View>

              {/* Event Details Grid */}
              <View style={styles.detailsGrid}>
                <View style={styles.detailColumn}>
                  <Text style={styles.detailLabel}>
                    {t("ticketDetails.eventDateTime")}
                  </Text>
                  <Text style={styles.detailValue}>
                    {ticket.date} - {ticket.time}
                  </Text>
                </View>
                <View style={styles.detailColumn}>
                  <Text style={styles.detailLabel}>
                    {t("ticketDetails.ticketCode")}
                  </Text>
                  <TouchableOpacity onPress={handleCopyTicketCode}>
                    <Text style={styles.detailValue}>{ticket.code}</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.detailsGrid}>
                <View style={styles.detailColumnFull}>
                  <Text style={styles.detailLabel}>
                    {t("ticketDetails.location")}
                  </Text>
                  <Text style={styles.detailValue}>{ticket.location}</Text>
                </View>
              </View>

              {/* Status */}
              <View style={styles.statusContainer}>
                <Text style={styles.statusLabel}>
                  {t("ticketDetails.status")}
                </Text>
                <Text
                  style={[
                    styles.statusValue,
                    ticket.canCheckIn && styles.statusActive
                  ]}
                >
                  {ticket.statusLabel}
                </Text>
              </View>

              {/* QR Code Section */}
              <View style={styles.qrCodeContainer}>
                {isLoadingQRCode ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator
                      size="large"
                      color={stylesConstants.colors.primary500}
                    />
                    <Text style={styles.loadingText}>
                      Carregando QR Code...
                    </Text>
                  </View>
                ) : qrCodeValue ? (
                  <View style={styles.qrCodeImage}>
                    <QRCode
                      value={qrCodeValue}
                      size={160}
                      bgColor="#FFFFFF"
                      fgColor="#000000"
                      includeMargin={false}
                    />
                  </View>
                ) : (
                  <View style={styles.qrCodeImage}>
                    <Text style={styles.errorText}>QR Code não disponível</Text>
                  </View>
                )}
                <TouchableOpacity
                  style={styles.fullScreenButton}
                  onPress={() => setShowQRModal(true)}
                  disabled={!qrCodeValue}
                >
                  <Text style={styles.fullScreenButtonText}>
                    Visualizar em tela cheia
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Buttons */}
        <View style={styles.bottomButtonsContainer}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <Text style={styles.closeButtonText}>{t("common.close")}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* QR Code Full Screen Modal */}
      {showQRModal && (
        <View style={styles.qrModalOverlay}>
          <View style={styles.qrModalContainer}>
            <View style={styles.qrModalHeader}>
              <Text style={styles.qrModalTitle}>QR Code</Text>
            </View>

            <View style={styles.qrModalContent}>
              <View style={styles.qrModalQRCode}>
                {qrCodeValue ? (
                  <QRCode
                    value={qrCodeValue}
                    size={280}
                    bgColor="#FFFFFF"
                    fgColor="#000000"
                    includeMargin={false}
                  />
                ) : (
                  <View style={styles.qrModalQRCodePlaceholder}>
                    <Text style={styles.qrModalQRCodeText}>
                      QR Code não disponível
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.qrModalCodeContainer}>
                <Text style={styles.qrModalCodeLabel}>Código do ingresso</Text>
                <View style={styles.qrModalCodeRow}>
                  <Text style={styles.qrModalCodeValue}>{ticket.code}</Text>
                  <TouchableOpacity
                    style={styles.copyButton}
                    onPress={handleCopyTicketCode}
                  >
                    <Text style={styles.copyButtonText}>📋</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={styles.qrModalCloseButton}
              onPress={() => setShowQRModal(false)}
            >
              <Text style={styles.qrModalCloseButtonText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default TicketDetails;
