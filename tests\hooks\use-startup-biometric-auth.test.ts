import {renderHook, act} from "@testing-library/react-native";
import {useStartupBiometricAuth} from "@/hooks/use-startup-biometric-auth";
import * as SecureStore from "expo-secure-store";
import * as LocalAuthentication from "expo-local-authentication";

// Mock das dependências
jest.mock("expo-secure-store");
jest.mock("expo-local-authentication");
jest.mock("@/services/api/auth/token-manager");
jest.mock("@/contexts/AuthContext");
jest.mock("@/contexts/error-dialog-context");
jest.mock("expo-router");

const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;
const mockLocalAuth = LocalAuthentication as jest.Mocked<typeof LocalAuthentication>;

describe("useStartupBiometricAuth", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve inicializar com estado correto", () => {
    const {result} = renderHook(() => useStartupBiometricAuth());

    expect(result.current.isChecking).toBe(false);
    expect(result.current.shouldPromptBiometric).toBe(false);
    expect(result.current.isAuthenticating).toBe(false);
    expect(result.current.hasCompletedCheck).toBe(false);
  });

  it("deve verificar se há dados de sessão biométrica", async () => {
    const sessionData = {
      token: "test-token",
      user: {id: "1", name: "Test User"},
      expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hora no futuro
    };

    mockSecureStore.getItemAsync.mockResolvedValue(JSON.stringify(sessionData));
    mockLocalAuth.hasHardwareAsync.mockResolvedValue(true);
    mockLocalAuth.isEnrolledAsync.mockResolvedValue(true);

    const {result} = renderHook(() => useStartupBiometricAuth());

    await act(async () => {
      await result.current.checkShouldPromptBiometric();
    });

    expect(result.current.shouldPromptBiometric).toBe(true);
    expect(result.current.hasCompletedCheck).toBe(false);
  });

  it("não deve solicitar biometria se não há dados salvos", async () => {
    mockSecureStore.getItemAsync.mockResolvedValue(null);

    const {result} = renderHook(() => useStartupBiometricAuth());

    await act(async () => {
      await result.current.checkShouldPromptBiometric();
    });

    expect(result.current.shouldPromptBiometric).toBe(false);
    expect(result.current.hasCompletedCheck).toBe(true);
  });

  it("não deve solicitar biometria se dispositivo não suporta", async () => {
    const sessionData = {
      token: "test-token",
      user: {id: "1", name: "Test User"},
      expiresAt: new Date(Date.now() + 3600000).toISOString()
    };

    mockSecureStore.getItemAsync.mockResolvedValue(JSON.stringify(sessionData));
    mockLocalAuth.hasHardwareAsync.mockResolvedValue(false);
    mockLocalAuth.isEnrolledAsync.mockResolvedValue(false);

    const {result} = renderHook(() => useStartupBiometricAuth());

    await act(async () => {
      await result.current.checkShouldPromptBiometric();
    });

    expect(result.current.shouldPromptBiometric).toBe(false);
    expect(result.current.hasCompletedCheck).toBe(true);
  });

  it("deve remover dados expirados", async () => {
    const expiredSessionData = {
      token: "test-token",
      user: {id: "1", name: "Test User"},
      expiresAt: new Date(Date.now() - 3600000).toISOString() // 1 hora no passado
    };

    mockSecureStore.getItemAsync.mockResolvedValue(JSON.stringify(expiredSessionData));
    mockSecureStore.deleteItemAsync.mockResolvedValue();

    const {result} = renderHook(() => useStartupBiometricAuth());

    await act(async () => {
      await result.current.checkShouldPromptBiometric();
    });

    expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith("session_data");
    expect(result.current.shouldPromptBiometric).toBe(false);
    expect(result.current.hasCompletedCheck).toBe(true);
  });
});
