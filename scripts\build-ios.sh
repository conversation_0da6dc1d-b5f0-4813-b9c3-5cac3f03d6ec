#!/bin/bash

# Club M iOS Build Script
# Builds production IPA for iOS (macOS only)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "iOS builds can only be performed on macOS"
    print_error "Current OS: $OSTYPE"
    exit 1
fi

# Check if we're in the project root
if [ ! -f "package.json" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_error "iOS directory not found. Make sure this is a React Native project."
    exit 1
fi

print_status "Starting iOS production build..."

# Check for Xcode
if ! command -v xcodebuild &> /dev/null; then
    print_error "Xcode is not installed or xcodebuild is not in PATH"
    print_error "Please install Xcode from the App Store"
    exit 1
fi

# Check for CocoaPods
if ! command -v pod &> /dev/null; then
    print_error "CocoaPods is not installed"
    print_error "Install with: sudo gem install cocoapods"
    exit 1
fi

# Setup production environment
print_status "Setting up production environment..."
node scripts/setup-production.js

# Install/Update CocoaPods dependencies
print_status "Installing/Updating CocoaPods dependencies..."
cd ios
pod install --repo-update

# Clean previous builds
print_status "Cleaning previous builds..."
xcodebuild clean -workspace ClubM.xcworkspace -scheme ClubM

# Build the app
print_status "Building production IPA..."
print_warning "Note: This build uses debug signing. For App Store distribution, you'll need:"
print_warning "1. Apple Developer Account"
print_warning "2. Distribution Certificate"
print_warning "3. Provisioning Profile"
print_warning "4. Configure signing in Xcode"

# Build for device (Release configuration)
xcodebuild archive \
    -workspace ClubM.xcworkspace \
    -scheme ClubM \
    -configuration Release \
    -destination generic/platform=iOS \
    -archivePath build/ClubM.xcarchive \
    CODE_SIGN_IDENTITY="" \
    CODE_SIGNING_REQUIRED=NO \
    CODE_SIGNING_ALLOWED=NO

# Export IPA
print_status "Exporting IPA..."

# Create export options plist
cat > build/ExportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>development</string>
    <key>compileBitcode</key>
    <false/>
    <key>signingStyle</key>
    <string>automatic</string>
</dict>
</plist>
EOF

xcodebuild -exportArchive \
    -archivePath build/ClubM.xcarchive \
    -exportPath build \
    -exportOptionsPlist build/ExportOptions.plist

# Check if build was successful
if [ $? -eq 0 ]; then
    print_success "iOS IPA build completed successfully!"

    # Find the IPA file
    IPA_PATH=$(find build -name "*.ipa" | head -1)

    if [ -n "$IPA_PATH" ]; then
        print_success "IPA location: ios/$IPA_PATH"

        # Get IPA size
        IPA_SIZE=$(du -h "$IPA_PATH" | cut -f1)
        print_status "IPA size: $IPA_SIZE"

        # Copy IPA to builds directory
        cd ..
        mkdir -p builds/ios
        cp "ios/$IPA_PATH" "builds/ios/club-m-production.ipa"
        print_success "IPA copied to: builds/ios/club-m-production.ipa"
    else
        print_warning "IPA file not found in expected location"
    fi
else
    print_error "iOS build failed!"
    exit 1
fi

print_success "iOS build process completed!"
print_warning ""
print_warning "IMPORTANT NOTES FOR PRODUCTION:"
print_warning "1. This build uses development signing and cannot be distributed"
print_warning "2. For App Store distribution, you need:"
print_warning "   - Apple Developer Account (\$99/year)"
print_warning "   - Distribution Certificate"
print_warning "   - App Store Provisioning Profile"
print_warning "3. Configure proper signing in Xcode before building for distribution"
print_warning "4. Use 'Product > Archive' in Xcode for App Store builds"
