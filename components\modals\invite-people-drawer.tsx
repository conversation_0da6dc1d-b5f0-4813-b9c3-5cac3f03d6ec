import React, {useState, useCallback, useMemo} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  Alert,
  ListRenderItemInfo,
  ScrollView
} from "react-native";
import {useTranslation} from "react-i18next";
import {UserInfo} from "@/models/api/auth.models";
import {useUsers} from "@/hooks/api/use-users";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import Avatar from "../user/avatar";
import SearchIcon from "../icons/search-icon";
import CheckIcon from "../icons/check-icon";
import {showSuccessToast} from "@/utils/error-handler";
import styles from "@/styles/modals/invite-people-drawer.style";

export interface InvitePeopleDrawerProps {
  visible: boolean;
  onClose: () => void;
  eventTitle?: string;
  onUsersSelected?: (userIds: number[]) => void;
}

interface SelectableUser extends UserInfo {
  isSelected: boolean;
}

const InvitePeopleDrawer: React.FC<InvitePeopleDrawerProps> = ({
  visible,
  onClose,
  eventTitle = "Encontro anual de empreendedores em Balneário Camboriú - SC",
  onUsersSelected
}) => {
  const {t} = useTranslation();
  const [searchValue, setSearchValue] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState<Set<number>>(
    new Set()
  );
  const [customMessage, setCustomMessage] = useState("");

  // Buscar lista de usuários para busca
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    isError: isUsersError
  } = useUsers(
    {
      search: searchValue || undefined,
      pageSize: 50
    },
    {
      enabled: visible && searchValue.length >= 2
    } as any
  );

  // Buscar usuários recentemente convidados (sem filtro de busca)
  const {data: recentUsersData} = useUsers(
    {
      pageSize: 20 // Buscar mais usuários para ter uma lista de "recentemente convidados"
    },
    {
      enabled: visible
    } as any
  );

  // Usuários recentemente convidados (primeiros 8 da lista geral) com filtro de busca
  const recentlyInvitedUsers = useMemo(() => {
    if (!recentUsersData?.data) return [];

    let users = recentUsersData.data.slice(0, 8);

    // Aplicar filtro de busca se houver termo de busca
    if (searchValue.trim()) {
      const searchTerm = searchValue.toLowerCase().trim();
      users = users.filter((user) => {
        const matchesSearch =
          user.name.toLowerCase().includes(searchTerm) ||
          user.email.toLowerCase().includes(searchTerm);

        // Manter usuários selecionados visíveis mesmo se não correspondem à busca
        return matchesSearch || selectedUserIds.has(user.id);
      });
    }

    return users;
  }, [recentUsersData?.data, searchValue, selectedUserIds]);

  // Preparar lista de usuários selecionáveis com filtro de busca
  const selectableUsers = useMemo(() => {
    if (!usersData?.data) return [];

    const allUsers = usersData.data.map((user) => ({
      ...user,
      isSelected: selectedUserIds.has(user.id)
    }));

    // Se não há termo de busca, retornar todos os usuários
    if (!searchValue.trim()) {
      return allUsers;
    }

    // Filtrar usuários baseado no termo de busca
    const searchTerm = searchValue.toLowerCase().trim();
    const filteredUsers = allUsers.filter((user) => {
      const matchesSearch =
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm);

      // Manter usuários selecionados visíveis mesmo se não correspondem à busca
      return matchesSearch || user.isSelected;
    });

    return filteredUsers;
  }, [usersData?.data, selectedUserIds, searchValue]);

  const handleUserToggle = useCallback((userId: number) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  }, []);

  const handleSendInvitation = useCallback(() => {
    if (selectedUserIds.size === 0) {
      Alert.alert(
        t("invitePeople.noUsersSelected.title", "Nenhum usuário selecionado"),
        t(
          "invitePeople.noUsersSelected.message",
          "Selecione pelo menos um usuário para enviar o convite."
        )
      );
      return;
    }

    // Converter Set para Array e enviar para o componente pai
    const userIdsArray = Array.from(selectedUserIds);

    if (onUsersSelected) {
      onUsersSelected(userIdsArray);
    }

    showSuccessToast(
      "Usuários selecionados!",
      `${selectedUserIds.size} usuários vão ser convidados para o evento`
    );

    onClose();
  }, [selectedUserIds, onClose, t, onUsersSelected]);

  const defaultMessage = useMemo(() => {
    return `Estou convidando você para o evento "${eventTitle}", participe comigo!`;
  }, [eventTitle]);

  const renderUserItem = useCallback(
    ({item}: ListRenderItemInfo<SelectableUser>) => (
      <TouchableOpacity
        style={[styles.userItem, item.isSelected && styles.userItemSelected]}
        onPress={() => handleUserToggle(item.id)}
      >
        <View style={styles.userInfo}>
          <Avatar user={item} name={item.name} size={40} borderSize={0} />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{item.name}</Text>
            <Text style={styles.userEmail}>{item.email}</Text>
          </View>
        </View>
        <View style={styles.checkboxContainer}>
          {item.isSelected && (
            <View style={styles.checkbox}>
              <CheckIcon width={16} height={16} />
            </View>
          )}
        </View>
      </TouchableOpacity>
    ),
    [handleUserToggle]
  );

  const renderRecentlyInvitedUser = useCallback(
    (user: any, index: number) => {
      const isSelected = selectedUserIds.has(user.id);
      return (
        <TouchableOpacity
          key={user.id}
          style={styles.recentUserItem}
          onPress={() => handleUserToggle(user.id)}
        >
          <View style={styles.recentUserAvatarContainer}>
            <Avatar user={user} name={user.name} size={44} borderSize={0} />
            {isSelected && (
              <View style={styles.recentUserCheckbox}>
                <CheckIcon width={12} height={12} />
              </View>
            )}
          </View>
          <Text style={styles.recentUserName} numberOfLines={2}>
            {user.name}
          </Text>
        </TouchableOpacity>
      );
    },
    [selectedUserIds, handleUserToggle]
  );

  const renderSearchResults = useCallback(() => {
    if (isLoadingUsers) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0F7C4D" />
          <Text style={styles.loadingText}>
            {t("invitePeople.loading", "Buscando usuários...")}
          </Text>
        </View>
      );
    }

    if (isUsersError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t(
              "invitePeople.error",
              "Erro ao buscar usuários. Tente novamente."
            )}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={selectableUsers}
        renderItem={renderUserItem}
        keyExtractor={(item) => item.id}
        style={styles.usersList}
        showsVerticalScrollIndicator={false}
      />
    );
  }, [isLoadingUsers, isUsersError, selectableUsers, renderUserItem, t]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="overFullScreen"
      transparent={true}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={onClose}
      >
        <TouchableOpacity
          style={styles.container}
          activeOpacity={1}
          onPress={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerIndicator} />
            <Text style={styles.headerTitle}>
              {t("invitePeople.title", "Convidar membro(s)")}
            </Text>
          </View>

          {/* Search Input */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <SearchIcon width={20} height={20} />
              <TextInput
                style={styles.searchInput}
                placeholder={t(
                  "invitePeople.searchPlaceholder",
                  "Buscar nome de usuário"
                )}
                placeholderTextColor="#F2F4F7"
                value={searchValue}
                onChangeText={setSearchValue}
              />
            </View>
          </View>

          {/* Recently Invited Section - Only show when not searching or when there are filtered results */}
          {(!searchValue.trim() || recentlyInvitedUsers.length > 0) && (
            <View style={styles.recentlyInvitedSection}>
              <Text style={styles.sectionTitle}>
                {t("invitePeople.recentlyInvited", "Convidados recentemente")}
              </Text>
              <View style={styles.recentUsersContainer}>
                {recentlyInvitedUsers.length > 0 ? (
                  <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.recentUsersContent}
                    style={{flex: 1}}
                    bounces={false}
                  >
                    {recentlyInvitedUsers.map(renderRecentlyInvitedUser)}
                  </ScrollView>
                ) : (
                  <View style={styles.emptyRecentUsersContainer}>
                    <Text style={styles.emptyRecentUsersText}>
                      {t(
                        "invitePeople.noRecentUsers",
                        "Nenhum usuário encontrado"
                      )}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Search Results */}
          {searchValue.length >= 2 && (
            <View style={styles.searchResultsSection}>
              {renderSearchResults()}
            </View>
          )}

          {/* Message Section */}
          <View style={styles.messageSection}>
            <View style={styles.messageInputContainer}>
              <TextInput
                style={styles.messageInput}
                placeholder={defaultMessage}
                placeholderTextColor="#DFE9F0"
                value={customMessage}
                onChangeText={setCustomMessage}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <FullSizeButton
                text={t("invitePeople.sendMessage", "Enviar mensagem")}
                onPress={handleSendInvitation}
                disabled={selectedUserIds.size === 0}
              />
              <InvisibleFullSizeButton
                text={t("invitePeople.close", "Fechar")}
                onPress={onClose}
              />
            </View>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

export default InvitePeopleDrawer;
