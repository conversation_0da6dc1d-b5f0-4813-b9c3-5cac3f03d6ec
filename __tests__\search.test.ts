/**
 * Search functionality tests
 */

import {SearchService} from "@/services/api/search/search.service";
import {RecentSearchesManager} from "@/utils/recent-searches";

// Mock the API client
jest.mock("@/services/api/base/api-client", () => ({
  apiClient: {
    get: jest.fn()
  }
}));

// Mock AsyncStorage
jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}));

describe("SearchService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("globalSearch", () => {
    it("should call the correct API endpoint with search parameters", async () => {
      const mockRawResponse = {
        data: [
          {
            user: {
              id: 1,
              name: "Test User",
              email: "<EMAIL>",
              createdAt: "2023-01-01T00:00:00Z"
            }
          }
        ],
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
        pageSize: 20,
        hasNextPage: false,
        hasPreviousPage: false
      };

      const {apiClient} = require("@/services/api/base/api-client");
      apiClient.get.mockResolvedValue(mockRawResponse);

      const result = await SearchService.globalSearch({
        search: "test",
        page: 1,
        pageSize: 20
      });

      expect(apiClient.get).toHaveBeenCalledWith("/api/app/home/<USER>", {
        Search: "test",
        Page: 1,
        PageSize: 20
      });

      // Check that the response is properly transformed
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual({
        id: "1",
        name: "Test User",
        subtitle: "<EMAIL>",
        description: expect.stringContaining("Membro desde"),
        type: "member",
        avatar: undefined,
        originalData: mockRawResponse.data[0].user
      });
      expect(result.pagination).toEqual({
        currentPage: 1,
        pageSize: 20,
        totalCount: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      });
    });
  });

  describe("searchMembers", () => {
    it("should transform user data to SearchResult format", async () => {
      const mockApiResponse = {
        data: [
          {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
            document: "123456789",
            status: "active"
          }
        ],
        pagination: {
          currentPage: 1,
          hasNextPage: false,
          totalCount: 1
        }
      };

      const {apiClient} = require("@/services/api/base/api-client");
      apiClient.get.mockResolvedValue(mockApiResponse);

      const result = await SearchService.searchMembers({
        search: "john",
        page: 1,
        pageSize: 10
      });

      expect(result.data[0]).toEqual({
        id: "1",
        name: "John Doe",
        subtitle: "<EMAIL>",
        type: "member",
        avatar: undefined,
        metadata: {
          document: "123456789",
          status: "active",
          createdAt: undefined
        }
      });
    });
  });
});

describe("RecentSearchesManager", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("addRecentSearch", () => {
    it("should not add searches with less than 2 characters", async () => {
      const AsyncStorage = require("@react-native-async-storage/async-storage");

      await RecentSearchesManager.addRecentSearch("a");

      expect(AsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it("should add valid search terms", async () => {
      const AsyncStorage = require("@react-native-async-storage/async-storage");
      AsyncStorage.getItem.mockResolvedValue("[]");

      await RecentSearchesManager.addRecentSearch("test search");

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        "@clubm_recent_searches",
        expect.stringContaining("test search")
      );
    });
  });

  describe("getRecentSearches", () => {
    it("should return empty array when no stored searches", async () => {
      const AsyncStorage = require("@react-native-async-storage/async-storage");
      AsyncStorage.getItem.mockResolvedValue(null);

      const result = await RecentSearchesManager.getRecentSearches();

      expect(result).toEqual([]);
    });

    it("should filter out old searches", async () => {
      const AsyncStorage = require("@react-native-async-storage/async-storage");
      const oldSearch = {
        term: "old search",
        timestamp: Date.now() - 31 * 24 * 60 * 60 * 1000 // 31 days ago
      };
      const recentSearch = {
        term: "recent search",
        timestamp: Date.now()
      };

      AsyncStorage.getItem.mockResolvedValue(
        JSON.stringify([oldSearch, recentSearch])
      );

      const result = await RecentSearchesManager.getRecentSearches();

      expect(result).toHaveLength(1);
      expect(result[0].term).toBe("recent search");
    });
  });
});
