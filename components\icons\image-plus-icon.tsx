import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ImagePlusIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const ImagePlusIcon: React.FC<ImagePlusIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg width={40} height={40} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M20 13.333v13.334M13.333 20h13.334"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M6.667 33.333h26.666c1.84 0 3.334-1.493 3.334-3.333V10c0-1.84-1.494-3.333-3.334-3.333H6.667C4.827 6.667 3.333 8.16 3.333 10v20c0 1.84 1.494 3.333 3.334 3.333Z"
      />
    </Svg>
  );
};

export default ImagePlusIcon;
