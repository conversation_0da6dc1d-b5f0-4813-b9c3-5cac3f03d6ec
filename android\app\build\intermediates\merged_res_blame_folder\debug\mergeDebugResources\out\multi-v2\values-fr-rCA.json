{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,365,483,575,736,851,973,1094,1257,1411,1545,1676,1856,1959,2139,2273,2407,2570,2710,2845,2957,3115,3207,3345,3456,3613", "endColumns": "177,131,117,91,160,114,121,120,162,153,133,130,179,102,179,133,133,162,139,134,111,157,91,137,110,156,113", "endOffsets": "228,360,478,570,731,846,968,1089,1252,1406,1540,1671,1851,1954,2134,2268,2402,2565,2705,2840,2952,3110,3202,3340,3451,3608,3722"}, "to": {"startLines": "36,37,72,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,165,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3388,3566,7718,7943,8263,8424,8865,8987,9108,9271,9425,9559,9690,9870,9973,10153,10287,10421,10584,16319,17950,18062,18220,18312,18450,18561,18718", "endColumns": "177,131,117,91,160,114,121,120,162,153,133,130,179,102,179,133,133,162,139,134,111,157,91,137,110,156,113", "endOffsets": "3561,3693,7831,8030,8419,8534,8982,9103,9266,9420,9554,9685,9865,9968,10148,10282,10416,10579,10719,16449,18057,18215,18307,18445,18556,18713,18827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "33,53,96,98,100,114,115,162,163,164,166,171,172,173,174,175,176,177,178,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3083,5223,10724,10874,11019,12079,12158,16066,16156,16248,16454,16874,16969,17060,17140,17220,17303,17380,17453,17642,17714,17797,17870", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3148,5298,10789,10952,11081,12153,12234,16151,16243,16314,16537,16964,17055,17135,17215,17298,17375,17448,17536,17709,17792,17865,17945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "43,44,45,46,47,48,49,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4193,4291,4393,4492,4594,4698,4802,17541", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4286,4388,4487,4589,4693,4797,4911,17637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5303,5409,5589,5719,5828,5999,6132,6253,6527,6722,6834,7019,7155,7315,7494,7567,7634", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "5404,5584,5714,5823,5994,6127,6248,6361,6717,6829,7014,7150,7310,7489,7562,7629,7713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "73,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7836,8539,8641,8760", "endColumns": "106,101,118,104", "endOffsets": "7938,8636,8755,8860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,16542", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,16624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,75,76,77,97,99,101,102,103,104,105,106,107,108,109,110,111,112,113,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3698,3805,3913,3995,4096,4916,5016,5138,8035,8100,8166,10794,10957,11086,11178,11245,11319,11380,11459,11523,11577,11693,11752,11814,11868,11950,12239,12331,12406,12501,12582,12666,12810,12889,12970,13117,13210,13289,13344,13395,13461,13540,13621,13692,13772,13844,13922,13997,14069,14180,14277,14354,14452,14550,14628,14709,14809,14866,14950,15016,15099,15186,15248,15312,15375,15451,15553,15660,15757,15863,15922,15977,16629,16716,16793", "endLines": "5,38,39,40,41,42,50,51,52,75,76,77,97,99,101,102,103,104,105,106,107,108,109,110,111,112,113,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3800,3908,3990,4091,4188,5011,5133,5218,8095,8161,8258,10869,11014,11173,11240,11314,11375,11454,11518,11572,11688,11747,11809,11863,11945,12074,12326,12401,12496,12577,12661,12805,12884,12965,13112,13205,13284,13339,13390,13456,13535,13616,13687,13767,13839,13917,13992,14064,14175,14272,14349,14447,14545,14623,14704,14804,14861,14945,15011,15094,15181,15243,15307,15370,15446,15548,15655,15752,15858,15917,15972,16061,16711,16788,16869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6366", "endColumns": "160", "endOffsets": "6522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3153,3269", "endColumns": "115,118", "endOffsets": "3264,3383"}}]}]}