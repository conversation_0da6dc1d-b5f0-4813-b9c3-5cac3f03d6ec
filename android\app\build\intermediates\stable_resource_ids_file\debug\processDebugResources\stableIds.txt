studio.takasaki.clubm:xml/secure_store_data_extraction_rules = 0x7f150007
studio.takasaki.clubm:xml/secure_store_backup_rules = 0x7f150006
studio.takasaki.clubm:xml/rn_dev_preferences = 0x7f150005
studio.takasaki.clubm:xml/image_picker_provider_paths = 0x7f150002
studio.takasaki.clubm:xml/file_system_provider_paths = 0x7f150001
studio.takasaki.clubm:xml/clipboard_provider_paths = 0x7f150000
studio.takasaki.clubm:styleable/ViewPager2 = 0x7f140098
studio.takasaki.clubm:styleable/View = 0x7f140096
studio.takasaki.clubm:styleable/Variant = 0x7f140095
studio.takasaki.clubm:styleable/Transition = 0x7f140094
studio.takasaki.clubm:styleable/TextInputLayout = 0x7f14008f
studio.takasaki.clubm:styleable/TabLayout = 0x7f14008c
studio.takasaki.clubm:styleable/SwitchMaterial = 0x7f14008a
studio.takasaki.clubm:styleable/State = 0x7f140084
studio.takasaki.clubm:styleable/SnackbarLayout = 0x7f140082
studio.takasaki.clubm:styleable/ShapeAppearance = 0x7f14007b
studio.takasaki.clubm:styleable/ScrollingViewBehavior_Layout = 0x7f140078
studio.takasaki.clubm:styleable/RangeSlider = 0x7f140073
studio.takasaki.clubm:styleable/NavigationBarView = 0x7f14006a
studio.takasaki.clubm:styleable/MotionScene = 0x7f140067
studio.takasaki.clubm:styleable/MotionLayout = 0x7f140066
studio.takasaki.clubm:styleable/MenuGroup = 0x7f140060
studio.takasaki.clubm:styleable/MaterialRadioButton = 0x7f140059
studio.takasaki.clubm:styleable/MaterialCheckBoxStates = 0x7f140057
studio.takasaki.clubm:styleable/MaterialCalendarItem = 0x7f140054
studio.takasaki.clubm:styleable/MaterialButtonToggleGroup = 0x7f140052
studio.takasaki.clubm:styleable/MaterialAlertDialogTheme = 0x7f14004f
studio.takasaki.clubm:styleable/MaterialAlertDialog = 0x7f14004e
studio.takasaki.clubm:styleable/LoadingImageView = 0x7f14004d
studio.takasaki.clubm:styleable/Layout = 0x7f140048
studio.takasaki.clubm:styleable/KeyTimeCycle = 0x7f140046
studio.takasaki.clubm:styleable/KeyPosition = 0x7f140045
studio.takasaki.clubm:styleable/KeyFramesVelocity = 0x7f140044
studio.takasaki.clubm:styleable/KeyFrame = 0x7f140042
studio.takasaki.clubm:styleable/KeyCycle = 0x7f140041
studio.takasaki.clubm:styleable/KeyAttribute = 0x7f140040
studio.takasaki.clubm:styleable/FragmentContainerView = 0x7f14003a
studio.takasaki.clubm:styleable/Fragment = 0x7f140039
studio.takasaki.clubm:styleable/FontFamilyFont = 0x7f140037
studio.takasaki.clubm:styleable/FontFamily = 0x7f140036
studio.takasaki.clubm:styleable/FloatingActionButton_Behavior_Layout = 0x7f140034
studio.takasaki.clubm:styleable/DrawerLayout = 0x7f140030
studio.takasaki.clubm:styleable/CoordinatorLayout_Layout = 0x7f14002c
studio.takasaki.clubm:styleable/ConstraintLayout_placeholder = 0x7f140029
studio.takasaki.clubm:styleable/ConstraintLayout_Layout = 0x7f140028
studio.takasaki.clubm:styleable/ColorStateListItem = 0x7f140025
studio.takasaki.clubm:styleable/CollapsingToolbarLayout_Layout = 0x7f140024
studio.takasaki.clubm:styleable/CollapsingToolbarLayout = 0x7f140023
studio.takasaki.clubm:styleable/CircularProgressIndicator = 0x7f140020
studio.takasaki.clubm:styleable/Carousel = 0x7f14001c
studio.takasaki.clubm:styleable/DrawerArrowToggle = 0x7f14002f
studio.takasaki.clubm:styleable/Badge = 0x7f140014
studio.takasaki.clubm:styleable/SwitchCompat = 0x7f140089
studio.takasaki.clubm:styleable/AppBarLayout_Layout = 0x7f14000c
studio.takasaki.clubm:styleable/AppBarLayout = 0x7f14000a
studio.takasaki.clubm:styleable/AnimatedStateListDrawableItem = 0x7f140008
studio.takasaki.clubm:styleable/ActivityChooserView = 0x7f140005
studio.takasaki.clubm:styleable/ActionMode = 0x7f140004
studio.takasaki.clubm:styleable/ActionMenuItemView = 0x7f140002
studio.takasaki.clubm:styleable/FlowLayout = 0x7f140035
studio.takasaki.clubm:styleable/ActionBar = 0x7f140000
studio.takasaki.clubm:style/Widget.Support.CoordinatorLayout = 0x7f130495
studio.takasaki.clubm:style/Widget.MaterialComponents.Tooltip = 0x7f130494
studio.takasaki.clubm:style/Widget.MaterialComponents.Toolbar = 0x7f130490
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f13048d
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Button = 0x7f130487
studio.takasaki.clubm:style/Widget.MaterialComponents.TextView = 0x7f130485
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f130480
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f13047e
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f13047d
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f13047a
studio.takasaki.clubm:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f130477
studio.takasaki.clubm:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f130475
studio.takasaki.clubm:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f130474
studio.takasaki.clubm:styleable/ActionMenuView = 0x7f140003
studio.takasaki.clubm:style/Widget.MaterialComponents.Snackbar = 0x7f130473
studio.takasaki.clubm:style/Widget.MaterialComponents.ProgressIndicator = 0x7f130470
studio.takasaki.clubm:style/Widget.MaterialComponents.PopupMenu = 0x7f13046c
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f13046a
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f130464
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f130462
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f13045f
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f13045e
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f130459
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f130457
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130454
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f130453
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f130452
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f130451
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f13044f
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f13044e
studio.takasaki.clubm:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f13044b
studio.takasaki.clubm:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f130448
studio.takasaki.clubm:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f130446
studio.takasaki.clubm:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f130445
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f13047c
studio.takasaki.clubm:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f130440
studio.takasaki.clubm:style/Widget.MaterialComponents.ChipGroup = 0x7f13043e
studio.takasaki.clubm:style/Widget.MaterialComponents.Chip.Filter = 0x7f13043d
studio.takasaki.clubm:style/Widget.MaterialComponents.Chip.Choice = 0x7f13043b
studio.takasaki.clubm:style/Widget.MaterialComponents.Chip.Action = 0x7f13043a
studio.takasaki.clubm:style/Widget.MaterialComponents.CheckedTextView = 0x7f130439
studio.takasaki.clubm:styleable/KeyFramesAcceleration = 0x7f140043
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f130437
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f130435
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f130434
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f130432
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f130431
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomNavigationView = 0x7f130427
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomAppBar = 0x7f130424
studio.takasaki.clubm:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f13041f
studio.takasaki.clubm:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f13041e
studio.takasaki.clubm:style/Widget.MaterialComponents.ActionMode = 0x7f13041b
studio.takasaki.clubm:style/redboxButton = 0x7f130496
studio.takasaki.clubm:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f13041a
studio.takasaki.clubm:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f130418
studio.takasaki.clubm:style/Widget.Material3.Tooltip = 0x7f130416
studio.takasaki.clubm:style/Widget.Material3.Toolbar.OnSurface = 0x7f130414
studio.takasaki.clubm:style/Widget.Material3.Toolbar = 0x7f130413
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130412
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130411
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13040d
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f13040c
studio.takasaki.clubm:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f13040a
studio.takasaki.clubm:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f130407
studio.takasaki.clubm:style/Widget.Material3.TabLayout.Secondary = 0x7f130406
studio.takasaki.clubm:style/Widget.Material3.Snackbar.TextView = 0x7f130403
studio.takasaki.clubm:style/Widget.Material3.Snackbar.FullWidth = 0x7f130402
studio.takasaki.clubm:style/Widget.Material3.Slider = 0x7f1303fd
studio.takasaki.clubm:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1303fc
studio.takasaki.clubm:style/Widget.Material3.SideSheet.Modal = 0x7f1303fb
studio.takasaki.clubm:style/Widget.Material3.SideSheet.Detached = 0x7f1303fa
studio.takasaki.clubm:style/Widget.Material3.SideSheet = 0x7f1303f9
studio.takasaki.clubm:styleable/KeyTrigger = 0x7f140047
studio.takasaki.clubm:style/Widget.Material3.SearchView.Prefix = 0x7f1303f7
studio.takasaki.clubm:styleable/MaterialCalendar = 0x7f140053
studio.takasaki.clubm:style/Widget.Material3.SearchView = 0x7f1303f6
studio.takasaki.clubm:style/Widget.Material3.SearchBar.Outlined = 0x7f1303f5
studio.takasaki.clubm:style/Widget.Material3.SearchBar = 0x7f1303f4
studio.takasaki.clubm:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1303f2
studio.takasaki.clubm:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1303f0
studio.takasaki.clubm:style/Widget.Material3.PopupMenu = 0x7f1303ee
studio.takasaki.clubm:style/Widget.Material3.NavigationView = 0x7f1303ed
studio.takasaki.clubm:style/Widget.Material3.NavigationRailView.Badge = 0x7f1303ec
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1303e9
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1303e6
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1303e4
studio.takasaki.clubm:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1303e0
studio.takasaki.clubm:style/Widget.Material3.MaterialDivider = 0x7f1303df
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1303de
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1303dd
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1303d9
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1303d6
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1303d4
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1303d3
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1303ce
studio.takasaki.clubm:styleable/AlertDialog = 0x7f140006
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1303cd
studio.takasaki.clubm:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1303c6
studio.takasaki.clubm:style/Widget.Material3.LinearProgressIndicator = 0x7f1303c5
studio.takasaki.clubm:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1303c4
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1303c3
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1303c2
studio.takasaki.clubm:styleable/OnClick = 0x7f14006d
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1303be
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1303b6
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1303b5
studio.takasaki.clubm:styleable/TabItem = 0x7f14008b
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1303b4
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1303b1
studio.takasaki.clubm:style/Widget.Material3.CompoundButton.Switch = 0x7f1303ae
studio.takasaki.clubm:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1303ab
studio.takasaki.clubm:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1303a9
studio.takasaki.clubm:style/Widget.Material3.CollapsingToolbar = 0x7f1303a8
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1303a5
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1303a4
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1303a1
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator = 0x7f1303a0
studio.takasaki.clubm:style/Widget.Material3.ChipGroup = 0x7f13039f
studio.takasaki.clubm:style/Widget.Material3.Chip.Suggestion = 0x7f13039d
studio.takasaki.clubm:style/Widget.MaterialComponents.TabLayout = 0x7f130476
studio.takasaki.clubm:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f13039c
studio.takasaki.clubm:style/Widget.Material3.Chip.Input.Elevated = 0x7f13039a
studio.takasaki.clubm:style/Widget.Material3.Chip.Assist = 0x7f130395
studio.takasaki.clubm:style/Widget.Material3.Button.UnelevatedButton = 0x7f130390
studio.takasaki.clubm:style/Widget.Material3.Button.TonalButton.Icon = 0x7f13038f
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f13038b
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f13038a
studio.takasaki.clubm:style/Widget.Material3.Button.IconButton.Filled = 0x7f130383
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1303e8
studio.takasaki.clubm:style/Widget.Material3.Button.IconButton = 0x7f130382
studio.takasaki.clubm:style/Widget.Material3.Button.ElevatedButton = 0x7f13037f
studio.takasaki.clubm:style/Widget.Material3.BottomSheet.DragHandle = 0x7f13037c
studio.takasaki.clubm:style/Widget.Material3.BottomSheet = 0x7f13037b
studio.takasaki.clubm:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f13037a
studio.takasaki.clubm:style/Widget.Material3.BottomNavigationView = 0x7f130379
studio.takasaki.clubm:style/Widget.Material3.BottomNavigation.Badge = 0x7f130378
studio.takasaki.clubm:style/Widget.Material3.BottomAppBar.Legacy = 0x7f130377
studio.takasaki.clubm:style/Widget.Material3.BottomAppBar = 0x7f130375
studio.takasaki.clubm:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130372
studio.takasaki.clubm:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f130371
studio.takasaki.clubm:style/Widget.Material3.Chip.Assist.Elevated = 0x7f130396
studio.takasaki.clubm:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f130370
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1303bc
studio.takasaki.clubm:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f13036f
studio.takasaki.clubm:style/Widget.Material3.ActionMode = 0x7f13036d
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker = 0x7f1303e1
studio.takasaki.clubm:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f130367
studio.takasaki.clubm:style/Widget.Design.NavigationView = 0x7f130366
studio.takasaki.clubm:style/Widget.Design.FloatingActionButton = 0x7f130365
studio.takasaki.clubm:style/Widget.Design.BottomSheet.Modal = 0x7f130363
studio.takasaki.clubm:style/Widget.Design.BottomNavigationView = 0x7f130362
studio.takasaki.clubm:style/Widget.Design.AppBarLayout = 0x7f130361
studio.takasaki.clubm:style/Widget.Autofill.InlineSuggestionTitle = 0x7f13035e
studio.takasaki.clubm:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f13035c
studio.takasaki.clubm:style/Widget.AppCompat.Spinner = 0x7f130351
studio.takasaki.clubm:style/Widget.AppCompat.SeekBar.Discrete = 0x7f130350
studio.takasaki.clubm:style/Widget.AppCompat.ProgressBar = 0x7f130348
studio.takasaki.clubm:style/Widget.AppCompat.PopupWindow = 0x7f130347
studio.takasaki.clubm:style/Widget.AppCompat.ListView = 0x7f130342
studio.takasaki.clubm:style/Widget.AppCompat.ListMenuView = 0x7f130340
studio.takasaki.clubm:style/Widget.AppCompat.Light.SearchView = 0x7f13033e
studio.takasaki.clubm:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f13033b
studio.takasaki.clubm:styleable/BaseProgressIndicator = 0x7f140015
studio.takasaki.clubm:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f130339
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f130337
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f130336
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f130335
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f130332
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f130330
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f13032e
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar = 0x7f13032a
studio.takasaki.clubm:style/Widget.AppCompat.ImageButton = 0x7f130329
studio.takasaki.clubm:style/Widget.AppCompat.CompoundButton.Switch = 0x7f130325
studio.takasaki.clubm:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f130322
studio.takasaki.clubm:style/Widget.AppCompat.ButtonBar = 0x7f130321
studio.takasaki.clubm:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f13031e
studio.takasaki.clubm:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f13031d
studio.takasaki.clubm:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1303c7
studio.takasaki.clubm:style/Widget.AppCompat.AutoCompleteTextView = 0x7f13031a
studio.takasaki.clubm:style/Widget.AppCompat.ActivityChooserView = 0x7f130319
studio.takasaki.clubm:style/Widget.AppCompat.ActionMode = 0x7f130318
studio.takasaki.clubm:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f130316
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionButton = 0x7f130333
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f13030d
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f13030b
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f130306
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f130303
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f130301
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1302fe
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1302fd
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1302f7
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1302f5
studio.takasaki.clubm:style/Widget.Material3.TabLayout = 0x7f130404
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1302f2
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton.Dialog = 0x7f130389
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1302ef
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ee
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1302ec
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1302eb
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1302ea
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1302e9
studio.takasaki.clubm:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1302e7
studio.takasaki.clubm:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1302e5
studio.takasaki.clubm:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1302e3
studio.takasaki.clubm:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1302e2
studio.takasaki.clubm:style/ThemeOverlay.Material3.TabLayout = 0x7f1302e0
studio.takasaki.clubm:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1302dc
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1302d8
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1302d7
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1302d6
studio.takasaki.clubm:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1302d0
studio.takasaki.clubm:style/Widget.AppCompat.ListView.DropDown = 0x7f130343
studio.takasaki.clubm:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1302cd
studio.takasaki.clubm:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1302cc
studio.takasaki.clubm:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1302c8
studio.takasaki.clubm:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1302c7
studio.takasaki.clubm:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1302c6
studio.takasaki.clubm:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1302c5
studio.takasaki.clubm:style/Widget.Material3.Button.IconButton.Outlined = 0x7f130385
studio.takasaki.clubm:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1302c2
studio.takasaki.clubm:style/ThemeOverlay.Material3.Dialog = 0x7f1302c1
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomSheet = 0x7f13042a
studio.takasaki.clubm:style/ThemeOverlay.Material3.Dark = 0x7f1302bd
studio.takasaki.clubm:style/ThemeOverlay.Material3.Chip = 0x7f1302bb
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1302b9
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1302b8
studio.takasaki.clubm:style/Widget.Material3.SearchView.Toolbar = 0x7f1303f8
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1302b4
studio.takasaki.clubm:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1302b2
studio.takasaki.clubm:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1302b1
studio.takasaki.clubm:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1302b0
studio.takasaki.clubm:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302ae
studio.takasaki.clubm:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1302ad
studio.takasaki.clubm:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ac
studio.takasaki.clubm:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1302ab
studio.takasaki.clubm:styleable/BottomAppBar = 0x7f140016
studio.takasaki.clubm:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1302aa
studio.takasaki.clubm:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f130338
studio.takasaki.clubm:style/ThemeOverlay.Material3 = 0x7f1302a8
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.Light = 0x7f1302a6
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1302a3
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.ActionBar = 0x7f13029f
studio.takasaki.clubm:style/ThemeOverlay.AppCompat = 0x7f13029e
studio.takasaki.clubm:style/Theme.SplashScreen.Common = 0x7f13029c
studio.takasaki.clubm:style/Theme.PlayCore.Transparent = 0x7f130297
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f130293
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f130292
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f130291
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f130290
studio.takasaki.clubm:styleable/ViewBackgroundHelper = 0x7f140097
studio.takasaki.clubm:styleable/AppCompatEmojiHelper = 0x7f14000d
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f13028e
studio.takasaki.clubm:style/Theme.SplashScreen.IconBackground = 0x7f13029d
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f13028b
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130289
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f130288
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Bridge = 0x7f130287
studio.takasaki.clubm:style/Theme.MaterialComponents.Light = 0x7f130285
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f13027f
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f13027e
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.Alert = 0x7f13027d
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f13027b
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f130279
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f130278
studio.takasaki.clubm:style/Widget.AppCompat.ActionBar.TabBar = 0x7f130312
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f130277
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f130276
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f130274
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f130273
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f130272
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f13026f
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight = 0x7f13026c
studio.takasaki.clubm:style/Theme.MaterialComponents.CompactMenu = 0x7f13026b
studio.takasaki.clubm:style/Theme.MaterialComponents.Bridge = 0x7f13026a
studio.takasaki.clubm:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f130269
studio.takasaki.clubm:style/Widget.Material3.Button = 0x7f13037e
studio.takasaki.clubm:style/Theme.MaterialComponents = 0x7f130268
studio.takasaki.clubm:style/Theme.Material3.Light.NoActionBar = 0x7f130266
studio.takasaki.clubm:style/Theme.Material3.Light.DialogWhenLarge = 0x7f130265
studio.takasaki.clubm:style/Theme.Material3.Light.Dialog.Alert = 0x7f130263
studio.takasaki.clubm:style/Theme.Material3.Light = 0x7f130260
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f13025b
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.Dark = 0x7f13025a
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationView = 0x7f13046b
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1302ff
studio.takasaki.clubm:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f130259
studio.takasaki.clubm:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f130257
studio.takasaki.clubm:styleable/MaterialCheckBox = 0x7f140056
studio.takasaki.clubm:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f130256
studio.takasaki.clubm:style/Theme.Material3.Dark.NoActionBar = 0x7f130250
studio.takasaki.clubm:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f13024f
studio.takasaki.clubm:style/Theme.Material3.Dark.Dialog.Alert = 0x7f13024d
studio.takasaki.clubm:style/Theme.FullScreenDialogAnimatedSlide = 0x7f130248
studio.takasaki.clubm:style/Theme.FullScreenDialogAnimatedFade = 0x7f130247
studio.takasaki.clubm:style/Theme.Design.NoActionBar = 0x7f130243
studio.takasaki.clubm:style/Theme.Design.Light.BottomSheetDialog = 0x7f130241
studio.takasaki.clubm:style/Theme.Catalyst = 0x7f13023b
studio.takasaki.clubm:style/Theme.AppCompat.Transparent.NoActionBar = 0x7f130239
studio.takasaki.clubm:style/Theme.AppCompat.Light.NoActionBar = 0x7f130237
studio.takasaki.clubm:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130235
studio.takasaki.clubm:style/Theme.AppCompat.Light = 0x7f130231
studio.takasaki.clubm:style/Theme.AppCompat.Empty = 0x7f130230
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f130229
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f130226
studio.takasaki.clubm:style/Theme.AppCompat.DayNight = 0x7f130225
studio.takasaki.clubm:style/Theme.AppCompat.CompactMenu = 0x7f130224
studio.takasaki.clubm:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130220
studio.takasaki.clubm:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f13021e
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Tooltip = 0x7f13021d
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Overline = 0x7f130219
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline6 = 0x7f130218
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline4 = 0x7f130216
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline3 = 0x7f130215
studio.takasaki.clubm:styleable/NavigationRailView = 0x7f14006b
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline1 = 0x7f130213
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Button = 0x7f130210
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Badge = 0x7f13020d
studio.takasaki.clubm:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f130206
studio.takasaki.clubm:style/TextAppearance.Material3.LabelLarge = 0x7f130203
studio.takasaki.clubm:style/TextAppearance.Material3.HeadlineMedium = 0x7f130201
studio.takasaki.clubm:style/TextAppearance.Material3.DisplaySmall = 0x7f1301ff
studio.takasaki.clubm:style/TextAppearance.Material3.DisplayMedium = 0x7f1301fe
studio.takasaki.clubm:style/TextAppearance.Material3.BodySmall = 0x7f1301fc
studio.takasaki.clubm:style/TextAppearance.Material3.BodyMedium = 0x7f1301fb
studio.takasaki.clubm:style/TextAppearance.Material3.BodyLarge = 0x7f1301fa
studio.takasaki.clubm:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1301f8
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1301f1
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1301ed
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1301ec
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1301ea
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f130425
studio.takasaki.clubm:style/TextAppearance.Design.Tab = 0x7f1301e8
studio.takasaki.clubm:style/TextAppearance.Design.Snackbar.Message = 0x7f1301e6
studio.takasaki.clubm:style/TextAppearance.Design.Hint = 0x7f1301e3
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f130458
studio.takasaki.clubm:style/TextAppearance.Design.Error = 0x7f1301e1
studio.takasaki.clubm:style/TextAppearance.Design.Counter.Overflow = 0x7f1301e0
studio.takasaki.clubm:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1301de
studio.takasaki.clubm:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1302d2
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1301db
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1301d6
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Info = 0x7f1301d5
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1301d1
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1301d0
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1301ce
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.Button = 0x7f1301ca
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1301c9
studio.takasaki.clubm:style/Widget.AppCompat.ActionButton = 0x7f130315
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1301c8
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1301c7
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1301c6
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1303c1
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1301c5
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1301c3
studio.takasaki.clubm:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f130478
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1301c2
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1301c1
studio.takasaki.clubm:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1301b9
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1302d4
studio.takasaki.clubm:style/TextAppearance.AppCompat.Menu = 0x7f1301b7
studio.takasaki.clubm:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1301b6
studio.takasaki.clubm:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1301b4
studio.takasaki.clubm:style/TextAppearance.AppCompat.Inverse = 0x7f1301ae
studio.takasaki.clubm:style/TextAppearance.AppCompat.Display4 = 0x7f1301ac
studio.takasaki.clubm:style/TextAppearance.AppCompat.Display1 = 0x7f1301a9
studio.takasaki.clubm:style/TextAppearance.AppCompat.Caption = 0x7f1301a8
studio.takasaki.clubm:style/TextAppearance.AppCompat.Button = 0x7f1301a7
studio.takasaki.clubm:style/TextAppearance.AppCompat.Body2 = 0x7f1301a6
studio.takasaki.clubm:style/TextAppearance.AppCompat.Body1 = 0x7f1301a5
studio.takasaki.clubm:style/SpinnerDatePickerDialog = 0x7f1301a2
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1301a0
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f13019f
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f130199
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f130192
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Button = 0x7f13018f
studio.takasaki.clubm:style/Theme.Material3.Light.Dialog = 0x7f130262
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f13018e
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f13018b
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents = 0x7f130189
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f13017d
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f13017b
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f13017a
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f130177
studio.takasaki.clubm:style/Widget.AppCompat.ActionBar.TabView = 0x7f130314
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f130176
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f130172
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f130171
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f13016e
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f13016d
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f13016c
studio.takasaki.clubm:styleable/ChipGroup = 0x7f14001f
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f13016b
studio.takasaki.clubm:styleable/RadialViewGroup = 0x7f140072
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f130169
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f130168
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f130167
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f130166
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f130164
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f130161
studio.takasaki.clubm:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1302c0
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f13015f
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f13015d
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f13015b
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f13045d
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f13015a
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f130157
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f130155
studio.takasaki.clubm:style/Platform.V25.AppCompat.Light = 0x7f130151
studio.takasaki.clubm:style/Platform.V21.AppCompat = 0x7f13014e
studio.takasaki.clubm:style/Platform.ThemeOverlay.AppCompat = 0x7f13014b
studio.takasaki.clubm:style/Platform.MaterialComponents.Light.Dialog = 0x7f13014a
studio.takasaki.clubm:style/Platform.MaterialComponents.Dialog = 0x7f130148
studio.takasaki.clubm:style/Platform.AppCompat.Light = 0x7f130146
studio.takasaki.clubm:style/NoAnimationDialog = 0x7f130144
studio.takasaki.clubm:style/Widget.Material3.Badge = 0x7f130373
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130142
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130140
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f13048a
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f13013f
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f13013e
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Text = 0x7f130138
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f130136
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f130135
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Body.Text = 0x7f130132
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1301f2
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Animation = 0x7f130131
studio.takasaki.clubm:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f130384
studio.takasaki.clubm:style/DialogAnimationSlide = 0x7f13012f
studio.takasaki.clubm:style/CardView.Light = 0x7f13012d
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1303e7
studio.takasaki.clubm:style/CalendarDatePickerDialog = 0x7f130129
studio.takasaki.clubm:style/Base.v27.Theme.SplashScreen = 0x7f130127
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.TextView = 0x7f130124
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f130122
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13011e
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f13011c
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f13011b
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.Chip = 0x7f130119
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f130117
studio.takasaki.clubm:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f130115
studio.takasaki.clubm:style/Base.Widget.Material3.Snackbar = 0x7f130113
studio.takasaki.clubm:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f130111
studio.takasaki.clubm:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f130110
studio.takasaki.clubm:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f13010c
studio.takasaki.clubm:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f13010b
studio.takasaki.clubm:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f130109
studio.takasaki.clubm:style/Base.Widget.Material3.CardView = 0x7f130106
studio.takasaki.clubm:style/Base.Widget.Material3.ActionBar.Solid = 0x7f130103
studio.takasaki.clubm:style/Base.Widget.AppCompat.Toolbar = 0x7f130100
studio.takasaki.clubm:style/Base.Widget.AppCompat.Spinner = 0x7f1300fc
studio.takasaki.clubm:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1300fb
studio.takasaki.clubm:style/Base.Widget.AppCompat.RatingBar = 0x7f1300f5
studio.takasaki.clubm:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f130441
studio.takasaki.clubm:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1300ec
studio.takasaki.clubm:style/Widget.Material3.TabLayout.OnSurface = 0x7f130405
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1300e7
studio.takasaki.clubm:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1300e0
studio.takasaki.clubm:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1300de
studio.takasaki.clubm:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1300da
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1300d6
studio.takasaki.clubm:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1302ce
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1300d5
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button = 0x7f1300d3
studio.takasaki.clubm:style/Widget.Autofill.InlineSuggestionChip = 0x7f13035a
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionMode = 0x7f1300d0
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1300cf
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1300ce
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1300cc
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1300cb
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1300ca
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f130334
studio.takasaki.clubm:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1300c7
studio.takasaki.clubm:style/Base.V7.Widget.AppCompat.EditText = 0x7f1300c6
studio.takasaki.clubm:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1300c1
studio.takasaki.clubm:style/Base.V28.Theme.AppCompat.Light = 0x7f1300bf
studio.takasaki.clubm:style/Base.V28.Theme.AppCompat = 0x7f1300be
studio.takasaki.clubm:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1300b8
studio.takasaki.clubm:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300b2
studio.takasaki.clubm:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1300b1
studio.takasaki.clubm:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1300ac
studio.takasaki.clubm:style/Base.V21.Theme.MaterialComponents = 0x7f1300ab
studio.takasaki.clubm:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1300aa
studio.takasaki.clubm:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1300a8
studio.takasaki.clubm:style/Base.V21.Theme.AppCompat = 0x7f1300a7
studio.takasaki.clubm:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1300a6
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1300a3
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f1300a1
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13009f
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f13009e
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f13009d
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Light = 0x7f13009b
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f13009a
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Light = 0x7f130093
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f130091
studio.takasaki.clubm:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f13008d
studio.takasaki.clubm:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f13008a
studio.takasaki.clubm:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f130089
studio.takasaki.clubm:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f130088
studio.takasaki.clubm:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f130086
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f130083
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f130081
studio.takasaki.clubm:style/Widget.MaterialComponents.CardView = 0x7f130438
studio.takasaki.clubm:style/Base.Theme.SplashScreen.Light = 0x7f13007d
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f13007a
studio.takasaki.clubm:style/TextAppearance.AppCompat.Tooltip = 0x7f1301c0
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f130078
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f130300
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130076
studio.takasaki.clubm:style/Widget.Design.TextInputEditText = 0x7f13036a
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130074
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f130072
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light = 0x7f130071
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Dialog = 0x7f13006b
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f13006a
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Bridge = 0x7f130069
studio.takasaki.clubm:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f130066
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f13005b
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13047f
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f13005a
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f130056
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light = 0x7f130055
studio.takasaki.clubm:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f130054
studio.takasaki.clubm:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f130051
studio.takasaki.clubm:style/Base.Theme.AppCompat = 0x7f13004e
studio.takasaki.clubm:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f13004d
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1303b2
studio.takasaki.clubm:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f13004c
studio.takasaki.clubm:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f130049
studio.takasaki.clubm:styleable/ButtonBarLayout = 0x7f140019
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f130045
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f130042
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f130040
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f13003f
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f13003e
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f13003c
studio.takasaki.clubm:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300b0
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f130036
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f130035
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f130033
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Small = 0x7f13002e
studio.takasaki.clubm:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f130264
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f130029
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f130027
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Large = 0x7f130024
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1300d4
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Dark = 0x7f13008f
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Headline = 0x7f130022
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Display2 = 0x7f13001f
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker = 0x7f130486
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Body1 = 0x7f13001a
studio.takasaki.clubm:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130017
studio.takasaki.clubm:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f130015
studio.takasaki.clubm:style/Base.DialogWindowTitle.AppCompat = 0x7f130014
studio.takasaki.clubm:style/Base.Animation.AppCompat.Tooltip = 0x7f130012
studio.takasaki.clubm:style/Animation.Material3.SideSheetDialog.Right = 0x7f13000b
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f13003a
studio.takasaki.clubm:style/Animation.Material3.SideSheetDialog.Left = 0x7f13000a
studio.takasaki.clubm:style/Animation.Material3.SideSheetDialog = 0x7f130009
studio.takasaki.clubm:style/Animation.Material3.BottomSheetDialog = 0x7f130008
studio.takasaki.clubm:style/ThemeOverlay.Design.TextInputEditText = 0x7f1302a7
studio.takasaki.clubm:style/Animation.Catalyst.RedBox = 0x7f130006
studio.takasaki.clubm:style/Animation.Catalyst.LogBox = 0x7f130005
studio.takasaki.clubm:style/Animation.AppCompat.DropDownUp = 0x7f130003
studio.takasaki.clubm:style/Animation.AppCompat.Dialog = 0x7f130002
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f13048b
studio.takasaki.clubm:style/CardView.Dark = 0x7f13012c
studio.takasaki.clubm:string/use_biometric_or_screen_lock_label = 0x7f120121
studio.takasaki.clubm:string/use_biometric_label = 0x7f120120
studio.takasaki.clubm:style/Base.V23.Theme.AppCompat = 0x7f1300b5
studio.takasaki.clubm:string/toolbar_description = 0x7f12011f
studio.takasaki.clubm:string/tablist_description = 0x7f12011d
studio.takasaki.clubm:string/state_busy_description = 0x7f120114
studio.takasaki.clubm:string/side_sheet_accessibility_pane_title = 0x7f120110
studio.takasaki.clubm:string/searchview_navigation_content_description = 0x7f12010f
studio.takasaki.clubm:string/searchview_clear_text_content_description = 0x7f12010e
studio.takasaki.clubm:string/searchbar_scrolling_view_behavior = 0x7f12010d
studio.takasaki.clubm:string/scrollbar_description = 0x7f12010b
studio.takasaki.clubm:string/screen_lock_prompt_message = 0x7f12010a
studio.takasaki.clubm:string/rn_tab_description = 0x7f120109
studio.takasaki.clubm:string/pick_image_gallery = 0x7f120105
studio.takasaki.clubm:string/path_password_eye_mask_visible = 0x7f120101
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents.Badge = 0x7f13018a
studio.takasaki.clubm:string/path_password_eye_mask_strike_through = 0x7f120100
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.Dialog = 0x7f130227
studio.takasaki.clubm:string/mtrl_timepicker_cancel = 0x7f1200fc
studio.takasaki.clubm:string/mtrl_switch_track_path = 0x7f1200fb
studio.takasaki.clubm:string/mtrl_switch_thumb_path_unchecked = 0x7f1200f9
studio.takasaki.clubm:string/mtrl_switch_thumb_path_pressed = 0x7f1200f8
studio.takasaki.clubm:string/mtrl_switch_thumb_path_name = 0x7f1200f7
studio.takasaki.clubm:string/mtrl_switch_thumb_path_morphing = 0x7f1200f6
studio.takasaki.clubm:string/mtrl_switch_thumb_path_checked = 0x7f1200f5
studio.takasaki.clubm:string/mtrl_picker_toggle_to_year_selection = 0x7f1200f3
studio.takasaki.clubm:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1200f0
studio.takasaki.clubm:string/mtrl_picker_text_input_year_abbr = 0x7f1200ee
studio.takasaki.clubm:style/Platform.MaterialComponents.Light = 0x7f130149
studio.takasaki.clubm:string/state_on_description = 0x7f120119
studio.takasaki.clubm:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1200ea
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f13040f
studio.takasaki.clubm:string/mtrl_picker_start_date_description = 0x7f1200e8
studio.takasaki.clubm:string/mtrl_picker_save = 0x7f1200e7
studio.takasaki.clubm:string/mtrl_picker_range_header_unselected = 0x7f1200e6
studio.takasaki.clubm:string/mtrl_picker_range_header_title = 0x7f1200e5
studio.takasaki.clubm:string/mtrl_picker_range_header_only_end_selected = 0x7f1200e2
studio.takasaki.clubm:string/mtrl_picker_navigate_to_year_description = 0x7f1200e0
studio.takasaki.clubm:style/Theme.Material3.DayNight.Dialog = 0x7f130254
studio.takasaki.clubm:string/mtrl_picker_navigate_to_current_year_description = 0x7f1200df
studio.takasaki.clubm:string/mtrl_picker_invalid_range = 0x7f1200de
studio.takasaki.clubm:string/mtrl_picker_invalid_format_example = 0x7f1200dc
studio.takasaki.clubm:string/mtrl_picker_date_header_title = 0x7f1200d7
studio.takasaki.clubm:string/mtrl_picker_date_header_selected = 0x7f1200d6
studio.takasaki.clubm:string/mtrl_picker_confirm = 0x7f1200d5
studio.takasaki.clubm:string/mtrl_picker_announce_current_selection = 0x7f1200d2
studio.takasaki.clubm:style/Base.Widget.AppCompat.PopupWindow = 0x7f1300f2
studio.takasaki.clubm:string/mtrl_picker_announce_current_range_selection = 0x7f1200d1
studio.takasaki.clubm:string/mtrl_picker_a11y_next_month = 0x7f1200cf
studio.takasaki.clubm:string/mtrl_exceed_max_badge_number_suffix = 0x7f1200ce
studio.takasaki.clubm:string/mtrl_chip_close_icon_content_description = 0x7f1200cc
studio.takasaki.clubm:string/mtrl_checkbox_state_description_indeterminate = 0x7f1200ca
studio.takasaki.clubm:string/mtrl_checkbox_button_path_group_name = 0x7f1200c6
studio.takasaki.clubm:string/mtrl_checkbox_button_icon_path_name = 0x7f1200c4
studio.takasaki.clubm:style/Theme.Material3.Dark = 0x7f13024a
studio.takasaki.clubm:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f1200c3
studio.takasaki.clubm:string/mtrl_checkbox_button_icon_path_group_name = 0x7f1200c2
studio.takasaki.clubm:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f130067
studio.takasaki.clubm:string/mtrl_badge_numberless_content_description = 0x7f1200c0
studio.takasaki.clubm:string/menubar_description = 0x7f1200be
studio.takasaki.clubm:string/menu_description = 0x7f1200bd
studio.takasaki.clubm:string/material_timepicker_select_time = 0x7f1200bb
studio.takasaki.clubm:string/material_timepicker_minute = 0x7f1200b9
studio.takasaki.clubm:string/material_slider_value = 0x7f1200b5
studio.takasaki.clubm:string/material_slider_range_start = 0x7f1200b4
studio.takasaki.clubm:string/material_motion_easing_decelerated = 0x7f1200af
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f130468
studio.takasaki.clubm:string/material_minute_suffix = 0x7f1200ad
studio.takasaki.clubm:string/material_hour_suffix = 0x7f1200ab
studio.takasaki.clubm:style/TextAppearance.Material3.TitleSmall = 0x7f13020c
studio.takasaki.clubm:string/material_hour_24h_suffix = 0x7f1200a9
studio.takasaki.clubm:style/Base.Theme.AppCompat.CompactMenu = 0x7f13004f
studio.takasaki.clubm:string/material_clock_toggle_content_description = 0x7f1200a8
studio.takasaki.clubm:styleable/ShapeableImageView = 0x7f14007c
studio.takasaki.clubm:string/m3_sys_motion_easing_standard_decelerate = 0x7f1200a6
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f13028c
studio.takasaki.clubm:string/m3_sys_motion_easing_standard = 0x7f1200a4
studio.takasaki.clubm:string/m3_sys_motion_easing_linear = 0x7f1200a3
studio.takasaki.clubm:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1200a1
studio.takasaki.clubm:string/m3_sys_motion_easing_legacy = 0x7f1200a0
studio.takasaki.clubm:string/m3_sys_motion_easing_emphasized_path_data = 0x7f12009f
studio.takasaki.clubm:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f12009e
studio.takasaki.clubm:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f12009d
studio.takasaki.clubm:string/m3_sys_motion_easing_emphasized = 0x7f12009c
studio.takasaki.clubm:string/m3_exceed_max_badge_text_suffix = 0x7f120097
studio.takasaki.clubm:string/imagebutton_description = 0x7f120094
studio.takasaki.clubm:string/ic_rotate_left_24 = 0x7f120090
studio.takasaki.clubm:string/ic_flip_24_horizontally = 0x7f12008e
studio.takasaki.clubm:style/Widget.Material3.BottomSheet.Modal = 0x7f13037d
studio.takasaki.clubm:string/ic_flip_24 = 0x7f12008d
studio.takasaki.clubm:string/header_description = 0x7f12008b
studio.takasaki.clubm:string/state_collapsed_description = 0x7f120115
studio.takasaki.clubm:string/google_crash_reporting_api_key = 0x7f120089
studio.takasaki.clubm:string/generic_error_user_canceled = 0x7f120086
studio.takasaki.clubm:string/generic_error_no_keyguard = 0x7f120085
studio.takasaki.clubm:string/generic_error_no_device_credential = 0x7f120084
studio.takasaki.clubm:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f130443
studio.takasaki.clubm:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1301b8
studio.takasaki.clubm:string/gcm_defaultSenderId = 0x7f120083
studio.takasaki.clubm:string/fingerprint_or_screen_lock_prompt_message = 0x7f120081
studio.takasaki.clubm:string/fingerprint_error_lockout = 0x7f12007d
studio.takasaki.clubm:string/face_or_screen_lock_prompt_message = 0x7f120073
studio.takasaki.clubm:string/fab_transformation_scrim_behavior = 0x7f120071
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f13032f
studio.takasaki.clubm:string/exposed_dropdown_menu_content_description = 0x7f120070
studio.takasaki.clubm:string/expo_splash_screen_resize_mode = 0x7f12006e
studio.takasaki.clubm:string/expo_notifications_fallback_channel_name = 0x7f12006d
studio.takasaki.clubm:styleable/StateListDrawableItem = 0x7f140086
studio.takasaki.clubm:string/error_a11y_label = 0x7f12006b
studio.takasaki.clubm:string/dev_launcher_reload = 0x7f12006a
studio.takasaki.clubm:string/crop_image_menu_crop = 0x7f120065
studio.takasaki.clubm:style/Widget.Material3.CardView.Elevated = 0x7f130391
studio.takasaki.clubm:string/copy_toast_msg = 0x7f120064
studio.takasaki.clubm:string/confirm_device_credential_password = 0x7f120063
studio.takasaki.clubm:string/common_signin_button_text = 0x7f120061
studio.takasaki.clubm:string/common_google_play_services_wear_update_text = 0x7f12005f
studio.takasaki.clubm:string/common_google_play_services_updating_text = 0x7f12005e
studio.takasaki.clubm:string/common_google_play_services_update_title = 0x7f12005d
studio.takasaki.clubm:string/common_google_play_services_update_button = 0x7f12005b
studio.takasaki.clubm:styleable/GenericDraweeHierarchy = 0x7f14003b
studio.takasaki.clubm:string/common_google_play_services_unsupported_text = 0x7f12005a
studio.takasaki.clubm:string/common_google_play_services_notification_ticker = 0x7f120058
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1300e9
studio.takasaki.clubm:string/common_google_play_services_notification_channel_name = 0x7f120057
studio.takasaki.clubm:string/common_google_play_services_install_title = 0x7f120056
studio.takasaki.clubm:string/common_google_play_services_install_text = 0x7f120055
studio.takasaki.clubm:style/Widget.Material3.Chip.Filter = 0x7f130397
studio.takasaki.clubm:string/common_google_play_services_install_button = 0x7f120054
studio.takasaki.clubm:string/common_google_play_services_enable_text = 0x7f120052
studio.takasaki.clubm:string/combobox_description = 0x7f120050
studio.takasaki.clubm:string/character_counter_pattern = 0x7f12004e
studio.takasaki.clubm:string/character_counter_overflowed_content_description = 0x7f12004d
studio.takasaki.clubm:styleable/ExtendedFloatingActionButton = 0x7f140031
studio.takasaki.clubm:string/catalyst_settings_title = 0x7f12004b
studio.takasaki.clubm:string/catalyst_settings = 0x7f12004a
studio.takasaki.clubm:string/catalyst_sample_profiler_toggle = 0x7f120049
studio.takasaki.clubm:string/catalyst_reload = 0x7f120045
studio.takasaki.clubm:string/catalyst_open_debugger_error = 0x7f120042
studio.takasaki.clubm:string/catalyst_hot_reloading_auto_disable = 0x7f12003d
studio.takasaki.clubm:string/catalyst_heap_capture = 0x7f12003b
studio.takasaki.clubm:string/catalyst_dismiss_button = 0x7f12003a
studio.takasaki.clubm:string/catalyst_dev_menu_header = 0x7f120038
studio.takasaki.clubm:string/catalyst_debug_open_disabled = 0x7f120037
studio.takasaki.clubm:string/catalyst_debug_open = 0x7f120036
studio.takasaki.clubm:string/call_notification_screening_text = 0x7f120031
studio.takasaki.clubm:string/bottomsheet_drag_handle_content_description = 0x7f12002a
studio.takasaki.clubm:string/bottomsheet_drag_handle_clicked = 0x7f120029
studio.takasaki.clubm:string/bottomsheet_action_collapse = 0x7f120026
studio.takasaki.clubm:string/bottom_sheet_behavior = 0x7f120025
studio.takasaki.clubm:style/Widget.Material3.PopupMenu.Overflow = 0x7f1303f1
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f130082
studio.takasaki.clubm:string/biometric_prompt_message = 0x7f120024
studio.takasaki.clubm:string/appbar_scrolling_view_behavior = 0x7f120022
studio.takasaki.clubm:string/androidx_startup = 0x7f120020
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f130426
studio.takasaki.clubm:string/alert_description = 0x7f12001d
studio.takasaki.clubm:string/abc_shareactionprovider_share_with_application = 0x7f12001b
studio.takasaki.clubm:string/abc_searchview_description_voice = 0x7f120019
studio.takasaki.clubm:string/abc_searchview_description_search = 0x7f120017
studio.takasaki.clubm:string/abc_searchview_description_query = 0x7f120016
studio.takasaki.clubm:string/abc_menu_sym_shortcut_label = 0x7f120012
studio.takasaki.clubm:string/abc_menu_meta_shortcut_label = 0x7f12000f
studio.takasaki.clubm:string/abc_menu_alt_shortcut_label = 0x7f12000a
studio.takasaki.clubm:string/abc_capital_on = 0x7f120009
studio.takasaki.clubm:string/abc_capital_off = 0x7f120008
studio.takasaki.clubm:string/abc_action_menu_overflow_description = 0x7f120004
studio.takasaki.clubm:string/abc_action_bar_up_description = 0x7f120003
studio.takasaki.clubm:string/ExpoLocalization_supportsRTL = 0x7f120001
studio.takasaki.clubm:raw/keep = 0x7f110001
studio.takasaki.clubm:plurals/mtrl_badge_content_description = 0x7f100000
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f130436
studio.takasaki.clubm:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f13033d
studio.takasaki.clubm:mipmap/ic_launcher_round = 0x7f0f0002
studio.takasaki.clubm:styleable/MaterialSwitch = 0x7f14005b
studio.takasaki.clubm:mipmap/ic_launcher = 0x7f0f0000
studio.takasaki.clubm:macro/m3_sys_color_dark_surface_tint = 0x7f0d0175
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0d0174
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0d0173
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0d0172
studio.takasaki.clubm:string/use_screen_lock_label = 0x7f120126
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0d0171
studio.takasaki.clubm:string/google_api_key = 0x7f120087
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0d0170
studio.takasaki.clubm:macro/m3_comp_top_app_bar_small_container_color = 0x7f0d016f
studio.takasaki.clubm:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0d016c
studio.takasaki.clubm:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0d016b
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0d016a
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0d0168
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0d0166
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0d0161
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0d0160
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0d0158
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0d0157
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0d0155
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0d0152
studio.takasaki.clubm:macro/m3_comp_time_picker_headline_type = 0x7f0d0151
studio.takasaki.clubm:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1302da
studio.takasaki.clubm:macro/m3_comp_time_picker_headline_color = 0x7f0d0150
studio.takasaki.clubm:macro/m3_comp_time_picker_container_color = 0x7f0d014e
studio.takasaki.clubm:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0d014a
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f130118
studio.takasaki.clubm:string/ExpoLocalization_forcesRTL = 0x7f120000
studio.takasaki.clubm:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0d0149
studio.takasaki.clubm:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0d0148
studio.takasaki.clubm:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0d0147
studio.takasaki.clubm:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0d0146
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f130178
studio.takasaki.clubm:macro/m3_comp_text_button_label_text_color = 0x7f0d0144
studio.takasaki.clubm:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0d0142
studio.takasaki.clubm:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0d013f
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f130159
studio.takasaki.clubm:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0d013e
studio.takasaki.clubm:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0d013c
studio.takasaki.clubm:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0d013b
studio.takasaki.clubm:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1302e4
studio.takasaki.clubm:macro/m3_comp_switch_unselected_icon_color = 0x7f0d013a
studio.takasaki.clubm:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0d0138
studio.takasaki.clubm:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0d0137
studio.takasaki.clubm:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0d0136
studio.takasaki.clubm:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0d0135
studio.takasaki.clubm:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0d0131
studio.takasaki.clubm:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0d012f
studio.takasaki.clubm:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0d012d
studio.takasaki.clubm:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0d012c
studio.takasaki.clubm:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0d012b
studio.takasaki.clubm:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0d0126
studio.takasaki.clubm:styleable/CustomAttribute = 0x7f14002e
studio.takasaki.clubm:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0d0125
studio.takasaki.clubm:macro/m3_comp_switch_selected_focus_track_color = 0x7f0d0123
studio.takasaki.clubm:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0d0121
studio.takasaki.clubm:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0d011e
studio.takasaki.clubm:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0d011d
studio.takasaki.clubm:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0d011b
studio.takasaki.clubm:macro/m3_sys_color_light_surface_tint = 0x7f0d0176
studio.takasaki.clubm:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0d011a
studio.takasaki.clubm:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0d0119
studio.takasaki.clubm:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0d0118
studio.takasaki.clubm:macro/m3_comp_suggestion_chip_container_shape = 0x7f0d0117
studio.takasaki.clubm:macro/m3_comp_snackbar_supporting_text_type = 0x7f0d0116
studio.takasaki.clubm:macro/m3_comp_snackbar_supporting_text_color = 0x7f0d0115
studio.takasaki.clubm:macro/m3_comp_slider_label_label_text_color = 0x7f0d0112
studio.takasaki.clubm:macro/m3_comp_slider_label_container_color = 0x7f0d0111
studio.takasaki.clubm:macro/m3_comp_slider_handle_color = 0x7f0d010f
studio.takasaki.clubm:macro/m3_comp_slider_disabled_handle_color = 0x7f0d010d
studio.takasaki.clubm:macro/m3_comp_slider_active_track_color = 0x7f0d010b
studio.takasaki.clubm:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0d010a
studio.takasaki.clubm:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0d0109
studio.takasaki.clubm:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0d0108
studio.takasaki.clubm:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0d0107
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d0103
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0d0101
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0d0100
studio.takasaki.clubm:string/common_google_play_services_enable_title = 0x7f120053
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0d00ff
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0d00fd
studio.takasaki.clubm:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0d00f9
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1300e3
studio.takasaki.clubm:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0d00f8
studio.takasaki.clubm:macro/m3_comp_search_view_docked_container_shape = 0x7f0d00f3
studio.takasaki.clubm:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0d00f0
studio.takasaki.clubm:macro/m3_comp_search_bar_supporting_text_type = 0x7f0d00ef
studio.takasaki.clubm:macro/m3_comp_search_bar_supporting_text_color = 0x7f0d00ee
studio.takasaki.clubm:macro/m3_comp_search_bar_input_text_type = 0x7f0d00ea
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionBar = 0x7f1300c8
studio.takasaki.clubm:macro/m3_comp_search_bar_input_text_color = 0x7f0d00e9
studio.takasaki.clubm:macro/m3_comp_search_bar_container_color = 0x7f0d00e6
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0d00e4
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0d00e3
studio.takasaki.clubm:styleable/ImageFilterView = 0x7f14003e
studio.takasaki.clubm:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1301b1
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0d00e2
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0d00e1
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0d00e0
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0d00de
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0d00dd
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_icon_color = 0x7f0d00dc
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0d00d8
studio.takasaki.clubm:macro/m3_comp_progress_indicator_track_color = 0x7f0d00d5
studio.takasaki.clubm:string/abc_menu_function_shortcut_label = 0x7f12000e
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0d00d2
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d00d0
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0d00cb
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0d00ca
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0d00c9
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0d00c0
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0d00bf
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0d00bd
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0d00bc
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0d00b9
studio.takasaki.clubm:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1200f2
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0d00b5
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0d00b4
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_caret_color = 0x7f0d00b0
studio.takasaki.clubm:macro/m3_comp_outlined_card_outline_color = 0x7f0d00ae
studio.takasaki.clubm:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0d00ab
studio.takasaki.clubm:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0d00a7
studio.takasaki.clubm:macro/m3_comp_outlined_button_outline_color = 0x7f0d00a6
studio.takasaki.clubm:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0d00a5
studio.takasaki.clubm:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0d00a4
studio.takasaki.clubm:style/Widget.AppCompat.SearchView = 0x7f13034d
studio.takasaki.clubm:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0d00a3
studio.takasaki.clubm:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0d00a2
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f130282
studio.takasaki.clubm:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0d00a1
studio.takasaki.clubm:macro/m3_comp_navigation_rail_label_text_type = 0x7f0d009f
studio.takasaki.clubm:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0d009d
studio.takasaki.clubm:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0d009b
studio.takasaki.clubm:styleable/LinearLayoutCompat_Layout = 0x7f14004a
studio.takasaki.clubm:macro/m3_comp_navigation_rail_container_color = 0x7f0d0099
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0d0098
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0d0096
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0d0093
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0d008e
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0d008b
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0d0088
studio.takasaki.clubm:styleable/Constraint = 0x7f140027
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0d0087
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0d0086
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.Small = 0x7f130183
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_headline_type = 0x7f0d0085
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_headline_color = 0x7f0d0084
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0d0082
studio.takasaki.clubm:styleable/MaterialButton = 0x7f140051
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0d0081
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0d007e
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0d007c
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0d007b
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0d007a
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1303d5
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0d0078
studio.takasaki.clubm:macro/m3_comp_navigation_bar_label_text_type = 0x7f0d0077
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0d0074
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f130137
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0d0071
studio.takasaki.clubm:styleable/Chip = 0x7f14001e
studio.takasaki.clubm:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1303aa
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0d006e
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0d006d
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0d006c
studio.takasaki.clubm:string/abc_action_bar_home_description = 0x7f120002
studio.takasaki.clubm:macro/m3_comp_navigation_bar_container_color = 0x7f0d006b
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0d0069
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0d0068
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0d0067
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0d0066
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0d0065
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0d0064
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0d0063
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0d0062
studio.takasaki.clubm:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f130053
studio.takasaki.clubm:macro/m3_comp_input_chip_label_text_type = 0x7f0d005c
studio.takasaki.clubm:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0d005a
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1303bb
studio.takasaki.clubm:macro/m3_comp_icon_button_selected_icon_color = 0x7f0d0059
studio.takasaki.clubm:macro/m3_comp_filter_chip_label_text_type = 0x7f0d0058
studio.takasaki.clubm:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0d0055
studio.takasaki.clubm:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0d0054
studio.takasaki.clubm:macro/m3_comp_slider_disabled_active_track_color = 0x7f0d010c
studio.takasaki.clubm:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0d0053
studio.takasaki.clubm:macro/m3_comp_filled_tonal_button_container_color = 0x7f0d0052
studio.takasaki.clubm:macro/m3_comp_filled_text_field_input_text_type = 0x7f0d0050
studio.takasaki.clubm:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f13035b
studio.takasaki.clubm:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0d004f
studio.takasaki.clubm:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0d004d
studio.takasaki.clubm:macro/m3_comp_filled_text_field_container_color = 0x7f0d004b
studio.takasaki.clubm:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0d0049
studio.takasaki.clubm:style/Base.Theme.SplashScreen.DayNight = 0x7f13007c
studio.takasaki.clubm:macro/m3_comp_filled_card_container_shape = 0x7f0d0047
studio.takasaki.clubm:macro/m3_comp_filled_button_label_text_type = 0x7f0d0045
studio.takasaki.clubm:macro/m3_comp_filled_button_label_text_color = 0x7f0d0044
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130077
studio.takasaki.clubm:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0d0042
studio.takasaki.clubm:macro/m3_comp_fab_tertiary_icon_color = 0x7f0d0040
studio.takasaki.clubm:macro/m3_comp_fab_surface_icon_color = 0x7f0d003e
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.Snackbar = 0x7f130121
studio.takasaki.clubm:macro/m3_comp_fab_surface_container_color = 0x7f0d003d
studio.takasaki.clubm:macro/m3_comp_fab_secondary_icon_color = 0x7f0d003c
studio.takasaki.clubm:macro/m3_comp_fab_primary_small_container_shape = 0x7f0d003a
studio.takasaki.clubm:macro/m3_comp_fab_primary_icon_color = 0x7f0d0038
studio.takasaki.clubm:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0d0034
studio.takasaki.clubm:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1302c3
studio.takasaki.clubm:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0d0033
studio.takasaki.clubm:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0d0031
studio.takasaki.clubm:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0d002f
studio.takasaki.clubm:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0d002e
studio.takasaki.clubm:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0d002d
studio.takasaki.clubm:macro/m3_comp_extended_fab_primary_container_color = 0x7f0d002c
studio.takasaki.clubm:macro/m3_comp_elevated_card_container_shape = 0x7f0d002b
studio.takasaki.clubm:macro/m3_comp_dialog_supporting_text_type = 0x7f0d0027
studio.takasaki.clubm:macro/m3_comp_dialog_headline_color = 0x7f0d0024
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1301f5
studio.takasaki.clubm:string/mtrl_picker_today_description = 0x7f1200ef
studio.takasaki.clubm:macro/m3_comp_dialog_container_shape = 0x7f0d0023
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1300a4
studio.takasaki.clubm:macro/m3_comp_dialog_container_color = 0x7f0d0022
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0d0021
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0d001e
studio.takasaki.clubm:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f130163
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0d00b6
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0d001c
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0d001b
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302f0
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0d0011
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_container_shape = 0x7f0d000e
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_container_color = 0x7f0d000d
studio.takasaki.clubm:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0d000c
studio.takasaki.clubm:string/mtrl_picker_announce_current_selection_none = 0x7f1200d3
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_icon_color = 0x7f0d000b
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0d000a
studio.takasaki.clubm:macro/m3_comp_bottom_app_bar_container_color = 0x7f0d0005
studio.takasaki.clubm:macro/m3_comp_badge_large_label_text_color = 0x7f0d0003
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130484
studio.takasaki.clubm:macro/m3_comp_badge_color = 0x7f0d0002
studio.takasaki.clubm:macro/m3_comp_assist_chip_label_text_type = 0x7f0d0001
studio.takasaki.clubm:layout/splash_screen_view = 0x7f0c0085
studio.takasaki.clubm:layout/select_dialog_multichoice_material = 0x7f0c0083
studio.takasaki.clubm:layout/redbox_view = 0x7f0c0081
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f130139
studio.takasaki.clubm:layout/redbox_item_frame = 0x7f0c007f
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1302d9
studio.takasaki.clubm:layout/paused_in_debugger_view = 0x7f0c007e
studio.takasaki.clubm:layout/notification_template_media_custom = 0x7f0c007b
studio.takasaki.clubm:layout/notification_template_lines_media = 0x7f0c0079
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f13027a
studio.takasaki.clubm:layout/notification_template_icon_group = 0x7f0c0078
studio.takasaki.clubm:layout/notification_template_custom_big = 0x7f0c0077
studio.takasaki.clubm:layout/notification_template_big_media_narrow_custom = 0x7f0c0076
studio.takasaki.clubm:layout/notification_template_big_media_narrow = 0x7f0c0075
studio.takasaki.clubm:style/AppTheme = 0x7f13000d
studio.takasaki.clubm:layout/notification_template_big_media_custom = 0x7f0c0074
studio.takasaki.clubm:layout/notification_media_cancel_action = 0x7f0c0072
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0d0102
studio.takasaki.clubm:layout/notification_action = 0x7f0c006f
studio.takasaki.clubm:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1300f1
studio.takasaki.clubm:string/app_name = 0x7f120021
studio.takasaki.clubm:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0d00d7
studio.takasaki.clubm:layout/mtrl_picker_header_title_text = 0x7f0c0069
studio.takasaki.clubm:layout/mtrl_picker_header_selection_text = 0x7f0c0068
studio.takasaki.clubm:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f130327
studio.takasaki.clubm:layout/mtrl_picker_header_fullscreen = 0x7f0c0067
studio.takasaki.clubm:string/abc_menu_space_shortcut_label = 0x7f120011
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0d0010
studio.takasaki.clubm:layout/mtrl_picker_fullscreen = 0x7f0c0065
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f130170
studio.takasaki.clubm:layout/mtrl_picker_dialog = 0x7f0c0064
studio.takasaki.clubm:layout/mtrl_picker_actions = 0x7f0c0063
studio.takasaki.clubm:layout/mtrl_navigation_rail_item = 0x7f0c0062
studio.takasaki.clubm:layout/mtrl_layout_snackbar_include = 0x7f0c0061
studio.takasaki.clubm:layout/mtrl_layout_snackbar = 0x7f0c0060
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Body2 = 0x7f13001b
studio.takasaki.clubm:layout/mtrl_calendar_year = 0x7f0c005f
studio.takasaki.clubm:layout/mtrl_calendar_vertical = 0x7f0c005e
studio.takasaki.clubm:layout/mtrl_calendar_month_navigation = 0x7f0c005c
studio.takasaki.clubm:layout/mtrl_calendar_month_labeled = 0x7f0c005b
studio.takasaki.clubm:style/ShapeAppearance.Material3.MediumComponent = 0x7f130185
studio.takasaki.clubm:layout/mtrl_calendar_days_of_week = 0x7f0c0058
studio.takasaki.clubm:layout/mtrl_calendar_day_of_week = 0x7f0c0057
studio.takasaki.clubm:string/common_google_play_services_unknown_issue = 0x7f120059
studio.takasaki.clubm:layout/redbox_item_title = 0x7f0c0080
studio.takasaki.clubm:layout/mtrl_calendar_day = 0x7f0c0056
studio.takasaki.clubm:string/clear_text_end_icon_content_description = 0x7f12004f
studio.takasaki.clubm:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0c0054
studio.takasaki.clubm:layout/mtrl_alert_dialog_title = 0x7f0c0051
studio.takasaki.clubm:layout/mtrl_alert_dialog_actions = 0x7f0c0050
studio.takasaki.clubm:layout/material_timepicker_textinput_display = 0x7f0c004e
studio.takasaki.clubm:layout/material_timepicker = 0x7f0c004c
studio.takasaki.clubm:style/Widget.AppCompat.RatingBar = 0x7f13034a
studio.takasaki.clubm:layout/material_time_chip = 0x7f0c004a
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f13006d
studio.takasaki.clubm:layout/material_textinput_timepicker = 0x7f0c0049
studio.takasaki.clubm:layout/material_radial_view_group = 0x7f0c0048
studio.takasaki.clubm:layout/material_clockface_view = 0x7f0c0047
studio.takasaki.clubm:layout/material_clockface_textview = 0x7f0c0046
studio.takasaki.clubm:layout/material_clock_display_divider = 0x7f0c0043
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0d00c5
studio.takasaki.clubm:layout/material_clock_display = 0x7f0c0042
studio.takasaki.clubm:style/ThemeOverlay.Material3.NavigationView = 0x7f1302db
studio.takasaki.clubm:layout/m3_side_sheet_dialog = 0x7f0c0040
studio.takasaki.clubm:layout/m3_alert_dialog_actions = 0x7f0c003d
studio.takasaki.clubm:layout/fps_view = 0x7f0c0039
studio.takasaki.clubm:layout/error_fragment = 0x7f0c0037
studio.takasaki.clubm:style/Theme.DevLauncher.LauncherActivity = 0x7f130245
studio.takasaki.clubm:layout/error_console_list_item = 0x7f0c0036
studio.takasaki.clubm:layout/error_activity_content_view = 0x7f0c0034
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat = 0x7f130019
studio.takasaki.clubm:layout/design_text_input_start_icon = 0x7f0c0032
studio.takasaki.clubm:layout/design_navigation_menu = 0x7f0c002f
studio.takasaki.clubm:layout/design_navigation_item_separator = 0x7f0c002d
studio.takasaki.clubm:layout/design_navigation_item = 0x7f0c002b
studio.takasaki.clubm:layout/design_menu_item_action_area = 0x7f0c002a
studio.takasaki.clubm:layout/design_layout_snackbar = 0x7f0c0026
studio.takasaki.clubm:layout/design_bottom_sheet_dialog = 0x7f0c0025
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0d008c
studio.takasaki.clubm:layout/design_bottom_navigation_item = 0x7f0c0024
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f130481
studio.takasaki.clubm:layout/custom_dialog = 0x7f0c0023
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f13026e
studio.takasaki.clubm:layout/crop_image_view = 0x7f0c0022
studio.takasaki.clubm:layout/browser_actions_context_menu_row = 0x7f0c0020
studio.takasaki.clubm:layout/browser_actions_context_menu_page = 0x7f0c001f
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1303d2
studio.takasaki.clubm:layout/alert_title_layout = 0x7f0c001c
studio.takasaki.clubm:layout/abc_search_view = 0x7f0c0019
studio.takasaki.clubm:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1301bf
studio.takasaki.clubm:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
studio.takasaki.clubm:style/AlertDialog.AppCompat.Light = 0x7f130001
studio.takasaki.clubm:layout/abc_screen_toolbar = 0x7f0c0017
studio.takasaki.clubm:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
studio.takasaki.clubm:layout/abc_screen_simple = 0x7f0c0015
studio.takasaki.clubm:layout/abc_screen_content_include = 0x7f0c0014
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f13038d
studio.takasaki.clubm:layout/abc_list_menu_item_icon = 0x7f0c000f
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0d0019
studio.takasaki.clubm:layout/abc_list_menu_item_checkbox = 0x7f0c000e
studio.takasaki.clubm:layout/abc_expanded_menu_layout = 0x7f0c000d
studio.takasaki.clubm:layout/abc_cascading_menu_item_layout = 0x7f0c000b
studio.takasaki.clubm:string/fingerprint_error_hw_not_available = 0x7f12007b
studio.takasaki.clubm:layout/abc_alert_dialog_material = 0x7f0c0009
studio.takasaki.clubm:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
studio.takasaki.clubm:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
studio.takasaki.clubm:layout/abc_action_mode_close_item_material = 0x7f0c0005
studio.takasaki.clubm:layout/abc_action_mode_bar = 0x7f0c0004
studio.takasaki.clubm:layout/abc_action_menu_layout = 0x7f0c0003
studio.takasaki.clubm:layout/abc_action_menu_item_layout = 0x7f0c0002
studio.takasaki.clubm:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0d00ac
studio.takasaki.clubm:interpolator/mtrl_linear_out_slow_in = 0x7f0b0011
studio.takasaki.clubm:interpolator/mtrl_fast_out_slow_in = 0x7f0b000f
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0b000d
studio.takasaki.clubm:string/use_fingerprint_label = 0x7f120124
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0b000c
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0b0008
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_emphasized = 0x7f0b0007
studio.takasaki.clubm:styleable/Snackbar = 0x7f140081
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Body2 = 0x7f13020f
studio.takasaki.clubm:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
studio.takasaki.clubm:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0d016d
studio.takasaki.clubm:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
studio.takasaki.clubm:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f130236
studio.takasaki.clubm:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
studio.takasaki.clubm:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1300d2
studio.takasaki.clubm:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
studio.takasaki.clubm:integer/status_bar_notification_info_maxnum = 0x7f0a0046
studio.takasaki.clubm:integer/show_password_duration = 0x7f0a0045
studio.takasaki.clubm:style/Theme.Design.BottomSheetDialog = 0x7f13023f
studio.takasaki.clubm:integer/react_native_dev_server_port = 0x7f0a0044
studio.takasaki.clubm:integer/mtrl_view_visible = 0x7f0a0043
studio.takasaki.clubm:integer/mtrl_switch_track_viewport_width = 0x7f0a003f
studio.takasaki.clubm:integer/mtrl_switch_thumb_viewport_size = 0x7f0a003d
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.None = 0x7f130182
studio.takasaki.clubm:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0a003a
studio.takasaki.clubm:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0a0039
studio.takasaki.clubm:integer/mtrl_chip_anim_duration = 0x7f0a0037
studio.takasaki.clubm:integer/mtrl_calendar_year_selector_span = 0x7f0a0034
studio.takasaki.clubm:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1302cb
studio.takasaki.clubm:integer/mtrl_calendar_selection_text_lines = 0x7f0a0033
studio.takasaki.clubm:style/Base.Widget.AppCompat.ButtonBar = 0x7f1300d9
studio.takasaki.clubm:integer/mtrl_btn_anim_duration_ms = 0x7f0a0031
studio.takasaki.clubm:integer/material_motion_duration_short_2 = 0x7f0a002d
studio.takasaki.clubm:integer/material_motion_duration_short_1 = 0x7f0a002c
studio.takasaki.clubm:integer/material_motion_duration_medium_2 = 0x7f0a002b
studio.takasaki.clubm:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0d0030
studio.takasaki.clubm:integer/material_motion_duration_long_2 = 0x7f0a0029
studio.takasaki.clubm:integer/m3_sys_shape_corner_small_corner_family = 0x7f0a0027
studio.takasaki.clubm:style/Widget.Material3.Slider.Legacy = 0x7f1303ff
studio.takasaki.clubm:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0a0026
studio.takasaki.clubm:integer/m3_sys_shape_corner_large_corner_family = 0x7f0a0025
studio.takasaki.clubm:style/Base.V7.Theme.AppCompat = 0x7f1300c0
studio.takasaki.clubm:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0a0022
studio.takasaki.clubm:integer/m3_sys_motion_duration_medium3 = 0x7f0a001b
studio.takasaki.clubm:integer/m3_sys_motion_duration_medium2 = 0x7f0a001a
studio.takasaki.clubm:integer/m3_sys_motion_duration_medium1 = 0x7f0a0019
studio.takasaki.clubm:integer/m3_sys_motion_duration_long2 = 0x7f0a0016
studio.takasaki.clubm:integer/m3_sys_motion_duration_extra_long4 = 0x7f0a0014
studio.takasaki.clubm:integer/m3_sys_motion_duration_extra_long2 = 0x7f0a0012
studio.takasaki.clubm:integer/m3_sys_motion_duration_extra_long1 = 0x7f0a0011
studio.takasaki.clubm:integer/m3_card_anim_delay_ms = 0x7f0a000e
studio.takasaki.clubm:integer/m3_badge_max_number = 0x7f0a000b
studio.takasaki.clubm:integer/hide_password_duration = 0x7f0a000a
studio.takasaki.clubm:integer/design_tab_indicator_anim_duration_ms = 0x7f0a0008
studio.takasaki.clubm:integer/config_tooltipAnimTime = 0x7f0a0005
studio.takasaki.clubm:integer/bottom_sheet_slide_duration = 0x7f0a0003
studio.takasaki.clubm:integer/abc_config_activityDefaultDur = 0x7f0a0000
studio.takasaki.clubm:id/wrap_content = 0x7f090224
studio.takasaki.clubm:id/wrap = 0x7f090223
studio.takasaki.clubm:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1302ca
studio.takasaki.clubm:id/with_icon = 0x7f090221
studio.takasaki.clubm:id/visible_removing_fragment_view_tag = 0x7f09021e
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1303cc
studio.takasaki.clubm:string/password_toggle_content_description = 0x7f1200fe
studio.takasaki.clubm:id/visible = 0x7f09021d
studio.takasaki.clubm:id/view_tree_saved_state_registry_owner = 0x7f09021b
studio.takasaki.clubm:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f09021a
studio.takasaki.clubm:styleable/SearchBar = 0x7f140079
studio.takasaki.clubm:integer/material_motion_duration_long_1 = 0x7f0a0028
studio.takasaki.clubm:id/view_offset_helper = 0x7f090216
studio.takasaki.clubm:id/view_clipped = 0x7f090215
studio.takasaki.clubm:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1200a2
studio.takasaki.clubm:id/use_hardware_layer = 0x7f090214
studio.takasaki.clubm:id/unlabeled = 0x7f090211
studio.takasaki.clubm:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f130065
studio.takasaki.clubm:id/uniform = 0x7f090210
studio.takasaki.clubm:id/unchecked = 0x7f09020f
studio.takasaki.clubm:id/triangle = 0x7f09020e
studio.takasaki.clubm:id/transition_transform = 0x7f09020d
studio.takasaki.clubm:id/transition_position = 0x7f09020b
studio.takasaki.clubm:id/transition_pause_alpha = 0x7f09020a
studio.takasaki.clubm:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1302c4
studio.takasaki.clubm:id/transition_layout_save = 0x7f090209
studio.takasaki.clubm:id/transition_image_transform = 0x7f090208
studio.takasaki.clubm:id/transform_origin = 0x7f090203
studio.takasaki.clubm:style/Base.Widget.Material3.FloatingActionButton = 0x7f13010e
studio.takasaki.clubm:id/transform = 0x7f090202
studio.takasaki.clubm:id/topPanel = 0x7f090200
studio.takasaki.clubm:id/top = 0x7f0901ff
studio.takasaki.clubm:id/toggle = 0x7f0901fe
studio.takasaki.clubm:id/titleDividerNoCustom = 0x7f0901fc
studio.takasaki.clubm:id/time = 0x7f0901fa
studio.takasaki.clubm:id/textinput_suffix_text = 0x7f0901f9
studio.takasaki.clubm:id/textinput_placeholder = 0x7f0901f7
studio.takasaki.clubm:id/text_input_start_icon = 0x7f0901f3
studio.takasaki.clubm:style/CalendarDatePickerStyle = 0x7f13012a
studio.takasaki.clubm:integer/default_icon_animation_duration = 0x7f0a0006
studio.takasaki.clubm:id/text_input_end_icon = 0x7f0901f1
studio.takasaki.clubm:id/textStart = 0x7f0901ef
studio.takasaki.clubm:id/textSpacerNoTitle = 0x7f0901ee
studio.takasaki.clubm:id/textSpacerNoButtons = 0x7f0901ed
studio.takasaki.clubm:id/text = 0x7f0901ea
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1300d1
studio.takasaki.clubm:id/tag_transition_group = 0x7f0901e6
studio.takasaki.clubm:id/tag_state_description = 0x7f0901e5
studio.takasaki.clubm:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f130061
studio.takasaki.clubm:id/tag_screen_reader_focusable = 0x7f0901e4
studio.takasaki.clubm:id/tag_on_receive_content_mime_types = 0x7f0901e3
studio.takasaki.clubm:style/Base.V26.Theme.AppCompat.Light = 0x7f1300bc
studio.takasaki.clubm:id/tag_on_apply_window_listener = 0x7f0901e1
studio.takasaki.clubm:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0d0143
studio.takasaki.clubm:id/tag_accessibility_heading = 0x7f0901df
studio.takasaki.clubm:id/tag_accessibility_actions = 0x7f0901dd
studio.takasaki.clubm:id/tabMode = 0x7f0901dc
studio.takasaki.clubm:style/Base.Theme.Material3.Dark.Dialog = 0x7f13005e
studio.takasaki.clubm:id/submenuarrow = 0x7f0901da
studio.takasaki.clubm:id/stop = 0x7f0901d8
studio.takasaki.clubm:id/status_bar_latest_event_content = 0x7f0901d7
studio.takasaki.clubm:id/startVertical = 0x7f0901d4
studio.takasaki.clubm:id/startHorizontal = 0x7f0901d2
studio.takasaki.clubm:id/src_over = 0x7f0901cf
studio.takasaki.clubm:id/src_atop = 0x7f0901cd
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f130133
studio.takasaki.clubm:string/mtrl_picker_text_input_date_hint = 0x7f1200e9
studio.takasaki.clubm:string/mtrl_exceed_max_badge_number_content_description = 0x7f1200cd
studio.takasaki.clubm:string/material_timepicker_clock_mode_description = 0x7f1200b7
studio.takasaki.clubm:id/view_tag_instance_handle = 0x7f090217
studio.takasaki.clubm:id/square = 0x7f0901cc
studio.takasaki.clubm:style/Widget.AppCompat.Spinner.DropDown = 0x7f130352
studio.takasaki.clubm:id/spread = 0x7f0901ca
studio.takasaki.clubm:id/splashscreen_icon_view = 0x7f0901c7
studio.takasaki.clubm:id/spacer = 0x7f0901c5
studio.takasaki.clubm:id/snapMargins = 0x7f0901c4
studio.takasaki.clubm:id/snackbar_text = 0x7f0901c2
studio.takasaki.clubm:id/snackbar_action = 0x7f0901c1
studio.takasaki.clubm:id/sin = 0x7f0901be
studio.takasaki.clubm:id/showHome = 0x7f0901bc
studio.takasaki.clubm:id/showCustom = 0x7f0901bb
studio.takasaki.clubm:id/search_button = 0x7f0901af
studio.takasaki.clubm:style/Base.V22.Theme.AppCompat.Light = 0x7f1300b4
studio.takasaki.clubm:id/search_bar = 0x7f0901ae
studio.takasaki.clubm:id/search_badge = 0x7f0901ad
studio.takasaki.clubm:id/scrollIndicatorUp = 0x7f0901aa
studio.takasaki.clubm:id/screen = 0x7f0901a7
studio.takasaki.clubm:style/TextAppearance.Design.Counter = 0x7f1301df
studio.takasaki.clubm:id/scale = 0x7f0901a6
studio.takasaki.clubm:id/sawtooth = 0x7f0901a5
studio.takasaki.clubm:id/save_overlay_view = 0x7f0901a4
studio.takasaki.clubm:id/save_non_transition_alpha = 0x7f0901a3
studio.takasaki.clubm:id/row_index_key = 0x7f0901a2
studio.takasaki.clubm:id/rounded = 0x7f0901a1
studio.takasaki.clubm:layout/mtrl_auto_complete_simple_item = 0x7f0c0055
studio.takasaki.clubm:id/rn_redbox_reload_button = 0x7f09019c
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.Slider = 0x7f130120
studio.takasaki.clubm:id/rn_frame_method = 0x7f090198
studio.takasaki.clubm:id/rightToLeft = 0x7f090194
studio.takasaki.clubm:id/right = 0x7f090193
studio.takasaki.clubm:id/report_drawn = 0x7f090191
studio.takasaki.clubm:id/reloadButton = 0x7f090190
studio.takasaki.clubm:id/rectangles = 0x7f09018f
studio.takasaki.clubm:id/rectangleHorizontalOnly = 0x7f09018d
studio.takasaki.clubm:id/rectangle = 0x7f09018c
studio.takasaki.clubm:id/ratio = 0x7f09018a
studio.takasaki.clubm:styleable/Spinner = 0x7f140083
studio.takasaki.clubm:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f13008b
studio.takasaki.clubm:integer/mtrl_switch_thumb_pressed_duration = 0x7f0a003b
studio.takasaki.clubm:id/radio = 0x7f090189
studio.takasaki.clubm:styleable/CoordinatorLayout = 0x7f14002b
studio.takasaki.clubm:style/Base.TextAppearance.MaterialComponents.Button = 0x7f130048
studio.takasaki.clubm:id/progress_horizontal = 0x7f090188
studio.takasaki.clubm:id/position = 0x7f090184
studio.takasaki.clubm:id/pointer_events = 0x7f090182
studio.takasaki.clubm:id/percent = 0x7f090180
studio.takasaki.clubm:id/peekHeight = 0x7f09017f
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1303cb
studio.takasaki.clubm:id/parent_matrix = 0x7f09017b
studio.takasaki.clubm:id/parentRelative = 0x7f09017a
studio.takasaki.clubm:id/parentPanel = 0x7f090179
studio.takasaki.clubm:styleable/ClockFaceView = 0x7f140021
studio.takasaki.clubm:id/parent = 0x7f090178
studio.takasaki.clubm:id/parallax = 0x7f090177
studio.takasaki.clubm:id/packed = 0x7f090176
studio.takasaki.clubm:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f13014d
studio.takasaki.clubm:id/outward = 0x7f090174
studio.takasaki.clubm:id/open_search_view_search_prefix = 0x7f09016f
studio.takasaki.clubm:id/open_search_view_root = 0x7f09016d
studio.takasaki.clubm:id/open_search_view_header_container = 0x7f09016c
studio.takasaki.clubm:style/Widget.Material3.NavigationRailView = 0x7f1303ea
studio.takasaki.clubm:id/open_search_view_divider = 0x7f090169
studio.takasaki.clubm:id/open_search_view_clear_button = 0x7f090167
studio.takasaki.clubm:id/open_search_view_background = 0x7f090166
studio.takasaki.clubm:id/onTouch = 0x7f090164
studio.takasaki.clubm:id/on = 0x7f090163
studio.takasaki.clubm:macro/m3_comp_filled_button_container_color = 0x7f0d0043
studio.takasaki.clubm:id/notification_main_column_container = 0x7f090161
studio.takasaki.clubm:string/fingerprint_prompt_message = 0x7f120082
studio.takasaki.clubm:id/notification_main_column = 0x7f090160
studio.takasaki.clubm:id/notification_background = 0x7f09015f
studio.takasaki.clubm:styleable/PropertySet = 0x7f140071
studio.takasaki.clubm:id/none = 0x7f09015d
studio.takasaki.clubm:id/never = 0x7f09015b
studio.takasaki.clubm:id/navigation_header_container = 0x7f09015a
studio.takasaki.clubm:style/Widget.AppCompat.PopupMenu = 0x7f130345
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f130043
studio.takasaki.clubm:id/navigation_bar_item_small_label_view = 0x7f090159
studio.takasaki.clubm:id/navigation_bar_item_labels_group = 0x7f090157
studio.takasaki.clubm:id/navigation_bar_item_icon_view = 0x7f090156
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1301d2
studio.takasaki.clubm:id/mtrl_view_tag_bottom_padding = 0x7f090152
studio.takasaki.clubm:id/mtrl_picker_title_text = 0x7f090151
studio.takasaki.clubm:style/Widget.Design.TextInputLayout = 0x7f13036b
studio.takasaki.clubm:macro/m3_comp_time_picker_container_shape = 0x7f0d014f
studio.takasaki.clubm:id/mtrl_picker_text_input_range_start = 0x7f090150
studio.takasaki.clubm:id/mtrl_picker_text_input_range_end = 0x7f09014f
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.Dialog = 0x7f1302a4
studio.takasaki.clubm:id/mtrl_picker_text_input_date = 0x7f09014e
studio.takasaki.clubm:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f13004b
studio.takasaki.clubm:id/mtrl_picker_header_toggle = 0x7f09014d
studio.takasaki.clubm:id/mtrl_picker_header_title_and_selection = 0x7f09014c
studio.takasaki.clubm:id/mtrl_picker_header_selection_text = 0x7f09014b
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1301f0
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1301cb
studio.takasaki.clubm:id/mtrl_picker_header = 0x7f09014a
studio.takasaki.clubm:id/mtrl_internal_children_alpha_tag = 0x7f090147
studio.takasaki.clubm:id/mtrl_child_content_container = 0x7f090146
studio.takasaki.clubm:id/mtrl_card_checked_layer_id = 0x7f090145
studio.takasaki.clubm:id/mtrl_calendar_text_input_frame = 0x7f090143
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1300a5
studio.takasaki.clubm:id/mtrl_calendar_months = 0x7f090141
studio.takasaki.clubm:id/mtrl_calendar_main_pane = 0x7f090140
studio.takasaki.clubm:id/mtrl_calendar_frame = 0x7f09013f
studio.takasaki.clubm:id/mtrl_calendar_days_of_week = 0x7f09013e
studio.takasaki.clubm:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0d0105
studio.takasaki.clubm:id/mtrl_calendar_day_selector_frame = 0x7f09013d
studio.takasaki.clubm:id/mtrl_anchor_parent = 0x7f09013c
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1301ef
studio.takasaki.clubm:string/mtrl_picker_day_of_week_column_header = 0x7f1200d9
studio.takasaki.clubm:id/motion_base = 0x7f09013b
studio.takasaki.clubm:string/mtrl_switch_thumb_group_name = 0x7f1200f4
studio.takasaki.clubm:integer/mtrl_card_anim_delay_ms = 0x7f0a0035
studio.takasaki.clubm:id/month_title = 0x7f09013a
studio.takasaki.clubm:id/month_navigation_next = 0x7f090138
studio.takasaki.clubm:id/month_navigation_fragment_toggle = 0x7f090137
studio.takasaki.clubm:id/month_navigation_bar = 0x7f090136
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f130092
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f130039
studio.takasaki.clubm:id/month_grid = 0x7f090135
studio.takasaki.clubm:id/mix_blend_mode = 0x7f090134
studio.takasaki.clubm:id/mini = 0x7f090133
studio.takasaki.clubm:id/media_actions = 0x7f090130
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0d001a
studio.takasaki.clubm:id/material_timepicker_view = 0x7f09012d
studio.takasaki.clubm:id/material_timepicker_mode_button = 0x7f09012b
studio.takasaki.clubm:id/material_minute_tv = 0x7f090127
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f13040e
studio.takasaki.clubm:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1300af
studio.takasaki.clubm:id/material_hour_tv = 0x7f090124
studio.takasaki.clubm:id/material_hour_text_input = 0x7f090123
studio.takasaki.clubm:id/material_clock_period_toggle = 0x7f090122
studio.takasaki.clubm:id/material_clock_level = 0x7f09011f
studio.takasaki.clubm:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f13039e
studio.takasaki.clubm:id/material_clock_display = 0x7f09011b
studio.takasaki.clubm:id/marquee = 0x7f090118
studio.takasaki.clubm:id/m3_side_sheet = 0x7f090116
studio.takasaki.clubm:id/list_item = 0x7f090114
studio.takasaki.clubm:id/listMode = 0x7f090113
studio.takasaki.clubm:id/linear = 0x7f090112
studio.takasaki.clubm:styleable/GradientColor = 0x7f14003c
studio.takasaki.clubm:id/layout = 0x7f09010b
studio.takasaki.clubm:id/labeled = 0x7f090109
studio.takasaki.clubm:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0a0040
studio.takasaki.clubm:id/italic = 0x7f090105
studio.takasaki.clubm:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f130284
studio.takasaki.clubm:id/invisible = 0x7f090102
studio.takasaki.clubm:id/invalidate_transform = 0x7f090101
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f13003d
studio.takasaki.clubm:id/spline = 0x7f0901c8
studio.takasaki.clubm:id/indeterminate = 0x7f0900ff
studio.takasaki.clubm:id/ignore = 0x7f0900fc
studio.takasaki.clubm:layout/design_text_input_end_icon = 0x7f0c0031
studio.takasaki.clubm:integer/mtrl_view_invisible = 0x7f0a0042
studio.takasaki.clubm:id/ifRoom = 0x7f0900fb
studio.takasaki.clubm:id/icon_only = 0x7f0900fa
studio.takasaki.clubm:macro/m3_comp_switch_unselected_track_color = 0x7f0d0140
studio.takasaki.clubm:id/icon_group = 0x7f0900f9
studio.takasaki.clubm:id/icon = 0x7f0900f8
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar = 0x7f13044d
studio.takasaki.clubm:id/ic_rotate_right_24 = 0x7f0900f7
studio.takasaki.clubm:id/ic_flip_24_vertically = 0x7f0900f5
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f13016f
studio.takasaki.clubm:id/ic_flip_24_horizontally = 0x7f0900f4
studio.takasaki.clubm:id/ic_flip_24 = 0x7f0900f3
studio.takasaki.clubm:id/homeButton = 0x7f0900f1
studio.takasaki.clubm:id/homeAsUp = 0x7f0900f0
studio.takasaki.clubm:id/home = 0x7f0900ef
studio.takasaki.clubm:id/hideable = 0x7f0900ee
studio.takasaki.clubm:id/hide_ime_id = 0x7f0900ed
studio.takasaki.clubm:id/header_title = 0x7f0900ec
studio.takasaki.clubm:string/common_open_on_phone = 0x7f120060
studio.takasaki.clubm:id/groups = 0x7f0900eb
studio.takasaki.clubm:style/Theme.Design.Light.NoActionBar = 0x7f130242
studio.takasaki.clubm:id/graph = 0x7f0900e8
studio.takasaki.clubm:id/gone = 0x7f0900e7
studio.takasaki.clubm:id/ghost_view_holder = 0x7f0900e5
studio.takasaki.clubm:id/ghost_view = 0x7f0900e4
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f1303a3
studio.takasaki.clubm:id/fullscreen_header = 0x7f0900e3
studio.takasaki.clubm:id/fragment_container_view_tag = 0x7f0900e2
studio.takasaki.clubm:id/fps_text = 0x7f0900e1
studio.takasaki.clubm:styleable/NavigationView = 0x7f14006c
studio.takasaki.clubm:string/catalyst_hot_reloading_stop = 0x7f12003f
studio.takasaki.clubm:id/open_search_bar_text_view = 0x7f090165
studio.takasaki.clubm:id/forever = 0x7f0900e0
studio.takasaki.clubm:id/focusCrop = 0x7f0900df
studio.takasaki.clubm:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f13010f
studio.takasaki.clubm:id/flip = 0x7f0900dd
studio.takasaki.clubm:id/fixed = 0x7f0900dc
studio.takasaki.clubm:id/fitXY = 0x7f0900db
studio.takasaki.clubm:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f130376
studio.takasaki.clubm:id/fitEnd = 0x7f0900d8
studio.takasaki.clubm:id/fitCenter = 0x7f0900d7
studio.takasaki.clubm:layout/error_console_fragment = 0x7f0c0035
studio.takasaki.clubm:id/fitBottomStart = 0x7f0900d6
studio.takasaki.clubm:id/fingerprint_error = 0x7f0900d3
studio.takasaki.clubm:id/fingerprint_description = 0x7f0900d2
studio.takasaki.clubm:id/material_timepicker_container = 0x7f09012a
studio.takasaki.clubm:id/filter = 0x7f0900d1
studio.takasaki.clubm:id/filled = 0x7f0900d0
studio.takasaki.clubm:id/fill_horizontal = 0x7f0900ce
studio.takasaki.clubm:id/fill = 0x7f0900cd
studio.takasaki.clubm:id/fade = 0x7f0900cc
studio.takasaki.clubm:id/expanded_menu = 0x7f0900cb
studio.takasaki.clubm:string/m3_ref_typeface_plain_medium = 0x7f12009a
studio.takasaki.clubm:id/escape = 0x7f0900c8
studio.takasaki.clubm:style/Widget.AppCompat.Spinner.Underlined = 0x7f130354
studio.takasaki.clubm:id/error_stack = 0x7f0900c5
studio.takasaki.clubm:id/error_main_content = 0x7f0900c4
studio.takasaki.clubm:id/error_footer = 0x7f0900c2
studio.takasaki.clubm:id/enterAlwaysCollapsed = 0x7f0900c0
studio.takasaki.clubm:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13046f
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1301ee
studio.takasaki.clubm:id/middle = 0x7f090132
studio.takasaki.clubm:id/endToStart = 0x7f0900bd
studio.takasaki.clubm:style/Base.Animation.AppCompat.Dialog = 0x7f130010
studio.takasaki.clubm:id/end = 0x7f0900bc
studio.takasaki.clubm:id/elastic = 0x7f0900ba
studio.takasaki.clubm:id/edit_text_id = 0x7f0900b9
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog = 0x7f13027c
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f130080
studio.takasaki.clubm:id/edge = 0x7f0900b7
studio.takasaki.clubm:id/easeInOut = 0x7f0900b5
studio.takasaki.clubm:id/dropdown_menu = 0x7f0900b3
studio.takasaki.clubm:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1301bd
studio.takasaki.clubm:id/dragUp = 0x7f0900b2
studio.takasaki.clubm:id/dragStart = 0x7f0900b1
studio.takasaki.clubm:id/dragRight = 0x7f0900b0
studio.takasaki.clubm:id/material_timepicker_cancel_button = 0x7f090129
studio.takasaki.clubm:id/dragLeft = 0x7f0900af
studio.takasaki.clubm:id/dragEnd = 0x7f0900ae
studio.takasaki.clubm:id/disableScroll = 0x7f0900ab
studio.takasaki.clubm:id/disableHome = 0x7f0900a9
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f130165
studio.takasaki.clubm:id/direct = 0x7f0900a8
studio.takasaki.clubm:id/dimensions = 0x7f0900a7
studio.takasaki.clubm:id/design_menu_item_text = 0x7f0900a4
studio.takasaki.clubm:id/design_menu_item_action_area = 0x7f0900a2
studio.takasaki.clubm:id/default_activity_button = 0x7f09009f
studio.takasaki.clubm:id/decelerate = 0x7f09009c
studio.takasaki.clubm:id/date_picker_actions = 0x7f09009b
studio.takasaki.clubm:id/material_timepicker_ok_button = 0x7f09012c
studio.takasaki.clubm:id/dark = 0x7f09009a
studio.takasaki.clubm:id/cut = 0x7f090099
studio.takasaki.clubm:id/crop_image_menu_crop = 0x7f090096
studio.takasaki.clubm:id/navigation_bar_item_large_label_view = 0x7f090158
studio.takasaki.clubm:id/cradle = 0x7f090094
studio.takasaki.clubm:id/counterclockwise = 0x7f090093
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0d00fa
studio.takasaki.clubm:id/contentPanel = 0x7f09008f
studio.takasaki.clubm:id/container = 0x7f09008d
studio.takasaki.clubm:id/console_reload_button = 0x7f09008c
studio.takasaki.clubm:id/console_home_button = 0x7f09008b
studio.takasaki.clubm:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1302af
studio.takasaki.clubm:id/clockwise = 0x7f090087
studio.takasaki.clubm:id/clip_vertical = 0x7f090086
studio.takasaki.clubm:style/Widget.Design.CollapsingToolbar = 0x7f130364
studio.takasaki.clubm:id/clip_horizontal = 0x7f090085
studio.takasaki.clubm:style/Widget.Compat.NotificationActionContainer = 0x7f13035f
studio.takasaki.clubm:id/clear_text = 0x7f090084
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f130280
studio.takasaki.clubm:id/circle_center = 0x7f090083
studio.takasaki.clubm:id/checked = 0x7f090081
studio.takasaki.clubm:id/center_vertical = 0x7f09007d
studio.takasaki.clubm:id/centerCrop = 0x7f09007a
studio.takasaki.clubm:id/center = 0x7f090079
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1300e8
studio.takasaki.clubm:id/cancel_button = 0x7f090077
studio.takasaki.clubm:style/Widget.AppCompat.ActionBar.TabText = 0x7f130313
studio.takasaki.clubm:id/cancel_action = 0x7f090076
studio.takasaki.clubm:id/button_text = 0x7f090075
studio.takasaki.clubm:string/catalyst_debug_connecting = 0x7f120034
studio.takasaki.clubm:id/button = 0x7f090073
studio.takasaki.clubm:id/browser_actions_menu_view = 0x7f090072
studio.takasaki.clubm:id/browser_actions_menu_items = 0x7f090071
studio.takasaki.clubm:id/browser_actions_menu_item_text = 0x7f090070
studio.takasaki.clubm:id/browser_actions_header_text = 0x7f09006e
studio.takasaki.clubm:id/oval = 0x7f090175
studio.takasaki.clubm:id/bounce = 0x7f09006d
studio.takasaki.clubm:id/bottom_sheet = 0x7f09006c
studio.takasaki.clubm:integer/cancel_button_image_alpha = 0x7f0a0004
studio.takasaki.clubm:id/bottom = 0x7f09006b
studio.takasaki.clubm:id/beginning = 0x7f090069
studio.takasaki.clubm:string/state_off_description = 0x7f120118
studio.takasaki.clubm:id/beginOnFirstDraw = 0x7f090068
studio.takasaki.clubm:id/baseline = 0x7f090067
studio.takasaki.clubm:id/autofill_inline_suggestion_title = 0x7f090065
studio.takasaki.clubm:id/autofill_inline_suggestion_start_icon = 0x7f090063
studio.takasaki.clubm:id/async = 0x7f09005d
studio.takasaki.clubm:id/asConfigured = 0x7f09005c
studio.takasaki.clubm:id/arc = 0x7f09005b
studio.takasaki.clubm:string/catalyst_inspector_toggle = 0x7f120040
studio.takasaki.clubm:id/animateToStart = 0x7f09005a
studio.takasaki.clubm:id/always = 0x7f090058
studio.takasaki.clubm:id/all = 0x7f090057
studio.takasaki.clubm:layout/mtrl_calendar_horizontal = 0x7f0c0059
studio.takasaki.clubm:id/aligned = 0x7f090056
studio.takasaki.clubm:id/material_clock_period_pm_button = 0x7f090121
studio.takasaki.clubm:id/alert_title = 0x7f090055
studio.takasaki.clubm:id/alertTitle = 0x7f090054
studio.takasaki.clubm:id/adjust_height = 0x7f090052
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f13045c
studio.takasaki.clubm:id/activity_chooser_view_content = 0x7f090050
studio.takasaki.clubm:style/Theme.Material3.DayNight = 0x7f130252
studio.takasaki.clubm:id/actions = 0x7f09004f
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f130197
studio.takasaki.clubm:id/action_text = 0x7f09004e
studio.takasaki.clubm:id/action_mode_close_button = 0x7f09004d
studio.takasaki.clubm:id/action_menu_presenter = 0x7f09004a
studio.takasaki.clubm:id/transition_current_scene = 0x7f090207
studio.takasaki.clubm:id/action_menu_divider = 0x7f090049
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1302b5
studio.takasaki.clubm:id/action_image = 0x7f090048
studio.takasaki.clubm:style/TextAppearance.Material3.ActionBar.Title = 0x7f1301f9
studio.takasaki.clubm:id/action_divider = 0x7f090047
studio.takasaki.clubm:id/action_context_bar = 0x7f090046
studio.takasaki.clubm:id/action_bar_title = 0x7f090044
studio.takasaki.clubm:id/action_bar_subtitle = 0x7f090043
studio.takasaki.clubm:id/action_bar_root = 0x7f090041
studio.takasaki.clubm:id/action_bar_container = 0x7f090040
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Body1 = 0x7f13020e
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300a2
studio.takasaki.clubm:id/accessibility_value = 0x7f09003c
studio.takasaki.clubm:id/accessibility_state = 0x7f09003a
studio.takasaki.clubm:id/accessibility_label = 0x7f090037
studio.takasaki.clubm:id/accessibility_custom_action_9 = 0x7f090035
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1301f4
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Title = 0x7f130032
studio.takasaki.clubm:id/accessibility_custom_action_8 = 0x7f090034
studio.takasaki.clubm:id/accessibility_custom_action_7 = 0x7f090033
studio.takasaki.clubm:id/accessibility_custom_action_6 = 0x7f090032
studio.takasaki.clubm:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1302be
studio.takasaki.clubm:string/androidx.credentials.TYPE_PUBLIC_KEY_CREDENTIAL = 0x7f12001f
studio.takasaki.clubm:id/accessibility_custom_action_4 = 0x7f090030
studio.takasaki.clubm:id/accessibility_custom_action_30 = 0x7f09002e
studio.takasaki.clubm:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0d0130
studio.takasaki.clubm:id/accessibility_custom_action_29 = 0x7f09002c
studio.takasaki.clubm:id/accessibility_custom_action_28 = 0x7f09002b
studio.takasaki.clubm:id/accessibility_custom_action_27 = 0x7f09002a
studio.takasaki.clubm:id/accessibility_custom_action_26 = 0x7f090029
studio.takasaki.clubm:id/accessibility_custom_action_25 = 0x7f090028
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionButton = 0x7f1300cd
studio.takasaki.clubm:id/accessibility_custom_action_23 = 0x7f090026
studio.takasaki.clubm:id/accessibility_custom_action_21 = 0x7f090024
studio.takasaki.clubm:style/Base.Widget.Material3.CollapsingToolbar = 0x7f130108
studio.takasaki.clubm:id/accessibility_custom_action_2 = 0x7f090022
studio.takasaki.clubm:id/accessibility_custom_action_19 = 0x7f090021
studio.takasaki.clubm:id/accessibility_custom_action_18 = 0x7f090020
studio.takasaki.clubm:id/accessibility_custom_action_17 = 0x7f09001f
studio.takasaki.clubm:style/Theme.AppCompat.Dialog.MinWidth = 0x7f13022e
studio.takasaki.clubm:id/accessibility_custom_action_16 = 0x7f09001e
studio.takasaki.clubm:id/accessibility_custom_action_15 = 0x7f09001d
studio.takasaki.clubm:id/accessibility_custom_action_14 = 0x7f09001c
studio.takasaki.clubm:id/accessibility_custom_action_12 = 0x7f09001a
studio.takasaki.clubm:id/graph_wrap = 0x7f0900e9
studio.takasaki.clubm:id/accessibility_custom_action_11 = 0x7f090019
studio.takasaki.clubm:id/accessibility_collection_item = 0x7f090015
studio.takasaki.clubm:id/accessibility_collection = 0x7f090014
studio.takasaki.clubm:id/accessibility_actions = 0x7f090013
studio.takasaki.clubm:id/material_clock_display_and_toggle = 0x7f09011c
studio.takasaki.clubm:id/accelerate = 0x7f090011
studio.takasaki.clubm:id/TOP_START = 0x7f090010
studio.takasaki.clubm:id/SYM = 0x7f09000e
studio.takasaki.clubm:id/SHOW_PROGRESS = 0x7f09000d
studio.takasaki.clubm:id/SHOW_PATH = 0x7f09000c
studio.takasaki.clubm:id/text_input_error_icon = 0x7f0901f2
studio.takasaki.clubm:id/SHOW_ALL = 0x7f09000b
studio.takasaki.clubm:id/NO_DEBUG = 0x7f090009
studio.takasaki.clubm:id/dialog_button = 0x7f0900a6
studio.takasaki.clubm:id/META = 0x7f090008
studio.takasaki.clubm:id/is_pooling_container_tag = 0x7f090104
studio.takasaki.clubm:id/ImageView_image = 0x7f090007
studio.takasaki.clubm:id/CropProgressBar = 0x7f090005
studio.takasaki.clubm:id/CTRL = 0x7f090003
studio.takasaki.clubm:id/ALT = 0x7f090000
studio.takasaki.clubm:id/withText = 0x7f090220
studio.takasaki.clubm:drawable/tooltip_frame_light = 0x7f08014e
studio.takasaki.clubm:styleable/CropImageView = 0x7f14002d
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f13016a
studio.takasaki.clubm:drawable/tooltip_frame_dark = 0x7f08014d
studio.takasaki.clubm:drawable/test_level_drawable = 0x7f08014c
studio.takasaki.clubm:drawable/splashscreen_logo = 0x7f08014b
studio.takasaki.clubm:drawable/ripple_effect = 0x7f080148
studio.takasaki.clubm:string/dev_launcher_go_to_home = 0x7f120069
studio.takasaki.clubm:drawable/paused_in_debugger_dialog_background = 0x7f080146
studio.takasaki.clubm:string/mtrl_picker_a11y_prev_month = 0x7f1200d0
studio.takasaki.clubm:drawable/paused_in_debugger_background = 0x7f080145
studio.takasaki.clubm:drawable/notification_tile_bg = 0x7f080143
studio.takasaki.clubm:drawable/notification_template_icon_bg = 0x7f080141
studio.takasaki.clubm:drawable/notification_bg_normal_pressed = 0x7f08013e
studio.takasaki.clubm:drawable/notification_bg_normal = 0x7f08013d
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0d00e5
studio.takasaki.clubm:macro/m3_comp_elevated_card_container_color = 0x7f0d002a
studio.takasaki.clubm:drawable/notification_bg_low_pressed = 0x7f08013c
studio.takasaki.clubm:drawable/notification_bg_low = 0x7f08013a
studio.takasaki.clubm:drawable/notification_bg = 0x7f080139
studio.takasaki.clubm:id/edit_query = 0x7f0900b8
studio.takasaki.clubm:drawable/mtrl_switch_track_decoration = 0x7f080135
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f13030f
studio.takasaki.clubm:drawable/mtrl_switch_track = 0x7f080134
studio.takasaki.clubm:style/Theme.App.SplashScreen = 0x7f130222
studio.takasaki.clubm:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f080132
studio.takasaki.clubm:drawable/mtrl_switch_thumb_unchecked = 0x7f080131
studio.takasaki.clubm:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f080130
studio.takasaki.clubm:drawable/mtrl_switch_thumb_pressed_checked = 0x7f08012f
studio.takasaki.clubm:drawable/mtrl_switch_thumb_checked = 0x7f08012b
studio.takasaki.clubm:macro/m3_comp_switch_unselected_handle_color = 0x7f0d0134
studio.takasaki.clubm:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0d010e
studio.takasaki.clubm:drawable/mtrl_switch_thumb = 0x7f08012a
studio.takasaki.clubm:id/material_value_index = 0x7f09012e
studio.takasaki.clubm:drawable/mtrl_navigation_bar_item_background = 0x7f080127
studio.takasaki.clubm:drawable/mtrl_ic_indeterminate = 0x7f080126
studio.takasaki.clubm:drawable/mtrl_ic_check_mark = 0x7f080122
studio.takasaki.clubm:drawable/mtrl_ic_cancel = 0x7f080121
studio.takasaki.clubm:drawable/mtrl_ic_arrow_drop_up = 0x7f080120
studio.takasaki.clubm:drawable/mtrl_dialog_background = 0x7f08011d
studio.takasaki.clubm:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f08011c
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f08011b
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f08011a
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f080119
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f080118
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f13040b
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f080116
studio.takasaki.clubm:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f080114
studio.takasaki.clubm:style/Animation.AppCompat.Tooltip = 0x7f130004
studio.takasaki.clubm:drawable/mtrl_checkbox_button = 0x7f080113
studio.takasaki.clubm:drawable/mtrl_bottomsheet_drag_handle = 0x7f080112
studio.takasaki.clubm:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f080110
studio.takasaki.clubm:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f08010e
studio.takasaki.clubm:id/rn_redbox_stack = 0x7f09019f
studio.takasaki.clubm:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f08010d
studio.takasaki.clubm:drawable/material_ic_edit_black_24dp = 0x7f08010b
studio.takasaki.clubm:string/state_mixed_description = 0x7f120117
studio.takasaki.clubm:drawable/material_ic_clear_black_24dp = 0x7f08010a
studio.takasaki.clubm:style/TextAppearance.AppCompat.Medium = 0x7f1301b5
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0d0091
studio.takasaki.clubm:drawable/mtrl_ic_checkbox_checked = 0x7f080123
studio.takasaki.clubm:drawable/material_ic_calendar_black_24dp = 0x7f080109
studio.takasaki.clubm:drawable/m3_tabs_line_indicator = 0x7f080105
studio.takasaki.clubm:styleable/ConstraintSet = 0x7f14002a
studio.takasaki.clubm:drawable/m3_tabs_background = 0x7f080104
studio.takasaki.clubm:integer/m3_sys_motion_duration_medium4 = 0x7f0a001c
studio.takasaki.clubm:drawable/m3_radiobutton_ripple = 0x7f080102
studio.takasaki.clubm:drawable/m3_password_eye = 0x7f080100
studio.takasaki.clubm:drawable/m3_bottom_sheet_drag_handle = 0x7f0800ff
studio.takasaki.clubm:id/chains = 0x7f09007f
studio.takasaki.clubm:drawable/m3_avd_show_password = 0x7f0800fe
studio.takasaki.clubm:drawable/ic_search_black_24 = 0x7f0800fa
studio.takasaki.clubm:macro/m3_comp_search_bar_leading_icon_color = 0x7f0d00eb
studio.takasaki.clubm:drawable/ic_rotate_right_24 = 0x7f0800f9
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0d0162
studio.takasaki.clubm:drawable/ic_password = 0x7f0800f6
studio.takasaki.clubm:drawable/ic_passkey = 0x7f0800f5
studio.takasaki.clubm:drawable/ic_other_sign_in = 0x7f0800f4
studio.takasaki.clubm:drawable/ic_mtrl_chip_checked_circle = 0x7f0800f2
studio.takasaki.clubm:drawable/ic_mtrl_chip_checked_black = 0x7f0800f1
studio.takasaki.clubm:drawable/ic_mtrl_checked_circle = 0x7f0800f0
studio.takasaki.clubm:style/Theme.Material3.Dark.SideSheetDialog = 0x7f130251
studio.takasaki.clubm:drawable/ic_m3_chip_check = 0x7f0800ed
studio.takasaki.clubm:drawable/ic_keyboard_black_24dp = 0x7f0800eb
studio.takasaki.clubm:drawable/ic_flip_24 = 0x7f0800ea
studio.takasaki.clubm:drawable/ic_call_decline_low = 0x7f0800e7
studio.takasaki.clubm:drawable/ic_call_decline = 0x7f0800e6
studio.takasaki.clubm:style/Base.V24.Theme.Material3.Light = 0x7f1300b9
studio.takasaki.clubm:string/google_app_id = 0x7f120088
studio.takasaki.clubm:drawable/ic_call_answer_video = 0x7f0800e4
studio.takasaki.clubm:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f130417
studio.takasaki.clubm:drawable/ic_call_answer_low = 0x7f0800e3
studio.takasaki.clubm:drawable/googleg_standard_color_18 = 0x7f0800df
studio.takasaki.clubm:string/mtrl_picker_toggle_to_day_selection = 0x7f1200f1
studio.takasaki.clubm:drawable/fingerprint_dialog_fp_icon = 0x7f0800dd
studio.takasaki.clubm:drawable/fingerprint_dialog_error = 0x7f0800dc
studio.takasaki.clubm:drawable/dev_laucher_ic_home_white_36dp = 0x7f0800da
studio.takasaki.clubm:style/Theme = 0x7f130221
studio.takasaki.clubm:drawable/design_ic_visibility_off = 0x7f0800d7
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f130041
studio.takasaki.clubm:string/fingerprint_error_user_canceled = 0x7f12007f
studio.takasaki.clubm:drawable/compat_splash_screen_no_icon_background = 0x7f0800d4
studio.takasaki.clubm:drawable/common_google_signin_btn_text_light_normal = 0x7f0800d1
studio.takasaki.clubm:drawable/common_google_signin_btn_text_disabled = 0x7f0800ce
studio.takasaki.clubm:drawable/common_google_signin_btn_text_dark = 0x7f0800ca
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Year = 0x7f1303db
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f0800c9
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_light_focused = 0x7f0800c7
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_disabled = 0x7f0800c5
studio.takasaki.clubm:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f0800bf
studio.takasaki.clubm:attr/cropMaxZoom = 0x7f040167
studio.takasaki.clubm:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f0800bd
studio.takasaki.clubm:style/Theme.Material3.Dark.Dialog = 0x7f13024c
studio.takasaki.clubm:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f0800b2
studio.takasaki.clubm:drawable/abc_textfield_default_mtrl_alpha = 0x7f0800b0
studio.takasaki.clubm:style/TextAppearance.Material3.SearchView.Prefix = 0x7f130209
studio.takasaki.clubm:attr/behavior_peekHeight = 0x7f040074
studio.takasaki.clubm:drawable/abc_textfield_activated_mtrl_alpha = 0x7f0800af
studio.takasaki.clubm:drawable/abc_text_select_handle_right_mtrl = 0x7f0800ae
studio.takasaki.clubm:drawable/abc_tab_indicator_material = 0x7f0800a9
studio.takasaki.clubm:drawable/abc_seekbar_track_material = 0x7f0800a2
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f130270
studio.takasaki.clubm:id/showTitle = 0x7f0901bd
studio.takasaki.clubm:attr/buttonTintMode = 0x7f04009c
studio.takasaki.clubm:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070155
studio.takasaki.clubm:drawable/abc_seekbar_thumb_material = 0x7f0800a0
studio.takasaki.clubm:drawable/abc_ratingbar_indicator_material = 0x7f080098
studio.takasaki.clubm:id/standard = 0x7f0901d0
studio.takasaki.clubm:id/accessibility_custom_action_0 = 0x7f090016
studio.takasaki.clubm:drawable/abc_popup_background_mtrl_mult = 0x7f080097
studio.takasaki.clubm:attr/textEndPadding = 0x7f040469
studio.takasaki.clubm:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080096
studio.takasaki.clubm:animator/mtrl_btn_state_list_anim = 0x7f020015
studio.takasaki.clubm:drawable/abc_list_selector_background_transition_holo_light = 0x7f080091
studio.takasaki.clubm:dimen/design_navigation_padding_bottom = 0x7f07007f
studio.takasaki.clubm:dimen/m3_slider_thumb_elevation = 0x7f0701ef
studio.takasaki.clubm:drawable/abc_item_background_holo_dark = 0x7f080088
studio.takasaki.clubm:drawable/abc_ic_voice_search_api_material = 0x7f080087
studio.takasaki.clubm:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702b9
studio.takasaki.clubm:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080084
studio.takasaki.clubm:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080081
studio.takasaki.clubm:layout/mtrl_picker_text_input_date_range = 0x7f0c006c
studio.takasaki.clubm:id/accessibility_custom_action_1 = 0x7f090017
studio.takasaki.clubm:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080080
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1303cf
studio.takasaki.clubm:string/catalyst_reload_error = 0x7f120047
studio.takasaki.clubm:drawable/abc_ic_ab_back_material = 0x7f08007b
studio.takasaki.clubm:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07023b
studio.takasaki.clubm:drawable/abc_control_background_material = 0x7f080078
studio.takasaki.clubm:id/up = 0x7f090212
studio.takasaki.clubm:color/material_personalized_color_outline = 0x7f0602a8
studio.takasaki.clubm:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080073
studio.takasaki.clubm:drawable/abc_btn_colored_material = 0x7f08006d
studio.takasaki.clubm:attr/layout_constraintBottom_toTopOf = 0x7f040294
studio.takasaki.clubm:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08006c
studio.takasaki.clubm:string/mtrl_picker_invalid_format = 0x7f1200db
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_warningtriangleiconlight = 0x7f080063
studio.takasaki.clubm:id/open_search_view_scrim = 0x7f09016e
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_warningtriangleicon = 0x7f080062
studio.takasaki.clubm:color/material_personalized_color_control_highlight = 0x7f060298
studio.takasaki.clubm:drawable/abc_star_black_48dp = 0x7f0800a5
studio.takasaki.clubm:styleable/ScrimInsetsFrameLayout = 0x7f140077
studio.takasaki.clubm:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f080133
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_usericonlight = 0x7f080061
studio.takasaki.clubm:attr/insetForeground = 0x7f040257
studio.takasaki.clubm:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f07024e
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_usericon = 0x7f080060
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f130482
studio.takasaki.clubm:layout/mtrl_alert_select_dialog_item = 0x7f0c0052
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_updateicon = 0x7f08005e
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_toolbaroverlayicon = 0x7f08005d
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Subhead = 0x7f130030
studio.takasaki.clubm:interpolator/mtrl_linear = 0x7f0b0010
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_showmenuatlaunchiconlight = 0x7f080059
studio.takasaki.clubm:id/action_bar_activity_content = 0x7f09003f
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_settingsfilledactiveicon = 0x7f080052
studio.takasaki.clubm:id/scrollIndicatorDown = 0x7f0901a9
studio.takasaki.clubm:attr/selectableItemBackground = 0x7f0403c4
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_runicon = 0x7f080051
studio.takasaki.clubm:style/Widget.AppCompat.ActionBar = 0x7f130310
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_logoicon = 0x7f08004d
studio.takasaki.clubm:macro/m3_comp_switch_selected_icon_color = 0x7f0d0129
studio.takasaki.clubm:dimen/design_navigation_item_vertical_padding = 0x7f07007d
studio.takasaki.clubm:attr/enableEdgeToEdge = 0x7f0401bc
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_inspectelementicon = 0x7f08004b
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_infoicon = 0x7f080049
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_homefilledinactiveiconlight = 0x7f080048
studio.takasaki.clubm:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700b1
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_homefilledactiveicon = 0x7f080045
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsfilledinactiveiconlight = 0x7f080042
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_debugicon = 0x7f08003e
studio.takasaki.clubm:style/Theme.AppCompat.Dialog.Alert = 0x7f13022d
studio.takasaki.clubm:string/menuitem_description = 0x7f1200bf
studio.takasaki.clubm:id/catalyst_redbox_title = 0x7f090078
studio.takasaki.clubm:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601e6
studio.takasaki.clubm:color/material_dynamic_neutral60 = 0x7f060245
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_clipboardicon = 0x7f08003d
studio.takasaki.clubm:id/action_bar_spinner = 0x7f090042
studio.takasaki.clubm:color/material_dynamic_neutral_variant60 = 0x7f060252
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_checkicon = 0x7f08003a
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_buildingicon = 0x7f080039
studio.takasaki.clubm:color/material_deep_teal_200 = 0x7f060233
studio.takasaki.clubm:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_chevronright = 0x7f080034
studio.takasaki.clubm:drawable/common_google_signin_btn_text_light = 0x7f0800cf
studio.takasaki.clubm:drawable/__node_modules_reactnavigation_elements_lib_module_assets_searchicon = 0x7f080031
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents = 0x7f1302e8
studio.takasaki.clubm:drawable/__node_modules_reactnavigation_elements_lib_module_assets_closeicon = 0x7f080030
studio.takasaki.clubm:attr/startIconMinSize = 0x7f0403f9
studio.takasaki.clubm:drawable/__node_modules_reactnavigation_elements_lib_module_assets_clearicon = 0x7f08002f
studio.takasaki.clubm:attr/checkedIconSize = 0x7f0400b2
studio.takasaki.clubm:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_close = 0x7f08002b
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070143
studio.takasaki.clubm:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_chevronleft = 0x7f080029
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080027
studio.takasaki.clubm:string/mtrl_timepicker_confirm = 0x7f1200fd
studio.takasaki.clubm:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602f5
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080026
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f13048f
studio.takasaki.clubm:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f07009e
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f08001f
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f130294
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001e
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001d
studio.takasaki.clubm:styleable/AnimatedStateListDrawableTransition = 0x7f140009
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f080019
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080018
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080017
studio.takasaki.clubm:color/m3_sys_color_light_surface_container_low = 0x7f060204
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080010
studio.takasaki.clubm:dimen/mtrl_snackbar_padding_horizontal = 0x7f0702f9
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000e
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000d
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080015
studio.takasaki.clubm:drawable/$m3_avd_show_password__2 = 0x7f08000b
studio.takasaki.clubm:styleable/MaterialTimePicker = 0x7f14005e
studio.takasaki.clubm:drawable/$m3_avd_show_password__0 = 0x7f080009
studio.takasaki.clubm:attr/retryImageScaleType = 0x7f0403a7
studio.takasaki.clubm:attr/checkMarkTintMode = 0x7f0400aa
studio.takasaki.clubm:drawable/$m3_avd_hide_password__0 = 0x7f080006
studio.takasaki.clubm:drawable/$avd_show_password__1 = 0x7f080004
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f130283
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f13006e
studio.takasaki.clubm:dimen/subtitle_shadow_radius = 0x7f070329
studio.takasaki.clubm:dimen/splashscreen_icon_size_with_background = 0x7f070325
studio.takasaki.clubm:string/android.credentials.TYPE_PASSWORD_CREDENTIAL = 0x7f12001e
studio.takasaki.clubm:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f070322
studio.takasaki.clubm:xml/library_file_paths = 0x7f150004
studio.takasaki.clubm:string/progressbar_description = 0x7f120106
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0d0154
studio.takasaki.clubm:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0d0132
studio.takasaki.clubm:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f070321
studio.takasaki.clubm:id/accessibility_hint = 0x7f090036
studio.takasaki.clubm:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070302
studio.takasaki.clubm:attr/actionViewClass = 0x7f040023
studio.takasaki.clubm:color/switch_thumb_material_light = 0x7f06031f
studio.takasaki.clubm:dimen/splashscreen_icon_mask_size_with_background = 0x7f070320
studio.takasaki.clubm:string/mtrl_picker_range_header_selected = 0x7f1200e4
studio.takasaki.clubm:dimen/notification_subtext_size = 0x7f07031c
studio.takasaki.clubm:dimen/notification_right_side_padding_top = 0x7f070319
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f130094
studio.takasaki.clubm:color/m3_sys_color_secondary_fixed_dim = 0x7f060213
studio.takasaki.clubm:dimen/notification_large_icon_height = 0x7f070314
studio.takasaki.clubm:attr/colorControlHighlight = 0x7f0400ec
studio.takasaki.clubm:dimen/notification_content_margin_start = 0x7f070313
studio.takasaki.clubm:string/abc_menu_ctrl_shortcut_label = 0x7f12000b
studio.takasaki.clubm:dimen/notification_big_circle_margin = 0x7f070312
studio.takasaki.clubm:dimen/notification_action_icon_size = 0x7f070310
studio.takasaki.clubm:id/staticPostLayout = 0x7f0901d6
studio.takasaki.clubm:dimen/mtrl_tooltip_padding = 0x7f07030e
studio.takasaki.clubm:attr/enforceTextAppearance = 0x7f0401c6
studio.takasaki.clubm:dimen/mtrl_tooltip_cornerSize = 0x7f07030b
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1302a5
studio.takasaki.clubm:dimen/mtrl_textinput_counter_margin_start = 0x7f070305
studio.takasaki.clubm:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070301
studio.takasaki.clubm:attr/boxStrokeColor = 0x7f040087
studio.takasaki.clubm:dimen/mtrl_switch_track_height = 0x7f0702fe
studio.takasaki.clubm:layout/notification_template_part_time = 0x7f0c007d
studio.takasaki.clubm:drawable/ic_clear_black_24 = 0x7f0800e8
studio.takasaki.clubm:attr/fastScrollVerticalTrackDrawable = 0x7f0401f5
studio.takasaki.clubm:dimen/mtrl_switch_thumb_elevation = 0x7f0702fb
studio.takasaki.clubm:drawable/btn_radio_on_mtrl = 0x7f0800be
studio.takasaki.clubm:attr/cropBorderLineColor = 0x7f040159
studio.takasaki.clubm:dimen/mtrl_snackbar_margin = 0x7f0702f7
studio.takasaki.clubm:dimen/mtrl_snackbar_background_corner_radius = 0x7f0702f5
studio.takasaki.clubm:dimen/mtrl_slider_track_height = 0x7f0702f1
studio.takasaki.clubm:drawable/abc_btn_radio_material = 0x7f08006f
studio.takasaki.clubm:dimen/mtrl_slider_thumb_radius = 0x7f0702ee
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f13019b
studio.takasaki.clubm:dimen/mtrl_slider_label_square_side = 0x7f0702ec
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080014
studio.takasaki.clubm:dimen/mtrl_progress_track_thickness = 0x7f0702e5
studio.takasaki.clubm:color/m3_sys_color_light_surface_variant = 0x7f060207
studio.takasaki.clubm:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702e3
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0d00bb
studio.takasaki.clubm:drawable/abc_ic_search_api_material = 0x7f080086
studio.takasaki.clubm:style/Theme.AppCompat.Light.Dialog = 0x7f130233
studio.takasaki.clubm:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702e2
studio.takasaki.clubm:color/colorPrimaryDark = 0x7f060037
studio.takasaki.clubm:color/design_default_color_secondary = 0x7f06005c
studio.takasaki.clubm:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702e1
studio.takasaki.clubm:string/mtrl_picker_out_of_range = 0x7f1200e1
studio.takasaki.clubm:dimen/mtrl_progress_circular_size_small = 0x7f0702e0
studio.takasaki.clubm:id/select_dialog_listview = 0x7f0901b7
studio.takasaki.clubm:dimen/mtrl_progress_circular_size_medium = 0x7f0702df
studio.takasaki.clubm:attr/elevationOverlayAccentColor = 0x7f0401b8
studio.takasaki.clubm:attr/badgeWithTextShapeAppearance = 0x7f040065
studio.takasaki.clubm:dimen/mtrl_tooltip_minWidth = 0x7f07030d
studio.takasaki.clubm:attr/motionDurationMedium3 = 0x7f040338
studio.takasaki.clubm:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702de
studio.takasaki.clubm:id/jumpToStart = 0x7f090108
studio.takasaki.clubm:dimen/mtrl_navigation_rail_text_size = 0x7f0702d7
studio.takasaki.clubm:drawable/__node_modules_reactnavigation_elements_lib_module_assets_backicon = 0x7f08002d
studio.takasaki.clubm:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702d6
studio.takasaki.clubm:attr/motionEasingLinearInterpolator = 0x7f040345
studio.takasaki.clubm:dimen/mtrl_navigation_item_icon_padding = 0x7f0702cb
studio.takasaki.clubm:attr/drawableStartCompat = 0x7f0401a8
studio.takasaki.clubm:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702ca
studio.takasaki.clubm:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702c7
studio.takasaki.clubm:id/error_footer_content = 0x7f0900c3
studio.takasaki.clubm:dimen/mtrl_navigation_elevation = 0x7f0702c9
studio.takasaki.clubm:dimen/mtrl_min_touch_target_size = 0x7f0702c6
studio.takasaki.clubm:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702c5
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f130031
studio.takasaki.clubm:dimen/mtrl_tooltip_minHeight = 0x7f07030c
studio.takasaki.clubm:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702c3
studio.takasaki.clubm:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702c0
studio.takasaki.clubm:dimen/mtrl_fab_translation_z_pressed = 0x7f0702bd
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f130156
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0d0018
studio.takasaki.clubm:color/m3_ref_palette_error10 = 0x7f060112
studio.takasaki.clubm:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702b8
studio.takasaki.clubm:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701a5
studio.takasaki.clubm:dimen/mtrl_extended_fab_top_padding = 0x7f0702b6
studio.takasaki.clubm:layout/design_navigation_item_header = 0x7f0c002c
studio.takasaki.clubm:color/m3_ref_palette_primary60 = 0x7f06014a
studio.takasaki.clubm:color/m3_timepicker_button_text_color = 0x7f060226
studio.takasaki.clubm:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702b5
studio.takasaki.clubm:attr/layout = 0x7f040285
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_performanceicon = 0x7f08004e
studio.takasaki.clubm:dimen/mtrl_extended_fab_min_height = 0x7f0702b2
studio.takasaki.clubm:style/AlertDialog.AppCompat = 0x7f130000
studio.takasaki.clubm:dimen/mtrl_extended_fab_icon_size = 0x7f0702b0
studio.takasaki.clubm:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702ac
studio.takasaki.clubm:color/material_dynamic_primary20 = 0x7f06025b
studio.takasaki.clubm:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702ab
studio.takasaki.clubm:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702aa
studio.takasaki.clubm:id/info = 0x7f090100
studio.takasaki.clubm:attr/spinnerDropDownItemStyle = 0x7f0403ef
studio.takasaki.clubm:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702a9
studio.takasaki.clubm:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702a7
studio.takasaki.clubm:dimen/mtrl_chip_text_size = 0x7f0702a6
studio.takasaki.clubm:dimen/mtrl_chip_pressed_translation_z = 0x7f0702a5
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button = 0x7f1302b3
studio.takasaki.clubm:dimen/mtrl_card_spacing = 0x7f0702a4
studio.takasaki.clubm:dimen/mtrl_card_elevation = 0x7f0702a3
studio.takasaki.clubm:string/face_prompt_message = 0x7f120074
studio.takasaki.clubm:dimen/mtrl_calendar_year_height = 0x7f07029b
studio.takasaki.clubm:style/Base.Widget.AppCompat.EditText = 0x7f1300e1
studio.takasaki.clubm:attr/expandedTitleGravity = 0x7f0401d5
studio.takasaki.clubm:dimen/mtrl_progress_circular_size = 0x7f0702dd
studio.takasaki.clubm:dimen/mtrl_calendar_title_baseline_to_top = 0x7f070298
studio.takasaki.clubm:id/ic_rotate_left_24 = 0x7f0900f6
studio.takasaki.clubm:drawable/abc_dialog_material_background = 0x7f080079
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0d0076
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_updateiconlight = 0x7f08005f
studio.takasaki.clubm:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f070295
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.Dialog = 0x7f130057
studio.takasaki.clubm:attr/cropperLabelTextSize = 0x7f040177
studio.takasaki.clubm:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f070294
studio.takasaki.clubm:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f070292
studio.takasaki.clubm:dimen/mtrl_calendar_navigation_top_padding = 0x7f070291
studio.takasaki.clubm:id/ignoreRequest = 0x7f0900fd
studio.takasaki.clubm:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
studio.takasaki.clubm:dimen/mtrl_calendar_landscape_header_width = 0x7f07028b
studio.takasaki.clubm:style/Base.Widget.AppCompat.SeekBar = 0x7f1300fa
studio.takasaki.clubm:color/mtrl_calendar_item_stroke_color = 0x7f0602db
studio.takasaki.clubm:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f070289
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f130195
studio.takasaki.clubm:drawable/abc_item_background_holo_light = 0x7f080089
studio.takasaki.clubm:attr/colorPrimaryFixedDim = 0x7f04010b
studio.takasaki.clubm:dimen/mtrl_calendar_header_selection_line_height = 0x7f070287
studio.takasaki.clubm:drawable/abc_ic_go_search_api_material = 0x7f08007f
studio.takasaki.clubm:color/primary_material_dark = 0x7f06030f
studio.takasaki.clubm:dimen/mtrl_calendar_day_today_stroke = 0x7f07027d
studio.takasaki.clubm:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f070283
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0d015e
studio.takasaki.clubm:id/rn_redbox_dismiss_button = 0x7f090199
studio.takasaki.clubm:dimen/mtrl_calendar_header_content_padding = 0x7f070282
studio.takasaki.clubm:color/m3_ref_palette_neutral20 = 0x7f060123
studio.takasaki.clubm:dimen/mtrl_calendar_day_horizontal_padding = 0x7f07027c
studio.takasaki.clubm:dimen/m3_comp_badge_size = 0x7f070104
studio.takasaki.clubm:dimen/mtrl_calendar_day_height = 0x7f07027b
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0d0008
studio.takasaki.clubm:attr/waveDecay = 0x7f0404cf
studio.takasaki.clubm:dimen/mtrl_calendar_content_padding = 0x7f070279
studio.takasaki.clubm:dimen/mtrl_calendar_bottom_padding = 0x7f070278
studio.takasaki.clubm:dimen/mtrl_calendar_action_padding = 0x7f070277
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f130158
studio.takasaki.clubm:dimen/mtrl_calendar_action_height = 0x7f070276
studio.takasaki.clubm:dimen/mtrl_btn_pressed_z = 0x7f07026d
studio.takasaki.clubm:string/use_fingerprint_or_screen_lock_label = 0x7f120125
studio.takasaki.clubm:drawable/abc_ic_clear_material = 0x7f08007d
studio.takasaki.clubm:attr/shapeAppearanceCornerSmall = 0x7f0403cd
studio.takasaki.clubm:color/m3_checkbox_button_tint = 0x7f06008b
studio.takasaki.clubm:dimen/mtrl_btn_padding_top = 0x7f07026c
studio.takasaki.clubm:macro/m3_comp_search_view_container_color = 0x7f0d00f1
studio.takasaki.clubm:id/rn_redbox_line_separator = 0x7f09019a
studio.takasaki.clubm:dimen/mtrl_btn_padding_right = 0x7f07026b
studio.takasaki.clubm:dimen/mtrl_btn_max_width = 0x7f070268
studio.takasaki.clubm:dimen/mtrl_btn_letter_spacing = 0x7f070267
studio.takasaki.clubm:color/m3_sys_color_dark_primary = 0x7f060189
studio.takasaki.clubm:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070264
studio.takasaki.clubm:dimen/mtrl_btn_hovered_z = 0x7f070263
studio.takasaki.clubm:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1300c4
studio.takasaki.clubm:dimen/mtrl_btn_elevation = 0x7f070261
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600d3
studio.takasaki.clubm:dimen/tooltip_horizontal_padding = 0x7f07032b
studio.takasaki.clubm:color/abc_primary_text_disable_only_material_dark = 0x7f060009
studio.takasaki.clubm:dimen/design_snackbar_padding_vertical_2lines = 0x7f07008a
studio.takasaki.clubm:dimen/mtrl_btn_dialog_btn_min_width = 0x7f07025e
studio.takasaki.clubm:dimen/mtrl_badge_with_text_size = 0x7f070256
studio.takasaki.clubm:color/m3_checkbox_button_icon_tint = 0x7f06008a
studio.takasaki.clubm:dimen/abc_dialog_corner_radius_material = 0x7f07001b
studio.takasaki.clubm:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_loader = 0x7f080036
studio.takasaki.clubm:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702bc
studio.takasaki.clubm:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070254
studio.takasaki.clubm:dimen/mtrl_badge_text_size = 0x7f070253
studio.takasaki.clubm:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070252
studio.takasaki.clubm:color/material_dynamic_neutral_variant99 = 0x7f060257
studio.takasaki.clubm:dimen/mtrl_badge_size = 0x7f070251
studio.takasaki.clubm:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07024a
studio.takasaki.clubm:dimen/material_time_picker_minimum_screen_width = 0x7f070249
studio.takasaki.clubm:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070244
studio.takasaki.clubm:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070242
studio.takasaki.clubm:drawable/mtrl_ic_arrow_drop_down = 0x7f08011f
studio.takasaki.clubm:dimen/material_helper_text_default_padding_top = 0x7f070241
studio.takasaki.clubm:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070240
studio.takasaki.clubm:dimen/mtrl_calendar_month_horizontal_padding = 0x7f07028d
studio.takasaki.clubm:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07023e
studio.takasaki.clubm:string/fallback_menu_item_open_in_browser = 0x7f120076
studio.takasaki.clubm:dimen/material_emphasis_high_type = 0x7f070239
studio.takasaki.clubm:dimen/autofill_inline_suggestion_icon_size = 0x7f070052
studio.takasaki.clubm:dimen/material_cursor_inset = 0x7f070234
studio.takasaki.clubm:style/ThemeOverlay.Material3.Search = 0x7f1302dd
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0b0009
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_branchiconlight = 0x7f080038
studio.takasaki.clubm:attr/chipStrokeWidth = 0x7f0400c8
studio.takasaki.clubm:dimen/material_clock_size = 0x7f070233
studio.takasaki.clubm:attr/actionBarTabStyle = 0x7f040007
studio.takasaki.clubm:dimen/material_clock_period_toggle_vertical_gap = 0x7f070231
studio.takasaki.clubm:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
studio.takasaki.clubm:dimen/material_clock_number_text_size = 0x7f07022e
studio.takasaki.clubm:dimen/material_clock_hand_stroke_width = 0x7f07022d
studio.takasaki.clubm:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f08010c
studio.takasaki.clubm:dimen/material_clock_display_width = 0x7f070228
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_xicon = 0x7f080064
studio.takasaki.clubm:attr/colorPrimaryDark = 0x7f040109
studio.takasaki.clubm:dimen/material_clock_display_padding = 0x7f070227
studio.takasaki.clubm:id/spread_inside = 0x7f0901cb
studio.takasaki.clubm:dimen/material_clock_display_height = 0x7f070226
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f13019e
studio.takasaki.clubm:dimen/material_bottom_sheet_max_width = 0x7f070225
studio.takasaki.clubm:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f130101
studio.takasaki.clubm:attr/itemActiveIndicatorStyle = 0x7f04025d
studio.takasaki.clubm:dimen/m3_toolbar_text_size_title = 0x7f070224
studio.takasaki.clubm:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080071
studio.takasaki.clubm:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f07021f
studio.takasaki.clubm:attr/duration = 0x7f0401b2
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07021d
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f070219
studio.takasaki.clubm:attr/textAppearanceSearchResultTitle = 0x7f040460
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070215
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070213
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070212
studio.takasaki.clubm:integer/mtrl_calendar_header_orientation = 0x7f0a0032
studio.takasaki.clubm:dimen/abc_text_size_body_2_material = 0x7f070040
studio.takasaki.clubm:dimen/m3_card_elevated_hovered_z = 0x7f0700ed
studio.takasaki.clubm:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070211
studio.takasaki.clubm:id/start = 0x7f0901d1
studio.takasaki.clubm:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07020e
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07020c
studio.takasaki.clubm:layout/m3_alert_dialog_title = 0x7f0c003e
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07020a
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070203
studio.takasaki.clubm:color/dev_launcher_colorAccentDark = 0x7f06006a
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070201
studio.takasaki.clubm:id/message = 0x7f090131
studio.takasaki.clubm:anim/abc_slide_out_top = 0x7f010009
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0701fe
studio.takasaki.clubm:style/Widget.Material3.Chip.Filter.Elevated = 0x7f130398
studio.takasaki.clubm:macro/m3_comp_menu_container_color = 0x7f0d005d
studio.takasaki.clubm:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702bf
studio.takasaki.clubm:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f04046f
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0701fb
studio.takasaki.clubm:color/mtrl_textinput_hovered_box_stroke_color = 0x7f060309
studio.takasaki.clubm:dimen/m3_sys_elevation_level5 = 0x7f0701f9
studio.takasaki.clubm:attr/visibilityMode = 0x7f0404cc
studio.takasaki.clubm:dimen/m3_sys_elevation_level2 = 0x7f0701f6
studio.takasaki.clubm:color/material_dynamic_color_light_on_error_container = 0x7f06023d
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000c
studio.takasaki.clubm:dimen/m3_sys_elevation_level1 = 0x7f0701f5
studio.takasaki.clubm:dimen/m3_sys_elevation_level0 = 0x7f0701f4
studio.takasaki.clubm:dimen/m3_snackbar_margin = 0x7f0701f3
studio.takasaki.clubm:integer/m3_btn_anim_duration_ms = 0x7f0a000d
studio.takasaki.clubm:dimen/m3_small_fab_size = 0x7f0701f1
studio.takasaki.clubm:dimen/m3_simple_item_color_hovered_alpha = 0x7f0701ed
studio.takasaki.clubm:style/Base.V7.Theme.AppCompat.Light = 0x7f1300c2
studio.takasaki.clubm:attr/layout_constraintHorizontal_weight = 0x7f0402a4
studio.takasaki.clubm:dimen/m3_side_sheet_width = 0x7f0701ec
studio.takasaki.clubm:dimen/mtrl_extended_fab_start_padding = 0x7f0702b4
studio.takasaki.clubm:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070270
studio.takasaki.clubm:dimen/m3_searchbar_text_size = 0x7f0701e5
studio.takasaki.clubm:attr/titleTextStyle = 0x7f04049f
studio.takasaki.clubm:color/material_dynamic_neutral_variant0 = 0x7f06024b
studio.takasaki.clubm:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701e4
studio.takasaki.clubm:attr/collapsedTitleTextColor = 0x7f0400e0
studio.takasaki.clubm:attr/crossfade = 0x7f040178
studio.takasaki.clubm:dimen/m3_searchbar_margin_vertical = 0x7f0701e1
studio.takasaki.clubm:dimen/m3_searchbar_margin_horizontal = 0x7f0701e0
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070214
studio.takasaki.clubm:dimen/m3_ripple_pressed_alpha = 0x7f0701dc
studio.takasaki.clubm:string/timer_description = 0x7f12011e
studio.takasaki.clubm:id/accessibility_links = 0x7f090038
studio.takasaki.clubm:dimen/m3_ripple_hovered_alpha = 0x7f0701db
studio.takasaki.clubm:dimen/m3_ripple_focused_alpha = 0x7f0701da
studio.takasaki.clubm:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701d7
studio.takasaki.clubm:style/TextAppearance.Material3.SearchView = 0x7f130208
studio.takasaki.clubm:dimen/m3_navigation_rail_item_padding_top = 0x7f0701d6
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1301d8
studio.takasaki.clubm:style/TextAppearance.AppCompat.Large = 0x7f1301af
studio.takasaki.clubm:dimen/m3_navigation_rail_item_min_height = 0x7f0701d3
studio.takasaki.clubm:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1303ad
studio.takasaki.clubm:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701d1
studio.takasaki.clubm:attr/haloRadius = 0x7f04022d
studio.takasaki.clubm:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08007c
studio.takasaki.clubm:dimen/m3_navigation_rail_elevation = 0x7f0701ce
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.Medium = 0x7f130181
studio.takasaki.clubm:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
studio.takasaki.clubm:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701cc
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f130488
studio.takasaki.clubm:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701cb
studio.takasaki.clubm:dimen/m3_navigation_item_shape_inset_start = 0x7f0701c8
studio.takasaki.clubm:attr/chipIconSize = 0x7f0400bd
studio.takasaki.clubm:dimen/design_snackbar_action_inline_max_width = 0x7f070081
studio.takasaki.clubm:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07021e
studio.takasaki.clubm:dimen/m3_navigation_item_icon_padding = 0x7f0701c5
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Chip = 0x7f130212
studio.takasaki.clubm:attr/state_lifted = 0x7f040404
studio.takasaki.clubm:dimen/m3_navigation_item_horizontal_padding = 0x7f0701c4
studio.takasaki.clubm:string/mtrl_checkbox_button_path_checked = 0x7f1200c5
studio.takasaki.clubm:layout/abc_list_menu_item_radio = 0x7f0c0011
studio.takasaki.clubm:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701c2
studio.takasaki.clubm:id/staticLayout = 0x7f0901d5
studio.takasaki.clubm:dimen/m3_large_fab_size = 0x7f0701be
studio.takasaki.clubm:dimen/m3_large_fab_max_image_size = 0x7f0701bd
studio.takasaki.clubm:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701bb
studio.takasaki.clubm:styleable/LinearProgressIndicator = 0x7f14004b
studio.takasaki.clubm:dimen/m3_fab_corner_size = 0x7f0701ba
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0d006a
studio.takasaki.clubm:attr/motionPathRotate = 0x7f04034c
studio.takasaki.clubm:dimen/m3_extended_fab_start_padding = 0x7f0701b7
studio.takasaki.clubm:styleable/MaterialTextView = 0x7f14005d
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001c
studio.takasaki.clubm:dimen/m3_extended_fab_min_height = 0x7f0701b6
studio.takasaki.clubm:dimen/m3_extended_fab_bottom_padding = 0x7f0701b3
studio.takasaki.clubm:layout/mtrl_search_view = 0x7f0c006e
studio.takasaki.clubm:dimen/splashscreen_icon_size = 0x7f070323
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1300ea
studio.takasaki.clubm:color/material_dynamic_primary40 = 0x7f06025d
studio.takasaki.clubm:anim/rns_slide_out_to_right = 0x7f01004b
studio.takasaki.clubm:dimen/m3_divider_heavy_thickness = 0x7f0701b2
studio.takasaki.clubm:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701b0
studio.takasaki.clubm:string/project_id = 0x7f120107
studio.takasaki.clubm:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701ac
studio.takasaki.clubm:attr/track = 0x7f0404ad
studio.takasaki.clubm:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701ab
studio.takasaki.clubm:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701a6
studio.takasaki.clubm:id/material_clock_hand = 0x7f09011e
studio.takasaki.clubm:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f070188
studio.takasaki.clubm:dimen/m3_comp_time_picker_container_elevation = 0x7f0701a4
studio.takasaki.clubm:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701a0
studio.takasaki.clubm:id/autofill_inline_suggestion_subtitle = 0x7f090064
studio.takasaki.clubm:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f07019e
studio.takasaki.clubm:dimen/m3_comp_switch_track_height = 0x7f07019b
studio.takasaki.clubm:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f07019a
studio.takasaki.clubm:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f070198
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f130025
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary90 = 0x7f0600f4
studio.takasaki.clubm:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f070192
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1303b7
studio.takasaki.clubm:style/Theme.AppCompat.Dialog = 0x7f13022c
studio.takasaki.clubm:dimen/m3_comp_suggestion_chip_container_height = 0x7f07018e
studio.takasaki.clubm:dimen/m3_comp_switch_track_width = 0x7f07019c
studio.takasaki.clubm:attr/emojiCompatEnabled = 0x7f0401bb
studio.takasaki.clubm:dimen/m3_comp_snackbar_container_elevation = 0x7f07018d
studio.takasaki.clubm:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0d0104
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080025
studio.takasaki.clubm:dimen/m3_comp_slider_stop_indicator_size = 0x7f07018c
studio.takasaki.clubm:dimen/m3_comp_slider_inactive_track_height = 0x7f07018b
studio.takasaki.clubm:styleable/MenuView = 0x7f140062
studio.takasaki.clubm:style/Base.Widget.Material3.BottomNavigationView = 0x7f130105
studio.takasaki.clubm:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f070189
studio.takasaki.clubm:attr/buttonBarNeutralButtonStyle = 0x7f04008e
studio.takasaki.clubm:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f070194
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f13017c
studio.takasaki.clubm:dimen/m3_comp_slider_active_handle_leading_space = 0x7f070186
studio.takasaki.clubm:dimen/m3_comp_slider_active_handle_height = 0x7f070185
studio.takasaki.clubm:color/material_dynamic_color_dark_on_error_container = 0x7f060239
studio.takasaki.clubm:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f070184
studio.takasaki.clubm:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f070183
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070204
studio.takasaki.clubm:dimen/m3_comp_sheet_side_docked_container_width = 0x7f070182
studio.takasaki.clubm:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07011d
studio.takasaki.clubm:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f070181
studio.takasaki.clubm:dimen/material_emphasis_disabled = 0x7f070237
studio.takasaki.clubm:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f070180
studio.takasaki.clubm:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f07017f
studio.takasaki.clubm:attr/trackThickness = 0x7f0404b8
studio.takasaki.clubm:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f070179
studio.takasaki.clubm:dimen/m3_side_sheet_modal_elevation = 0x7f0701ea
studio.takasaki.clubm:dimen/m3_comp_search_view_container_elevation = 0x7f070177
studio.takasaki.clubm:id/embed = 0x7f0900bb
studio.takasaki.clubm:drawable/redbox_top_border_background = 0x7f080147
studio.takasaki.clubm:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f070199
studio.takasaki.clubm:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070175
studio.takasaki.clubm:styleable/PopupWindow = 0x7f14006f
studio.takasaki.clubm:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1300f9
studio.takasaki.clubm:attr/badgeTextAppearance = 0x7f04005e
studio.takasaki.clubm:dimen/m3_comp_search_bar_container_height = 0x7f070174
studio.takasaki.clubm:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070220
studio.takasaki.clubm:dimen/m3_comp_search_bar_container_elevation = 0x7f070173
studio.takasaki.clubm:dimen/m3_comp_scrim_container_opacity = 0x7f070171
studio.takasaki.clubm:string/material_clock_display_divider = 0x7f1200a7
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsiconlight = 0x7f080044
studio.takasaki.clubm:style/Base.Animation.AppCompat.DropDownUp = 0x7f130011
studio.takasaki.clubm:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f07016e
studio.takasaki.clubm:id/fingerprint_icon = 0x7f0900d4
studio.takasaki.clubm:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07016d
studio.takasaki.clubm:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07016c
studio.takasaki.clubm:id/role = 0x7f0901a0
studio.takasaki.clubm:attr/homeAsUpIndicator = 0x7f04023d
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary95 = 0x7f0600f5
studio.takasaki.clubm:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f07016b
studio.takasaki.clubm:color/m3_sys_color_dark_outline_variant = 0x7f060188
studio.takasaki.clubm:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f070169
studio.takasaki.clubm:dimen/m3_comp_progress_indicator_track_thickness = 0x7f070168
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070164
studio.takasaki.clubm:string/expo_splash_screen_status_bar_translucent = 0x7f12006f
studio.takasaki.clubm:dimen/design_fab_translation_z_hovered_focused = 0x7f070076
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070162
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0600db
studio.takasaki.clubm:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700bb
studio.takasaki.clubm:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07015b
studio.takasaki.clubm:attr/boxBackgroundColor = 0x7f040080
studio.takasaki.clubm:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07015a
studio.takasaki.clubm:dimen/abc_seekbar_track_background_height_material = 0x7f070038
studio.takasaki.clubm:dimen/m3_comp_outlined_button_outline_width = 0x7f070153
studio.takasaki.clubm:attr/textAppearanceListItemSmall = 0x7f04045c
studio.takasaki.clubm:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070152
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_standard = 0x7f0b000b
studio.takasaki.clubm:attr/floatingActionButtonPrimaryStyle = 0x7f0401fc
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070150
studio.takasaki.clubm:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f13033f
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_homefilledinactiveicon = 0x7f080047
studio.takasaki.clubm:attr/textAppearanceCaption = 0x7f040448
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_icon_size = 0x7f07014f
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07014e
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07014d
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton = 0x7f130388
studio.takasaki.clubm:style/TextAppearance.Material3.SearchBar = 0x7f130207
studio.takasaki.clubm:drawable/notification_action_background = 0x7f080138
studio.takasaki.clubm:dimen/subtitle_shadow_offset = 0x7f070328
studio.takasaki.clubm:layout/abc_tooltip = 0x7f0c001b
studio.takasaki.clubm:attr/flow_wrapMode = 0x7f040218
studio.takasaki.clubm:dimen/mtrl_btn_icon_padding = 0x7f070265
studio.takasaki.clubm:attr/itemTextAppearance = 0x7f040275
studio.takasaki.clubm:color/notification_icon_bg_color = 0x7f06030b
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_container_width = 0x7f07014c
studio.takasaki.clubm:drawable/abc_seekbar_tick_mark_material = 0x7f0800a1
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07014a
studio.takasaki.clubm:id/normal = 0x7f09015e
studio.takasaki.clubm:attr/colorSecondary = 0x7f040110
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f070149
studio.takasaki.clubm:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0702f8
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070147
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary0 = 0x7f060104
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070146
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070144
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_icon_size = 0x7f070140
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07013e
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0d0165
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_container_height = 0x7f07013d
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07013c
studio.takasaki.clubm:id/expand_activities_button = 0x7f0900ca
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07013b
studio.takasaki.clubm:style/TextAppearance.Material3.TitleLarge = 0x7f13020a
studio.takasaki.clubm:id/chronometer = 0x7f090082
studio.takasaki.clubm:dimen/m3_comp_menu_container_elevation = 0x7f070139
studio.takasaki.clubm:dimen/material_textinput_min_width = 0x7f070247
studio.takasaki.clubm:dimen/m3_comp_input_chip_container_elevation = 0x7f070134
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f07013f
studio.takasaki.clubm:attr/actionBarPopupTheme = 0x7f040002
studio.takasaki.clubm:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070133
studio.takasaki.clubm:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13046d
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1303ca
studio.takasaki.clubm:attr/simpleItemSelectedColor = 0x7f0403e2
studio.takasaki.clubm:dimen/mtrl_bottomappbar_height = 0x7f07025c
studio.takasaki.clubm:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070132
studio.takasaki.clubm:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07012d
studio.takasaki.clubm:attr/layout_constraintWidth_default = 0x7f0402b4
studio.takasaki.clubm:attr/tabSelectedTextAppearance = 0x7f040437
studio.takasaki.clubm:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070125
studio.takasaki.clubm:styleable/AppCompatSeekBar = 0x7f14000f
studio.takasaki.clubm:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070122
studio.takasaki.clubm:style/Widget.AppCompat.RatingBar.Indicator = 0x7f13034b
studio.takasaki.clubm:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070121
studio.takasaki.clubm:color/abc_search_url_text_pressed = 0x7f06000f
studio.takasaki.clubm:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070120
studio.takasaki.clubm:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07011c
studio.takasaki.clubm:attr/collapsedTitleTextAppearance = 0x7f0400df
studio.takasaki.clubm:color/design_default_color_background = 0x7f060052
studio.takasaki.clubm:dimen/m3_comp_fab_primary_container_elevation = 0x7f070119
studio.takasaki.clubm:id/textinput_error = 0x7f0901f5
studio.takasaki.clubm:attr/colorTertiaryContainer = 0x7f040121
studio.takasaki.clubm:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700c2
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070116
studio.takasaki.clubm:dimen/m3_snackbar_action_text_color_alpha = 0x7f0701f2
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070111
studio.takasaki.clubm:integer/mtrl_view_gone = 0x7f0a0041
studio.takasaki.clubm:dimen/m3_comp_elevated_card_icon_size = 0x7f07010f
studio.takasaki.clubm:attr/contentDescription = 0x7f04012c
studio.takasaki.clubm:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07010d
studio.takasaki.clubm:dimen/mtrl_extended_fab_min_width = 0x7f0702b3
studio.takasaki.clubm:dimen/m3_comp_elevated_button_container_elevation = 0x7f07010c
studio.takasaki.clubm:layout/material_clock_period_toggle = 0x7f0c0044
studio.takasaki.clubm:id/accessibility_custom_action_20 = 0x7f090023
studio.takasaki.clubm:dimen/m3_comp_divider_thickness = 0x7f07010b
studio.takasaki.clubm:color/m3_text_button_ripple_color_selector = 0x7f06021e
studio.takasaki.clubm:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070106
studio.takasaki.clubm:attr/colorContainer = 0x7f0400ea
studio.takasaki.clubm:dimen/m3_comp_badge_large_size = 0x7f070103
studio.takasaki.clubm:dimen/m3_chip_hovered_translation_z = 0x7f0700fc
studio.takasaki.clubm:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f07019d
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080021
studio.takasaki.clubm:drawable/mtrl_ic_checkbox_unchecked = 0x7f080124
studio.takasaki.clubm:dimen/mtrl_extended_fab_end_padding = 0x7f0702ae
studio.takasaki.clubm:interpolator/mtrl_fast_out_linear_in = 0x7f0b000e
studio.takasaki.clubm:dimen/m3_chip_elevated_elevation = 0x7f0700fb
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1302fb
studio.takasaki.clubm:attr/behavior_skipCollapsed = 0x7f040077
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_dark = 0x7f0800c1
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f13019a
studio.takasaki.clubm:dimen/m3_carousel_small_item_size_min = 0x7f0700f6
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0d0072
studio.takasaki.clubm:dimen/mtrl_slider_label_radius = 0x7f0702eb
studio.takasaki.clubm:dimen/m3_carousel_small_item_size_max = 0x7f0700f5
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1300e6
studio.takasaki.clubm:id/textinput_helper_text = 0x7f0901f6
studio.takasaki.clubm:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701d0
studio.takasaki.clubm:dimen/m3_carousel_small_item_default_corner_size = 0x7f0700f4
studio.takasaki.clubm:color/mtrl_navigation_bar_item_tint = 0x7f0602f0
studio.takasaki.clubm:dimen/m3_carousel_gone_size = 0x7f0700f3
studio.takasaki.clubm:drawable/compat_splash_screen = 0x7f0800d3
studio.takasaki.clubm:dimen/m3_carousel_debug_keyline_width = 0x7f0700f1
studio.takasaki.clubm:dimen/m3_card_stroke_width = 0x7f0700f0
studio.takasaki.clubm:dimen/m3_card_elevated_dragged_z = 0x7f0700eb
studio.takasaki.clubm:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0d0127
studio.takasaki.clubm:color/secondary_text_default_material_light = 0x7f060318
studio.takasaki.clubm:dimen/m3_btn_translation_z_base = 0x7f0700e6
studio.takasaki.clubm:dimen/m3_btn_text_btn_padding_left = 0x7f0700e4
studio.takasaki.clubm:attr/layout_constraintGuide_percent = 0x7f04029d
studio.takasaki.clubm:drawable/btn_checkbox_unchecked_mtrl = 0x7f0800ba
studio.takasaki.clubm:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700e2
studio.takasaki.clubm:string/radiogroup_description = 0x7f120108
studio.takasaki.clubm:string/abc_action_mode_done = 0x7f120005
studio.takasaki.clubm:id/noScroll = 0x7f09015c
studio.takasaki.clubm:dimen/m3_btn_padding_top = 0x7f0700e0
studio.takasaki.clubm:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0d004a
studio.takasaki.clubm:dimen/m3_badge_with_text_size = 0x7f0700bd
studio.takasaki.clubm:dimen/m3_btn_padding_bottom = 0x7f0700dd
studio.takasaki.clubm:styleable/RecycleListView = 0x7f140074
studio.takasaki.clubm:dimen/m3_btn_inset = 0x7f0700db
studio.takasaki.clubm:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f130353
studio.takasaki.clubm:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_chevronright = 0x7f08002a
studio.takasaki.clubm:dimen/m3_btn_icon_only_min_width = 0x7f0700da
studio.takasaki.clubm:string/catalyst_change_bundle_location = 0x7f120032
studio.takasaki.clubm:dimen/m3_btn_icon_only_default_size = 0x7f0700d8
studio.takasaki.clubm:dimen/mtrl_slider_thumb_elevation = 0x7f0702ed
studio.takasaki.clubm:drawable/$m3_avd_hide_password__2 = 0x7f080008
studio.takasaki.clubm:attr/subtitleTextAppearance = 0x7f040413
studio.takasaki.clubm:dimen/m3_btn_icon_btn_padding_right = 0x7f0700d6
studio.takasaki.clubm:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f130419
studio.takasaki.clubm:id/action_mode_bar = 0x7f09004b
studio.takasaki.clubm:dimen/m3_navigation_item_shape_inset_top = 0x7f0701c9
studio.takasaki.clubm:dimen/m3_btn_elevated_btn_elevation = 0x7f0700d3
studio.takasaki.clubm:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070306
studio.takasaki.clubm:style/TextAppearance.Material3.LabelSmall = 0x7f130205
studio.takasaki.clubm:attr/subtitleCentered = 0x7f040412
studio.takasaki.clubm:dimen/m3_btn_disabled_elevation = 0x7f0700d1
studio.takasaki.clubm:dimen/m3_btn_dialog_btn_spacing = 0x7f0700d0
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070145
studio.takasaki.clubm:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0d0141
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0d00c1
studio.takasaki.clubm:dimen/m3_btn_dialog_btn_min_width = 0x7f0700cf
studio.takasaki.clubm:dimen/design_navigation_elevation = 0x7f070078
studio.takasaki.clubm:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700ce
studio.takasaki.clubm:dimen/m3_comp_switch_disabled_track_opacity = 0x7f070195
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f130428
studio.takasaki.clubm:dimen/m3_bottomappbar_height = 0x7f0700cd
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f13026d
studio.takasaki.clubm:anim/m3_motion_fade_exit = 0x7f01002a
studio.takasaki.clubm:color/error_color_material_dark = 0x7f060075
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_checkiconlight = 0x7f08003b
studio.takasaki.clubm:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700ca
studio.takasaki.clubm:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f13041d
studio.takasaki.clubm:dimen/m3_card_elevated_elevation = 0x7f0700ec
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0d015b
studio.takasaki.clubm:id/browser_actions_menu_item_icon = 0x7f09006f
studio.takasaki.clubm:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700c9
studio.takasaki.clubm:dimen/material_divider_thickness = 0x7f070236
studio.takasaki.clubm:attr/tabMinWidth = 0x7f04042e
studio.takasaki.clubm:color/common_google_signin_btn_text_light_disabled = 0x7f06003f
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600bc
studio.takasaki.clubm:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700c8
studio.takasaki.clubm:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1303ef
studio.takasaki.clubm:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070221
studio.takasaki.clubm:dimen/m3_bottom_sheet_elevation = 0x7f0700c7
studio.takasaki.clubm:string/m3_sys_motion_easing_standard_accelerate = 0x7f1200a5
studio.takasaki.clubm:dimen/m3_bottom_nav_min_height = 0x7f0700c5
studio.takasaki.clubm:string/material_timepicker_pm = 0x7f1200ba
studio.takasaki.clubm:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700c1
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1302d5
studio.takasaki.clubm:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700c0
studio.takasaki.clubm:dimen/m3_badge_with_text_offset = 0x7f0700bc
studio.takasaki.clubm:dimen/mtrl_calendar_header_height_fullscreen = 0x7f070286
studio.takasaki.clubm:dimen/m3_badge_offset = 0x7f0700b8
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_xiconlight = 0x7f080065
studio.takasaki.clubm:attr/defaultQueryHint = 0x7f04018d
studio.takasaki.clubm:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602c6
studio.takasaki.clubm:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700b6
studio.takasaki.clubm:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700b4
studio.takasaki.clubm:color/m3_ref_palette_secondary95 = 0x7f06015b
studio.takasaki.clubm:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700b3
studio.takasaki.clubm:dimen/mtrl_progress_circular_radius = 0x7f0702dc
studio.takasaki.clubm:string/catalyst_hot_reloading_auto_enable = 0x7f12003e
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0d0164
studio.takasaki.clubm:color/design_box_stroke_color = 0x7f060044
studio.takasaki.clubm:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700b0
studio.takasaki.clubm:string/catalyst_loading_from_url = 0x7f120041
studio.takasaki.clubm:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700ac
studio.takasaki.clubm:color/m3_calendar_item_stroke_color = 0x7f060086
studio.takasaki.clubm:dimen/m3_alert_dialog_icon_margin = 0x7f0700a5
studio.takasaki.clubm:dimen/m3_alert_dialog_corner_size = 0x7f0700a3
studio.takasaki.clubm:dimen/m3_alert_dialog_action_top_padding = 0x7f0700a2
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f13006f
studio.takasaki.clubm:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701a1
studio.takasaki.clubm:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700a0
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f130096
studio.takasaki.clubm:layout/abc_dialog_title_material = 0x7f0c000c
studio.takasaki.clubm:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401fa
studio.takasaki.clubm:dimen/hint_pressed_alpha_material_light = 0x7f07009d
studio.takasaki.clubm:dimen/hint_alpha_material_light = 0x7f07009b
studio.takasaki.clubm:id/centerInside = 0x7f09007b
studio.takasaki.clubm:dimen/hint_alpha_material_dark = 0x7f07009a
studio.takasaki.clubm:dimen/highlight_alpha_material_light = 0x7f070099
studio.takasaki.clubm:styleable/ThemeEnforcement = 0x7f140090
studio.takasaki.clubm:string/bottomsheet_action_expand = 0x7f120027
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0d0156
studio.takasaki.clubm:dimen/highlight_alpha_material_colored = 0x7f070097
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f130058
studio.takasaki.clubm:dimen/disabled_alpha_material_light = 0x7f070092
studio.takasaki.clubm:attr/motionDurationExtraLong3 = 0x7f040330
studio.takasaki.clubm:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402cf
studio.takasaki.clubm:dimen/notification_top_pad_large_text = 0x7f07031e
studio.takasaki.clubm:dimen/disabled_alpha_material_dark = 0x7f070091
studio.takasaki.clubm:drawable/notification_template_icon_low_bg = 0x7f080142
studio.takasaki.clubm:attr/displayOptions = 0x7f040196
studio.takasaki.clubm:dimen/design_tab_text_size_2line = 0x7f07008f
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600bf
studio.takasaki.clubm:dimen/design_tab_scrollable_min_width = 0x7f07008d
studio.takasaki.clubm:dimen/design_tab_max_width = 0x7f07008c
studio.takasaki.clubm:string/fingerprint_not_recognized = 0x7f120080
studio.takasaki.clubm:dimen/design_snackbar_padding_horizontal = 0x7f070088
studio.takasaki.clubm:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1301b2
studio.takasaki.clubm:dimen/mtrl_calendar_year_width = 0x7f07029e
studio.takasaki.clubm:dimen/design_snackbar_min_width = 0x7f070087
studio.takasaki.clubm:id/search_voice_btn = 0x7f0901b6
studio.takasaki.clubm:attr/svg = 0x7f04041a
studio.takasaki.clubm:attr/sideSheetDialogTheme = 0x7f0403df
studio.takasaki.clubm:dimen/notification_large_icon_width = 0x7f070315
studio.takasaki.clubm:dimen/design_snackbar_elevation = 0x7f070084
studio.takasaki.clubm:dimen/design_navigation_item_horizontal_padding = 0x7f07007b
studio.takasaki.clubm:attr/fabSize = 0x7f0401ed
studio.takasaki.clubm:drawable/$avd_hide_password__2 = 0x7f080002
studio.takasaki.clubm:attr/tabIconTint = 0x7f040423
studio.takasaki.clubm:dimen/design_fab_size_normal = 0x7f070075
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f13002c
studio.takasaki.clubm:attr/fontWeight = 0x7f040224
studio.takasaki.clubm:dimen/design_fab_size_mini = 0x7f070074
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1300e4
studio.takasaki.clubm:attr/actionModeCloseButtonStyle = 0x7f040011
studio.takasaki.clubm:dimen/design_appbar_elevation = 0x7f070061
studio.takasaki.clubm:dimen/design_fab_elevation = 0x7f070072
studio.takasaki.clubm:dimen/design_bottom_sheet_modal_elevation = 0x7f07006f
studio.takasaki.clubm:dimen/design_bottom_sheet_elevation = 0x7f07006e
studio.takasaki.clubm:attr/mock_labelColor = 0x7f04032a
studio.takasaki.clubm:dimen/design_bottom_navigation_label_padding = 0x7f07006a
studio.takasaki.clubm:dimen/design_bottom_navigation_active_item_max_width = 0x7f070062
studio.takasaki.clubm:color/material_dynamic_tertiary70 = 0x7f06027a
studio.takasaki.clubm:dimen/def_drawer_elevation = 0x7f070060
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1303da
studio.takasaki.clubm:drawable/common_full_open_on_phone = 0x7f0800c0
studio.takasaki.clubm:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f07017c
studio.takasaki.clubm:dimen/compat_notification_large_icon_max_height = 0x7f07005e
studio.takasaki.clubm:id/collapseActionView = 0x7f090088
studio.takasaki.clubm:dimen/compat_control_corner_material = 0x7f07005d
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.Light = 0x7f130084
studio.takasaki.clubm:dimen/compat_button_padding_horizontal_material = 0x7f07005b
studio.takasaki.clubm:id/glide_custom_view_target_tag = 0x7f0900e6
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080011
studio.takasaki.clubm:string/catalyst_perf_monitor = 0x7f120043
studio.takasaki.clubm:layout/material_timepicker_dialog = 0x7f0c004d
studio.takasaki.clubm:color/material_dynamic_color_dark_on_error = 0x7f060238
studio.takasaki.clubm:dimen/tooltip_precise_anchor_extra_offset = 0x7f07032d
studio.takasaki.clubm:color/m3_navigation_item_icon_tint = 0x7f0600af
studio.takasaki.clubm:dimen/mtrl_calendar_year_vertical_padding = 0x7f07029d
studio.takasaki.clubm:dimen/compat_button_inset_vertical_material = 0x7f07005a
studio.takasaki.clubm:dimen/cardview_default_elevation = 0x7f070056
studio.takasaki.clubm:dimen/m3_alert_dialog_icon_size = 0x7f0700a6
studio.takasaki.clubm:id/search_edit_frame = 0x7f0901b1
studio.takasaki.clubm:attr/itemTextColor = 0x7f040279
studio.takasaki.clubm:dimen/design_bottom_navigation_shadow_height = 0x7f07006c
studio.takasaki.clubm:dimen/cardview_compat_inset_shadow = 0x7f070055
studio.takasaki.clubm:dimen/design_bottom_navigation_text_size = 0x7f07006d
studio.takasaki.clubm:dimen/browser_actions_context_menu_max_width = 0x7f070053
studio.takasaki.clubm:style/Theme.AutofillInlineSuggestion = 0x7f13023a
studio.takasaki.clubm:menu/crop_image_menu = 0x7f0e0000
studio.takasaki.clubm:dimen/appcompat_dialog_background_inset = 0x7f070051
studio.takasaki.clubm:style/Base.v21.Theme.SplashScreen = 0x7f130125
studio.takasaki.clubm:dimen/abc_text_size_title_material = 0x7f07004f
studio.takasaki.clubm:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300a0
studio.takasaki.clubm:dimen/abc_text_size_small_material = 0x7f07004c
studio.takasaki.clubm:string/m3_ref_typeface_plain_regular = 0x7f12009b
studio.takasaki.clubm:attr/listChoiceBackgroundIndicator = 0x7f0402ce
studio.takasaki.clubm:dimen/abc_text_size_menu_header_material = 0x7f07004a
studio.takasaki.clubm:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600b3
studio.takasaki.clubm:dimen/abc_text_size_display_3_material = 0x7f070045
studio.takasaki.clubm:layout/abc_activity_chooser_view = 0x7f0c0006
studio.takasaki.clubm:integer/mtrl_switch_thumb_motion_duration = 0x7f0a0038
studio.takasaki.clubm:attr/textStartPadding = 0x7f040473
studio.takasaki.clubm:dimen/abc_text_size_display_2_material = 0x7f070044
studio.takasaki.clubm:drawable/abc_list_selector_holo_dark = 0x7f080094
studio.takasaki.clubm:color/material_personalized_color_background = 0x7f060296
studio.takasaki.clubm:dimen/abc_text_size_body_1_material = 0x7f07003f
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0701ff
studio.takasaki.clubm:dimen/abc_switch_padding = 0x7f07003e
studio.takasaki.clubm:dimen/abc_star_small = 0x7f07003d
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070216
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f130455
studio.takasaki.clubm:attr/layout_constraintLeft_toLeftOf = 0x7f0402a6
studio.takasaki.clubm:dimen/abc_star_medium = 0x7f07003c
studio.takasaki.clubm:attr/roundBottomEnd = 0x7f0403ac
studio.takasaki.clubm:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_close = 0x7f080035
studio.takasaki.clubm:style/Widget.Material3.Badge.AdjustToBounds = 0x7f130374
studio.takasaki.clubm:dimen/material_emphasis_medium = 0x7f07023a
studio.takasaki.clubm:attr/overlapAnchor = 0x7f040366
studio.takasaki.clubm:dimen/abc_star_big = 0x7f07003b
studio.takasaki.clubm:color/common_google_signin_btn_text_light = 0x7f06003d
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070205
studio.takasaki.clubm:id/custom = 0x7f090097
studio.takasaki.clubm:dimen/abc_progress_bar_height_material = 0x7f070035
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f13045a
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_error_container = 0x7f0601bc
studio.takasaki.clubm:dimen/abc_panel_menu_list_width = 0x7f070034
studio.takasaki.clubm:dimen/abc_list_item_height_small_material = 0x7f070032
studio.takasaki.clubm:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f130323
studio.takasaki.clubm:attr/font = 0x7f040219
studio.takasaki.clubm:dimen/tooltip_vertical_padding = 0x7f07032f
studio.takasaki.clubm:dimen/abc_list_item_height_material = 0x7f070031
studio.takasaki.clubm:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f04041b
studio.takasaki.clubm:dimen/abc_list_item_height_large_material = 0x7f070030
studio.takasaki.clubm:string/character_counter_content_description = 0x7f12004c
studio.takasaki.clubm:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
studio.takasaki.clubm:attr/checkMarkTint = 0x7f0400a9
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601e0
studio.takasaki.clubm:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
studio.takasaki.clubm:dimen/abc_disabled_alpha_material_light = 0x7f070028
studio.takasaki.clubm:dimen/mtrl_btn_padding_left = 0x7f07026a
studio.takasaki.clubm:dimen/abc_dialog_title_divider_material = 0x7f070026
studio.takasaki.clubm:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080077
studio.takasaki.clubm:dimen/abc_dialog_padding_top_material = 0x7f070025
studio.takasaki.clubm:interpolator/fast_out_slow_in = 0x7f0b0006
studio.takasaki.clubm:drawable/design_snackbar_background = 0x7f0800d9
studio.takasaki.clubm:dimen/abc_dialog_padding_material = 0x7f070024
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents = 0x7f13013a
studio.takasaki.clubm:dimen/mtrl_slider_tick_radius = 0x7f0702f0
studio.takasaki.clubm:dimen/abc_dialog_min_width_minor = 0x7f070023
studio.takasaki.clubm:styleable/AppCompatTheme = 0x7f140012
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601c3
studio.takasaki.clubm:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070107
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.DayNight = 0x7f13025c
studio.takasaki.clubm:attr/simpleItemSelectedRippleColor = 0x7f0403e3
studio.takasaki.clubm:attr/listPopupWindowStyle = 0x7f0402d5
studio.takasaki.clubm:attr/materialButtonOutlinedStyle = 0x7f0402eb
studio.takasaki.clubm:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
studio.takasaki.clubm:string/side_sheet_behavior = 0x7f120111
studio.takasaki.clubm:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070250
studio.takasaki.clubm:style/Animation.Design.BottomSheetDialog = 0x7f130007
studio.takasaki.clubm:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700b5
studio.takasaki.clubm:dimen/design_bottom_navigation_icon_size = 0x7f070067
studio.takasaki.clubm:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0d005e
studio.takasaki.clubm:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
studio.takasaki.clubm:id/tag_window_insets_animation_callback = 0x7f0901e9
studio.takasaki.clubm:dimen/abc_dialog_fixed_height_major = 0x7f07001c
studio.takasaki.clubm:attr/firstBaselineToTopHeight = 0x7f0401f6
studio.takasaki.clubm:dimen/mtrl_slider_label_padding = 0x7f0702ea
studio.takasaki.clubm:dimen/abc_control_corner_material = 0x7f070018
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0d0083
studio.takasaki.clubm:dimen/abc_config_prefDialogWidth = 0x7f070017
studio.takasaki.clubm:id/skipCollapsed = 0x7f0901bf
studio.takasaki.clubm:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
studio.takasaki.clubm:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f070193
studio.takasaki.clubm:dimen/abc_button_padding_horizontal_material = 0x7f070014
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601b2
studio.takasaki.clubm:dimen/abc_alert_dialog_button_dimen = 0x7f070011
studio.takasaki.clubm:string/fingerprint_dialog_icon_description = 0x7f120079
studio.takasaki.clubm:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
studio.takasaki.clubm:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f130463
studio.takasaki.clubm:dimen/abc_action_button_min_width_material = 0x7f07000e
studio.takasaki.clubm:attr/trackColorInactive = 0x7f0404b0
studio.takasaki.clubm:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
studio.takasaki.clubm:attr/layout_dodgeInsetEdges = 0x7f0402b8
studio.takasaki.clubm:attr/cropAutoZoomEnabled = 0x7f040153
studio.takasaki.clubm:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
studio.takasaki.clubm:dimen/abc_action_bar_elevation_material = 0x7f070005
studio.takasaki.clubm:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
studio.takasaki.clubm:color/m3_dynamic_primary_text_disable_only = 0x7f0600a1
studio.takasaki.clubm:color/tooltip_background_light = 0x7f060323
studio.takasaki.clubm:string/dev_launcher_error_header = 0x7f120068
studio.takasaki.clubm:color/tooltip_background_dark = 0x7f060322
studio.takasaki.clubm:color/switch_thumb_normal_material_dark = 0x7f060320
studio.takasaki.clubm:color/switch_thumb_material_dark = 0x7f06031e
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0d0153
studio.takasaki.clubm:color/switch_thumb_disabled_material_light = 0x7f06031d
studio.takasaki.clubm:color/switch_thumb_disabled_material_dark = 0x7f06031c
studio.takasaki.clubm:id/chain = 0x7f09007e
studio.takasaki.clubm:color/splashscreen_background = 0x7f06031b
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07021a
studio.takasaki.clubm:attr/helperTextEnabled = 0x7f040231
studio.takasaki.clubm:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f070196
studio.takasaki.clubm:style/TextAppearance.AppCompat.Small = 0x7f1301ba
studio.takasaki.clubm:attr/cardMaxElevation = 0x7f0400a1
studio.takasaki.clubm:color/secondary_text_default_material_dark = 0x7f060317
studio.takasaki.clubm:color/ripple_material_dark = 0x7f060315
studio.takasaki.clubm:anim/m3_side_sheet_enter_from_right = 0x7f01002c
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070118
studio.takasaki.clubm:drawable/common_google_signin_btn_text_light_focused = 0x7f0800d0
studio.takasaki.clubm:attr/materialAlertDialogBodyTextStyle = 0x7f0402e5
studio.takasaki.clubm:dimen/design_textinput_caption_translate_y = 0x7f070090
studio.takasaki.clubm:color/secondary_text_disabled_material_dark = 0x7f060319
studio.takasaki.clubm:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f130356
studio.takasaki.clubm:color/primary_text_default_material_dark = 0x7f060311
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f13002f
studio.takasaki.clubm:attr/viewAspectRatio = 0x7f0404ca
studio.takasaki.clubm:color/primary_material_light = 0x7f060310
studio.takasaki.clubm:dimen/m3_comp_fab_primary_icon_size = 0x7f07011e
studio.takasaki.clubm:color/m3_ref_palette_primary10 = 0x7f060144
studio.takasaki.clubm:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f07017b
studio.takasaki.clubm:attr/startIconScaleType = 0x7f0403fa
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_showmenuatlaunchicon = 0x7f080058
studio.takasaki.clubm:color/primary_dark_material_dark = 0x7f06030d
studio.takasaki.clubm:color/mtrl_textinput_focused_box_stroke_color = 0x7f060308
studio.takasaki.clubm:color/mtrl_textinput_filled_box_default_background_color = 0x7f060307
studio.takasaki.clubm:id/design_navigation_view = 0x7f0900a5
studio.takasaki.clubm:color/mtrl_tabs_ripple_color = 0x7f060303
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1303e3
studio.takasaki.clubm:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
studio.takasaki.clubm:color/material_grey_100 = 0x7f06027f
studio.takasaki.clubm:dimen/mtrl_navigation_item_icon_size = 0x7f0702cc
studio.takasaki.clubm:color/mtrl_tabs_icon_color_selector_colored = 0x7f060301
studio.takasaki.clubm:style/TextAppearance.AppCompat = 0x7f1301a4
studio.takasaki.clubm:attr/saturation = 0x7f0403ba
studio.takasaki.clubm:color/notification_action_color_filter = 0x7f06030a
studio.takasaki.clubm:color/mtrl_tabs_icon_color_selector = 0x7f060300
studio.takasaki.clubm:attr/listMenuViewStyle = 0x7f0402d4
studio.takasaki.clubm:color/mtrl_switch_thumb_icon_tint = 0x7f0602fb
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0d015d
studio.takasaki.clubm:attr/queryPatterns = 0x7f04039a
studio.takasaki.clubm:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f070109
studio.takasaki.clubm:color/mtrl_scrim_color = 0x7f0602fa
studio.takasaki.clubm:styleable/Transform = 0x7f140093
studio.takasaki.clubm:style/Widget.Material3.CheckedTextView = 0x7f130394
studio.takasaki.clubm:string/abc_menu_shift_shortcut_label = 0x7f120010
studio.takasaki.clubm:color/mtrl_popupmenu_overlay_color = 0x7f0602f9
studio.takasaki.clubm:color/mtrl_navigation_item_text_color = 0x7f0602f4
studio.takasaki.clubm:color/mtrl_navigation_bar_ripple_color = 0x7f0602f1
studio.takasaki.clubm:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602ef
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f130228
studio.takasaki.clubm:drawable/common_google_signin_btn_text_dark_normal = 0x7f0800cc
studio.takasaki.clubm:drawable/abc_ratingbar_small_material = 0x7f08009a
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1303a7
studio.takasaki.clubm:attr/closeIcon = 0x7f0400d3
studio.takasaki.clubm:color/mtrl_filled_icon_tint = 0x7f0602eb
studio.takasaki.clubm:color/mtrl_fab_bg_color_selector = 0x7f0602e7
studio.takasaki.clubm:dimen/design_fab_translation_z_pressed = 0x7f070077
studio.takasaki.clubm:id/fill_vertical = 0x7f0900cf
studio.takasaki.clubm:color/mtrl_error = 0x7f0602e6
studio.takasaki.clubm:id/main_layout = 0x7f090117
studio.takasaki.clubm:attr/customNavigationLayout = 0x7f040184
studio.takasaki.clubm:color/mtrl_choice_chip_text_color = 0x7f0602e5
studio.takasaki.clubm:attr/shapeAppearanceCornerLarge = 0x7f0403cb
studio.takasaki.clubm:color/mtrl_chip_surface_color = 0x7f0602e1
studio.takasaki.clubm:integer/material_motion_duration_medium_1 = 0x7f0a002a
studio.takasaki.clubm:dimen/design_navigation_separator_vertical_padding = 0x7f070080
studio.takasaki.clubm:color/mtrl_card_view_ripple = 0x7f0602de
studio.takasaki.clubm:color/mtrl_card_view_foreground = 0x7f0602dd
studio.takasaki.clubm:color/mtrl_calendar_selected_range = 0x7f0602dc
studio.takasaki.clubm:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701dd
studio.takasaki.clubm:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
studio.takasaki.clubm:attr/roundPercent = 0x7f0403b0
studio.takasaki.clubm:color/mtrl_btn_text_color_disabled = 0x7f0602d8
studio.takasaki.clubm:color/mtrl_btn_text_btn_ripple_color = 0x7f0602d7
studio.takasaki.clubm:dimen/mtrl_navigation_rail_icon_size = 0x7f0702d4
studio.takasaki.clubm:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602d6
studio.takasaki.clubm:color/mtrl_btn_stroke_color_selector = 0x7f0602d5
studio.takasaki.clubm:anim/rns_slide_in_from_bottom = 0x7f010046
studio.takasaki.clubm:color/mtrl_btn_ripple_color = 0x7f0602d4
studio.takasaki.clubm:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701e2
studio.takasaki.clubm:color/mtrl_btn_bg_color_selector = 0x7f0602d3
studio.takasaki.clubm:layout/mtrl_calendar_month = 0x7f0c005a
studio.takasaki.clubm:attr/counterOverflowTextColor = 0x7f04014e
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary40 = 0x7f0600ef
studio.takasaki.clubm:color/material_timepicker_clock_text_color = 0x7f0602d0
studio.takasaki.clubm:color/material_timepicker_button_background = 0x7f0602ce
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f13013d
studio.takasaki.clubm:string/state_unselected_description = 0x7f12011a
studio.takasaki.clubm:color/material_slider_inactive_track_color = 0x7f0602cc
studio.takasaki.clubm:color/switch_thumb_normal_material_light = 0x7f060321
studio.takasaki.clubm:color/material_slider_inactive_tick_marks_color = 0x7f0602cb
studio.takasaki.clubm:id/autoCompleteToStart = 0x7f090061
studio.takasaki.clubm:animator/fragment_fade_enter = 0x7f020005
studio.takasaki.clubm:attr/maxNumber = 0x7f04031b
studio.takasaki.clubm:drawable/abc_switch_thumb_material = 0x7f0800a7
studio.takasaki.clubm:id/rn_redbox_loading_indicator = 0x7f09019b
studio.takasaki.clubm:color/material_slider_active_tick_marks_color = 0x7f0602c8
studio.takasaki.clubm:styleable/CheckedTextView = 0x7f14001d
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1301e9
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001a
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Display3 = 0x7f130020
studio.takasaki.clubm:macro/m3_comp_filled_icon_button_container_color = 0x7f0d0048
studio.takasaki.clubm:drawable/notification_oversize_large_icon_bg = 0x7f080140
studio.takasaki.clubm:color/material_personalized_hint_foreground = 0x7f0602c4
studio.takasaki.clubm:string/material_motion_easing_standard = 0x7f1200b2
studio.takasaki.clubm:attr/switchMinWidth = 0x7f04041c
studio.takasaki.clubm:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
studio.takasaki.clubm:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1303f3
studio.takasaki.clubm:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602c3
studio.takasaki.clubm:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602c2
studio.takasaki.clubm:dimen/mtrl_calendar_header_text_padding = 0x7f070288
studio.takasaki.clubm:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602bf
studio.takasaki.clubm:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0d00df
studio.takasaki.clubm:color/material_personalized_color_surface_inverse = 0x7f0602bb
studio.takasaki.clubm:styleable/MockView = 0x7f140063
studio.takasaki.clubm:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1300ee
studio.takasaki.clubm:color/material_personalized_color_surface_container_lowest = 0x7f0602b9
studio.takasaki.clubm:dimen/m3_chip_icon_size = 0x7f0700fd
studio.takasaki.clubm:color/material_personalized_color_surface_container_low = 0x7f0602b8
studio.takasaki.clubm:color/material_personalized_color_surface_container_high = 0x7f0602b6
studio.takasaki.clubm:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402e6
studio.takasaki.clubm:color/material_slider_thumb_color = 0x7f0602cd
studio.takasaki.clubm:layout/notification_action_tombstone = 0x7f0c0070
studio.takasaki.clubm:color/material_personalized_color_surface_bright = 0x7f0602b4
studio.takasaki.clubm:attr/viewInflaterClass = 0x7f0404cb
studio.takasaki.clubm:color/material_personalized_color_surface = 0x7f0602b3
studio.takasaki.clubm:integer/m3_sys_motion_duration_long1 = 0x7f0a0015
studio.takasaki.clubm:color/material_personalized_color_secondary_text_inverse = 0x7f0602b2
studio.takasaki.clubm:layout/mtrl_alert_dialog = 0x7f0c004f
studio.takasaki.clubm:color/material_personalized_color_secondary_container = 0x7f0602b0
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0d0014
studio.takasaki.clubm:color/material_personalized_color_primary_text_inverse = 0x7f0602ae
studio.takasaki.clubm:id/easeIn = 0x7f0900b4
studio.takasaki.clubm:color/material_personalized_color_primary_text = 0x7f0602ad
studio.takasaki.clubm:string/abc_searchview_description_clear = 0x7f120015
studio.takasaki.clubm:color/material_personalized_color_outline_variant = 0x7f0602a9
studio.takasaki.clubm:color/m3_sys_color_on_secondary_fixed_variant = 0x7f06020d
studio.takasaki.clubm:attr/statusBarScrim = 0x7f040408
studio.takasaki.clubm:color/m3_timepicker_display_ripple_color = 0x7f060229
studio.takasaki.clubm:dimen/m3_side_sheet_margin_detached = 0x7f0701e9
studio.takasaki.clubm:color/material_personalized_color_on_tertiary_container = 0x7f0602a7
studio.takasaki.clubm:attr/badgeTextColor = 0x7f04005f
studio.takasaki.clubm:drawable/abc_text_cursor_material = 0x7f0800ab
studio.takasaki.clubm:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f070296
studio.takasaki.clubm:macro/m3_comp_outlined_card_container_shape = 0x7f0d00a9
studio.takasaki.clubm:layout/mtrl_alert_select_dialog_multichoice = 0x7f0c0053
studio.takasaki.clubm:dimen/abc_button_inset_horizontal_material = 0x7f070012
studio.takasaki.clubm:color/material_personalized_color_on_surface_inverse = 0x7f0602a4
studio.takasaki.clubm:dimen/mtrl_btn_text_btn_padding_left = 0x7f070271
studio.takasaki.clubm:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08009d
studio.takasaki.clubm:color/material_personalized_color_on_surface = 0x7f0602a3
studio.takasaki.clubm:color/material_personalized_color_on_secondary_container = 0x7f0602a2
studio.takasaki.clubm:color/material_personalized_color_on_primary = 0x7f06029f
studio.takasaki.clubm:color/m3_slider_halo_color_legacy = 0x7f06016f
studio.takasaki.clubm:color/material_personalized_color_on_error_container = 0x7f06029e
studio.takasaki.clubm:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f13024e
studio.takasaki.clubm:dimen/m3_extended_fab_top_padding = 0x7f0701b8
studio.takasaki.clubm:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0d014d
studio.takasaki.clubm:color/m3_card_foreground_color = 0x7f060087
studio.takasaki.clubm:color/material_personalized_color_on_background = 0x7f06029c
studio.takasaki.clubm:color/m3_sys_color_light_tertiary = 0x7f060208
studio.takasaki.clubm:color/material_personalized_color_control_normal = 0x7f060299
studio.takasaki.clubm:color/material_on_surface_stroke = 0x7f060293
studio.takasaki.clubm:string/catalyst_dev_menu_sub_header = 0x7f120039
studio.takasaki.clubm:color/material_on_primary_disabled = 0x7f06028d
studio.takasaki.clubm:string/call_notification_hang_up_action = 0x7f12002e
studio.takasaki.clubm:string/abc_search_hint = 0x7f120014
studio.takasaki.clubm:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f070170
studio.takasaki.clubm:color/material_harmonized_color_on_error_container = 0x7f060289
studio.takasaki.clubm:color/material_harmonized_color_error_container = 0x7f060287
studio.takasaki.clubm:drawable/$avd_hide_password__1 = 0x7f080001
studio.takasaki.clubm:color/material_grey_850 = 0x7f060284
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline5 = 0x7f130217
studio.takasaki.clubm:color/material_dynamic_neutral95 = 0x7f060249
studio.takasaki.clubm:color/material_grey_800 = 0x7f060283
studio.takasaki.clubm:dimen/mtrl_calendar_days_of_week_height = 0x7f070280
studio.takasaki.clubm:color/material_dynamic_tertiary99 = 0x7f06027e
studio.takasaki.clubm:drawable/notification_icon_background = 0x7f08013f
studio.takasaki.clubm:attr/transitionFlags = 0x7f0404bd
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant70 = 0x7f06013e
studio.takasaki.clubm:color/material_dynamic_tertiary90 = 0x7f06027c
studio.takasaki.clubm:color/material_dynamic_tertiary80 = 0x7f06027b
studio.takasaki.clubm:id/light = 0x7f09010f
studio.takasaki.clubm:dimen/mtrl_btn_focused_z = 0x7f070262
studio.takasaki.clubm:color/material_dynamic_tertiary60 = 0x7f060279
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07021b
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f13032b
studio.takasaki.clubm:style/MaterialAlertDialog.Material3 = 0x7f130130
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070141
studio.takasaki.clubm:color/m3_dynamic_dark_hint_foreground = 0x7f06009b
studio.takasaki.clubm:color/material_dynamic_tertiary40 = 0x7f060277
studio.takasaki.clubm:attr/colorOnError = 0x7f0400f3
studio.takasaki.clubm:attr/backgroundOverlayColorAlpha = 0x7f040052
studio.takasaki.clubm:color/mtrl_on_surface_ripple_color = 0x7f0602f6
studio.takasaki.clubm:color/material_dynamic_secondary99 = 0x7f060271
studio.takasaki.clubm:color/material_dynamic_secondary70 = 0x7f06026d
studio.takasaki.clubm:color/material_dynamic_secondary40 = 0x7f06026a
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Display1 = 0x7f13001e
studio.takasaki.clubm:attr/alertDialogButtonGroupStyle = 0x7f04002a
studio.takasaki.clubm:color/material_dynamic_tertiary95 = 0x7f06027d
studio.takasaki.clubm:color/material_dynamic_secondary30 = 0x7f060269
studio.takasaki.clubm:style/ShapeAppearance.Material3.SmallComponent = 0x7f130187
studio.takasaki.clubm:color/material_dynamic_secondary20 = 0x7f060268
studio.takasaki.clubm:color/mtrl_filled_stroke_color = 0x7f0602ec
studio.takasaki.clubm:color/material_dynamic_secondary100 = 0x7f060267
studio.takasaki.clubm:drawable/ic_arrow_back_24 = 0x7f0800e0
studio.takasaki.clubm:attr/layout_goneMarginBottom = 0x7f0402bb
studio.takasaki.clubm:dimen/abc_text_size_caption_material = 0x7f070042
studio.takasaki.clubm:color/material_dynamic_secondary10 = 0x7f060266
studio.takasaki.clubm:id/search_mag_icon = 0x7f0901b3
studio.takasaki.clubm:color/material_dynamic_secondary0 = 0x7f060265
studio.takasaki.clubm:layout/select_dialog_singlechoice_material = 0x7f0c0084
studio.takasaki.clubm:attr/onTouchUp = 0x7f040365
studio.takasaki.clubm:color/material_dynamic_primary95 = 0x7f060263
studio.takasaki.clubm:color/material_dynamic_primary90 = 0x7f060262
studio.takasaki.clubm:color/material_dynamic_primary0 = 0x7f060258
studio.takasaki.clubm:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130016
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0701fa
studio.takasaki.clubm:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0d00e8
studio.takasaki.clubm:attr/passwordToggleDrawable = 0x7f040376
studio.takasaki.clubm:drawable/abc_text_select_handle_middle_mtrl = 0x7f0800ad
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600d4
studio.takasaki.clubm:attr/navigationIconTint = 0x7f040356
studio.takasaki.clubm:color/material_dynamic_primary60 = 0x7f06025f
studio.takasaki.clubm:color/material_dynamic_neutral_variant95 = 0x7f060256
studio.takasaki.clubm:id/open_search_view_content_container = 0x7f090168
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_container_width = 0x7f070142
studio.takasaki.clubm:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
studio.takasaki.clubm:color/material_dynamic_neutral_variant90 = 0x7f060255
studio.takasaki.clubm:color/material_dynamic_neutral_variant100 = 0x7f06024d
studio.takasaki.clubm:id/fitStart = 0x7f0900d9
studio.takasaki.clubm:color/material_dynamic_neutral_variant10 = 0x7f06024c
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f13028f
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070161
studio.takasaki.clubm:dimen/mtrl_card_checked_icon_margin = 0x7f07029f
studio.takasaki.clubm:dimen/abc_action_bar_default_height_material = 0x7f070002
studio.takasaki.clubm:color/primary_text_disabled_material_light = 0x7f060314
studio.takasaki.clubm:color/mtrl_btn_text_color_selector = 0x7f0602d9
studio.takasaki.clubm:attr/cornerFamilyBottomLeft = 0x7f040140
studio.takasaki.clubm:color/material_dynamic_primary100 = 0x7f06025a
studio.takasaki.clubm:color/material_dynamic_neutral99 = 0x7f06024a
studio.takasaki.clubm:dimen/abc_text_size_button_material = 0x7f070041
studio.takasaki.clubm:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f070166
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070208
studio.takasaki.clubm:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f070159
studio.takasaki.clubm:attr/buttonBarNegativeButtonStyle = 0x7f04008d
studio.takasaki.clubm:color/material_dynamic_neutral70 = 0x7f060246
studio.takasaki.clubm:string/material_motion_easing_linear = 0x7f1200b1
studio.takasaki.clubm:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702c8
studio.takasaki.clubm:string/common_google_play_services_enable_button = 0x7f120051
studio.takasaki.clubm:color/catalyst_redbox_background = 0x7f060035
studio.takasaki.clubm:color/material_dynamic_neutral50 = 0x7f060244
studio.takasaki.clubm:color/material_dynamic_neutral100 = 0x7f060240
studio.takasaki.clubm:layout/mtrl_search_bar = 0x7f0c006d
studio.takasaki.clubm:dimen/m3_btn_icon_btn_padding_left = 0x7f0700d5
studio.takasaki.clubm:styleable/AnimatedStateListDrawableCompat = 0x7f140007
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Title = 0x7f1301dc
studio.takasaki.clubm:color/material_dynamic_neutral10 = 0x7f06023f
studio.takasaki.clubm:layout/design_layout_tab_text = 0x7f0c0029
studio.takasaki.clubm:attr/layout_keyline = 0x7f0402c2
studio.takasaki.clubm:color/material_dynamic_neutral0 = 0x7f06023e
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Line2 = 0x7f1301d7
studio.takasaki.clubm:dimen/m3_btn_disabled_translation_z = 0x7f0700d2
studio.takasaki.clubm:color/material_dynamic_color_light_error = 0x7f06023a
studio.takasaki.clubm:attr/cropSnapRadius = 0x7f040173
studio.takasaki.clubm:color/material_dynamic_color_dark_error = 0x7f060236
studio.takasaki.clubm:style/Widget.MaterialComponents.ShapeableImageView = 0x7f130471
studio.takasaki.clubm:id/shortcut = 0x7f0901ba
studio.takasaki.clubm:drawable/abc_action_bar_item_background_material = 0x7f080067
studio.takasaki.clubm:color/material_divider_color = 0x7f060235
studio.takasaki.clubm:drawable/abc_edit_text_material = 0x7f08007a
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0d0089
studio.takasaki.clubm:dimen/design_bottom_sheet_peek_height_min = 0x7f070070
studio.takasaki.clubm:string/material_timepicker_text_input_mode_description = 0x7f1200bc
studio.takasaki.clubm:macro/m3_comp_time_picker_clock_dial_color = 0x7f0d014c
studio.takasaki.clubm:color/material_deep_teal_500 = 0x7f060234
studio.takasaki.clubm:layout/design_layout_tab_icon = 0x7f0c0028
studio.takasaki.clubm:attr/barrierMargin = 0x7f04006b
studio.takasaki.clubm:color/material_cursor_color = 0x7f060232
studio.takasaki.clubm:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07015c
studio.takasaki.clubm:color/material_blue_grey_800 = 0x7f06022f
studio.takasaki.clubm:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07024d
studio.takasaki.clubm:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f130444
studio.takasaki.clubm:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702d3
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601df
studio.takasaki.clubm:color/m3_timepicker_time_input_stroke_color = 0x7f06022d
studio.takasaki.clubm:styleable/CompoundButton = 0x7f140026
studio.takasaki.clubm:attr/gapBetweenBars = 0x7f040229
studio.takasaki.clubm:dimen/design_snackbar_text_size = 0x7f07008b
studio.takasaki.clubm:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
studio.takasaki.clubm:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f06022b
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0d001d
studio.takasaki.clubm:color/m3_timepicker_display_text_color = 0x7f06022a
studio.takasaki.clubm:dimen/tooltip_corner_radius = 0x7f07032a
studio.takasaki.clubm:attr/colorOnPrimaryContainer = 0x7f0400f6
studio.takasaki.clubm:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070138
studio.takasaki.clubm:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f07030f
studio.takasaki.clubm:color/m3_timepicker_display_background_color = 0x7f060228
studio.takasaki.clubm:color/m3_timepicker_clock_text_color = 0x7f060227
studio.takasaki.clubm:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1302bf
studio.takasaki.clubm:color/m3_timepicker_button_ripple_color = 0x7f060225
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0d0094
studio.takasaki.clubm:attr/textAppearanceHeadline6 = 0x7f040451
studio.takasaki.clubm:color/material_dynamic_primary30 = 0x7f06025c
studio.takasaki.clubm:color/m3_timepicker_button_background_color = 0x7f060224
studio.takasaki.clubm:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701a7
studio.takasaki.clubm:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f07018a
studio.takasaki.clubm:style/Base.Widget.AppCompat.PopupMenu = 0x7f1300f0
studio.takasaki.clubm:color/m3_textfield_stroke_color = 0x7f060223
studio.takasaki.clubm:drawable/mtrl_ic_error = 0x7f080125
studio.takasaki.clubm:color/m3_textfield_label_color = 0x7f060222
studio.takasaki.clubm:color/m3_textfield_input_text_color = 0x7f060221
studio.takasaki.clubm:color/m3_text_button_foreground_color_selector = 0x7f06021d
studio.takasaki.clubm:style/Platform.AppCompat = 0x7f130145
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080020
studio.takasaki.clubm:color/m3_text_button_background_color_selector = 0x7f06021c
studio.takasaki.clubm:color/m3_tabs_text_color = 0x7f06021a
studio.takasaki.clubm:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f130324
studio.takasaki.clubm:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080083
studio.takasaki.clubm:attr/tabSecondaryStyle = 0x7f040436
studio.takasaki.clubm:attr/itemRippleColor = 0x7f04026a
studio.takasaki.clubm:color/m3_tabs_ripple_color = 0x7f060218
studio.takasaki.clubm:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f130442
studio.takasaki.clubm:attr/fontProviderCerts = 0x7f04021c
studio.takasaki.clubm:dimen/notification_main_column_padding_top = 0x7f070316
studio.takasaki.clubm:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070258
studio.takasaki.clubm:color/m3_tabs_icon_color_secondary = 0x7f060217
studio.takasaki.clubm:color/m3_tabs_icon_color = 0x7f060216
studio.takasaki.clubm:color/m3_sys_color_tertiary_fixed_dim = 0x7f060215
studio.takasaki.clubm:color/m3_sys_color_primary_fixed_dim = 0x7f060211
studio.takasaki.clubm:color/m3_button_foreground_color_selector = 0x7f060081
studio.takasaki.clubm:color/m3_sys_color_primary_fixed = 0x7f060210
studio.takasaki.clubm:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700c3
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f13007f
studio.takasaki.clubm:string/abc_menu_delete_shortcut_label = 0x7f12000c
studio.takasaki.clubm:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f06020f
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070218
studio.takasaki.clubm:color/m3_sys_color_on_secondary_fixed = 0x7f06020c
studio.takasaki.clubm:color/m3_sys_color_on_primary_fixed = 0x7f06020a
studio.takasaki.clubm:color/m3_sys_color_light_surface_dim = 0x7f060206
studio.takasaki.clubm:color/m3_sys_color_light_surface_container_lowest = 0x7f060205
studio.takasaki.clubm:attr/colorOnErrorContainer = 0x7f0400f4
studio.takasaki.clubm:color/m3_sys_color_light_surface_bright = 0x7f060200
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0d006f
studio.takasaki.clubm:color/m3_sys_color_light_surface = 0x7f0601ff
studio.takasaki.clubm:color/m3_sys_color_light_primary = 0x7f0601fb
studio.takasaki.clubm:attr/motionDurationExtraLong2 = 0x7f04032f
studio.takasaki.clubm:color/m3_sys_color_light_outline_variant = 0x7f0601fa
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f130090
studio.takasaki.clubm:attr/minHideDelay = 0x7f040323
studio.takasaki.clubm:color/m3_sys_color_light_on_tertiary_container = 0x7f0601f8
studio.takasaki.clubm:color/m3_sys_color_light_on_tertiary = 0x7f0601f7
studio.takasaki.clubm:color/m3_sys_color_light_on_surface = 0x7f0601f5
studio.takasaki.clubm:attr/textAppearanceDisplayMedium = 0x7f04044a
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070113
studio.takasaki.clubm:color/m3_sys_color_light_on_secondary_container = 0x7f0601f4
studio.takasaki.clubm:dimen/mtrl_btn_stroke_size = 0x7f07026f
studio.takasaki.clubm:color/m3_sys_color_light_on_primary_container = 0x7f0601f2
studio.takasaki.clubm:color/m3_sys_color_light_on_background = 0x7f0601ee
studio.takasaki.clubm:color/m3_sys_color_light_inverse_primary = 0x7f0601ec
studio.takasaki.clubm:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f130134
studio.takasaki.clubm:color/m3_sys_color_light_error_container = 0x7f0601ea
studio.takasaki.clubm:attr/state_collapsed = 0x7f0403fe
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsfilledactiveiconlight = 0x7f080040
studio.takasaki.clubm:style/Theme.Design.Light = 0x7f130240
studio.takasaki.clubm:dimen/notification_top_pad = 0x7f07031d
studio.takasaki.clubm:color/m3_sys_color_light_background = 0x7f0601e8
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1301a1
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_chevronrighticon = 0x7f08003c
studio.takasaki.clubm:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601e7
studio.takasaki.clubm:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601e2
studio.takasaki.clubm:array/crypto_fingerprint_fallback_prefixes = 0x7f030001
studio.takasaki.clubm:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f07028a
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601e1
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601dd
studio.takasaki.clubm:color/m3_sys_color_dark_surface_variant = 0x7f060195
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601dc
studio.takasaki.clubm:style/Platform.V21.AppCompat.Light = 0x7f13014f
studio.takasaki.clubm:drawable/abc_textfield_search_material = 0x7f0800b3
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601db
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f130098
studio.takasaki.clubm:dimen/mtrl_btn_padding_bottom = 0x7f070269
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601d9
studio.takasaki.clubm:style/Theme.AppCompat.Light.DarkActionBar = 0x7f130232
studio.takasaki.clubm:attr/colorOutlineVariant = 0x7f040106
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601d8
studio.takasaki.clubm:dimen/mtrl_card_dragged_z = 0x7f0702a2
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1303bf
studio.takasaki.clubm:layout/material_chip_input_combo = 0x7f0c0041
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601d6
studio.takasaki.clubm:style/TextAppearance.Material3.DisplayLarge = 0x7f1301fd
studio.takasaki.clubm:string/mtrl_picker_range_header_only_start_selected = 0x7f1200e3
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601d5
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601d3
studio.takasaki.clubm:color/material_dynamic_neutral_variant30 = 0x7f06024f
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface = 0x7f0601d1
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1303dc
studio.takasaki.clubm:attr/dialogPreferredPadding = 0x7f040194
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601d0
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_secondary = 0x7f0601cf
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601ce
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_outline = 0x7f0601cb
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601ca
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601c8
studio.takasaki.clubm:drawable/abc_star_half_black_48dp = 0x7f0800a6
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601c7
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601c6
studio.takasaki.clubm:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070257
studio.takasaki.clubm:color/material_personalized_color_control_activated = 0x7f060297
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_shakedeviceiconlight = 0x7f080057
studio.takasaki.clubm:dimen/design_navigation_icon_padding = 0x7f070079
studio.takasaki.clubm:style/Widget.Design.Snackbar = 0x7f130368
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601bf
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f13018d
studio.takasaki.clubm:id/open_search_view_dummy_toolbar = 0x7f09016a
studio.takasaki.clubm:dimen/fingerprint_icon_size = 0x7f070096
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f130461
studio.takasaki.clubm:dimen/m3_appbar_scrim_height_trigger = 0x7f0700aa
studio.takasaki.clubm:color/material_on_background_emphasis_high_type = 0x7f06028b
studio.takasaki.clubm:color/material_dynamic_neutral_variant80 = 0x7f060254
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_error = 0x7f0601bb
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_background = 0x7f0601ba
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601b9
studio.takasaki.clubm:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1300db
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601b8
studio.takasaki.clubm:attr/circleCrop = 0x7f0400cb
studio.takasaki.clubm:attr/colorControlNormal = 0x7f0400ed
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601b7
studio.takasaki.clubm:dimen/m3_btn_padding_left = 0x7f0700de
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601b5
studio.takasaki.clubm:attr/layout_constraintEnd_toStartOf = 0x7f04029a
studio.takasaki.clubm:color/m3_textfield_filled_background_color = 0x7f06021f
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0601ae
studio.takasaki.clubm:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700ab
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_primary = 0x7f0601ab
studio.takasaki.clubm:array/crypto_fingerprint_fallback_vendors = 0x7f030002
studio.takasaki.clubm:dimen/notification_media_narrow_margin = 0x7f070317
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_outline = 0x7f0601a9
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f0601a6
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f0601a4
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_primary = 0x7f0601a1
studio.takasaki.clubm:layout/abc_alert_dialog_title_material = 0x7f0c000a
studio.takasaki.clubm:id/masked = 0x7f090119
studio.takasaki.clubm:attr/motionInterpolator = 0x7f04034a
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_error = 0x7f06019f
studio.takasaki.clubm:macro/m3_comp_switch_selected_hover_track_color = 0x7f0d0128
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f06019d
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600ca
studio.takasaki.clubm:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f07016f
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f06019c
studio.takasaki.clubm:id/add = 0x7f090051
studio.takasaki.clubm:dimen/design_navigation_max_width = 0x7f07007e
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f06019b
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_settingsfilledactiveiconlight = 0x7f080053
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1302a1
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_error_container = 0x7f06019a
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_error = 0x7f060199
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_background = 0x7f060198
studio.takasaki.clubm:attr/materialSearchViewToolbarStyle = 0x7f04030e
studio.takasaki.clubm:color/m3_sys_color_dark_tertiary = 0x7f060196
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f13030a
studio.takasaki.clubm:color/m3_sys_color_dark_surface_dim = 0x7f060194
studio.takasaki.clubm:drawable/abc_list_longpressed_holo = 0x7f08008d
studio.takasaki.clubm:attr/mock_showLabel = 0x7f04032c
studio.takasaki.clubm:color/m3_sys_color_dark_surface_container_lowest = 0x7f060193
studio.takasaki.clubm:dimen/abc_dialog_fixed_width_major = 0x7f07001e
studio.takasaki.clubm:color/m3_sys_color_dark_surface_container_highest = 0x7f060191
studio.takasaki.clubm:color/m3_sys_color_dark_surface_container_high = 0x7f060190
studio.takasaki.clubm:style/Theme.AppCompat.DialogWhenLarge = 0x7f13022f
studio.takasaki.clubm:color/m3_sys_color_dark_surface = 0x7f06018d
studio.takasaki.clubm:color/background_floating_material_light = 0x7f06001e
studio.takasaki.clubm:color/material_personalized__highlighted_text_inverse = 0x7f060295
studio.takasaki.clubm:color/m3_sys_color_dark_secondary_container = 0x7f06018c
studio.takasaki.clubm:color/m3_sys_color_dark_primary_container = 0x7f06018a
studio.takasaki.clubm:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f0800b9
studio.takasaki.clubm:string/call_notification_ongoing_text = 0x7f120030
studio.takasaki.clubm:anim/mtrl_bottom_sheet_slide_in = 0x7f01002f
studio.takasaki.clubm:color/m3_sys_color_dark_on_tertiary_container = 0x7f060186
studio.takasaki.clubm:attr/round = 0x7f0403aa
studio.takasaki.clubm:color/m3_sys_color_dark_on_secondary_container = 0x7f060182
studio.takasaki.clubm:color/m3_sys_color_dark_on_secondary = 0x7f060181
studio.takasaki.clubm:color/material_on_background_disabled = 0x7f06028a
studio.takasaki.clubm:color/m3_sys_color_dark_on_primary = 0x7f06017f
studio.takasaki.clubm:color/m3_sys_color_dark_on_error_container = 0x7f06017e
studio.takasaki.clubm:color/m3_sys_color_dark_error_container = 0x7f060178
studio.takasaki.clubm:color/m3_sys_color_dark_background = 0x7f060176
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1303d0
studio.takasaki.clubm:attr/motionEasingStandard = 0x7f040346
studio.takasaki.clubm:dimen/m3_side_sheet_standard_elevation = 0x7f0701eb
studio.takasaki.clubm:color/material_on_primary_emphasis_high_type = 0x7f06028e
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f130469
studio.takasaki.clubm:color/m3_switch_track_tint = 0x7f060175
studio.takasaki.clubm:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f13033a
studio.takasaki.clubm:color/m3_switch_thumb_tint = 0x7f060174
studio.takasaki.clubm:color/m3_slider_thumb_color_legacy = 0x7f060173
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface = 0x7f0601af
studio.takasaki.clubm:id/FUNCTION = 0x7f090006
studio.takasaki.clubm:color/m3_slider_thumb_color = 0x7f060172
studio.takasaki.clubm:color/m3_slider_inactive_track_color = 0x7f060170
studio.takasaki.clubm:color/m3_sys_color_dark_on_primary_container = 0x7f060180
studio.takasaki.clubm:color/m3_simple_item_ripple_color = 0x7f06016c
studio.takasaki.clubm:color/m3_selection_control_ripple_color_selector = 0x7f06016b
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f130174
studio.takasaki.clubm:color/m3_ref_palette_white = 0x7f06016a
studio.takasaki.clubm:attr/sliderStyle = 0x7f0403e9
studio.takasaki.clubm:color/m3_ref_palette_tertiary99 = 0x7f060169
studio.takasaki.clubm:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f07019f
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f130099
studio.takasaki.clubm:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f130095
studio.takasaki.clubm:color/m3_ref_palette_tertiary80 = 0x7f060166
studio.takasaki.clubm:color/m3_ref_palette_tertiary70 = 0x7f060165
studio.takasaki.clubm:style/Base.Theme.MaterialComponents = 0x7f130068
studio.takasaki.clubm:color/m3_ref_palette_tertiary60 = 0x7f060164
studio.takasaki.clubm:anim/rns_fade_in = 0x7f010037
studio.takasaki.clubm:color/m3_ref_palette_tertiary40 = 0x7f060162
studio.takasaki.clubm:color/m3_ref_palette_tertiary20 = 0x7f060160
studio.takasaki.clubm:attr/drawerLayoutCornerSize = 0x7f0401ad
studio.takasaki.clubm:color/m3_ref_palette_tertiary100 = 0x7f06015f
studio.takasaki.clubm:dimen/design_bottom_navigation_active_item_min_width = 0x7f070063
studio.takasaki.clubm:color/m3_ref_palette_tertiary0 = 0x7f06015d
studio.takasaki.clubm:color/m3_ref_palette_secondary99 = 0x7f06015c
studio.takasaki.clubm:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
studio.takasaki.clubm:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f08009e
studio.takasaki.clubm:color/m3_ref_palette_secondary70 = 0x7f060158
studio.takasaki.clubm:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f07028c
studio.takasaki.clubm:id/accessibility_custom_action_24 = 0x7f090027
studio.takasaki.clubm:color/m3_ref_palette_secondary50 = 0x7f060156
studio.takasaki.clubm:color/m3_ref_palette_secondary40 = 0x7f060155
studio.takasaki.clubm:color/m3_ref_palette_secondary20 = 0x7f060153
studio.takasaki.clubm:color/m3_ref_palette_secondary0 = 0x7f060150
studio.takasaki.clubm:color/m3_ref_palette_primary99 = 0x7f06014f
studio.takasaki.clubm:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1300f6
studio.takasaki.clubm:dimen/mtrl_low_ripple_default_alpha = 0x7f0702c2
studio.takasaki.clubm:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070085
studio.takasaki.clubm:id/end_padder = 0x7f0900be
studio.takasaki.clubm:color/m3_ref_palette_primary95 = 0x7f06014e
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f13044c
studio.takasaki.clubm:drawable/ic_m3_chip_close = 0x7f0800ef
studio.takasaki.clubm:color/m3_ref_palette_primary80 = 0x7f06014c
studio.takasaki.clubm:color/m3_ref_palette_primary70 = 0x7f06014b
studio.takasaki.clubm:style/Base.Widget.Material3.TabLayout = 0x7f130114
studio.takasaki.clubm:id/scrollable = 0x7f0901ac
studio.takasaki.clubm:color/m3_ref_palette_primary50 = 0x7f060149
studio.takasaki.clubm:attr/keyPositionType = 0x7f04027b
studio.takasaki.clubm:color/m3_ref_palette_primary40 = 0x7f060148
studio.takasaki.clubm:integer/m3_sys_motion_duration_extra_long3 = 0x7f0a0013
studio.takasaki.clubm:color/m3_ref_palette_primary30 = 0x7f060147
studio.takasaki.clubm:color/m3_ref_palette_primary100 = 0x7f060145
studio.takasaki.clubm:color/m3_ref_palette_primary0 = 0x7f060143
studio.takasaki.clubm:dimen/m3_searchview_divider_size = 0x7f0701e6
studio.takasaki.clubm:style/Theme.Material3.Light.BottomSheetDialog = 0x7f130261
studio.takasaki.clubm:string/image_description = 0x7f120093
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant99 = 0x7f060142
studio.takasaki.clubm:attr/materialCalendarMonth = 0x7f0402f8
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant95 = 0x7f060141
studio.takasaki.clubm:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f070293
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant90 = 0x7f060140
studio.takasaki.clubm:color/dim_foreground_material_light = 0x7f060074
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant80 = 0x7f06013f
studio.takasaki.clubm:color/m3_timepicker_secondary_text_button_text_color = 0x7f06022c
studio.takasaki.clubm:attr/materialCalendarHeaderToggleButton = 0x7f0402f7
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant40 = 0x7f06013b
studio.takasaki.clubm:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0d00e7
studio.takasaki.clubm:dimen/m3_searchbar_elevation = 0x7f0701de
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant20 = 0x7f060139
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant100 = 0x7f060138
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant10 = 0x7f060137
studio.takasaki.clubm:color/m3_ref_palette_neutral98 = 0x7f060134
studio.takasaki.clubm:drawable/design_ic_visibility = 0x7f0800d6
studio.takasaki.clubm:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702d9
studio.takasaki.clubm:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0d00af
studio.takasaki.clubm:color/m3_ref_palette_neutral95 = 0x7f060132
studio.takasaki.clubm:string/mtrl_checkbox_button_path_name = 0x7f1200c7
studio.takasaki.clubm:attr/startIconTintMode = 0x7f0403fc
studio.takasaki.clubm:color/m3_ref_palette_neutral90 = 0x7f06012f
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1303bd
studio.takasaki.clubm:attr/boxCollapsedPaddingTop = 0x7f040082
studio.takasaki.clubm:color/m3_ref_palette_neutral87 = 0x7f06012e
studio.takasaki.clubm:style/Theme.Material3.Light.SideSheetDialog = 0x7f130267
studio.takasaki.clubm:drawable/btn_checkbox_checked_mtrl = 0x7f0800b8
studio.takasaki.clubm:color/m3_ref_palette_neutral80 = 0x7f06012d
studio.takasaki.clubm:attr/buttonSize = 0x7f040098
studio.takasaki.clubm:attr/spanCount = 0x7f0403ed
studio.takasaki.clubm:color/m3_ref_palette_neutral70 = 0x7f06012c
studio.takasaki.clubm:attr/panelMenuListWidth = 0x7f040374
studio.takasaki.clubm:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701c6
studio.takasaki.clubm:style/Theme.Hidden = 0x7f130249
studio.takasaki.clubm:color/m3_ref_palette_neutral50 = 0x7f060129
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0d007d
studio.takasaki.clubm:bool/mtrl_btn_textappearance_all_caps = 0x7f050002
studio.takasaki.clubm:dimen/mtrl_calendar_month_vertical_padding = 0x7f07028e
studio.takasaki.clubm:color/m3_ref_palette_neutral24 = 0x7f060125
studio.takasaki.clubm:macro/m3_comp_switch_selected_track_color = 0x7f0d012e
studio.takasaki.clubm:id/textinput_counter = 0x7f0901f4
studio.takasaki.clubm:dimen/material_clock_period_toggle_height = 0x7f07022f
studio.takasaki.clubm:color/material_personalized_color_tertiary_container = 0x7f0602be
studio.takasaki.clubm:color/m3_ref_palette_neutral17 = 0x7f060122
studio.takasaki.clubm:style/TextAppearance.AppCompat.Subhead = 0x7f1301bc
studio.takasaki.clubm:dimen/m3_comp_filled_button_container_elevation = 0x7f070126
studio.takasaki.clubm:color/m3_ref_palette_neutral100 = 0x7f060120
studio.takasaki.clubm:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f140032
studio.takasaki.clubm:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
studio.takasaki.clubm:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1300ae
studio.takasaki.clubm:color/m3_ref_palette_neutral10 = 0x7f06011f
studio.takasaki.clubm:styleable/SideSheetBehavior_Layout = 0x7f14007d
studio.takasaki.clubm:styleable/MaterialShape = 0x7f14005a
studio.takasaki.clubm:attr/cropAspectRatioX = 0x7f040151
studio.takasaki.clubm:dimen/m3_timepicker_window_elevation = 0x7f070223
studio.takasaki.clubm:dimen/mtrl_shape_corner_size_large_component = 0x7f0702e6
studio.takasaki.clubm:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070304
studio.takasaki.clubm:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601e4
studio.takasaki.clubm:color/m3_ref_palette_neutral0 = 0x7f06011e
studio.takasaki.clubm:style/Base.ThemeOverlay.AppCompat = 0x7f13007e
studio.takasaki.clubm:id/accessibility_custom_action_5 = 0x7f090031
studio.takasaki.clubm:dimen/mtrl_toolbar_default_height = 0x7f070309
studio.takasaki.clubm:style/Widget.Material3.Button.Icon = 0x7f130381
studio.takasaki.clubm:attr/tabBackground = 0x7f040420
studio.takasaki.clubm:color/m3_ref_palette_error80 = 0x7f06011a
studio.takasaki.clubm:color/m3_ref_palette_error60 = 0x7f060118
studio.takasaki.clubm:color/m3_ref_palette_error50 = 0x7f060117
studio.takasaki.clubm:color/m3_sys_color_on_primary_fixed_variant = 0x7f06020b
studio.takasaki.clubm:attr/pressedTranslationZ = 0x7f040392
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601b1
studio.takasaki.clubm:color/m3_ref_palette_error40 = 0x7f060116
studio.takasaki.clubm:dimen/m3_small_fab_max_image_size = 0x7f0701f0
studio.takasaki.clubm:animator/fragment_close_enter = 0x7f020003
studio.takasaki.clubm:drawable/abc_btn_default_mtrl_shape = 0x7f08006e
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0d00fc
studio.takasaki.clubm:layout/mtrl_picker_header_toggle = 0x7f0c006a
studio.takasaki.clubm:attr/errorEnabled = 0x7f0401cb
studio.takasaki.clubm:dimen/m3_card_dragged_z = 0x7f0700e9
studio.takasaki.clubm:dimen/m3_alert_dialog_elevation = 0x7f0700a4
studio.takasaki.clubm:color/m3_sys_color_light_on_error_container = 0x7f0601f0
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070206
studio.takasaki.clubm:color/m3_ref_palette_error30 = 0x7f060115
studio.takasaki.clubm:color/m3_ref_palette_error100 = 0x7f060113
studio.takasaki.clubm:color/material_personalized_color_surface_dim = 0x7f0602ba
studio.takasaki.clubm:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_chevronleft = 0x7f080033
studio.takasaki.clubm:dimen/m3_badge_horizontal_offset = 0x7f0700b7
studio.takasaki.clubm:string/biometric_or_screen_lock_prompt_message = 0x7f120023
studio.takasaki.clubm:id/CropOverlayView = 0x7f090004
studio.takasaki.clubm:color/m3_ref_palette_error0 = 0x7f060111
studio.takasaki.clubm:attr/roundedCornerRadius = 0x7f0403b6
studio.takasaki.clubm:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401df
studio.takasaki.clubm:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f070190
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary95 = 0x7f06010f
studio.takasaki.clubm:color/secondary_text_disabled_material_light = 0x7f06031a
studio.takasaki.clubm:color/material_dynamic_neutral90 = 0x7f060248
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary90 = 0x7f06010e
studio.takasaki.clubm:color/material_dynamic_neutral_variant20 = 0x7f06024e
studio.takasaki.clubm:id/compress = 0x7f090089
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary80 = 0x7f06010d
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0d0169
studio.takasaki.clubm:color/m3_ref_palette_neutral22 = 0x7f060124
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary30 = 0x7f060108
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary20 = 0x7f060107
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary100 = 0x7f060106
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary10 = 0x7f060105
studio.takasaki.clubm:id/item_touch_helper_previous_elevation = 0x7f090106
studio.takasaki.clubm:attr/minSeparation = 0x7f040324
studio.takasaki.clubm:dimen/mtrl_btn_text_size = 0x7f070273
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0d0020
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary95 = 0x7f060102
studio.takasaki.clubm:attr/textAppearanceSmallPopupMenu = 0x7f040461
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary90 = 0x7f060101
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary80 = 0x7f060100
studio.takasaki.clubm:attr/titleCollapseMode = 0x7f040493
studio.takasaki.clubm:color/m3_tonal_button_ripple_color_selector = 0x7f06022e
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600ff
studio.takasaki.clubm:string/fingerprint_dialog_touch_sensor = 0x7f12007a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600fd
studio.takasaki.clubm:attr/dividerVertical = 0x7f04019e
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600fc
studio.takasaki.clubm:string/fallback_menu_item_share_link = 0x7f120077
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600f8
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0d015c
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070163
studio.takasaki.clubm:dimen/notification_small_icon_background_padding = 0x7f07031a
studio.takasaki.clubm:dimen/mtrl_fab_elevation = 0x7f0702ba
studio.takasaki.clubm:id/image = 0x7f0900fe
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600f7
studio.takasaki.clubm:color/dev_launcher_white = 0x7f060070
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary99 = 0x7f0600f6
studio.takasaki.clubm:drawable/ic_launcher_background = 0x7f0800ec
studio.takasaki.clubm:drawable/$m3_avd_show_password__1 = 0x7f08000a
studio.takasaki.clubm:dimen/material_textinput_max_width = 0x7f070246
studio.takasaki.clubm:dimen/material_clock_period_toggle_width = 0x7f070232
studio.takasaki.clubm:anim/catalyst_push_up_out = 0x7f01001b
studio.takasaki.clubm:color/material_dynamic_neutral_variant50 = 0x7f060251
studio.takasaki.clubm:drawable/notification_bg_low_normal = 0x7f08013b
studio.takasaki.clubm:color/m3_ref_palette_tertiary30 = 0x7f060161
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary60 = 0x7f0600f1
studio.takasaki.clubm:dimen/m3_card_elevated_disabled_z = 0x7f0700ea
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant60 = 0x7f06013d
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary30 = 0x7f0600ee
studio.takasaki.clubm:color/mtrl_text_btn_text_color_selector = 0x7f060304
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary20 = 0x7f0600ed
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary10 = 0x7f0600eb
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600e9
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070200
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0600e8
studio.takasaki.clubm:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f070197
studio.takasaki.clubm:string/common_signin_button_text_long = 0x7f120062
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0600e7
studio.takasaki.clubm:attr/itemPaddingTop = 0x7f040269
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0600e5
studio.takasaki.clubm:color/m3_sys_color_light_error = 0x7f0601e9
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog = 0x7f13028a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0600e4
studio.takasaki.clubm:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f130349
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600e0
studio.takasaki.clubm:style/Theme.Catalyst.LogBox = 0x7f13023c
studio.takasaki.clubm:anim/abc_slide_in_bottom = 0x7f010006
studio.takasaki.clubm:color/material_dynamic_tertiary10 = 0x7f060273
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600df
studio.takasaki.clubm:attr/counterMaxLength = 0x7f04014c
studio.takasaki.clubm:drawable/abc_btn_borderless_material = 0x7f080068
studio.takasaki.clubm:attr/checkMarkCompat = 0x7f0400a8
studio.takasaki.clubm:anim/rns_slide_in_from_right = 0x7f010048
studio.takasaki.clubm:attr/titleTextAppearance = 0x7f04049c
studio.takasaki.clubm:attr/placeholderText = 0x7f040384
studio.takasaki.clubm:attr/isMaterial3Theme = 0x7f04025b
studio.takasaki.clubm:string/abc_menu_enter_shortcut_label = 0x7f12000d
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0d00fb
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600dd
studio.takasaki.clubm:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1300ef
studio.takasaki.clubm:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0d0041
studio.takasaki.clubm:anim/abc_tooltip_exit = 0x7f01000b
studio.takasaki.clubm:dimen/m3_comp_input_chip_container_height = 0x7f070135
studio.takasaki.clubm:attr/menuGravity = 0x7f040321
studio.takasaki.clubm:macro/m3_comp_slider_inactive_track_color = 0x7f0d0110
studio.takasaki.clubm:dimen/mtrl_progress_circular_inset_small = 0x7f0702db
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600da
studio.takasaki.clubm:id/material_clock_period_am_button = 0x7f090120
studio.takasaki.clubm:dimen/m3_badge_vertical_offset = 0x7f0700ba
studio.takasaki.clubm:styleable/FloatingActionButton = 0x7f140033
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0600d6
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0600d5
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600d0
studio.takasaki.clubm:style/Widget.Material3.Slider.Label = 0x7f1303fe
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.Large = 0x7f130180
studio.takasaki.clubm:id/reverseSawtooth = 0x7f090192
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600cd
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600c9
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f130198
studio.takasaki.clubm:string/call_notification_decline_action = 0x7f12002d
studio.takasaki.clubm:attr/layout_constraintHorizontal_chainStyle = 0x7f0402a3
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600c8
studio.takasaki.clubm:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0d00f6
studio.takasaki.clubm:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602c1
studio.takasaki.clubm:color/abc_decor_view_status_guard_light = 0x7f060006
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600c7
studio.takasaki.clubm:attr/commitIcon = 0x7f040124
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600c5
studio.takasaki.clubm:attr/actionProviderClass = 0x7f040021
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600c1
studio.takasaki.clubm:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f130299
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0d001f
studio.takasaki.clubm:attr/constraintSet = 0x7f040126
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_light_normal = 0x7f0800c8
studio.takasaki.clubm:dimen/mtrl_calendar_day_width = 0x7f07027f
studio.takasaki.clubm:anim/abc_tooltip_enter = 0x7f01000a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600bb
studio.takasaki.clubm:attr/colorErrorContainer = 0x7f0400ef
studio.takasaki.clubm:color/m3_radiobutton_button_tint = 0x7f0600b7
studio.takasaki.clubm:color/m3_primary_text_disable_only = 0x7f0600b6
studio.takasaki.clubm:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
studio.takasaki.clubm:anim/rns_default_exit_out = 0x7f010035
studio.takasaki.clubm:color/m3_navigation_rail_ripple_color_selector = 0x7f0600b4
studio.takasaki.clubm:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f13024b
studio.takasaki.clubm:attr/flow_horizontalAlign = 0x7f04020a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary40 = 0x7f060109
studio.takasaki.clubm:styleable/ActionBarLayout = 0x7f140001
studio.takasaki.clubm:attr/customFloatValue = 0x7f040182
studio.takasaki.clubm:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600b2
studio.takasaki.clubm:attr/checkedIconVisible = 0x7f0400b4
studio.takasaki.clubm:attr/itemFillColor = 0x7f04025f
studio.takasaki.clubm:attr/layout_constraintVertical_bias = 0x7f0402b1
studio.takasaki.clubm:string/catalyst_perf_monitor_stop = 0x7f120044
studio.takasaki.clubm:color/m3_navigation_bar_ripple_color_selector = 0x7f0600ad
studio.takasaki.clubm:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702ce
studio.takasaki.clubm:attr/postSplashScreenTheme = 0x7f04038c
studio.takasaki.clubm:attr/state_error = 0x7f040401
studio.takasaki.clubm:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600ac
studio.takasaki.clubm:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600ab
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1301cc
studio.takasaki.clubm:macro/m3_comp_snackbar_container_shape = 0x7f0d0114
studio.takasaki.clubm:drawable/abc_switch_track_mtrl_alpha = 0x7f0800a8
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0d0079
studio.takasaki.clubm:attr/applyMotionScene = 0x7f040037
studio.takasaki.clubm:attr/constraintSetEnd = 0x7f040127
studio.takasaki.clubm:color/abc_primary_text_material_light = 0x7f06000c
studio.takasaki.clubm:attr/cornerSize = 0x7f040146
studio.takasaki.clubm:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f130281
studio.takasaki.clubm:attr/strokeWidth = 0x7f04040a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0600d8
studio.takasaki.clubm:color/m3_fab_ripple_color_selector = 0x7f0600a6
studio.takasaki.clubm:id/mtrl_calendar_year_selector_frame = 0x7f090144
studio.takasaki.clubm:attr/thumbHeight = 0x7f040478
studio.takasaki.clubm:color/m3_elevated_chip_background_color = 0x7f0600a3
studio.takasaki.clubm:color/mtrl_outlined_icon_tint = 0x7f0602f7
studio.takasaki.clubm:color/m3_efab_ripple_color_selector = 0x7f0600a2
studio.takasaki.clubm:style/Widget.AppCompat.ListView.Menu = 0x7f130344
studio.takasaki.clubm:style/Widget.AppCompat.ActionButton.Overflow = 0x7f130317
studio.takasaki.clubm:attr/chipMinTouchTargetSize = 0x7f0400c1
studio.takasaki.clubm:attr/minHeight = 0x7f040322
studio.takasaki.clubm:anim/rns_no_animation_250 = 0x7f010043
studio.takasaki.clubm:color/m3_dynamic_highlighted_text = 0x7f06009f
studio.takasaki.clubm:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f080111
studio.takasaki.clubm:color/m3_icon_button_icon_color_selector = 0x7f0600aa
studio.takasaki.clubm:color/m3_dynamic_default_color_primary_text = 0x7f06009d
studio.takasaki.clubm:color/material_dynamic_primary99 = 0x7f060264
studio.takasaki.clubm:integer/m3_sys_motion_duration_short4 = 0x7f0a0020
studio.takasaki.clubm:color/m3_dynamic_dark_primary_text_disable_only = 0x7f06009c
studio.takasaki.clubm:id/design_menu_item_action_area_stub = 0x7f0900a3
studio.takasaki.clubm:color/m3_dynamic_dark_default_color_primary_text = 0x7f060098
studio.takasaki.clubm:color/material_personalized_color_primary = 0x7f0602aa
studio.takasaki.clubm:dimen/design_tab_text_size = 0x7f07008e
studio.takasaki.clubm:dimen/mtrl_badge_horizontal_edge_offset = 0x7f07024f
studio.takasaki.clubm:color/m3_default_color_secondary_text = 0x7f060097
studio.takasaki.clubm:color/material_dynamic_tertiary50 = 0x7f060278
studio.takasaki.clubm:anim/rns_ios_from_right_background_close = 0x7f01003e
studio.takasaki.clubm:color/m3_default_color_primary_text = 0x7f060096
studio.takasaki.clubm:color/m3_ref_palette_tertiary10 = 0x7f06015e
studio.takasaki.clubm:color/m3_dark_hint_foreground = 0x7f060094
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1303ba
studio.takasaki.clubm:drawable/googleg_disabled_color_18 = 0x7f0800de
studio.takasaki.clubm:attr/layout_constraintGuide_begin = 0x7f04029b
studio.takasaki.clubm:color/m3_dark_highlighted_text = 0x7f060093
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080012
studio.takasaki.clubm:color/m3_dynamic_dark_default_color_secondary_text = 0x7f060099
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f13045b
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600c0
studio.takasaki.clubm:attr/startIconContentDescription = 0x7f0403f7
studio.takasaki.clubm:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1300dc
studio.takasaki.clubm:color/m3_dark_default_color_primary_text = 0x7f060091
studio.takasaki.clubm:color/material_personalized__highlighted_text = 0x7f060294
studio.takasaki.clubm:color/m3_chip_background_color = 0x7f06008d
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary70 = 0x7f0600f2
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f130190
studio.takasaki.clubm:attr/backgroundInsetStart = 0x7f040050
studio.takasaki.clubm:attr/counterEnabled = 0x7f04014b
studio.takasaki.clubm:attr/boxCornerRadiusBottomEnd = 0x7f040083
studio.takasaki.clubm:attr/cropShowProgressBar = 0x7f040172
studio.takasaki.clubm:color/m3_calendar_item_disabled_text = 0x7f060085
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1303b9
studio.takasaki.clubm:id/coordinator = 0x7f090091
studio.takasaki.clubm:dimen/m3_menu_elevation = 0x7f0701c0
studio.takasaki.clubm:attr/trackTintMode = 0x7f0404ba
studio.takasaki.clubm:attr/tabTextColor = 0x7f04043b
studio.takasaki.clubm:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
studio.takasaki.clubm:attr/cropBorderCornerLength = 0x7f040156
studio.takasaki.clubm:color/m3_sys_color_dark_on_surface = 0x7f060183
studio.takasaki.clubm:color/m3_button_ripple_color = 0x7f060083
studio.takasaki.clubm:color/m3_assist_chip_stroke_color = 0x7f06007e
studio.takasaki.clubm:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700e3
studio.takasaki.clubm:styleable/StateListDrawable = 0x7f140085
studio.takasaki.clubm:attr/errorTextAppearance = 0x7f0401d0
studio.takasaki.clubm:color/m3_appbar_overlay_color = 0x7f06007c
studio.takasaki.clubm:attr/actionBarItemBackground = 0x7f040001
studio.takasaki.clubm:drawable/mtrl_tabs_default_indicator = 0x7f080136
studio.takasaki.clubm:color/highlighted_text_material_dark = 0x7f060079
studio.takasaki.clubm:color/foreground_material_light = 0x7f060078
studio.takasaki.clubm:style/Widget.AppCompat.SeekBar = 0x7f13034f
studio.takasaki.clubm:color/dev_launcher_secondaryBackgroundColor = 0x7f06006f
studio.takasaki.clubm:dimen/compat_notification_large_icon_max_width = 0x7f07005f
studio.takasaki.clubm:color/m3_ref_palette_primary20 = 0x7f060146
studio.takasaki.clubm:attr/itemIconSize = 0x7f040263
studio.takasaki.clubm:attr/colorError = 0x7f0400ee
studio.takasaki.clubm:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07010a
studio.takasaki.clubm:attr/fabCradleMargin = 0x7f0401e9
studio.takasaki.clubm:color/dev_launcher_primary = 0x7f06006e
studio.takasaki.clubm:drawable/m3_avd_hide_password = 0x7f0800fd
studio.takasaki.clubm:color/material_personalized_primary_text_disable_only = 0x7f0602c7
studio.takasaki.clubm:color/dev_launcher_errorMessage = 0x7f06006d
studio.takasaki.clubm:animator/mtrl_card_state_list_anim = 0x7f020017
studio.takasaki.clubm:macro/m3_comp_assist_chip_container_shape = 0x7f0d0000
studio.takasaki.clubm:attr/clockNumberTextColor = 0x7f0400d2
studio.takasaki.clubm:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f130447
studio.takasaki.clubm:dimen/splashscreen_icon_size_no_background = 0x7f070324
studio.takasaki.clubm:color/dev_launcher_backgroundColor = 0x7f060069
studio.takasaki.clubm:color/design_fab_stroke_end_inner_color = 0x7f060063
studio.takasaki.clubm:id/tag_on_receive_content_listener = 0x7f0901e2
studio.takasaki.clubm:attr/voiceIcon = 0x7f0404cd
studio.takasaki.clubm:dimen/clock_face_margin_start = 0x7f070058
studio.takasaki.clubm:color/design_fab_shadow_mid_color = 0x7f060061
studio.takasaki.clubm:dimen/tooltip_y_offset_non_touch = 0x7f070330
studio.takasaki.clubm:attr/lastBaselineToBottomHeight = 0x7f040283
studio.takasaki.clubm:color/m3_sys_color_light_surface_container = 0x7f060201
studio.takasaki.clubm:attr/textInputFilledExposedDropdownMenuStyle = 0x7f04046b
studio.takasaki.clubm:color/design_fab_shadow_end_color = 0x7f060060
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationRailView = 0x7f130466
studio.takasaki.clubm:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070176
studio.takasaki.clubm:attr/itemMaxLines = 0x7f040265
studio.takasaki.clubm:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070101
studio.takasaki.clubm:string/search_menu_title = 0x7f12010c
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600ce
studio.takasaki.clubm:attr/drawPath = 0x7f0401a2
studio.takasaki.clubm:id/left = 0x7f09010c
studio.takasaki.clubm:color/design_default_color_primary_variant = 0x7f06005b
studio.takasaki.clubm:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f130346
studio.takasaki.clubm:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080066
studio.takasaki.clubm:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701af
studio.takasaki.clubm:color/design_default_color_primary_dark = 0x7f06005a
studio.takasaki.clubm:id/stretch = 0x7f0901d9
studio.takasaki.clubm:color/design_default_color_on_surface = 0x7f060058
studio.takasaki.clubm:attr/materialTimePickerTitleStyle = 0x7f040313
studio.takasaki.clubm:styleable/TextAppearance = 0x7f14008d
studio.takasaki.clubm:color/design_default_color_on_primary = 0x7f060056
studio.takasaki.clubm:color/design_default_color_on_error = 0x7f060055
studio.takasaki.clubm:anim/abc_fade_in = 0x7f010000
studio.takasaki.clubm:attr/listPreferredItemHeightLarge = 0x7f0402d7
studio.takasaki.clubm:color/design_default_color_on_background = 0x7f060054
studio.takasaki.clubm:id/accessibility_custom_action_10 = 0x7f090018
studio.takasaki.clubm:color/mtrl_switch_thumb_tint = 0x7f0602fc
studio.takasaki.clubm:color/design_default_color_error = 0x7f060053
studio.takasaki.clubm:attr/tooltipStyle = 0x7f0404a7
studio.takasaki.clubm:macro/m3_comp_fab_primary_container_color = 0x7f0d0036
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600f9
studio.takasaki.clubm:color/m3_hint_foreground = 0x7f0600a9
studio.takasaki.clubm:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f070275
studio.takasaki.clubm:color/material_on_background_emphasis_medium = 0x7f06028c
studio.takasaki.clubm:anim/rns_slide_out_to_bottom = 0x7f010049
studio.takasaki.clubm:color/design_dark_default_color_secondary_variant = 0x7f060050
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07015e
studio.takasaki.clubm:anim/abc_popup_exit = 0x7f010004
studio.takasaki.clubm:color/design_dark_default_color_primary_variant = 0x7f06004e
studio.takasaki.clubm:attr/panelBackground = 0x7f040372
studio.takasaki.clubm:color/design_dark_default_color_on_background = 0x7f060047
studio.takasaki.clubm:color/material_dynamic_secondary50 = 0x7f06026b
studio.takasaki.clubm:color/material_dynamic_neutral80 = 0x7f060247
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_secondary = 0x7f0601ad
studio.takasaki.clubm:macro/m3_comp_search_view_header_input_text_color = 0x7f0d00f4
studio.takasaki.clubm:color/design_dark_default_color_error = 0x7f060046
studio.takasaki.clubm:color/design_dark_default_color_background = 0x7f060045
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07020b
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1301cf
studio.takasaki.clubm:layout/abc_list_menu_item_layout = 0x7f0c0010
studio.takasaki.clubm:id/accessibility_role = 0x7f090039
studio.takasaki.clubm:attr/multiChoiceItemLayout = 0x7f040353
studio.takasaki.clubm:color/m3_ref_palette_neutral4 = 0x7f060127
studio.takasaki.clubm:color/design_bottom_navigation_shadow_color = 0x7f060043
studio.takasaki.clubm:color/design_default_color_surface = 0x7f06005e
studio.takasaki.clubm:attr/icon = 0x7f040242
studio.takasaki.clubm:color/common_google_signin_btn_text_light_pressed = 0x7f060041
studio.takasaki.clubm:color/material_dynamic_tertiary20 = 0x7f060275
studio.takasaki.clubm:dimen/abc_action_button_min_height_material = 0x7f07000d
studio.takasaki.clubm:color/common_google_signin_btn_text_light_default = 0x7f06003e
studio.takasaki.clubm:attr/materialCalendarFullscreenTheme = 0x7f0402f0
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600bd
studio.takasaki.clubm:color/material_personalized_color_surface_container = 0x7f0602b5
studio.takasaki.clubm:integer/app_bar_elevation_anim_duration = 0x7f0a0002
studio.takasaki.clubm:color/design_snackbar_background_color = 0x7f060068
studio.takasaki.clubm:attr/boxBackgroundMode = 0x7f040081
studio.takasaki.clubm:attr/forceDefaultNavigationOnClickListener = 0x7f040226
studio.takasaki.clubm:drawable/abc_btn_check_material_anim = 0x7f08006a
studio.takasaki.clubm:string/splash_screen_text = 0x7f120113
studio.takasaki.clubm:dimen/highlight_alpha_material_dark = 0x7f070098
studio.takasaki.clubm:color/common_google_signin_btn_text_dark_focused = 0x7f06003b
studio.takasaki.clubm:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
studio.takasaki.clubm:color/m3_sys_color_dark_on_background = 0x7f06017c
studio.takasaki.clubm:color/common_google_signin_btn_text_dark = 0x7f060038
studio.takasaki.clubm:dimen/abc_search_view_preferred_width = 0x7f070037
studio.takasaki.clubm:animator/m3_extended_fab_show_motion_spec = 0x7f020013
studio.takasaki.clubm:color/colorPrimary = 0x7f060036
studio.takasaki.clubm:id/selection_type = 0x7f0901b9
studio.takasaki.clubm:drawable/abc_tab_indicator_mtrl_alpha = 0x7f0800aa
studio.takasaki.clubm:id/search_src_text = 0x7f0901b5
studio.takasaki.clubm:attr/checkedIconEnabled = 0x7f0400af
studio.takasaki.clubm:attr/linearProgressIndicatorStyle = 0x7f0402cd
studio.takasaki.clubm:color/m3_dark_default_color_secondary_text = 0x7f060092
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0d008a
studio.takasaki.clubm:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070178
studio.takasaki.clubm:color/cardview_shadow_start_color = 0x7f060033
studio.takasaki.clubm:attr/expandedTitleMarginTop = 0x7f0401da
studio.takasaki.clubm:color/mtrl_filled_background_color = 0x7f0602ea
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1303e5
studio.takasaki.clubm:attr/cornerFamilyTopLeft = 0x7f040142
studio.takasaki.clubm:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1300f4
studio.takasaki.clubm:color/cardview_shadow_end_color = 0x7f060032
studio.takasaki.clubm:styleable/Autofill.InlineSuggestion = 0x7f140013
studio.takasaki.clubm:integer/mtrl_switch_track_viewport_height = 0x7f0a003e
studio.takasaki.clubm:color/material_harmonized_color_on_error = 0x7f060288
studio.takasaki.clubm:attr/tickColorActive = 0x7f040486
studio.takasaki.clubm:color/cardview_light_background = 0x7f060031
studio.takasaki.clubm:id/decor_content_parent = 0x7f09009e
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600e1
studio.takasaki.clubm:color/cardview_dark_background = 0x7f060030
studio.takasaki.clubm:style/Widget.AppCompat.Toolbar = 0x7f130357
studio.takasaki.clubm:color/material_dynamic_primary10 = 0x7f060259
studio.takasaki.clubm:color/call_notification_decline_color = 0x7f06002f
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Headline2 = 0x7f130214
studio.takasaki.clubm:color/call_notification_answer_color = 0x7f06002e
studio.takasaki.clubm:drawable/abc_text_select_handle_left_mtrl = 0x7f0800ac
studio.takasaki.clubm:id/selected = 0x7f0901b8
studio.takasaki.clubm:attr/showDividers = 0x7f0403d8
studio.takasaki.clubm:color/browser_actions_text_color = 0x7f06002a
studio.takasaki.clubm:dimen/abc_dialog_min_width_major = 0x7f070022
studio.takasaki.clubm:style/Widget.MaterialComponents.FloatingActionButton = 0x7f130449
studio.takasaki.clubm:attr/boxCornerRadiusTopEnd = 0x7f040085
studio.takasaki.clubm:color/browser_actions_divider_color = 0x7f060029
studio.takasaki.clubm:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1300f7
studio.takasaki.clubm:style/Base.V23.Theme.AppCompat.Light = 0x7f1300b6
studio.takasaki.clubm:color/bright_foreground_material_light = 0x7f060027
studio.takasaki.clubm:attr/actionModeSplitBackground = 0x7f04001b
studio.takasaki.clubm:id/view_tag_native_id = 0x7f090218
studio.takasaki.clubm:color/common_google_signin_btn_text_dark_default = 0x7f060039
studio.takasaki.clubm:attr/paddingTopSystemWindowInsets = 0x7f040371
studio.takasaki.clubm:color/background_floating_material_dark = 0x7f06001d
studio.takasaki.clubm:color/abc_tint_switch_track = 0x7f060018
studio.takasaki.clubm:color/abc_tint_spinner = 0x7f060017
studio.takasaki.clubm:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1301bb
studio.takasaki.clubm:id/auto = 0x7f09005e
studio.takasaki.clubm:color/abc_tint_seek_thumb = 0x7f060016
studio.takasaki.clubm:color/background_material_light = 0x7f060020
studio.takasaki.clubm:color/abc_tint_default = 0x7f060014
studio.takasaki.clubm:drawable/abc_list_selector_disabled_holo_dark = 0x7f080092
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary70 = 0x7f06010c
studio.takasaki.clubm:attr/textAppearanceHeadline1 = 0x7f04044c
studio.takasaki.clubm:color/common_google_signin_btn_text_dark_disabled = 0x7f06003a
studio.takasaki.clubm:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0701c1
studio.takasaki.clubm:color/abc_secondary_text_material_light = 0x7f060012
studio.takasaki.clubm:dimen/m3_simple_item_color_selected_alpha = 0x7f0701ee
studio.takasaki.clubm:macro/m3_comp_fab_primary_container_shape = 0x7f0d0037
studio.takasaki.clubm:attr/listLayout = 0x7f0402d3
studio.takasaki.clubm:dimen/tooltip_y_offset_touch = 0x7f070331
studio.takasaki.clubm:dimen/m3_searchbar_height = 0x7f0701df
studio.takasaki.clubm:layout/design_navigation_menu_item = 0x7f0c0030
studio.takasaki.clubm:attr/useMaterialThemeColors = 0x7f0404c6
studio.takasaki.clubm:color/abc_primary_text_disable_only_material_light = 0x7f06000a
studio.takasaki.clubm:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f13043f
studio.takasaki.clubm:drawable/avd_hide_password = 0x7f0800b6
studio.takasaki.clubm:color/abc_hint_foreground_material_light = 0x7f060008
studio.takasaki.clubm:color/m3_card_ripple_color = 0x7f060088
studio.takasaki.clubm:attr/tintMode = 0x7f04048f
studio.takasaki.clubm:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1302cf
studio.takasaki.clubm:color/abc_hint_foreground_material_dark = 0x7f060007
studio.takasaki.clubm:anim/rns_no_animation_20 = 0x7f010042
studio.takasaki.clubm:color/abc_decor_view_status_guard = 0x7f060005
studio.takasaki.clubm:attr/trackColor = 0x7f0404ae
studio.takasaki.clubm:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0d00d4
studio.takasaki.clubm:color/abc_btn_colored_borderless_text_material = 0x7f060002
studio.takasaki.clubm:styleable/ListPopupWindow = 0x7f14004c
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1303d1
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f130196
studio.takasaki.clubm:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070102
studio.takasaki.clubm:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f130060
studio.takasaki.clubm:color/browser_actions_bg_grey = 0x7f060028
studio.takasaki.clubm:attr/deltaPolarAngle = 0x7f040190
studio.takasaki.clubm:color/catalyst_logbox_background = 0x7f060034
studio.takasaki.clubm:bool/abc_action_bar_embed_tabs = 0x7f050000
studio.takasaki.clubm:dimen/m3_appbar_size_compact = 0x7f0700ad
studio.takasaki.clubm:styleable/MaterialToolbar = 0x7f14005f
studio.takasaki.clubm:attr/yearStyle = 0x7f0404e3
studio.takasaki.clubm:integer/m3_sys_motion_duration_long3 = 0x7f0a0017
studio.takasaki.clubm:drawable/autofill_inline_suggestion_chip_background = 0x7f0800b5
studio.takasaki.clubm:attr/actionBarTheme = 0x7f040009
studio.takasaki.clubm:attr/windowSplashScreenIconBackgroundColor = 0x7f0404e1
studio.takasaki.clubm:attr/chipBackgroundColor = 0x7f0400b7
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600ba
studio.takasaki.clubm:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0d00ec
studio.takasaki.clubm:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070151
studio.takasaki.clubm:attr/windowSplashScreenAnimatedIcon = 0x7f0404de
studio.takasaki.clubm:id/center_horizontal = 0x7f09007c
studio.takasaki.clubm:attr/placeholderImageScaleType = 0x7f040383
studio.takasaki.clubm:attr/windowFixedHeightMinor = 0x7f0404d8
studio.takasaki.clubm:id/action0 = 0x7f09003d
studio.takasaki.clubm:attr/windowFixedHeightMajor = 0x7f0404d7
studio.takasaki.clubm:attr/tabMaxWidth = 0x7f04042d
studio.takasaki.clubm:attr/prefixText = 0x7f04038d
studio.takasaki.clubm:attr/windowActionBar = 0x7f0404d4
studio.takasaki.clubm:attr/layout_constraintCircleAngle = 0x7f040296
studio.takasaki.clubm:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0d0051
studio.takasaki.clubm:attr/waveVariesBy = 0x7f0404d3
studio.takasaki.clubm:attr/wavePeriod = 0x7f0404d1
studio.takasaki.clubm:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07013a
studio.takasaki.clubm:id/open_search_view_status_bar_spacer = 0x7f090170
studio.takasaki.clubm:attr/trackDecorationTintMode = 0x7f0404b4
studio.takasaki.clubm:attr/values = 0x7f0404c7
studio.takasaki.clubm:attr/thumbTextPadding = 0x7f040480
studio.takasaki.clubm:integer/abc_config_activityShortDur = 0x7f0a0001
studio.takasaki.clubm:color/m3_dynamic_dark_highlighted_text = 0x7f06009a
studio.takasaki.clubm:drawable/ic_call_answer_video_low = 0x7f0800e5
studio.takasaki.clubm:color/m3_sys_color_light_primary_container = 0x7f0601fc
studio.takasaki.clubm:array/hide_fingerprint_instantly_prefixes = 0x7f030004
studio.takasaki.clubm:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0d0056
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_container_color = 0x7f0d0006
studio.takasaki.clubm:color/design_fab_stroke_top_inner_color = 0x7f060065
studio.takasaki.clubm:dimen/abc_text_size_title_material_toolbar = 0x7f070050
studio.takasaki.clubm:color/m3_chip_ripple_color = 0x7f06008e
studio.takasaki.clubm:styleable/MotionHelper = 0x7f140065
studio.takasaki.clubm:layout/design_layout_snackbar_include = 0x7f0c0027
studio.takasaki.clubm:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701aa
studio.takasaki.clubm:attr/ttcIndex = 0x7f0404c3
studio.takasaki.clubm:attr/triggerReceiver = 0x7f0404c1
studio.takasaki.clubm:attr/triggerId = 0x7f0404c0
studio.takasaki.clubm:attr/textColorAlertDialogListItem = 0x7f040467
studio.takasaki.clubm:attr/trackInsideCornerSize = 0x7f0404b6
studio.takasaki.clubm:color/m3_sys_color_light_on_error = 0x7f0601ef
studio.takasaki.clubm:anim/rns_ios_from_left_background_close = 0x7f01003a
studio.takasaki.clubm:attr/paddingStartSystemWindowInsets = 0x7f04036f
studio.takasaki.clubm:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1302e6
studio.takasaki.clubm:attr/iconTint = 0x7f040248
studio.takasaki.clubm:id/search_close_btn = 0x7f0901b0
studio.takasaki.clubm:attr/backgroundSplit = 0x7f040053
studio.takasaki.clubm:attr/materialCircleRadius = 0x7f040301
studio.takasaki.clubm:attr/trackHeight = 0x7f0404b5
studio.takasaki.clubm:attr/autoSizeMinTextSize = 0x7f040040
studio.takasaki.clubm:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0d0139
studio.takasaki.clubm:dimen/mtrl_switch_text_padding = 0x7f0702fa
studio.takasaki.clubm:attr/trackDecorationTint = 0x7f0404b3
studio.takasaki.clubm:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0d009a
studio.takasaki.clubm:attr/touchAnchorSide = 0x7f0404ab
studio.takasaki.clubm:drawable/abc_btn_radio_material_anim = 0x7f080070
studio.takasaki.clubm:integer/m3_sys_motion_duration_short3 = 0x7f0a001f
studio.takasaki.clubm:attr/percentWidth = 0x7f04037d
studio.takasaki.clubm:attr/tooltipText = 0x7f0404a8
studio.takasaki.clubm:id/jumpToEnd = 0x7f090107
studio.takasaki.clubm:attr/tooltipFrameBackground = 0x7f0404a6
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f13025d
studio.takasaki.clubm:attr/tooltipForegroundColor = 0x7f0404a5
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_loadingindicatoricon = 0x7f08004c
studio.takasaki.clubm:attr/behavior_autoShrink = 0x7f04006d
studio.takasaki.clubm:macro/m3_comp_dialog_supporting_text_color = 0x7f0d0026
studio.takasaki.clubm:attr/toolbarSurfaceStyle = 0x7f0404a4
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f13003b
studio.takasaki.clubm:color/m3_sys_color_dark_inverse_surface = 0x7f06017b
studio.takasaki.clubm:string/fingerprint_error_no_fingerprints = 0x7f12007e
studio.takasaki.clubm:attr/toolbarId = 0x7f0404a1
studio.takasaki.clubm:attr/cropAspectRatioY = 0x7f040152
studio.takasaki.clubm:attr/toggleCheckedStateOnClick = 0x7f0404a0
studio.takasaki.clubm:attr/titlePositionInterpolator = 0x7f04049b
studio.takasaki.clubm:attr/layout_constraintLeft_toRightOf = 0x7f0402a7
studio.takasaki.clubm:drawable/mtrl_popupmenu_background_overlay = 0x7f080129
studio.takasaki.clubm:attr/materialCalendarHeaderDivider = 0x7f0402f3
studio.takasaki.clubm:attr/textInputFilledDenseStyle = 0x7f04046a
studio.takasaki.clubm:attr/titleMarginTop = 0x7f040499
studio.takasaki.clubm:string/mtrl_picker_end_date_description = 0x7f1200da
studio.takasaki.clubm:attr/titleCentered = 0x7f040492
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f0800c4
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_threefingerlongpressiconlight = 0x7f08005c
studio.takasaki.clubm:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701d5
studio.takasaki.clubm:attr/tint = 0x7f04048e
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070207
studio.takasaki.clubm:attr/tickVisible = 0x7f04048d
studio.takasaki.clubm:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f130491
studio.takasaki.clubm:attr/tickRadiusActive = 0x7f04048b
studio.takasaki.clubm:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702b7
studio.takasaki.clubm:style/Widget.AppCompat.EditText = 0x7f130328
studio.takasaki.clubm:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700a8
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0d0080
studio.takasaki.clubm:id/material_clock_face = 0x7f09011d
studio.takasaki.clubm:attr/roundAsCircle = 0x7f0403ab
studio.takasaki.clubm:attr/tickMarkTint = 0x7f040489
studio.takasaki.clubm:id/cropImageView = 0x7f090095
studio.takasaki.clubm:attr/tickMark = 0x7f040488
studio.takasaki.clubm:color/mtrl_switch_track_decoration_tint = 0x7f0602fd
studio.takasaki.clubm:color/dim_foreground_material_dark = 0x7f060073
studio.takasaki.clubm:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701ae
studio.takasaki.clubm:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070131
studio.takasaki.clubm:attr/tickColor = 0x7f040485
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13028d
studio.takasaki.clubm:dimen/mtrl_btn_z = 0x7f070274
studio.takasaki.clubm:color/m3_sys_color_light_on_secondary = 0x7f0601f3
studio.takasaki.clubm:attr/barrierDirection = 0x7f04006a
studio.takasaki.clubm:styleable/AppBarLayoutStates = 0x7f14000b
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsicon = 0x7f080043
studio.takasaki.clubm:attr/thumbIconTintMode = 0x7f04047c
studio.takasaki.clubm:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
studio.takasaki.clubm:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070108
studio.takasaki.clubm:string/catalyst_hot_reloading = 0x7f12003c
studio.takasaki.clubm:attr/thumbElevation = 0x7f040477
studio.takasaki.clubm:attr/textInputLayoutFocusedRectEnabled = 0x7f04046d
studio.takasaki.clubm:attr/thumbColor = 0x7f040476
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f130271
studio.takasaki.clubm:id/list_view = 0x7f090115
studio.takasaki.clubm:attr/height = 0x7f04022f
studio.takasaki.clubm:layout/abc_action_bar_up_container = 0x7f0c0001
studio.takasaki.clubm:attr/thickness = 0x7f040475
studio.takasaki.clubm:attr/theme = 0x7f040474
studio.takasaki.clubm:attr/badgeShapeAppearanceOverlay = 0x7f04005b
studio.takasaki.clubm:attr/endIconContentDescription = 0x7f0401be
studio.takasaki.clubm:styleable/SVGImageView = 0x7f140076
studio.takasaki.clubm:attr/textInputOutlinedStyle = 0x7f040470
studio.takasaki.clubm:attr/seekBarStyle = 0x7f0403c3
studio.takasaki.clubm:attr/tabIndicatorColor = 0x7f040428
studio.takasaki.clubm:attr/mock_labelBackgroundColor = 0x7f040329
studio.takasaki.clubm:anim/catalyst_slide_up = 0x7f01001d
studio.takasaki.clubm:id/transitionToStart = 0x7f090205
studio.takasaki.clubm:attr/state_above_anchor = 0x7f0403fd
studio.takasaki.clubm:attr/textAppearanceTitleLarge = 0x7f040464
studio.takasaki.clubm:color/m3_sys_color_light_outline = 0x7f0601f9
studio.takasaki.clubm:style/TextAppearance.Design.Placeholder = 0x7f1301e4
studio.takasaki.clubm:color/material_dynamic_tertiary100 = 0x7f060274
studio.takasaki.clubm:color/abc_primary_text_material_dark = 0x7f06000b
studio.takasaki.clubm:attr/buttonPanelSideLayout = 0x7f040097
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0d00d1
studio.takasaki.clubm:attr/textAppearanceSubtitle2 = 0x7f040463
studio.takasaki.clubm:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
studio.takasaki.clubm:attr/drawableTintMode = 0x7f0401aa
studio.takasaki.clubm:string/hide_bottom_view_on_scroll_behavior = 0x7f12008c
studio.takasaki.clubm:color/material_dynamic_secondary60 = 0x7f06026c
studio.takasaki.clubm:string/ic_flip_24_vertically = 0x7f12008f
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0d00d9
studio.takasaki.clubm:attr/textAppearanceSearchResultSubtitle = 0x7f04045f
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601b3
studio.takasaki.clubm:id/enterAlways = 0x7f0900bf
studio.takasaki.clubm:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601de
studio.takasaki.clubm:attr/textAppearanceOverline = 0x7f04045d
studio.takasaki.clubm:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f070299
studio.takasaki.clubm:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130422
studio.takasaki.clubm:dimen/m3_card_hovered_z = 0x7f0700ef
studio.takasaki.clubm:attr/textAppearanceLineHeightEnabled = 0x7f040459
studio.takasaki.clubm:color/mtrl_indicator_text_color = 0x7f0602ed
studio.takasaki.clubm:color/material_dynamic_neutral30 = 0x7f060242
studio.takasaki.clubm:attr/textAppearanceLabelMedium = 0x7f040456
studio.takasaki.clubm:macro/m3_comp_input_chip_container_shape = 0x7f0d005b
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070117
studio.takasaki.clubm:style/ThemeOverlay.Material3.Light = 0x7f1302d1
studio.takasaki.clubm:attr/textAppearanceHeadlineMedium = 0x7f040453
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f130460
studio.takasaki.clubm:id/src_in = 0x7f0901ce
studio.takasaki.clubm:attr/verticalOffsetWithText = 0x7f0404c9
studio.takasaki.clubm:dimen/abc_text_size_subhead_material = 0x7f07004d
studio.takasaki.clubm:string/fallback_menu_item_copy_link = 0x7f120075
studio.takasaki.clubm:attr/textAppearanceHeadline5 = 0x7f040450
studio.takasaki.clubm:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f070100
studio.takasaki.clubm:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0d0106
studio.takasaki.clubm:layout/material_clock_period_toggle_land = 0x7f0c0045
studio.takasaki.clubm:color/material_dynamic_secondary90 = 0x7f06026f
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f130073
studio.takasaki.clubm:attr/textAppearanceHeadline4 = 0x7f04044f
studio.takasaki.clubm:drawable/dev_launcher_ic_refresh_white_36dp = 0x7f0800db
studio.takasaki.clubm:dimen/mtrl_tooltip_arrowSize = 0x7f07030a
studio.takasaki.clubm:attr/titleMargins = 0x7f04049a
studio.takasaki.clubm:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602ee
studio.takasaki.clubm:attr/textAppearanceHeadline3 = 0x7f04044e
studio.takasaki.clubm:dimen/m3_datepicker_elevation = 0x7f0701b1
studio.takasaki.clubm:attr/pressedStateOverlayImage = 0x7f040391
studio.takasaki.clubm:color/material_dynamic_primary50 = 0x7f06025e
studio.takasaki.clubm:attr/itemPadding = 0x7f040267
studio.takasaki.clubm:attr/textAppearanceDisplaySmall = 0x7f04044b
studio.takasaki.clubm:attr/gestureInsetBottomIgnored = 0x7f04022a
studio.takasaki.clubm:dimen/material_clock_hand_padding = 0x7f07022c
studio.takasaki.clubm:styleable/AppCompatImageView = 0x7f14000e
studio.takasaki.clubm:attr/textAppearanceDisplayLarge = 0x7f040449
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f13015e
studio.takasaki.clubm:id/submit_area = 0x7f0901db
studio.takasaki.clubm:attr/textAppearanceButton = 0x7f040447
studio.takasaki.clubm:color/material_grey_900 = 0x7f060285
studio.takasaki.clubm:attr/prefixTextAppearance = 0x7f04038e
studio.takasaki.clubm:attr/colorTertiaryFixed = 0x7f040122
studio.takasaki.clubm:style/Base.Widget.Material3.ActionMode = 0x7f130104
studio.takasaki.clubm:attr/textAppearanceBody2 = 0x7f040443
studio.takasaki.clubm:attr/textAppearanceBody1 = 0x7f040442
studio.takasaki.clubm:attr/tabIconTintMode = 0x7f040424
studio.takasaki.clubm:dimen/mtrl_calendar_year_horizontal_padding = 0x7f07029c
studio.takasaki.clubm:style/Base.Widget.Material3.Chip = 0x7f130107
studio.takasaki.clubm:attr/telltales_velocityMode = 0x7f040440
studio.takasaki.clubm:drawable/btn_radio_off_mtrl = 0x7f0800bc
studio.takasaki.clubm:dimen/tooltip_precise_anchor_threshold = 0x7f07032e
studio.takasaki.clubm:id/line3 = 0x7f090111
studio.takasaki.clubm:attr/failureImageScaleType = 0x7f0401f0
studio.takasaki.clubm:attr/telltales_tailScale = 0x7f04043f
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601c5
studio.takasaki.clubm:style/Base.Theme.Material3.Dark = 0x7f13005c
studio.takasaki.clubm:attr/tabUnboundedRipple = 0x7f04043c
studio.takasaki.clubm:dimen/m3_btn_max_width = 0x7f0700dc
studio.takasaki.clubm:macro/m3_comp_search_view_header_input_text_type = 0x7f0d00f5
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601d4
studio.takasaki.clubm:attr/snackbarStyle = 0x7f0403eb
studio.takasaki.clubm:dimen/m3_navigation_item_shape_inset_end = 0x7f0701c7
studio.takasaki.clubm:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f130052
studio.takasaki.clubm:color/abc_search_url_text = 0x7f06000d
studio.takasaki.clubm:id/easeOut = 0x7f0900b6
studio.takasaki.clubm:drawable/rn_edit_text_material = 0x7f080149
studio.takasaki.clubm:attr/goIcon = 0x7f04022b
studio.takasaki.clubm:dimen/mtrl_btn_disabled_elevation = 0x7f07025f
studio.takasaki.clubm:color/mtrl_navigation_item_background_color = 0x7f0602f2
studio.takasaki.clubm:attr/motionDurationMedium4 = 0x7f040339
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1302f4
studio.takasaki.clubm:attr/tabTextAppearance = 0x7f04043a
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Light = 0x7f1302fa
studio.takasaki.clubm:attr/tabStyle = 0x7f040439
studio.takasaki.clubm:attr/autofillInlineSuggestionTitle = 0x7f040049
studio.takasaki.clubm:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401ff
studio.takasaki.clubm:attr/layout_constraintWidth_min = 0x7f0402b6
studio.takasaki.clubm:styleable/Slider = 0x7f140080
studio.takasaki.clubm:attr/tabSelectedTextColor = 0x7f040438
studio.takasaki.clubm:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070127
studio.takasaki.clubm:id/navigation_bar_item_icon_container = 0x7f090155
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600c3
studio.takasaki.clubm:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1300c9
studio.takasaki.clubm:drawable/design_password_eye = 0x7f0800d8
studio.takasaki.clubm:attr/subtitle = 0x7f040411
studio.takasaki.clubm:attr/fontProviderQuery = 0x7f040220
studio.takasaki.clubm:attr/fastScrollEnabled = 0x7f0401f1
studio.takasaki.clubm:attr/tabPaddingStart = 0x7f040433
studio.takasaki.clubm:attr/tabPaddingBottom = 0x7f040431
studio.takasaki.clubm:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701a3
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0600de
studio.takasaki.clubm:attr/tabMode = 0x7f04042f
studio.takasaki.clubm:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f130186
studio.takasaki.clubm:dimen/abc_action_bar_content_inset_material = 0x7f070000
studio.takasaki.clubm:color/material_dynamic_color_dark_error_container = 0x7f060237
studio.takasaki.clubm:attr/tabInlineLabel = 0x7f04042c
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f070209
studio.takasaki.clubm:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07015d
studio.takasaki.clubm:macro/m3_comp_fab_secondary_container_color = 0x7f0d003b
studio.takasaki.clubm:attr/tickRadiusInactive = 0x7f04048c
studio.takasaki.clubm:attr/tabIndicatorAnimationDuration = 0x7f040426
studio.takasaki.clubm:dimen/mtrl_shape_corner_size_small_component = 0x7f0702e8
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Item = 0x7f1303d8
studio.takasaki.clubm:color/m3_navigation_item_background_color = 0x7f0600ae
studio.takasaki.clubm:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
studio.takasaki.clubm:attr/layout_scrollInterpolator = 0x7f0402c6
studio.takasaki.clubm:attr/tabGravity = 0x7f040422
studio.takasaki.clubm:dimen/abc_button_inset_vertical_material = 0x7f070013
studio.takasaki.clubm:attr/tabContentStart = 0x7f040421
studio.takasaki.clubm:id/wide = 0x7f09021f
studio.takasaki.clubm:attr/progressBarImageScaleType = 0x7f040395
studio.takasaki.clubm:attr/switchTextAppearance = 0x7f04041f
studio.takasaki.clubm:attr/chipCornerRadius = 0x7f0400b8
studio.takasaki.clubm:color/m3_ref_palette_secondary80 = 0x7f060159
studio.takasaki.clubm:color/mtrl_tabs_colored_ripple_color = 0x7f0602ff
studio.takasaki.clubm:attr/thumbIconSize = 0x7f04047a
studio.takasaki.clubm:color/abc_search_url_text_normal = 0x7f06000e
studio.takasaki.clubm:dimen/mtrl_btn_corner_radius = 0x7f07025d
studio.takasaki.clubm:attr/switchPadding = 0x7f04041d
studio.takasaki.clubm:drawable/avd_show_password = 0x7f0800b7
studio.takasaki.clubm:color/m3_bottom_sheet_drag_handle_color = 0x7f06007f
studio.takasaki.clubm:dimen/mtrl_slider_widget_height = 0x7f0702f3
studio.takasaki.clubm:attr/progressBarPadding = 0x7f040396
studio.takasaki.clubm:attr/offsetAlignmentMode = 0x7f04035f
studio.takasaki.clubm:raw/firebase_common_keep = 0x7f110000
studio.takasaki.clubm:attr/cornerSizeTopRight = 0x7f04014a
studio.takasaki.clubm:attr/suggestionRowLayout = 0x7f040419
studio.takasaki.clubm:string/status_bar_notification_info_overflow = 0x7f12011b
studio.takasaki.clubm:attr/roundTopStart = 0x7f0403b4
studio.takasaki.clubm:attr/tickColorInactive = 0x7f040487
studio.takasaki.clubm:attr/suffixTextColor = 0x7f040418
studio.takasaki.clubm:color/m3_card_stroke_color = 0x7f060089
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f13022a
studio.takasaki.clubm:attr/textAppearanceSubtitle1 = 0x7f040462
studio.takasaki.clubm:attr/suffixTextAppearance = 0x7f040417
studio.takasaki.clubm:dimen/mtrl_btn_text_btn_padding_right = 0x7f070272
studio.takasaki.clubm:attr/showText = 0x7f0403dc
studio.takasaki.clubm:color/bright_foreground_inverse_material_light = 0x7f060025
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13011d
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f0601a2
studio.takasaki.clubm:dimen/mtrl_slider_track_side_padding = 0x7f0702f2
studio.takasaki.clubm:attr/passwordToggleTintMode = 0x7f040379
studio.takasaki.clubm:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07024b
studio.takasaki.clubm:animator/m3_card_elevated_state_list_anim = 0x7f02000c
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant30 = 0x7f06013a
studio.takasaki.clubm:attr/textAppearanceBodyLarge = 0x7f040444
studio.takasaki.clubm:id/content = 0x7f09008e
studio.takasaki.clubm:attr/suffixText = 0x7f040416
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0d008d
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0d0060
studio.takasaki.clubm:attr/subtitleTextStyle = 0x7f040415
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f07015f
studio.takasaki.clubm:dimen/m3_sys_elevation_level3 = 0x7f0701f7
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070165
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1302ed
studio.takasaki.clubm:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070160
studio.takasaki.clubm:attr/subtitleTextColor = 0x7f040414
studio.takasaki.clubm:attr/materialIconButtonFilledTonalStyle = 0x7f040307
studio.takasaki.clubm:attr/transitionEasing = 0x7f0404bc
studio.takasaki.clubm:attr/submitBackground = 0x7f040410
studio.takasaki.clubm:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f13008e
studio.takasaki.clubm:attr/subheaderInsetEnd = 0x7f04040d
studio.takasaki.clubm:attr/statusBarForeground = 0x7f040407
studio.takasaki.clubm:attr/textAppearanceHeadlineLarge = 0x7f040452
studio.takasaki.clubm:id/transitionToEnd = 0x7f090204
studio.takasaki.clubm:dimen/m3_badge_with_text_vertical_offset = 0x7f0700be
studio.takasaki.clubm:styleable/OnSwipe = 0x7f14006e
studio.takasaki.clubm:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0d012a
studio.takasaki.clubm:attr/statusBarBackground = 0x7f040406
studio.takasaki.clubm:attr/state_indeterminate = 0x7f040402
studio.takasaki.clubm:attr/transitionShapeAppearance = 0x7f0404bf
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_light = 0x7f0800c6
studio.takasaki.clubm:attr/cropMinCropWindowHeight = 0x7f04016a
studio.takasaki.clubm:color/m3_tabs_text_color_secondary = 0x7f06021b
studio.takasaki.clubm:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08006b
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f13021a
studio.takasaki.clubm:integer/m3_sys_motion_duration_short2 = 0x7f0a001e
studio.takasaki.clubm:attr/textAppearanceListItemSecondary = 0x7f04045b
studio.takasaki.clubm:attr/layout_editor_absoluteY = 0x7f0402ba
studio.takasaki.clubm:attr/materialDisplayDividerStyle = 0x7f040303
studio.takasaki.clubm:attr/telltales_tailColor = 0x7f04043e
studio.takasaki.clubm:id/title = 0x7f0901fb
studio.takasaki.clubm:id/contiguous = 0x7f090090
studio.takasaki.clubm:attr/state_dragged = 0x7f040400
studio.takasaki.clubm:attr/state_collapsible = 0x7f0403ff
studio.takasaki.clubm:attr/startIconTint = 0x7f0403fb
studio.takasaki.clubm:color/browser_actions_title_color = 0x7f06002b
studio.takasaki.clubm:attr/customDimension = 0x7f040181
studio.takasaki.clubm:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f0800cd
studio.takasaki.clubm:attr/startIconDrawable = 0x7f0403f8
studio.takasaki.clubm:attr/minTouchTargetSize = 0x7f040325
studio.takasaki.clubm:attr/startIconCheckable = 0x7f0403f6
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601b4
studio.takasaki.clubm:attr/stackFromEnd = 0x7f0403f4
studio.takasaki.clubm:styleable/ForegroundLinearLayout = 0x7f140038
studio.takasaki.clubm:id/scroll = 0x7f0901a8
studio.takasaki.clubm:anim/rns_ios_from_left_foreground_close = 0x7f01003c
studio.takasaki.clubm:attr/splashScreenIconSize = 0x7f0403f1
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1302f8
studio.takasaki.clubm:string/abc_activity_chooser_view_see_all = 0x7f120006
studio.takasaki.clubm:attr/spinnerStyle = 0x7f0403f0
studio.takasaki.clubm:attr/snackbarTextViewStyle = 0x7f0403ec
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0d00db
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0d00ce
studio.takasaki.clubm:id/multiply = 0x7f090153
studio.takasaki.clubm:attr/cropMinCropResultWidthPX = 0x7f040169
studio.takasaki.clubm:attr/snackbarButtonStyle = 0x7f0403ea
studio.takasaki.clubm:attr/fastScrollHorizontalTrackDrawable = 0x7f0401f3
studio.takasaki.clubm:color/abc_search_url_text_selected = 0x7f060010
studio.takasaki.clubm:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f130153
studio.takasaki.clubm:attr/sizePercent = 0x7f0403e8
studio.takasaki.clubm:dimen/compat_button_inset_horizontal_material = 0x7f070059
studio.takasaki.clubm:attr/toolbarNavigationButtonStyle = 0x7f0404a2
studio.takasaki.clubm:style/TextAppearance.AppCompat.Display2 = 0x7f1301aa
studio.takasaki.clubm:id/BOTTOM_END = 0x7f090001
studio.takasaki.clubm:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f07016a
studio.takasaki.clubm:drawable/mtrl_switch_thumb_checked_pressed = 0x7f08012c
studio.takasaki.clubm:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700a1
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_primary = 0x7f0601cd
studio.takasaki.clubm:attr/titleMarginStart = 0x7f040498
studio.takasaki.clubm:styleable/TextInputEditText = 0x7f14008e
studio.takasaki.clubm:style/Widget.AppCompat.RatingBar.Small = 0x7f13034c
studio.takasaki.clubm:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f13018c
studio.takasaki.clubm:string/pick_image_camera = 0x7f120103
studio.takasaki.clubm:attr/windowMinWidthMajor = 0x7f0404db
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f130479
studio.takasaki.clubm:attr/singleLine = 0x7f0403e6
studio.takasaki.clubm:color/design_fab_stroke_end_outer_color = 0x7f060064
studio.takasaki.clubm:attr/simpleItemLayout = 0x7f0403e1
studio.takasaki.clubm:color/m3_sys_color_dark_surface_bright = 0x7f06018e
studio.takasaki.clubm:attr/sideSheetModalStyle = 0x7f0403e0
studio.takasaki.clubm:attr/shrinkMotionSpec = 0x7f0403de
studio.takasaki.clubm:attr/behavior_fitToContents = 0x7f040070
studio.takasaki.clubm:drawable/abc_cab_background_top_material = 0x7f080076
studio.takasaki.clubm:attr/showMotionSpec = 0x7f0403da
studio.takasaki.clubm:attr/thumbTintMode = 0x7f040482
studio.takasaki.clubm:attr/showAsAction = 0x7f0403d6
studio.takasaki.clubm:dimen/m3_comp_filter_chip_container_height = 0x7f07012f
studio.takasaki.clubm:color/m3_sys_color_light_surface_container_highest = 0x7f060203
studio.takasaki.clubm:attr/showAnimationBehavior = 0x7f0403d5
studio.takasaki.clubm:color/androidx_core_ripple_material_light = 0x7f06001b
studio.takasaki.clubm:attr/counterOverflowTextAppearance = 0x7f04014d
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0d00b2
studio.takasaki.clubm:attr/shortcutMatchRequired = 0x7f0403d3
studio.takasaki.clubm:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702e4
studio.takasaki.clubm:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1302d3
studio.takasaki.clubm:dimen/m3_comp_filled_card_container_elevation = 0x7f070128
studio.takasaki.clubm:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1303e2
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1302f1
studio.takasaki.clubm:attr/colorSecondaryFixed = 0x7f040112
studio.takasaki.clubm:attr/region_widthLessThan = 0x7f0403a3
studio.takasaki.clubm:attr/shapeAppearanceLargeComponent = 0x7f0403ce
studio.takasaki.clubm:string/dev_launcher_error_details = 0x7f120067
studio.takasaki.clubm:attr/itemPaddingBottom = 0x7f040268
studio.takasaki.clubm:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700a9
studio.takasaki.clubm:attr/shapeAppearanceCornerMedium = 0x7f0403cc
studio.takasaki.clubm:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f130059
studio.takasaki.clubm:attr/compatShadowEnabled = 0x7f040125
studio.takasaki.clubm:attr/colorOnSecondaryFixedVariant = 0x7f0400fd
studio.takasaki.clubm:attr/shapeAppearanceCornerExtraLarge = 0x7f0403c9
studio.takasaki.clubm:layout/support_simple_spinner_dropdown_item = 0x7f0c0086
studio.takasaki.clubm:interpolator/m3_sys_motion_easing_linear = 0x7f0b000a
studio.takasaki.clubm:attr/thumbRadius = 0x7f04047d
studio.takasaki.clubm:style/Base.Theme.Material3.Light = 0x7f130062
studio.takasaki.clubm:attr/shapeAppearance = 0x7f0403c8
studio.takasaki.clubm:color/material_timepicker_modebutton_tint = 0x7f0602d2
studio.takasaki.clubm:attr/checkedIconMargin = 0x7f0400b1
studio.takasaki.clubm:attr/selectorSize = 0x7f0403c7
studio.takasaki.clubm:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0d011f
studio.takasaki.clubm:dimen/abc_floating_window_z = 0x7f07002f
studio.takasaki.clubm:color/material_blue_grey_950 = 0x7f060231
studio.takasaki.clubm:attr/selectionRequired = 0x7f0403c6
studio.takasaki.clubm:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f130047
studio.takasaki.clubm:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070230
studio.takasaki.clubm:style/TextAppearance.Material3.TitleMedium = 0x7f13020b
studio.takasaki.clubm:attr/selectableItemBackgroundBorderless = 0x7f0403c5
studio.takasaki.clubm:attr/motionEasingDecelerated = 0x7f04033f
studio.takasaki.clubm:style/TextAppearance.Material3.LabelMedium = 0x7f130204
studio.takasaki.clubm:attr/textAppearanceTitleMedium = 0x7f040465
studio.takasaki.clubm:string/use_face_or_screen_lock_label = 0x7f120123
studio.takasaki.clubm:color/m3_ref_palette_neutral40 = 0x7f060128
studio.takasaki.clubm:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07025b
studio.takasaki.clubm:dimen/design_bottom_navigation_margin = 0x7f07006b
studio.takasaki.clubm:attr/searchViewStyle = 0x7f0403c2
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600be
studio.takasaki.clubm:id/deltaRelative = 0x7f0900a0
studio.takasaki.clubm:attr/searchIcon = 0x7f0403c0
studio.takasaki.clubm:attr/scrimVisibleHeightTrigger = 0x7f0403be
studio.takasaki.clubm:styleable/AppCompatTextHelper = 0x7f140010
studio.takasaki.clubm:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f130296
studio.takasaki.clubm:attr/layout_constraintCircle = 0x7f040295
studio.takasaki.clubm:attr/scrimBackground = 0x7f0403bd
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f13042f
studio.takasaki.clubm:color/material_dynamic_primary70 = 0x7f060260
studio.takasaki.clubm:attr/scrimAnimationDuration = 0x7f0403bc
studio.takasaki.clubm:attr/title = 0x7f040491
studio.takasaki.clubm:attr/scopeUris = 0x7f0403bb
studio.takasaki.clubm:styleable/GradientColorItem = 0x7f14003d
studio.takasaki.clubm:attr/autoSizePresetSizes = 0x7f040041
studio.takasaki.clubm:attr/shapeAppearanceMediumComponent = 0x7f0403cf
studio.takasaki.clubm:animator/design_appbar_state_list_animator = 0x7f020000
studio.takasaki.clubm:dimen/m3_chip_disabled_translation_z = 0x7f0700f9
studio.takasaki.clubm:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701a9
studio.takasaki.clubm:attr/roundingBorderColor = 0x7f0403b7
studio.takasaki.clubm:attr/contentPaddingStart = 0x7f040138
studio.takasaki.clubm:id/accessibility_action_clickable_span = 0x7f090012
studio.takasaki.clubm:attr/materialButtonToggleGroupStyle = 0x7f0402ed
studio.takasaki.clubm:attr/dragScale = 0x7f0401a0
studio.takasaki.clubm:attr/roundBottomStart = 0x7f0403af
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_homefilledactiveiconlight = 0x7f080046
studio.takasaki.clubm:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f130492
studio.takasaki.clubm:attr/roundBottomLeft = 0x7f0403ad
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f130044
studio.takasaki.clubm:attr/retryImage = 0x7f0403a6
studio.takasaki.clubm:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0a0023
studio.takasaki.clubm:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
studio.takasaki.clubm:attr/removeEmbeddedFabElevation = 0x7f0403a5
studio.takasaki.clubm:color/dev_launcher_errorLogButton = 0x7f06006c
studio.takasaki.clubm:attr/colorPrimaryVariant = 0x7f04010e
studio.takasaki.clubm:attr/region_heightMoreThan = 0x7f0403a2
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f13002b
studio.takasaki.clubm:id/off = 0x7f090162
studio.takasaki.clubm:attr/textAppearanceTitleSmall = 0x7f040466
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialDivider = 0x7f130465
studio.takasaki.clubm:attr/itemStrokeWidth = 0x7f040274
studio.takasaki.clubm:string/mtrl_switch_track_decoration_path = 0x7f1200fa
studio.takasaki.clubm:attr/ratingBarStyleSmall = 0x7f04039f
studio.takasaki.clubm:style/Widget.Design.TabLayout = 0x7f130369
studio.takasaki.clubm:color/bright_foreground_disabled_material_light = 0x7f060023
studio.takasaki.clubm:attr/layout_constraintBaseline_creator = 0x7f040290
studio.takasaki.clubm:attr/badgeRadius = 0x7f040059
studio.takasaki.clubm:attr/placeholder_emptyVisibility = 0x7f040387
studio.takasaki.clubm:attr/queryBackground = 0x7f040398
studio.takasaki.clubm:attr/colorButtonNormal = 0x7f0400e9
studio.takasaki.clubm:attr/progressBarAutoRotateInterval = 0x7f040393
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f130143
studio.takasaki.clubm:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702c1
studio.takasaki.clubm:styleable/MenuItem = 0x7f140061
studio.takasaki.clubm:style/ShapeAppearance.Material3.Tooltip = 0x7f130188
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0d0007
studio.takasaki.clubm:attr/preserveIconSpacing = 0x7f040390
studio.takasaki.clubm:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07023d
studio.takasaki.clubm:attr/materialClockStyle = 0x7f040302
studio.takasaki.clubm:attr/reverseLayout = 0x7f0403a8
studio.takasaki.clubm:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
studio.takasaki.clubm:style/TextAppearance.Material3.HeadlineSmall = 0x7f130202
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant50 = 0x7f06013c
studio.takasaki.clubm:attr/popupTheme = 0x7f04038a
studio.takasaki.clubm:attr/popupMenuBackground = 0x7f040388
studio.takasaki.clubm:attr/buttonGravity = 0x7f040092
studio.takasaki.clubm:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f13010d
studio.takasaki.clubm:drawable/ic_call_answer = 0x7f0800e2
studio.takasaki.clubm:attr/singleChoiceItemLayout = 0x7f0403e5
studio.takasaki.clubm:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
studio.takasaki.clubm:color/material_personalized_color_tertiary = 0x7f0602bd
studio.takasaki.clubm:attr/cropInitialCropWindowPaddingRatio = 0x7f040164
studio.takasaki.clubm:attr/endIconTintMode = 0x7f0401c4
studio.takasaki.clubm:attr/placeholderTextAppearance = 0x7f040385
studio.takasaki.clubm:drawable/$avd_show_password__0 = 0x7f080003
studio.takasaki.clubm:attr/placeholderImage = 0x7f040382
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0d0061
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601d2
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Caption = 0x7f130211
studio.takasaki.clubm:attr/layout_goneMarginLeft = 0x7f0402bd
studio.takasaki.clubm:attr/percentY = 0x7f04037f
studio.takasaki.clubm:style/Widget.Material3.Chip.Input.Icon = 0x7f13039b
studio.takasaki.clubm:attr/path_percent = 0x7f04037b
studio.takasaki.clubm:string/catalyst_reload_button = 0x7f120046
studio.takasaki.clubm:color/m3_sys_color_light_tertiary_container = 0x7f060209
studio.takasaki.clubm:attr/layout_collapseParallaxMultiplier = 0x7f04028d
studio.takasaki.clubm:dimen/m3_searchview_elevation = 0x7f0701e7
studio.takasaki.clubm:color/material_slider_halo_color = 0x7f0602ca
studio.takasaki.clubm:attr/pathMotionArc = 0x7f04037a
studio.takasaki.clubm:dimen/m3_comp_fab_primary_small_container_height = 0x7f070123
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f130154
studio.takasaki.clubm:string/mtrl_checkbox_button_path_unchecked = 0x7f1200c8
studio.takasaki.clubm:attr/customIntegerValue = 0x7f040183
studio.takasaki.clubm:drawable/common_google_signin_btn_text_dark_focused = 0x7f0800cb
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1301c4
studio.takasaki.clubm:attr/passwordToggleContentDescription = 0x7f040375
studio.takasaki.clubm:attr/panelMenuListTheme = 0x7f040373
studio.takasaki.clubm:color/design_dark_default_color_secondary = 0x7f06004f
studio.takasaki.clubm:id/right_side = 0x7f090196
studio.takasaki.clubm:attr/paddingTopNoTitle = 0x7f040370
studio.takasaki.clubm:attr/paddingRightSystemWindowInsets = 0x7f04036d
studio.takasaki.clubm:attr/paddingLeftSystemWindowInsets = 0x7f04036c
studio.takasaki.clubm:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0d00da
studio.takasaki.clubm:attr/paddingBottomSystemWindowInsets = 0x7f04036a
studio.takasaki.clubm:color/m3_sys_color_light_on_surface_variant = 0x7f0601f6
studio.takasaki.clubm:color/design_dark_default_color_surface = 0x7f060051
studio.takasaki.clubm:dimen/abc_text_size_display_4_material = 0x7f070046
studio.takasaki.clubm:attr/overlayImage = 0x7f040368
studio.takasaki.clubm:attr/onShow = 0x7f040364
studio.takasaki.clubm:style/Theme.Design = 0x7f13023e
studio.takasaki.clubm:attr/onPositiveCross = 0x7f040363
studio.takasaki.clubm:style/Platform.Widget.AppCompat.Spinner = 0x7f130152
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0d00b7
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600c2
studio.takasaki.clubm:attr/drawerArrowStyle = 0x7f0401ac
studio.takasaki.clubm:attr/onHide = 0x7f040361
studio.takasaki.clubm:color/m3_chip_stroke_color = 0x7f06008f
studio.takasaki.clubm:attr/appBarLayoutStyle = 0x7f040036
studio.takasaki.clubm:attr/colorSurfaceDim = 0x7f04011c
studio.takasaki.clubm:attr/waveOffset = 0x7f0404d0
studio.takasaki.clubm:attr/numericModifiers = 0x7f04035e
studio.takasaki.clubm:dimen/subtitle_outline_width = 0x7f070327
studio.takasaki.clubm:color/m3_ref_palette_neutral12 = 0x7f060121
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0d008f
studio.takasaki.clubm:attr/textInputOutlinedDenseStyle = 0x7f04046e
studio.takasaki.clubm:attr/popupMenuStyle = 0x7f040389
studio.takasaki.clubm:attr/logoScaleType = 0x7f0402e0
studio.takasaki.clubm:color/m3_ref_palette_neutral92 = 0x7f060130
studio.takasaki.clubm:attr/number = 0x7f04035d
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Menu = 0x7f13002a
studio.takasaki.clubm:attr/lineSpacing = 0x7f0402cc
studio.takasaki.clubm:attr/nestedScrollViewStyle = 0x7f04035b
studio.takasaki.clubm:styleable/MaterialAutoCompleteTextView = 0x7f140050
studio.takasaki.clubm:dimen/hint_pressed_alpha_material_dark = 0x7f07009c
studio.takasaki.clubm:attr/navigationViewStyle = 0x7f040359
studio.takasaki.clubm:attr/windowFixedWidthMinor = 0x7f0404da
studio.takasaki.clubm:attr/navigationMode = 0x7f040357
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0d00cd
studio.takasaki.clubm:attr/navigationContentDescription = 0x7f040354
studio.takasaki.clubm:attr/moveWhenScrollAtTop = 0x7f040352
studio.takasaki.clubm:attr/motionTarget = 0x7f04034f
studio.takasaki.clubm:attr/motionProgress = 0x7f04034d
studio.takasaki.clubm:attr/closeIconVisible = 0x7f0400d9
studio.takasaki.clubm:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0d009e
studio.takasaki.clubm:attr/motionEasingStandardInterpolator = 0x7f040349
studio.takasaki.clubm:attr/motionEasingStandardDecelerateInterpolator = 0x7f040348
studio.takasaki.clubm:style/Widget.AppCompat.ActionBar.Solid = 0x7f130311
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601b6
studio.takasaki.clubm:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040342
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600cc
studio.takasaki.clubm:attr/motionEasingEmphasized = 0x7f040340
studio.takasaki.clubm:dimen/m3_btn_text_btn_padding_right = 0x7f0700e5
studio.takasaki.clubm:dimen/design_snackbar_padding_vertical = 0x7f070089
studio.takasaki.clubm:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f040341
studio.takasaki.clubm:style/Theme.ReactNative.AppCompat.Light = 0x7f130298
studio.takasaki.clubm:id/path = 0x7f09017d
studio.takasaki.clubm:attr/motionEasingAccelerated = 0x7f04033e
studio.takasaki.clubm:attr/pivotAnchor = 0x7f040381
studio.takasaki.clubm:id/scrollView = 0x7f0901ab
studio.takasaki.clubm:id/accessibility_custom_action_22 = 0x7f090025
studio.takasaki.clubm:attr/motionDurationShort2 = 0x7f04033b
studio.takasaki.clubm:attr/motionDurationShort1 = 0x7f04033a
studio.takasaki.clubm:id/disablePostScroll = 0x7f0900aa
studio.takasaki.clubm:color/m3_sys_color_light_secondary = 0x7f0601fd
studio.takasaki.clubm:attr/simpleItems = 0x7f0403e4
studio.takasaki.clubm:attr/motionDurationMedium2 = 0x7f040337
studio.takasaki.clubm:attr/listItemLayout = 0x7f0402d2
studio.takasaki.clubm:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
studio.takasaki.clubm:attr/motionDurationLong2 = 0x7f040333
studio.takasaki.clubm:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070137
studio.takasaki.clubm:attr/motionDurationExtraLong4 = 0x7f040331
studio.takasaki.clubm:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0d0120
studio.takasaki.clubm:macro/m3_comp_filter_chip_container_shape = 0x7f0d0057
studio.takasaki.clubm:attr/closeItemLayout = 0x7f0400da
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f130037
studio.takasaki.clubm:attr/motionDebug = 0x7f04032d
studio.takasaki.clubm:array/assume_strong_biometrics_models = 0x7f030000
studio.takasaki.clubm:macro/m3_comp_extended_fab_surface_container_color = 0x7f0d0032
studio.takasaki.clubm:attr/clickAction = 0x7f0400ce
studio.takasaki.clubm:attr/windowSplashScreenAnimationDuration = 0x7f0404df
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070110
studio.takasaki.clubm:layout/notification_template_media = 0x7f0c007a
studio.takasaki.clubm:attr/errorIconDrawable = 0x7f0401cc
studio.takasaki.clubm:id/blocking = 0x7f09006a
studio.takasaki.clubm:attr/shouldRemoveExpandedCorners = 0x7f0403d4
studio.takasaki.clubm:attr/motionDurationShort4 = 0x7f04033d
studio.takasaki.clubm:attr/itemIconTint = 0x7f040264
studio.takasaki.clubm:id/line1 = 0x7f090110
studio.takasaki.clubm:color/button_material_dark = 0x7f06002c
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0d00c2
studio.takasaki.clubm:attr/mock_showDiagonals = 0x7f04032b
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f1303a2
studio.takasaki.clubm:attr/materialTimePickerStyle = 0x7f040311
studio.takasaki.clubm:id/legacy = 0x7f09010e
studio.takasaki.clubm:id/adjust_width = 0x7f090053
studio.takasaki.clubm:attr/layout_constrainedWidth = 0x7f04028f
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080016
studio.takasaki.clubm:attr/navigationIcon = 0x7f040355
studio.takasaki.clubm:string/mtrl_picker_cancel = 0x7f1200d4
studio.takasaki.clubm:attr/mock_label = 0x7f040328
studio.takasaki.clubm:dimen/subtitle_corner_radius = 0x7f070326
studio.takasaki.clubm:id/tag_unhandled_key_listeners = 0x7f0901e8
studio.takasaki.clubm:attr/mock_diagonalsColor = 0x7f040327
studio.takasaki.clubm:attr/menu = 0x7f04031f
studio.takasaki.clubm:attr/layout_constraintRight_toRightOf = 0x7f0402aa
studio.takasaki.clubm:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010024
studio.takasaki.clubm:styleable/AppCompatTextView = 0x7f140011
studio.takasaki.clubm:attr/measureWithLargestChild = 0x7f04031e
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1303d7
studio.takasaki.clubm:attr/maxWidth = 0x7f04031d
studio.takasaki.clubm:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0d00f7
studio.takasaki.clubm:dimen/mtrl_card_checked_icon_size = 0x7f0702a0
studio.takasaki.clubm:attr/yearTodayStyle = 0x7f0404e4
studio.takasaki.clubm:attr/motionEasingStandardAccelerateInterpolator = 0x7f040347
studio.takasaki.clubm:dimen/m3_fab_translation_z_pressed = 0x7f0701bc
studio.takasaki.clubm:style/Widget.Material3.CardView.Filled = 0x7f130392
studio.takasaki.clubm:attr/maxImageSize = 0x7f040319
studio.takasaki.clubm:drawable/abc_list_pressed_holo_dark = 0x7f08008e
studio.takasaki.clubm:drawable/ic_resume = 0x7f0800f7
studio.takasaki.clubm:dimen/mtrl_switch_thumb_size = 0x7f0702fd
studio.takasaki.clubm:color/abc_tint_edittext = 0x7f060015
studio.takasaki.clubm:dimen/fastscroll_minimum_range = 0x7f070095
studio.takasaki.clubm:attr/maxHeight = 0x7f040318
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130483
studio.takasaki.clubm:style/Widget.Material3.AppBarLayout = 0x7f13036e
studio.takasaki.clubm:color/material_personalized_color_secondary = 0x7f0602af
studio.takasaki.clubm:attr/layout_constraintBaseline_toBaselineOf = 0x7f040291
studio.takasaki.clubm:attr/autoAdjustToWithinGrandparentBounds = 0x7f04003c
studio.takasaki.clubm:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601e5
studio.takasaki.clubm:attr/maxAcceleration = 0x7f040314
studio.takasaki.clubm:id/tag_accessibility_pane_title = 0x7f0901e0
studio.takasaki.clubm:id/slide = 0x7f0901c0
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0701fc
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600fb
studio.takasaki.clubm:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f130234
studio.takasaki.clubm:attr/shapeAppearanceOverlay = 0x7f0403d0
studio.takasaki.clubm:attr/materialTimePickerTheme = 0x7f040312
studio.takasaki.clubm:style/Base.ThemeOverlay.Material3.Dialog = 0x7f130087
studio.takasaki.clubm:string/error_icon_content_description = 0x7f12006c
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0d0167
studio.takasaki.clubm:color/material_personalized_color_surface_variant = 0x7f0602bc
studio.takasaki.clubm:attr/materialSwitchStyle = 0x7f04030f
studio.takasaki.clubm:id/animateToEnd = 0x7f090059
studio.takasaki.clubm:attr/materialSearchViewToolbarHeight = 0x7f04030d
studio.takasaki.clubm:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700c6
studio.takasaki.clubm:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0d0009
studio.takasaki.clubm:attr/listPreferredItemHeightSmall = 0x7f0402d8
studio.takasaki.clubm:attr/passwordToggleEnabled = 0x7f040377
studio.takasaki.clubm:attr/centerIfNoTextEnabled = 0x7f0400a6
studio.takasaki.clubm:attr/materialIconButtonOutlinedStyle = 0x7f040308
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600cb
studio.takasaki.clubm:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13046e
studio.takasaki.clubm:dimen/abc_button_padding_vertical_material = 0x7f070015
studio.takasaki.clubm:attr/imageAspectRatioAdjust = 0x7f04024c
studio.takasaki.clubm:color/design_icon_tint = 0x7f060067
studio.takasaki.clubm:attr/perpendicularPath_percent = 0x7f040380
studio.takasaki.clubm:dimen/abc_text_size_menu_material = 0x7f07004b
studio.takasaki.clubm:attr/flow_horizontalGap = 0x7f04020c
studio.takasaki.clubm:attr/materialDividerStyle = 0x7f040305
studio.takasaki.clubm:attr/materialDividerHeavyStyle = 0x7f040304
studio.takasaki.clubm:drawable/ic_mtrl_chip_close_circle = 0x7f0800f3
studio.takasaki.clubm:attr/chipGroupStyle = 0x7f0400ba
studio.takasaki.clubm:dimen/m3_comp_fab_primary_container_height = 0x7f07011a
studio.takasaki.clubm:animator/m3_appbar_state_list_animator = 0x7f020009
studio.takasaki.clubm:attr/materialIconButtonFilledStyle = 0x7f040306
studio.takasaki.clubm:id/honorRequest = 0x7f0900f2
studio.takasaki.clubm:attr/colorSecondaryContainer = 0x7f040111
studio.takasaki.clubm:attr/materialCardViewStyle = 0x7f040300
studio.takasaki.clubm:color/mtrl_fab_icon_text_color_selector = 0x7f0602e8
studio.takasaki.clubm:attr/materialCardViewElevatedStyle = 0x7f0402fd
studio.takasaki.clubm:color/m3_ref_palette_error95 = 0x7f06011c
studio.takasaki.clubm:attr/materialCalendarYearNavigationButton = 0x7f0402fc
studio.takasaki.clubm:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601e3
studio.takasaki.clubm:attr/materialCalendarTheme = 0x7f0402fb
studio.takasaki.clubm:attr/layout_constraintCircleRadius = 0x7f040297
studio.takasaki.clubm:attr/forceApplySystemWindowInsetTop = 0x7f040225
studio.takasaki.clubm:color/m3_chip_text_color = 0x7f060090
studio.takasaki.clubm:style/Base.Widget.AppCompat.SearchView = 0x7f1300f8
studio.takasaki.clubm:attr/onNegativeCross = 0x7f040362
studio.takasaki.clubm:attr/tabIndicatorGravity = 0x7f04042a
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1301f7
studio.takasaki.clubm:style/SpinnerDatePickerStyle = 0x7f1301a3
studio.takasaki.clubm:attr/layoutDuringTransition = 0x7f040287
studio.takasaki.clubm:attr/region_heightLessThan = 0x7f0403a1
studio.takasaki.clubm:attr/materialCalendarHeaderLayout = 0x7f0402f4
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f130034
studio.takasaki.clubm:id/text2 = 0x7f0901eb
studio.takasaki.clubm:drawable/design_fab_background = 0x7f0800d5
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600c4
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070202
studio.takasaki.clubm:dimen/design_snackbar_max_width = 0x7f070086
studio.takasaki.clubm:attr/bottomSheetDialogTheme = 0x7f04007d
studio.takasaki.clubm:attr/textAppearanceHeadlineSmall = 0x7f040454
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f130175
studio.takasaki.clubm:attr/motion_postLayoutCollision = 0x7f040350
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0d00c7
studio.takasaki.clubm:attr/indicatorDirectionCircular = 0x7f040251
studio.takasaki.clubm:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f07017e
studio.takasaki.clubm:dimen/design_snackbar_background_corner_radius = 0x7f070083
studio.takasaki.clubm:attr/materialCalendarDayOfWeekLabel = 0x7f0402ef
studio.takasaki.clubm:color/m3_ref_palette_primary90 = 0x7f06014d
studio.takasaki.clubm:id/textinput_prefix_text = 0x7f0901f8
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600e6
studio.takasaki.clubm:attr/layout_constraintWidth_max = 0x7f0402b5
studio.takasaki.clubm:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701ad
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton = 0x7f130430
studio.takasaki.clubm:attr/fabAnchorMode = 0x7f0401e7
studio.takasaki.clubm:styleable/MaterialCardView = 0x7f140055
studio.takasaki.clubm:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f08012d
studio.takasaki.clubm:attr/materialButtonStyle = 0x7f0402ec
studio.takasaki.clubm:attr/materialSearchViewStyle = 0x7f04030c
studio.takasaki.clubm:color/material_on_surface_disabled = 0x7f060290
studio.takasaki.clubm:attr/transitionDisable = 0x7f0404bb
studio.takasaki.clubm:attr/materialAlertDialogTitleTextStyle = 0x7f0402ea
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_dark_normal = 0x7f0800c3
studio.takasaki.clubm:color/m3_assist_chip_icon_tint_color = 0x7f06007d
studio.takasaki.clubm:color/m3_sys_color_dark_secondary = 0x7f06018b
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f0601a0
studio.takasaki.clubm:attr/materialAlertDialogTitlePanelStyle = 0x7f0402e9
studio.takasaki.clubm:style/Base.V26.Theme.AppCompat = 0x7f1300bb
studio.takasaki.clubm:attr/layout_constraintTag = 0x7f0402ad
studio.takasaki.clubm:color/material_timepicker_clockface = 0x7f0602d1
studio.takasaki.clubm:style/Base.Widget.AppCompat.ImageButton = 0x7f1300e2
studio.takasaki.clubm:attr/editTextBackground = 0x7f0401b4
studio.takasaki.clubm:string/abc_activitychooserview_choose_application = 0x7f120007
studio.takasaki.clubm:attr/materialAlertDialogTitleIconStyle = 0x7f0402e8
studio.takasaki.clubm:attr/materialAlertDialogTheme = 0x7f0402e7
studio.takasaki.clubm:id/mtrl_picker_fullscreen = 0x7f090149
studio.takasaki.clubm:id/barrier = 0x7f090066
studio.takasaki.clubm:id/accessibility_custom_action_3 = 0x7f09002d
studio.takasaki.clubm:attr/colorSurfaceVariant = 0x7f04011e
studio.takasaki.clubm:style/TextAppearance.AppCompat.Headline = 0x7f1301ad
studio.takasaki.clubm:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0a003c
studio.takasaki.clubm:attr/cropTouchRadius = 0x7f040174
studio.takasaki.clubm:string/use_face_label = 0x7f120122
studio.takasaki.clubm:id/pin = 0x7f090181
studio.takasaki.clubm:color/m3_navigation_item_ripple_color = 0x7f0600b0
studio.takasaki.clubm:attr/marginTopSystemWindowInsets = 0x7f0402e4
studio.takasaki.clubm:layout/fingerprint_dialog_layout = 0x7f0c0038
studio.takasaki.clubm:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070130
studio.takasaki.clubm:attr/colorOnPrimary = 0x7f0400f5
studio.takasaki.clubm:attr/marginRightSystemWindowInsets = 0x7f0402e3
studio.takasaki.clubm:style/Widget.AppCompat.Light.PopupMenu = 0x7f13033c
studio.takasaki.clubm:attr/marginLeftSystemWindowInsets = 0x7f0402e2
studio.takasaki.clubm:attr/overlay = 0x7f040367
studio.takasaki.clubm:anim/rns_slide_in_from_left = 0x7f010047
studio.takasaki.clubm:attr/textAppearancePopupMenuHeader = 0x7f04045e
studio.takasaki.clubm:dimen/abc_control_inset_material = 0x7f070019
studio.takasaki.clubm:attr/textAppearanceLargePopupMenu = 0x7f040458
studio.takasaki.clubm:attr/logo = 0x7f0402dd
studio.takasaki.clubm:xml/image_share_filepaths = 0x7f150003
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Display = 0x7f130489
studio.takasaki.clubm:color/mtrl_chip_close_icon_tint = 0x7f0602e0
studio.takasaki.clubm:color/m3_slider_active_track_color = 0x7f06016d
studio.takasaki.clubm:attr/listPreferredItemPaddingStart = 0x7f0402dc
studio.takasaki.clubm:attr/listPreferredItemPaddingRight = 0x7f0402db
studio.takasaki.clubm:dimen/mtrl_navigation_rail_margin = 0x7f0702d5
studio.takasaki.clubm:attr/listPreferredItemPaddingLeft = 0x7f0402da
studio.takasaki.clubm:attr/listPreferredItemPaddingEnd = 0x7f0402d9
studio.takasaki.clubm:attr/tabPaddingTop = 0x7f040434
studio.takasaki.clubm:style/Base.V24.Theme.Material3.Dark = 0x7f1300b7
studio.takasaki.clubm:string/abc_prepend_shortcut_label = 0x7f120013
studio.takasaki.clubm:attr/errorIconTintMode = 0x7f0401ce
studio.takasaki.clubm:attr/listDividerAlertDialog = 0x7f0402d1
studio.takasaki.clubm:dimen/material_time_picker_minimum_screen_height = 0x7f070248
studio.takasaki.clubm:style/TextAppearance.Design.Suffix = 0x7f1301e7
studio.takasaki.clubm:attr/colorSurface = 0x7f040115
studio.takasaki.clubm:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f130410
studio.takasaki.clubm:color/m3_button_background_color_selector = 0x7f060080
studio.takasaki.clubm:styleable/NavigationBarActiveIndicator = 0x7f140069
studio.takasaki.clubm:attr/nestedScrollable = 0x7f04035c
studio.takasaki.clubm:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0d013d
studio.takasaki.clubm:attr/coordinatorLayoutStyle = 0x7f04013d
studio.takasaki.clubm:attr/lineHeight = 0x7f0402cb
studio.takasaki.clubm:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701a8
studio.takasaki.clubm:attr/cropBorderLineThickness = 0x7f04015a
studio.takasaki.clubm:attr/limitBoundsTo = 0x7f0402ca
studio.takasaki.clubm:attr/liftOnScrollTargetViewId = 0x7f0402c9
studio.takasaki.clubm:dimen/material_clock_hand_center_dot_radius = 0x7f07022b
studio.takasaki.clubm:dimen/mtrl_fab_min_touch_target = 0x7f0702bb
studio.takasaki.clubm:color/abc_background_cache_hint_selector_material_light = 0x7f060001
studio.takasaki.clubm:dimen/m3_btn_stroke_size = 0x7f0700e1
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.Dark = 0x7f1302a0
studio.takasaki.clubm:dimen/m3_chip_corner_size = 0x7f0700f8
studio.takasaki.clubm:attr/layout_constraintBottom_creator = 0x7f040292
studio.takasaki.clubm:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700a7
studio.takasaki.clubm:attr/layout_scrollEffect = 0x7f0402c4
studio.takasaki.clubm:color/material_dynamic_neutral_variant40 = 0x7f060250
studio.takasaki.clubm:id/checkbox = 0x7f090080
studio.takasaki.clubm:attr/trackCornerRadius = 0x7f0404b1
studio.takasaki.clubm:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07012a
studio.takasaki.clubm:drawable/notify_panel_notification_icon_bg = 0x7f080144
studio.takasaki.clubm:attr/layout_goneMarginStart = 0x7f0402bf
studio.takasaki.clubm:attr/windowActionBarOverlay = 0x7f0404d5
studio.takasaki.clubm:attr/titleTextEllipsize = 0x7f04049e
studio.takasaki.clubm:attr/showTitle = 0x7f0403dd
studio.takasaki.clubm:attr/textAppearanceListItem = 0x7f04045a
studio.takasaki.clubm:dimen/compat_button_padding_vertical_material = 0x7f07005c
studio.takasaki.clubm:attr/bottomSheetDragHandleStyle = 0x7f04007e
studio.takasaki.clubm:anim/catalyst_fade_in = 0x7f010018
studio.takasaki.clubm:color/abc_tint_btn_checkable = 0x7f060013
studio.takasaki.clubm:dimen/mtrl_btn_inset = 0x7f070266
studio.takasaki.clubm:color/m3_sys_color_dark_tertiary_container = 0x7f060197
studio.takasaki.clubm:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f130112
studio.takasaki.clubm:attr/brightness = 0x7f04008b
studio.takasaki.clubm:attr/region_widthMoreThan = 0x7f0403a4
studio.takasaki.clubm:attr/layout_goneMarginEnd = 0x7f0402bc
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0d0012
studio.takasaki.clubm:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700cb
studio.takasaki.clubm:string/material_timepicker_am = 0x7f1200b6
studio.takasaki.clubm:color/m3_highlighted_text = 0x7f0600a8
studio.takasaki.clubm:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
studio.takasaki.clubm:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0d011c
studio.takasaki.clubm:attr/marginHorizontal = 0x7f0402e1
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1301eb
studio.takasaki.clubm:attr/layout_editor_absoluteX = 0x7f0402b9
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f13032c
studio.takasaki.clubm:color/m3_sys_color_dark_outline = 0x7f060187
studio.takasaki.clubm:attr/layout_constraintWidth_percent = 0x7f0402b7
studio.takasaki.clubm:color/material_personalized_color_on_secondary = 0x7f0602a1
studio.takasaki.clubm:style/Theme.MaterialComponents.NoActionBar = 0x7f130295
studio.takasaki.clubm:style/Theme.Material3.DayNight.NoActionBar = 0x7f130258
studio.takasaki.clubm:color/material_dynamic_color_light_error_container = 0x7f06023b
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary60 = 0x7f06010b
studio.takasaki.clubm:attr/indicatorInset = 0x7f040253
studio.takasaki.clubm:style/ThemeOverlay.Material3.ActionBar = 0x7f1302a9
studio.takasaki.clubm:attr/layout_constraintVertical_chainStyle = 0x7f0402b2
studio.takasaki.clubm:color/m3_ref_palette_neutral96 = 0x7f060133
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f13032d
studio.takasaki.clubm:attr/layout_anchorGravity = 0x7f04028a
studio.takasaki.clubm:style/Widget.AppCompat.ListPopupWindow = 0x7f130341
studio.takasaki.clubm:attr/spinBars = 0x7f0403ee
studio.takasaki.clubm:style/TextAppearance.AppCompat.Title = 0x7f1301be
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f13013c
studio.takasaki.clubm:layout/dev_loading_view = 0x7f0c0033
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_shakedeviceicon = 0x7f080056
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600d7
studio.takasaki.clubm:attr/layout_constraintTop_toTopOf = 0x7f0402b0
studio.takasaki.clubm:attr/arrowShaftLength = 0x7f04003a
studio.takasaki.clubm:attr/layout_constraintTop_toBottomOf = 0x7f0402af
studio.takasaki.clubm:color/bright_foreground_material_dark = 0x7f060026
studio.takasaki.clubm:id/pathRelative = 0x7f09017e
studio.takasaki.clubm:attr/paddingStart = 0x7f04036e
studio.takasaki.clubm:color/m3_sys_color_light_surface_container_high = 0x7f060202
studio.takasaki.clubm:attr/layout_constraintTop_creator = 0x7f0402ae
studio.takasaki.clubm:styleable/RecyclerView = 0x7f140075
studio.takasaki.clubm:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070148
studio.takasaki.clubm:attr/fadeDuration = 0x7f0401ee
studio.takasaki.clubm:attr/layout_constraintStart_toEndOf = 0x7f0402ab
studio.takasaki.clubm:attr/transitionPathRotate = 0x7f0404be
studio.takasaki.clubm:attr/layout_constraintRight_toLeftOf = 0x7f0402a9
studio.takasaki.clubm:attr/layout_constraintRight_creator = 0x7f0402a8
studio.takasaki.clubm:attr/colorSurfaceInverse = 0x7f04011d
studio.takasaki.clubm:dimen/fastscroll_default_thickness = 0x7f070093
studio.takasaki.clubm:attr/layout_constraintHeight_percent = 0x7f0402a1
studio.takasaki.clubm:color/m3_button_outline_color_selector = 0x7f060082
studio.takasaki.clubm:layout/ime_secondary_split_test_activity = 0x7f0c003b
studio.takasaki.clubm:attr/bottomInsetScrimEnabled = 0x7f04007b
studio.takasaki.clubm:attr/useCompatPadding = 0x7f0404c4
studio.takasaki.clubm:attr/layout_constraintHeight_max = 0x7f04029f
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_infoiconlight = 0x7f08004a
studio.takasaki.clubm:attr/colorSurfaceContainerHigh = 0x7f040118
studio.takasaki.clubm:attr/layout_constraintDimensionRatio = 0x7f040298
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary50 = 0x7f06010a
studio.takasaki.clubm:attr/indeterminateAnimationType = 0x7f04024e
studio.takasaki.clubm:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0d00ed
studio.takasaki.clubm:id/title_template = 0x7f0901fd
studio.takasaki.clubm:attr/buttonStyle = 0x7f040099
studio.takasaki.clubm:attr/motionDurationLong1 = 0x7f040332
studio.takasaki.clubm:layout/crop_image_activity = 0x7f0c0021
studio.takasaki.clubm:attr/colorSecondaryVariant = 0x7f040114
studio.takasaki.clubm:attr/defaultScrollFlagsEnabled = 0x7f04018e
studio.takasaki.clubm:attr/layout_collapseMode = 0x7f04028c
studio.takasaki.clubm:attr/collapsedSize = 0x7f0400dd
studio.takasaki.clubm:attr/lastItemDecorated = 0x7f040284
studio.takasaki.clubm:anim/design_bottom_sheet_slide_in = 0x7f01001e
studio.takasaki.clubm:style/DialogAnimationFade = 0x7f13012e
studio.takasaki.clubm:attr/largeFontVerticalOffsetAdjustment = 0x7f040282
studio.takasaki.clubm:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1300dd
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary0 = 0x7f0600ea
studio.takasaki.clubm:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1302de
studio.takasaki.clubm:attr/motionDurationLong4 = 0x7f040335
studio.takasaki.clubm:attr/itemStrokeColor = 0x7f040273
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070114
studio.takasaki.clubm:drawable/abc_ratingbar_material = 0x7f080099
studio.takasaki.clubm:attr/textAppearanceLabelSmall = 0x7f040457
studio.takasaki.clubm:attr/paddingBottomNoButtons = 0x7f040369
studio.takasaki.clubm:attr/chipSpacingVertical = 0x7f0400c4
studio.takasaki.clubm:string/material_motion_easing_emphasized = 0x7f1200b0
studio.takasaki.clubm:integer/m3_chip_anim_duration = 0x7f0a0010
studio.takasaki.clubm:dimen/material_emphasis_disabled_background = 0x7f070238
studio.takasaki.clubm:dimen/abc_text_size_medium_material = 0x7f070049
studio.takasaki.clubm:integer/m3_card_anim_duration_ms = 0x7f0a000f
studio.takasaki.clubm:attr/actualImageScaleType = 0x7f040027
studio.takasaki.clubm:attr/clockFaceBackgroundColor = 0x7f0400cf
studio.takasaki.clubm:attr/checkedIcon = 0x7f0400ae
studio.takasaki.clubm:color/dim_foreground_disabled_material_light = 0x7f060072
studio.takasaki.clubm:attr/hideOnContentScroll = 0x7f040237
studio.takasaki.clubm:styleable/Tooltip = 0x7f140092
studio.takasaki.clubm:attr/itemShapeInsetTop = 0x7f040271
studio.takasaki.clubm:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f13004a
studio.takasaki.clubm:dimen/m3_bottom_nav_item_padding_top = 0x7f0700c4
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary99 = 0x7f060103
studio.takasaki.clubm:attr/flow_verticalBias = 0x7f040215
studio.takasaki.clubm:attr/itemShapeFillColor = 0x7f04026d
studio.takasaki.clubm:attr/iconEndPadding = 0x7f040243
studio.takasaki.clubm:attr/actionBarSize = 0x7f040003
studio.takasaki.clubm:color/material_dynamic_neutral20 = 0x7f060241
studio.takasaki.clubm:attr/closeIconStartPadding = 0x7f0400d7
studio.takasaki.clubm:attr/badgeShapeAppearance = 0x7f04005a
studio.takasaki.clubm:attr/itemShapeAppearance = 0x7f04026b
studio.takasaki.clubm:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f13041c
studio.takasaki.clubm:dimen/tooltip_margin = 0x7f07032c
studio.takasaki.clubm:style/Widget.AppCompat.Button = 0x7f13031b
studio.takasaki.clubm:attr/colorPrimarySurface = 0x7f04010d
studio.takasaki.clubm:attr/shapeAppearanceCornerExtraSmall = 0x7f0403ca
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0d0097
studio.takasaki.clubm:attr/layout_constraintVertical_weight = 0x7f0402b3
studio.takasaki.clubm:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070105
studio.takasaki.clubm:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0d00aa
studio.takasaki.clubm:color/material_personalized_color_error_container = 0x7f06029b
studio.takasaki.clubm:attr/divider = 0x7f040197
studio.takasaki.clubm:attr/dialogTheme = 0x7f040195
studio.takasaki.clubm:color/dim_foreground_disabled_material_dark = 0x7f060071
studio.takasaki.clubm:attr/layout_goneMarginTop = 0x7f0402c0
studio.takasaki.clubm:attr/itemIconPadding = 0x7f040262
studio.takasaki.clubm:id/tag_accessibility_clickable_spans = 0x7f0901de
studio.takasaki.clubm:id/month_navigation_previous = 0x7f090139
studio.takasaki.clubm:color/material_blue_grey_900 = 0x7f060230
studio.takasaki.clubm:id/search_go_btn = 0x7f0901b2
studio.takasaki.clubm:attr/itemHorizontalTranslationEnabled = 0x7f040261
studio.takasaki.clubm:attr/itemHorizontalPadding = 0x7f040260
studio.takasaki.clubm:dimen/m3_carousel_extra_small_item_size = 0x7f0700f2
studio.takasaki.clubm:dimen/mtrl_calendar_day_vertical_padding = 0x7f07027e
studio.takasaki.clubm:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f130162
studio.takasaki.clubm:attr/checkedButton = 0x7f0400ac
studio.takasaki.clubm:attr/cropFlipVertically = 0x7f040160
studio.takasaki.clubm:attr/keylines = 0x7f04027d
studio.takasaki.clubm:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07020d
studio.takasaki.clubm:dimen/m3_navigation_rail_icon_size = 0x7f0701cf
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0d00c8
studio.takasaki.clubm:attr/isMaterial3DynamicColorApplied = 0x7f04025a
studio.takasaki.clubm:attr/isLightTheme = 0x7f040259
studio.takasaki.clubm:attr/tabIndicatorHeight = 0x7f04042b
studio.takasaki.clubm:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070210
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600fe
studio.takasaki.clubm:attr/barLength = 0x7f040068
studio.takasaki.clubm:id/dragDown = 0x7f0900ad
studio.takasaki.clubm:drawable/ic_m3_chip_checked_circle = 0x7f0800ee
studio.takasaki.clubm:attr/initialActivityCount = 0x7f040256
studio.takasaki.clubm:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f13008c
studio.takasaki.clubm:attr/tabRippleColor = 0x7f040435
studio.takasaki.clubm:attr/staggered = 0x7f0403f5
studio.takasaki.clubm:attr/indicatorColor = 0x7f040250
studio.takasaki.clubm:attr/arcMode = 0x7f040038
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Media = 0x7f1301d9
studio.takasaki.clubm:drawable/material_cursor_drawable = 0x7f080108
studio.takasaki.clubm:attr/flow_firstVerticalStyle = 0x7f040209
studio.takasaki.clubm:id/accessibility_state_expanded = 0x7f09003b
studio.takasaki.clubm:anim/mtrl_card_lowers_interpolator = 0x7f010031
studio.takasaki.clubm:color/abc_btn_colored_text_material = 0x7f060003
studio.takasaki.clubm:attr/colorControlActivated = 0x7f0400eb
studio.takasaki.clubm:attr/indeterminateProgressStyle = 0x7f04024f
studio.takasaki.clubm:attr/thumbTrackGapSize = 0x7f040483
studio.takasaki.clubm:style/Base.v27.Theme.SplashScreen.Light = 0x7f130128
studio.takasaki.clubm:attr/subheaderInsetStart = 0x7f04040e
studio.takasaki.clubm:attr/deriveConstraintsFrom = 0x7f040192
studio.takasaki.clubm:attr/itemTextAppearanceInactive = 0x7f040278
studio.takasaki.clubm:attr/flow_firstHorizontalStyle = 0x7f040207
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Time = 0x7f1301da
studio.takasaki.clubm:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f130116
studio.takasaki.clubm:attr/imageButtonStyle = 0x7f04024d
studio.takasaki.clubm:attr/progressBarImage = 0x7f040394
studio.takasaki.clubm:dimen/m3_btn_translation_z_hovered = 0x7f0700e7
studio.takasaki.clubm:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1301dd
studio.takasaki.clubm:id/pressed = 0x7f090186
studio.takasaki.clubm:attr/iconStartPadding = 0x7f040247
studio.takasaki.clubm:id/material_textinput_timepicker = 0x7f090128
studio.takasaki.clubm:id/disjoint = 0x7f0900ac
studio.takasaki.clubm:attr/materialCalendarMonthNavigationButton = 0x7f0402f9
studio.takasaki.clubm:attr/homeLayout = 0x7f04023e
studio.takasaki.clubm:string/google_storage_bucket = 0x7f12008a
studio.takasaki.clubm:dimen/m3_btn_icon_only_default_padding = 0x7f0700d7
studio.takasaki.clubm:dimen/material_cursor_width = 0x7f070235
studio.takasaki.clubm:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f130173
studio.takasaki.clubm:attr/hintTextAppearance = 0x7f04023b
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1303b3
studio.takasaki.clubm:style/Base.TextAppearance.Material3.Search = 0x7f130046
studio.takasaki.clubm:color/design_fab_shadow_start_color = 0x7f060062
studio.takasaki.clubm:attr/itemShapeAppearanceOverlay = 0x7f04026c
studio.takasaki.clubm:attr/hintAnimationEnabled = 0x7f040239
studio.takasaki.clubm:attr/layout_optimizationLevel = 0x7f0402c3
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13011f
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f13009c
studio.takasaki.clubm:id/action_bar = 0x7f09003e
studio.takasaki.clubm:dimen/mtrl_calendar_navigation_height = 0x7f070290
studio.takasaki.clubm:attr/hideMotionSpec = 0x7f040235
studio.takasaki.clubm:dimen/m3_comp_outlined_card_container_elevation = 0x7f070154
studio.takasaki.clubm:attr/hideAnimationBehavior = 0x7f040234
studio.takasaki.clubm:attr/helperTextTextColor = 0x7f040233
studio.takasaki.clubm:attr/headerLayout = 0x7f04022e
studio.takasaki.clubm:attr/onCross = 0x7f040360
studio.takasaki.clubm:drawable/abc_spinner_textfield_background_material = 0x7f0800a4
studio.takasaki.clubm:anim/catalyst_slide_down = 0x7f01001c
studio.takasaki.clubm:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702cf
studio.takasaki.clubm:color/foreground_material_dark = 0x7f060077
studio.takasaki.clubm:color/design_dark_default_color_on_secondary = 0x7f06004a
studio.takasaki.clubm:anim/rns_no_animation_medium = 0x7f010045
studio.takasaki.clubm:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f07023f
studio.takasaki.clubm:attr/drawableEndCompat = 0x7f0401a4
studio.takasaki.clubm:attr/counterTextColor = 0x7f040150
studio.takasaki.clubm:anim/design_bottom_sheet_slide_out = 0x7f01001f
studio.takasaki.clubm:attr/itemSpacing = 0x7f040272
studio.takasaki.clubm:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701bf
studio.takasaki.clubm:attr/progressBarStyle = 0x7f040397
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f13019d
studio.takasaki.clubm:attr/alertDialogStyle = 0x7f04002c
studio.takasaki.clubm:id/cos = 0x7f090092
studio.takasaki.clubm:dimen/abc_text_size_display_1_material = 0x7f070043
studio.takasaki.clubm:color/material_dynamic_primary80 = 0x7f060261
studio.takasaki.clubm:integer/m3_sys_motion_duration_short1 = 0x7f0a001d
studio.takasaki.clubm:attr/itemMinHeight = 0x7f040266
studio.takasaki.clubm:styleable/BottomNavigationView = 0x7f140017
studio.takasaki.clubm:attr/subMenuArrow = 0x7f04040b
studio.takasaki.clubm:attr/fontVariationSettings = 0x7f040223
studio.takasaki.clubm:attr/fontProviderSystemFontFamily = 0x7f040221
studio.takasaki.clubm:color/highlighted_text_material_light = 0x7f06007a
studio.takasaki.clubm:attr/singleSelection = 0x7f0403e7
studio.takasaki.clubm:attr/buttonIconDimen = 0x7f040094
studio.takasaki.clubm:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1300ff
studio.takasaki.clubm:dimen/design_bottom_navigation_height = 0x7f070066
studio.takasaki.clubm:color/material_personalized_color_surface_container_highest = 0x7f0602b7
studio.takasaki.clubm:attr/titleMarginBottom = 0x7f040496
studio.takasaki.clubm:attr/fontProviderPackage = 0x7f04021f
studio.takasaki.clubm:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
studio.takasaki.clubm:color/m3_sys_color_dark_error = 0x7f060177
studio.takasaki.clubm:string/material_timepicker_hour = 0x7f1200b8
studio.takasaki.clubm:attr/flow_verticalGap = 0x7f040216
studio.takasaki.clubm:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08009b
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary80 = 0x7f0600f3
studio.takasaki.clubm:attr/dropDownListViewStyle = 0x7f0401b0
studio.takasaki.clubm:attr/motionDurationLong3 = 0x7f040334
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0601a8
studio.takasaki.clubm:style/Widget.Material3.ActionBar.Solid = 0x7f13036c
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130304
studio.takasaki.clubm:attr/layout_scrollFlags = 0x7f0402c5
studio.takasaki.clubm:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1300df
studio.takasaki.clubm:attr/flow_verticalAlign = 0x7f040214
studio.takasaki.clubm:style/Widget.Material3.Button.TonalButton = 0x7f13038e
studio.takasaki.clubm:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f130085
studio.takasaki.clubm:id/leftToRight = 0x7f09010d
studio.takasaki.clubm:attr/state_liftable = 0x7f040403
studio.takasaki.clubm:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07024c
studio.takasaki.clubm:id/transition_clip = 0x7f090206
studio.takasaki.clubm:attr/layout_anchor = 0x7f040289
studio.takasaki.clubm:attr/foregroundInsidePadding = 0x7f040227
studio.takasaki.clubm:attr/flow_lastVerticalBias = 0x7f040210
studio.takasaki.clubm:attr/flow_lastHorizontalStyle = 0x7f04020f
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0d0016
studio.takasaki.clubm:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701d2
studio.takasaki.clubm:id/fingerprint_subtitle = 0x7f0900d5
studio.takasaki.clubm:attr/roundTopRight = 0x7f0403b3
studio.takasaki.clubm:attr/flow_horizontalBias = 0x7f04020b
studio.takasaki.clubm:attr/flow_firstHorizontalBias = 0x7f040206
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button.Small = 0x7f1300d8
studio.takasaki.clubm:attr/buttonTint = 0x7f04009b
studio.takasaki.clubm:id/textEnd = 0x7f0901ec
studio.takasaki.clubm:attr/nestedScrollFlags = 0x7f04035a
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601cc
studio.takasaki.clubm:attr/chipStandaloneStyle = 0x7f0400c5
studio.takasaki.clubm:attr/expandedTitleTextColor = 0x7f0401dc
studio.takasaki.clubm:attr/flow_firstVerticalBias = 0x7f040208
studio.takasaki.clubm:attr/itemTextAppearanceActive = 0x7f040276
studio.takasaki.clubm:string/spinbutton_description = 0x7f120112
studio.takasaki.clubm:string/mtrl_picker_date_header_unselected = 0x7f1200d8
studio.takasaki.clubm:attr/floatingActionButtonSurfaceStyle = 0x7f040204
studio.takasaki.clubm:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0d00ad
studio.takasaki.clubm:attr/floatingActionButtonStyle = 0x7f040203
studio.takasaki.clubm:integer/google_play_services_version = 0x7f0a0009
studio.takasaki.clubm:attr/placeholderTextColor = 0x7f040386
studio.takasaki.clubm:drawable/icon_background = 0x7f0800fb
studio.takasaki.clubm:attr/contentInsetRight = 0x7f040130
studio.takasaki.clubm:attr/floatingActionButtonSecondaryStyle = 0x7f0401fd
studio.takasaki.clubm:color/m3_ref_palette_black = 0x7f0600b9
studio.takasaki.clubm:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
studio.takasaki.clubm:attr/autofillInlineSuggestionEndIconStyle = 0x7f040046
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary100 = 0x7f0600ec
studio.takasaki.clubm:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401fb
studio.takasaki.clubm:drawable/abc_spinner_mtrl_am_alpha = 0x7f0800a3
studio.takasaki.clubm:attr/materialCalendarHeaderCancelButton = 0x7f0402f1
studio.takasaki.clubm:dimen/abc_disabled_alpha_material_dark = 0x7f070027
studio.takasaki.clubm:attr/iconifiedByDefault = 0x7f04024a
studio.takasaki.clubm:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_alerttriangle = 0x7f080028
studio.takasaki.clubm:color/m3_sys_color_tertiary_fixed = 0x7f060214
studio.takasaki.clubm:attr/floatingActionButtonSmallTertiaryStyle = 0x7f040202
studio.takasaki.clubm:dimen/mtrl_navigation_rail_compact_width = 0x7f0702d0
studio.takasaki.clubm:style/TextAppearance.Design.Prefix = 0x7f1301e5
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_error = 0x7f0601c1
studio.takasaki.clubm:attr/floatingActionButtonLargeStyle = 0x7f0401f9
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsfilledinactiveicon = 0x7f080041
studio.takasaki.clubm:id/tag_unhandled_key_event_manager = 0x7f0901e7
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080022
studio.takasaki.clubm:attr/customColorDrawableValue = 0x7f04017f
studio.takasaki.clubm:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401f7
studio.takasaki.clubm:color/m3_sys_color_dark_surface_container = 0x7f06018f
studio.takasaki.clubm:dimen/notification_right_icon_size = 0x7f070318
studio.takasaki.clubm:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600fa
studio.takasaki.clubm:attr/badgeWithTextShapeAppearanceOverlay = 0x7f040066
studio.takasaki.clubm:attr/extendedFloatingActionButtonStyle = 0x7f0401e1
studio.takasaki.clubm:style/Widget.MaterialComponents.Button = 0x7f13042c
studio.takasaki.clubm:string/material_slider_range_end = 0x7f1200b3
studio.takasaki.clubm:drawable/abc_list_selector_holo_light = 0x7f080095
studio.takasaki.clubm:dimen/design_bottom_navigation_item_min_width = 0x7f070069
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0d00d3
studio.takasaki.clubm:attr/curveFit = 0x7f04017d
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070115
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f130193
studio.takasaki.clubm:color/m3_radiobutton_ripple_tint = 0x7f0600b8
studio.takasaki.clubm:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070308
studio.takasaki.clubm:color/design_default_color_on_secondary = 0x7f060057
studio.takasaki.clubm:attr/hintTextColor = 0x7f04023c
studio.takasaki.clubm:attr/closeIconSize = 0x7f0400d6
studio.takasaki.clubm:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
studio.takasaki.clubm:dimen/m3_searchbar_padding_start = 0x7f0701e3
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f13015c
studio.takasaki.clubm:color/material_on_surface_emphasis_high_type = 0x7f060291
studio.takasaki.clubm:color/m3_slider_inactive_track_color_legacy = 0x7f060171
studio.takasaki.clubm:string/material_hour_selection = 0x7f1200aa
studio.takasaki.clubm:attr/failureImage = 0x7f0401ef
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f08000f
studio.takasaki.clubm:attr/haloColor = 0x7f04022c
studio.takasaki.clubm:attr/boxCornerRadiusBottomStart = 0x7f040084
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001b
studio.takasaki.clubm:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f130286
studio.takasaki.clubm:attr/trackTint = 0x7f0404b9
studio.takasaki.clubm:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1303a6
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0d00cc
studio.takasaki.clubm:id/transition_scene_layoutid_cache = 0x7f09020c
studio.takasaki.clubm:dimen/mtrl_extended_fab_elevation = 0x7f0702ad
studio.takasaki.clubm:color/material_grey_600 = 0x7f060282
studio.takasaki.clubm:attr/chipSpacingHorizontal = 0x7f0400c3
studio.takasaki.clubm:attr/fontProviderFetchStrategy = 0x7f04021d
studio.takasaki.clubm:attr/fabCradleRoundedCornerRadius = 0x7f0401ea
studio.takasaki.clubm:integer/material_motion_path = 0x7f0a002e
studio.takasaki.clubm:color/common_google_signin_btn_text_dark_pressed = 0x7f06003c
studio.takasaki.clubm:color/design_dark_default_color_primary_dark = 0x7f06004d
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Inverse = 0x7f130023
studio.takasaki.clubm:color/material_dynamic_neutral_variant70 = 0x7f060253
studio.takasaki.clubm:attr/radioButtonStyle = 0x7f04039b
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0d007f
studio.takasaki.clubm:attr/cardUseCompatPadding = 0x7f0400a3
studio.takasaki.clubm:attr/prefixTextColor = 0x7f04038f
studio.takasaki.clubm:attr/colorPrimaryInverse = 0x7f04010c
studio.takasaki.clubm:attr/fabAnimationMode = 0x7f0401e8
studio.takasaki.clubm:dimen/abc_dropdownitem_icon_width = 0x7f070029
studio.takasaki.clubm:id/floating = 0x7f0900de
studio.takasaki.clubm:attr/actionBarDivider = 0x7f040000
studio.takasaki.clubm:id/autoComplete = 0x7f09005f
studio.takasaki.clubm:anim/abc_popup_enter = 0x7f010003
studio.takasaki.clubm:color/bright_foreground_inverse_material_dark = 0x7f060024
studio.takasaki.clubm:layout/m3_auto_complete_simple_item = 0x7f0c003f
studio.takasaki.clubm:attr/extraMultilineHeightEnabled = 0x7f0401e4
studio.takasaki.clubm:color/m3_sys_color_dark_on_surface_variant = 0x7f060184
studio.takasaki.clubm:id/search_plate = 0x7f0901b4
studio.takasaki.clubm:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401e3
studio.takasaki.clubm:anim/m3_side_sheet_exit_to_left = 0x7f01002d
studio.takasaki.clubm:anim/rns_ios_from_left_foreground_open = 0x7f01003d
studio.takasaki.clubm:attr/actionOverflowButtonStyle = 0x7f04001f
studio.takasaki.clubm:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0d0035
studio.takasaki.clubm:attr/imageAspectRatio = 0x7f04024b
studio.takasaki.clubm:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401e2
studio.takasaki.clubm:drawable/$avd_show_password__2 = 0x7f080005
studio.takasaki.clubm:color/design_dark_default_color_on_primary = 0x7f060049
studio.takasaki.clubm:color/mtrl_textinput_disabled_color = 0x7f060306
studio.takasaki.clubm:attr/cropMinCropWindowWidth = 0x7f04016b
studio.takasaki.clubm:macro/m3_comp_text_button_label_text_type = 0x7f0d0145
studio.takasaki.clubm:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07012e
studio.takasaki.clubm:attr/roundTopLeft = 0x7f0403b2
studio.takasaki.clubm:id/open_search_view_toolbar = 0x7f090171
studio.takasaki.clubm:color/m3_tabs_ripple_color_secondary = 0x7f060219
studio.takasaki.clubm:attr/behavior_expandedOffset = 0x7f04006f
studio.takasaki.clubm:attr/colorOnTertiaryFixedVariant = 0x7f040104
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1303b8
studio.takasaki.clubm:attr/extendStrategy = 0x7f0401de
studio.takasaki.clubm:dimen/splashscreen_icon_mask_size_no_background = 0x7f07031f
studio.takasaki.clubm:attr/showPaths = 0x7f0403db
studio.takasaki.clubm:attr/badgeWithTextWidth = 0x7f040067
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130307
studio.takasaki.clubm:style/Theme.AppCompat = 0x7f130223
studio.takasaki.clubm:id/material_label = 0x7f090125
studio.takasaki.clubm:attr/cornerSizeTopLeft = 0x7f040149
studio.takasaki.clubm:macro/m3_comp_switch_selected_handle_color = 0x7f0d0124
studio.takasaki.clubm:attr/colorOnSurfaceInverse = 0x7f0400ff
studio.takasaki.clubm:attr/expandedTitleTextAppearance = 0x7f0401db
studio.takasaki.clubm:attr/hideNavigationIcon = 0x7f040236
studio.takasaki.clubm:macro/m3_comp_dialog_headline_type = 0x7f0d0025
studio.takasaki.clubm:attr/expandedTitleMarginStart = 0x7f0401d9
studio.takasaki.clubm:attr/boxStrokeErrorColor = 0x7f040088
studio.takasaki.clubm:attr/endIconDrawable = 0x7f0401bf
studio.takasaki.clubm:drawable/$avd_hide_password__0 = 0x7f080000
studio.takasaki.clubm:anim/m3_side_sheet_enter_from_left = 0x7f01002b
studio.takasaki.clubm:attr/expandedTitleMarginEnd = 0x7f0401d8
studio.takasaki.clubm:string/summary_description = 0x7f12011c
studio.takasaki.clubm:attr/expandedTitleMarginBottom = 0x7f0401d7
studio.takasaki.clubm:id/progress_circular = 0x7f090187
studio.takasaki.clubm:color/material_dynamic_tertiary30 = 0x7f060276
studio.takasaki.clubm:color/m3_sys_color_dark_surface_container_low = 0x7f060192
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.Full = 0x7f13017f
studio.takasaki.clubm:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080072
studio.takasaki.clubm:attr/horizontalOffset = 0x7f04023f
studio.takasaki.clubm:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130018
studio.takasaki.clubm:attr/expanded = 0x7f0401d3
studio.takasaki.clubm:attr/textAppearanceBodySmall = 0x7f040446
studio.takasaki.clubm:attr/currentState = 0x7f04017a
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07021c
studio.takasaki.clubm:attr/errorShown = 0x7f0401cf
studio.takasaki.clubm:color/mtrl_outlined_stroke_color = 0x7f0602f8
studio.takasaki.clubm:color/mtrl_choice_chip_background_color = 0x7f0602e3
studio.takasaki.clubm:id/rectangleVerticalOnly = 0x7f09018e
studio.takasaki.clubm:attr/yearSelectedStyle = 0x7f0404e2
studio.takasaki.clubm:attr/motionEasingLinear = 0x7f040344
studio.takasaki.clubm:attr/layout_constraintBottom_toBottomOf = 0x7f040293
studio.takasaki.clubm:attr/allowStacking = 0x7f04002e
studio.takasaki.clubm:attr/errorAccessibilityLabel = 0x7f0401c8
studio.takasaki.clubm:integer/design_snackbar_text_max_lines = 0x7f0a0007
studio.takasaki.clubm:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070136
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar = 0x7f1303c8
studio.takasaki.clubm:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f130275
studio.takasaki.clubm:color/material_personalized_hint_foreground_inverse = 0x7f0602c5
studio.takasaki.clubm:layout/material_time_input = 0x7f0c004b
studio.takasaki.clubm:id/special_effects_controller_view_tag = 0x7f0901c6
studio.takasaki.clubm:attr/errorTextColor = 0x7f0401d1
studio.takasaki.clubm:attr/enforceMaterialTheme = 0x7f0401c5
studio.takasaki.clubm:attr/colorOnSecondaryContainer = 0x7f0400fb
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0d0073
studio.takasaki.clubm:attr/endIconTint = 0x7f0401c3
studio.takasaki.clubm:array/delay_showing_prompt_models = 0x7f030003
studio.takasaki.clubm:attr/endIconScaleType = 0x7f0401c2
studio.takasaki.clubm:attr/toolbarStyle = 0x7f0404a3
studio.takasaki.clubm:attr/strokeColor = 0x7f040409
studio.takasaki.clubm:attr/endIconMode = 0x7f0401c1
studio.takasaki.clubm:styleable/MaterialTextAppearance = 0x7f14005c
studio.takasaki.clubm:color/m3_fab_efab_background_color_selector = 0x7f0600a4
studio.takasaki.clubm:attr/checkedChip = 0x7f0400ad
studio.takasaki.clubm:attr/endIconCheckable = 0x7f0401bd
studio.takasaki.clubm:attr/elevationOverlayColor = 0x7f0401b9
studio.takasaki.clubm:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0d0013
studio.takasaki.clubm:attr/dynamicColorThemeOverlay = 0x7f0401b3
studio.takasaki.clubm:attr/actionModeCutDrawable = 0x7f040015
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1302b6
studio.takasaki.clubm:attr/drawerLayoutStyle = 0x7f0401ae
studio.takasaki.clubm:id/withinBounds = 0x7f090222
studio.takasaki.clubm:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702b1
studio.takasaki.clubm:attr/textAllCaps = 0x7f040441
studio.takasaki.clubm:anim/linear_indeterminate_line1_head_interpolator = 0x7f010023
studio.takasaki.clubm:attr/drawableTopCompat = 0x7f0401ab
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f130075
studio.takasaki.clubm:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f07018f
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f130038
studio.takasaki.clubm:dimen/design_bottom_navigation_active_text_size = 0x7f070064
studio.takasaki.clubm:attr/chipIconEnabled = 0x7f0400bc
studio.takasaki.clubm:attr/drawableTint = 0x7f0401a9
studio.takasaki.clubm:anim/rns_standard_accelerate_interpolator = 0x7f01004c
studio.takasaki.clubm:attr/drawableSize = 0x7f0401a7
studio.takasaki.clubm:id/action_container = 0x7f090045
studio.takasaki.clubm:attr/windowNoTitle = 0x7f0404dd
studio.takasaki.clubm:attr/passwordToggleTint = 0x7f040378
studio.takasaki.clubm:style/ShapeAppearance.Material3.LargeComponent = 0x7f130184
studio.takasaki.clubm:dimen/m3_searchview_height = 0x7f0701e8
studio.takasaki.clubm:anim/rns_default_enter_out = 0x7f010033
studio.takasaki.clubm:attr/hoveredFocusedTranslationZ = 0x7f040241
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f130433
studio.takasaki.clubm:attr/editTextColor = 0x7f0401b5
studio.takasaki.clubm:dimen/design_navigation_item_icon_padding = 0x7f07007c
studio.takasaki.clubm:attr/popupWindowStyle = 0x7f04038b
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Button = 0x7f13001c
studio.takasaki.clubm:attr/badgeStyle = 0x7f04005c
studio.takasaki.clubm:style/Widget.Material3.Button.OutlinedButton = 0x7f130386
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0d0163
studio.takasaki.clubm:attr/dragDirection = 0x7f04019f
studio.takasaki.clubm:dimen/m3_fab_border_width = 0x7f0701b9
studio.takasaki.clubm:attr/materialCalendarStyle = 0x7f0402fa
studio.takasaki.clubm:dimen/mtrl_card_corner_radius = 0x7f0702a1
studio.takasaki.clubm:attr/cropGuidelinesColor = 0x7f040162
studio.takasaki.clubm:drawable/abc_list_divider_mtrl_alpha = 0x7f08008b
studio.takasaki.clubm:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401fe
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13030c
studio.takasaki.clubm:style/Base.Widget.Design.TabLayout = 0x7f130102
studio.takasaki.clubm:attr/colorTertiary = 0x7f040120
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080023
studio.takasaki.clubm:dimen/m3_sys_elevation_level4 = 0x7f0701f8
studio.takasaki.clubm:attr/customStringValue = 0x7f040186
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0d015a
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600d1
studio.takasaki.clubm:color/error_color_material_light = 0x7f060076
studio.takasaki.clubm:attr/expandedHintEnabled = 0x7f0401d4
studio.takasaki.clubm:style/Widget.AppCompat.TextView = 0x7f130355
studio.takasaki.clubm:attr/dividerPadding = 0x7f04019c
studio.takasaki.clubm:dimen/notification_small_icon_size_as_large = 0x7f07031b
studio.takasaki.clubm:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1300c3
studio.takasaki.clubm:attr/dividerHorizontal = 0x7f040199
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600c6
studio.takasaki.clubm:string/ic_rotate_right_24 = 0x7f120091
studio.takasaki.clubm:anim/design_snackbar_out = 0x7f010021
studio.takasaki.clubm:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
studio.takasaki.clubm:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f13005f
studio.takasaki.clubm:attr/isAutofillInlineSuggestionTheme = 0x7f040258
studio.takasaki.clubm:styleable/ViewStubCompat = 0x7f140099
studio.takasaki.clubm:dimen/mtrl_btn_disabled_z = 0x7f070260
studio.takasaki.clubm:macro/m3_comp_search_view_divider_color = 0x7f0d00f2
studio.takasaki.clubm:id/outline = 0x7f090173
studio.takasaki.clubm:color/biometric_error_color = 0x7f060021
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0d0090
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0d0015
studio.takasaki.clubm:attr/dividerColor = 0x7f040198
studio.takasaki.clubm:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
studio.takasaki.clubm:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400e2
studio.takasaki.clubm:attr/thumbTint = 0x7f040481
studio.takasaki.clubm:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
studio.takasaki.clubm:attr/deltaPolarRadius = 0x7f040191
studio.takasaki.clubm:attr/framePosition = 0x7f040228
studio.takasaki.clubm:style/Widget.AppCompat.SearchView.ActionBar = 0x7f13034e
studio.takasaki.clubm:attr/defaultState = 0x7f04018f
studio.takasaki.clubm:string/default_error_msg = 0x7f120066
studio.takasaki.clubm:attr/materialCalendarHeaderConfirmButton = 0x7f0402f2
studio.takasaki.clubm:attr/cropBorderCornerOffset = 0x7f040157
studio.takasaki.clubm:dimen/mtrl_calendar_year_corner = 0x7f07029a
studio.takasaki.clubm:attr/defaultMarginsEnabled = 0x7f04018c
studio.takasaki.clubm:attr/contentPaddingTop = 0x7f040139
studio.takasaki.clubm:string/abc_shareactionprovider_share_with = 0x7f12001a
studio.takasaki.clubm:attr/colorPrimary = 0x7f040107
studio.takasaki.clubm:attr/ensureMinTouchTargetSize = 0x7f0401c7
studio.takasaki.clubm:dimen/mtrl_calendar_day_corner = 0x7f07027a
studio.takasaki.clubm:attr/dayTodayStyle = 0x7f04018a
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f13013b
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0d0075
studio.takasaki.clubm:attr/colorOutline = 0x7f040105
studio.takasaki.clubm:attr/dayStyle = 0x7f040189
studio.takasaki.clubm:attr/searchHintIcon = 0x7f0403bf
studio.takasaki.clubm:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401f8
studio.takasaki.clubm:attr/actualImageUri = 0x7f040028
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon = 0x7f080115
studio.takasaki.clubm:attr/daySelectedStyle = 0x7f040188
studio.takasaki.clubm:attr/materialIconButtonStyle = 0x7f040309
studio.takasaki.clubm:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070300
studio.takasaki.clubm:dimen/mtrl_switch_thumb_icon_size = 0x7f0702fc
studio.takasaki.clubm:attr/fabCradleVerticalOffset = 0x7f0401eb
studio.takasaki.clubm:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1301b0
studio.takasaki.clubm:attr/customPixelDimension = 0x7f040185
studio.takasaki.clubm:attr/floatingActionButtonTertiaryStyle = 0x7f040205
studio.takasaki.clubm:attr/thumbStrokeWidth = 0x7f04047f
studio.takasaki.clubm:style/Widget.Material3.CardView.Outlined = 0x7f130393
studio.takasaki.clubm:attr/buttonIcon = 0x7f040093
studio.takasaki.clubm:attr/motion_triggerOnCollision = 0x7f040351
studio.takasaki.clubm:color/material_dynamic_color_light_on_error = 0x7f06023c
studio.takasaki.clubm:color/m3_ref_palette_secondary30 = 0x7f060154
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.Icon = 0x7f13042d
studio.takasaki.clubm:color/bright_foreground_disabled_material_dark = 0x7f060022
studio.takasaki.clubm:attr/materialCalendarDay = 0x7f0402ee
studio.takasaki.clubm:attr/closeIconTint = 0x7f0400d8
studio.takasaki.clubm:attr/layout_constraintHeight_default = 0x7f04029e
studio.takasaki.clubm:attr/textColorSearchUrl = 0x7f040468
studio.takasaki.clubm:style/Widget.Compat.NotificationActionText = 0x7f130360
studio.takasaki.clubm:attr/customColorValue = 0x7f040180
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1301d3
studio.takasaki.clubm:color/m3_chip_assist_text_color = 0x7f06008c
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600d2
studio.takasaki.clubm:attr/chipStartPadding = 0x7f0400c6
studio.takasaki.clubm:string/icon_content_description = 0x7f120092
studio.takasaki.clubm:attr/activeIndicatorLabelPadding = 0x7f040024
studio.takasaki.clubm:attr/thumbStrokeColor = 0x7f04047e
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_background = 0x7f0601c0
studio.takasaki.clubm:attr/cursorColor = 0x7f04017b
studio.takasaki.clubm:color/m3_fab_efab_foreground_color_selector = 0x7f0600a5
studio.takasaki.clubm:id/TOP_END = 0x7f09000f
studio.takasaki.clubm:color/background_material_dark = 0x7f06001f
studio.takasaki.clubm:color/material_personalized_color_on_tertiary = 0x7f0602a6
studio.takasaki.clubm:drawable/m3_tabs_transparent_background = 0x7f080107
studio.takasaki.clubm:attr/actionModeFindDrawable = 0x7f040016
studio.takasaki.clubm:attr/autoSizeTextType = 0x7f040043
studio.takasaki.clubm:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0d00c6
studio.takasaki.clubm:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0d009c
studio.takasaki.clubm:attr/floatingActionButtonSmallStyle = 0x7f040200
studio.takasaki.clubm:attr/warmth = 0x7f0404ce
studio.takasaki.clubm:attr/liftOnScroll = 0x7f0402c7
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13048c
studio.takasaki.clubm:dimen/m3_badge_size = 0x7f0700b9
studio.takasaki.clubm:id/react_test_id = 0x7f09018b
studio.takasaki.clubm:attr/fabAlignmentMode = 0x7f0401e5
studio.takasaki.clubm:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1300e5
studio.takasaki.clubm:attr/badgeVerticalPadding = 0x7f040060
studio.takasaki.clubm:dimen/m3_badge_with_text_vertical_padding = 0x7f0700bf
studio.takasaki.clubm:color/m3_dynamic_hint_foreground = 0x7f0600a0
studio.takasaki.clubm:style/Base.V14.Theme.MaterialComponents = 0x7f130097
studio.takasaki.clubm:attr/contentInsetEndWithActions = 0x7f04012e
studio.takasaki.clubm:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_alerttriangle = 0x7f080032
studio.takasaki.clubm:layout/design_navigation_item_subheader = 0x7f0c002e
studio.takasaki.clubm:attr/windowActionModeOverlay = 0x7f0404d6
studio.takasaki.clubm:drawable/abc_list_pressed_holo_light = 0x7f08008f
studio.takasaki.clubm:attr/contentPaddingEnd = 0x7f040135
studio.takasaki.clubm:style/Widget.Autofill = 0x7f130359
studio.takasaki.clubm:id/material_minute_text_input = 0x7f090126
studio.takasaki.clubm:color/design_dark_default_color_primary = 0x7f06004c
studio.takasaki.clubm:attr/dividerInsetEnd = 0x7f04019a
studio.takasaki.clubm:attr/endIconMinSize = 0x7f0401c0
studio.takasaki.clubm:attr/cropShowCropOverlay = 0x7f040170
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f13021b
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0d00be
studio.takasaki.clubm:attr/cropMultiTouchEnabled = 0x7f04016c
studio.takasaki.clubm:layout/mtrl_picker_text_input_date = 0x7f0c006b
studio.takasaki.clubm:dimen/abc_action_bar_stacked_max_height = 0x7f070009
studio.takasaki.clubm:style/Base.v21.Theme.SplashScreen.Light = 0x7f130126
studio.takasaki.clubm:attr/buttonIconTintMode = 0x7f040096
studio.takasaki.clubm:attr/flow_maxElementsWrap = 0x7f040212
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0d0017
studio.takasaki.clubm:attr/cropMinCropResultHeightPX = 0x7f040168
studio.takasaki.clubm:attr/windowSplashScreenBackground = 0x7f0404e0
studio.takasaki.clubm:attr/elevationOverlayEnabled = 0x7f0401ba
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Caption = 0x7f13001d
studio.takasaki.clubm:attr/materialCalendarHeaderTitle = 0x7f0402f6
studio.takasaki.clubm:attr/waveShape = 0x7f0404d2
studio.takasaki.clubm:attr/cropMaxCropResultHeightPX = 0x7f040165
studio.takasaki.clubm:color/m3_sys_color_light_on_primary = 0x7f0601f1
studio.takasaki.clubm:style/Theme.DevLauncher.ErrorActivity = 0x7f130244
studio.takasaki.clubm:attr/materialThemeOverlay = 0x7f040310
studio.takasaki.clubm:style/Base.Theme.Material3.Light.Dialog = 0x7f130064
studio.takasaki.clubm:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702cd
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_settingsfilledinactiveiconlight = 0x7f080055
studio.takasaki.clubm:attr/cropBorderCornerThickness = 0x7f040158
studio.takasaki.clubm:style/Platform.MaterialComponents = 0x7f130147
studio.takasaki.clubm:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0d00cf
studio.takasaki.clubm:animator/design_fab_hide_motion_spec = 0x7f020001
studio.takasaki.clubm:attr/actionModeTheme = 0x7f04001d
studio.takasaki.clubm:styleable/SignInButton = 0x7f14007e
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f130309
studio.takasaki.clubm:color/material_on_primary_emphasis_medium = 0x7f06028f
studio.takasaki.clubm:attr/cropFlipHorizontally = 0x7f04015f
studio.takasaki.clubm:attr/cropFixAspectRatio = 0x7f04015e
studio.takasaki.clubm:dimen/m3_ripple_default_alpha = 0x7f0701d9
studio.takasaki.clubm:string/call_notification_incoming_text = 0x7f12002f
studio.takasaki.clubm:color/material_grey_50 = 0x7f060281
studio.takasaki.clubm:color/m3_navigation_item_text_color = 0x7f0600b1
studio.takasaki.clubm:attr/cropCornerCircleFillColor = 0x7f04015c
studio.takasaki.clubm:dimen/material_clock_face_margin_bottom = 0x7f070229
studio.takasaki.clubm:id/mtrl_motion_snapshot_view = 0x7f090148
studio.takasaki.clubm:attr/ratingBarStyleIndicator = 0x7f04039e
studio.takasaki.clubm:styleable/Toolbar = 0x7f140091
studio.takasaki.clubm:color/material_on_surface_emphasis_medium = 0x7f060292
studio.takasaki.clubm:drawable/$m3_avd_hide_password__1 = 0x7f080007
studio.takasaki.clubm:color/ripple_material_light = 0x7f060316
studio.takasaki.clubm:drawable/common_google_signin_btn_text_light_normal_background = 0x7f0800d2
studio.takasaki.clubm:attr/flow_lastHorizontalBias = 0x7f04020e
studio.takasaki.clubm:layout/notification_template_part_chronometer = 0x7f0c007c
studio.takasaki.clubm:attr/cropCenterMoveEnabled = 0x7f04015b
studio.takasaki.clubm:attr/fabCustomSize = 0x7f0401ec
studio.takasaki.clubm:style/ThemeOverlay.AppCompat.DayNight = 0x7f1302a2
studio.takasaki.clubm:id/view_tree_view_model_store_owner = 0x7f09021c
studio.takasaki.clubm:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_loader = 0x7f08002c
studio.takasaki.clubm:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1303b0
studio.takasaki.clubm:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f130141
studio.takasaki.clubm:attr/checkedTextViewStyle = 0x7f0400b6
studio.takasaki.clubm:attr/roundingBorderPadding = 0x7f0403b8
studio.takasaki.clubm:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f13000c
studio.takasaki.clubm:attr/actionModeCloseDrawable = 0x7f040013
studio.takasaki.clubm:attr/roundTopEnd = 0x7f0403b1
studio.takasaki.clubm:style/Widget.Material3.DrawerLayout = 0x7f1303af
studio.takasaki.clubm:id/rn_frame_file = 0x7f090197
studio.takasaki.clubm:attr/queryHint = 0x7f040399
studio.takasaki.clubm:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702a8
studio.takasaki.clubm:dimen/m3_comp_elevated_card_container_elevation = 0x7f07010e
studio.takasaki.clubm:anim/rns_no_animation_350 = 0x7f010044
studio.takasaki.clubm:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f13017e
studio.takasaki.clubm:attr/colorOnSecondary = 0x7f0400fa
studio.takasaki.clubm:attr/indicatorDirectionLinear = 0x7f040252
studio.takasaki.clubm:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0700ff
studio.takasaki.clubm:attr/cropBackgroundColor = 0x7f040154
studio.takasaki.clubm:style/Theme.AppCompat.NoActionBar = 0x7f130238
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_refreshicon = 0x7f080050
studio.takasaki.clubm:attr/backgroundInsetEnd = 0x7f04004f
studio.takasaki.clubm:attr/actionBarTabTextStyle = 0x7f040008
studio.takasaki.clubm:attr/backgroundTint = 0x7f040055
studio.takasaki.clubm:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1300bd
studio.takasaki.clubm:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f070259
studio.takasaki.clubm:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f13044a
studio.takasaki.clubm:color/primary_text_default_material_light = 0x7f060312
studio.takasaki.clubm:string/mtrl_picker_invalid_format_use = 0x7f1200dd
studio.takasaki.clubm:attr/coplanarSiblingViewId = 0x7f04013e
studio.takasaki.clubm:attr/shapeAppearanceSmallComponent = 0x7f0403d1
studio.takasaki.clubm:color/m3_ref_palette_neutral_variant0 = 0x7f060136
studio.takasaki.clubm:attr/chipIconTint = 0x7f0400be
studio.takasaki.clubm:attr/percentHeight = 0x7f04037c
studio.takasaki.clubm:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f130421
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_outline_color = 0x7f0d00c3
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_branchicon = 0x7f080037
studio.takasaki.clubm:string/state_expanded_description = 0x7f120116
studio.takasaki.clubm:id/mtrl_calendar_selection_frame = 0x7f090142
studio.takasaki.clubm:attr/actionModeStyle = 0x7f04001c
studio.takasaki.clubm:attr/cropMaxCropResultWidthPX = 0x7f040166
studio.takasaki.clubm:attr/counterTextAppearance = 0x7f04014f
studio.takasaki.clubm:id/labelled_by = 0x7f09010a
studio.takasaki.clubm:color/m3_button_ripple_color_selector = 0x7f060084
studio.takasaki.clubm:color/mtrl_navigation_item_icon_tint = 0x7f0602f3
studio.takasaki.clubm:integer/m3_sys_motion_duration_long4 = 0x7f0a0018
studio.takasaki.clubm:color/mtrl_switch_track_tint = 0x7f0602fe
studio.takasaki.clubm:attr/cornerSizeBottomRight = 0x7f040148
studio.takasaki.clubm:attr/buttonBarStyle = 0x7f040090
studio.takasaki.clubm:attr/actionModeCopyDrawable = 0x7f040014
studio.takasaki.clubm:attr/horizontalOffsetWithText = 0x7f040240
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f130429
studio.takasaki.clubm:attr/cornerShape = 0x7f040145
studio.takasaki.clubm:attr/chipSurfaceColor = 0x7f0400ca
studio.takasaki.clubm:attr/cornerFamilyTopRight = 0x7f040143
studio.takasaki.clubm:attr/cornerFamilyBottomRight = 0x7f040141
studio.takasaki.clubm:attr/errorIconTint = 0x7f0401cd
studio.takasaki.clubm:style/Theme.SplashScreen = 0x7f13029b
studio.takasaki.clubm:attr/arrowHeadLength = 0x7f040039
studio.takasaki.clubm:attr/labelVisibilityMode = 0x7f040281
studio.takasaki.clubm:string/path_password_strike_through = 0x7f120102
studio.takasaki.clubm:attr/thumbWidth = 0x7f040484
studio.takasaki.clubm:style/Widget.Material3.Toolbar.Surface = 0x7f130415
studio.takasaki.clubm:attr/controlBackground = 0x7f04013c
studio.takasaki.clubm:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070217
studio.takasaki.clubm:color/mtrl_btn_transparent_bg_color = 0x7f0602da
studio.takasaki.clubm:dimen/abc_text_size_headline_material = 0x7f070047
studio.takasaki.clubm:color/notification_material_background_media_default_color = 0x7f06030c
studio.takasaki.clubm:color/material_dynamic_neutral40 = 0x7f060243
studio.takasaki.clubm:attr/contrast = 0x7f04013b
studio.takasaki.clubm:attr/colorOnSurface = 0x7f0400fe
studio.takasaki.clubm:dimen/notification_action_text_size = 0x7f070311
studio.takasaki.clubm:color/m3_sys_color_light_inverse_surface = 0x7f0601ed
studio.takasaki.clubm:styleable/MaterialDivider = 0x7f140058
studio.takasaki.clubm:attr/errorAccessibilityLiveRegion = 0x7f0401c9
studio.takasaki.clubm:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08009c
studio.takasaki.clubm:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070158
studio.takasaki.clubm:attr/contentPaddingRight = 0x7f040137
studio.takasaki.clubm:id/accessibility_custom_action_31 = 0x7f09002f
studio.takasaki.clubm:color/design_fab_stroke_top_outer_color = 0x7f060066
studio.takasaki.clubm:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f13021c
studio.takasaki.clubm:attr/contentInsetStartWithNavigation = 0x7f040132
studio.takasaki.clubm:attr/switchStyle = 0x7f04041e
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0601aa
studio.takasaki.clubm:drawable/abc_list_focused_holo = 0x7f08008c
studio.takasaki.clubm:anim/rns_ios_from_left_background_open = 0x7f01003b
studio.takasaki.clubm:attr/cornerRadius = 0x7f040144
studio.takasaki.clubm:animator/design_fab_show_motion_spec = 0x7f020002
studio.takasaki.clubm:dimen/mtrl_slider_tick_min_spacing = 0x7f0702ef
studio.takasaki.clubm:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f07020f
studio.takasaki.clubm:macro/m3_comp_elevated_button_container_color = 0x7f0d0029
studio.takasaki.clubm:dimen/m3_comp_slider_active_handle_width = 0x7f070187
studio.takasaki.clubm:color/mtrl_textinput_default_box_stroke_color = 0x7f060305
studio.takasaki.clubm:attr/backgroundInsetBottom = 0x7f04004e
studio.takasaki.clubm:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1303eb
studio.takasaki.clubm:attr/addElevationShadow = 0x7f040029
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600cf
studio.takasaki.clubm:dimen/m3_comp_search_bar_avatar_size = 0x7f070172
studio.takasaki.clubm:style/Widget.MaterialComponents.Badge = 0x7f130423
studio.takasaki.clubm:macro/m3_comp_filled_card_container_color = 0x7f0d0046
studio.takasaki.clubm:attr/content = 0x7f04012b
studio.takasaki.clubm:color/material_grey_300 = 0x7f060280
studio.takasaki.clubm:attr/navigationRailStyle = 0x7f040358
studio.takasaki.clubm:attr/autoSizeMaxTextSize = 0x7f04003f
studio.takasaki.clubm:attr/constraints = 0x7f04012a
studio.takasaki.clubm:style/Widget.Material3.MaterialCalendar.Day = 0x7f1303c9
studio.takasaki.clubm:color/design_error = 0x7f06005f
studio.takasaki.clubm:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0d000f
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_questionmarkicon = 0x7f08004f
studio.takasaki.clubm:attr/constraint_referenced_ids = 0x7f040129
studio.takasaki.clubm:attr/constraintSetStart = 0x7f040128
studio.takasaki.clubm:mipmap/ic_launcher_foreground = 0x7f0f0001
studio.takasaki.clubm:attr/isMaterialTheme = 0x7f04025c
studio.takasaki.clubm:attr/lStar = 0x7f04027e
studio.takasaki.clubm:color/material_personalized_color_primary_inverse = 0x7f0602ac
studio.takasaki.clubm:color/material_personalized_color_on_surface_variant = 0x7f0602a5
studio.takasaki.clubm:attr/keyboardIcon = 0x7f04027c
studio.takasaki.clubm:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0701d8
studio.takasaki.clubm:dimen/m3_card_disabled_z = 0x7f0700e8
studio.takasaki.clubm:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f130493
studio.takasaki.clubm:attr/colorTertiaryFixedDim = 0x7f040123
studio.takasaki.clubm:attr/maxButtonHeight = 0x7f040316
studio.takasaki.clubm:color/m3_sys_color_dark_on_tertiary = 0x7f060185
studio.takasaki.clubm:attr/materialCalendarHeaderSelection = 0x7f0402f5
studio.takasaki.clubm:id/textTop = 0x7f0901f0
studio.takasaki.clubm:dimen/m3_extended_fab_icon_padding = 0x7f0701b5
studio.takasaki.clubm:attr/listChoiceIndicatorSingleAnimated = 0x7f0402d0
studio.takasaki.clubm:attr/cornerSizeBottomLeft = 0x7f040147
studio.takasaki.clubm:attr/fabAlignmentModeEndMargin = 0x7f0401e6
studio.takasaki.clubm:id/autofill_inline_suggestion_end_icon = 0x7f090062
studio.takasaki.clubm:color/m3_sys_color_dark_on_error = 0x7f06017d
studio.takasaki.clubm:color/accent_material_light = 0x7f06001a
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f130194
studio.takasaki.clubm:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702c4
studio.takasaki.clubm:style/Base.Widget.AppCompat.ListMenuView = 0x7f1300eb
studio.takasaki.clubm:attr/roundingBorderWidth = 0x7f0403b9
studio.takasaki.clubm:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f070129
studio.takasaki.clubm:attr/itemVerticalPadding = 0x7f04027a
studio.takasaki.clubm:dimen/design_bottom_navigation_elevation = 0x7f070065
studio.takasaki.clubm:style/Base.Widget.AppCompat.TextView = 0x7f1300fe
studio.takasaki.clubm:attr/dialogCornerRadius = 0x7f040193
studio.takasaki.clubm:id/design_bottom_sheet = 0x7f0900a1
studio.takasaki.clubm:drawable/indeterminate_static = 0x7f0800fc
studio.takasaki.clubm:color/material_personalized_color_text_primary_inverse = 0x7f0602c0
studio.takasaki.clubm:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f13035d
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601da
studio.takasaki.clubm:style/Theme.Catalyst.RedBox = 0x7f13023d
studio.takasaki.clubm:attr/textInputFilledStyle = 0x7f04046c
studio.takasaki.clubm:style/Base.CardView = 0x7f130013
studio.takasaki.clubm:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f07026e
studio.takasaki.clubm:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f130255
studio.takasaki.clubm:attr/colorSwitchThumbNormal = 0x7f04011f
studio.takasaki.clubm:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f130409
studio.takasaki.clubm:attr/contentPadding = 0x7f040133
studio.takasaki.clubm:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1300c5
studio.takasaki.clubm:integer/m3_sys_shape_corner_full_corner_family = 0x7f0a0024
studio.takasaki.clubm:dimen/m3_navigation_rail_default_width = 0x7f0701cd
studio.takasaki.clubm:dimen/design_bottom_navigation_item_max_width = 0x7f070068
studio.takasaki.clubm:attr/minWidth = 0x7f040326
studio.takasaki.clubm:anim/abc_grow_fade_in_from_bottom = 0x7f010002
studio.takasaki.clubm:attr/colorSurfaceContainerLowest = 0x7f04011b
studio.takasaki.clubm:string/abc_searchview_description_submit = 0x7f120018
studio.takasaki.clubm:color/m3_ref_palette_tertiary90 = 0x7f060167
studio.takasaki.clubm:string/link_description = 0x7f120096
studio.takasaki.clubm:attr/colorSurfaceContainerHighest = 0x7f040119
studio.takasaki.clubm:color/material_dynamic_secondary80 = 0x7f06026e
studio.takasaki.clubm:layout/autofill_inline_suggestion = 0x7f0c001d
studio.takasaki.clubm:id/snap = 0x7f0901c3
studio.takasaki.clubm:attr/trackColorActive = 0x7f0404af
studio.takasaki.clubm:dimen/mtrl_calendar_dialog_background_inset = 0x7f070281
studio.takasaki.clubm:dimen/m3_btn_padding_right = 0x7f0700df
studio.takasaki.clubm:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f070167
studio.takasaki.clubm:attr/defaultDuration = 0x7f04018b
studio.takasaki.clubm:color/material_personalized_color_error = 0x7f06029a
studio.takasaki.clubm:attr/colorSecondaryFixedDim = 0x7f040113
studio.takasaki.clubm:string/bottomsheet_action_expand_halfway = 0x7f120028
studio.takasaki.clubm:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
studio.takasaki.clubm:id/group_divider = 0x7f0900ea
studio.takasaki.clubm:attr/textInputStyle = 0x7f040471
studio.takasaki.clubm:layout/notification_template_big_media = 0x7f0c0073
studio.takasaki.clubm:attr/trackStopIndicatorSize = 0x7f0404b7
studio.takasaki.clubm:attr/backgroundInsetTop = 0x7f040051
studio.takasaki.clubm:attr/autofillInlineSuggestionStartIconStyle = 0x7f040047
studio.takasaki.clubm:attr/actionMenuTextAppearance = 0x7f04000e
studio.takasaki.clubm:attr/iconGravity = 0x7f040244
studio.takasaki.clubm:color/abc_color_highlight_material = 0x7f060004
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f13002d
studio.takasaki.clubm:attr/colorPrimaryContainer = 0x7f040108
studio.takasaki.clubm:id/password_toggle = 0x7f09017c
studio.takasaki.clubm:attr/maxLines = 0x7f04031a
studio.takasaki.clubm:anim/rns_ios_from_right_foreground_open = 0x7f010041
studio.takasaki.clubm:id/error_title = 0x7f0900c6
studio.takasaki.clubm:attr/itemBackground = 0x7f04025e
studio.takasaki.clubm:dimen/m3_card_elevation = 0x7f0700ee
studio.takasaki.clubm:string/material_motion_easing_accelerated = 0x7f1200ae
studio.takasaki.clubm:drawable/rns_rounder_top_corners_shape = 0x7f08014a
studio.takasaki.clubm:attr/recyclerViewStyle = 0x7f0403a0
studio.takasaki.clubm:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f130160
studio.takasaki.clubm:id/inward = 0x7f090103
studio.takasaki.clubm:attr/layoutManager = 0x7f040288
studio.takasaki.clubm:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701d4
studio.takasaki.clubm:attr/colorOnTertiaryFixed = 0x7f040103
studio.takasaki.clubm:attr/colorOnTertiary = 0x7f040101
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f130079
studio.takasaki.clubm:attr/drawableLeftCompat = 0x7f0401a5
studio.takasaki.clubm:style/TextAppearance.Design.HelperText = 0x7f1301e2
studio.takasaki.clubm:string/fcm_fallback_notification_channel_label = 0x7f120078
studio.takasaki.clubm:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f08010f
studio.takasaki.clubm:attr/circularProgressIndicatorStyle = 0x7f0400cd
studio.takasaki.clubm:attr/actionModeBackground = 0x7f040010
studio.takasaki.clubm:color/m3_popupmenu_overlay_color = 0x7f0600b5
studio.takasaki.clubm:drawable/navigation_empty_icon = 0x7f080137
studio.takasaki.clubm:dimen/mtrl_high_ripple_default_alpha = 0x7f0702be
studio.takasaki.clubm:attr/motionEasingEmphasizedInterpolator = 0x7f040343
studio.takasaki.clubm:attr/behavior_overlapTop = 0x7f040073
studio.takasaki.clubm:attr/titleTextColor = 0x7f04049d
studio.takasaki.clubm:attr/extendMotionSpec = 0x7f0401dd
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f130305
studio.takasaki.clubm:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1301cd
studio.takasaki.clubm:attr/colorSurfaceContainerLow = 0x7f04011a
studio.takasaki.clubm:attr/collapsedTitleGravity = 0x7f0400de
studio.takasaki.clubm:attr/elevation = 0x7f0401b7
studio.takasaki.clubm:dimen/design_snackbar_action_text_color_alpha = 0x7f070082
studio.takasaki.clubm:id/pooling_container_listener_holder_tag = 0x7f090183
studio.takasaki.clubm:attr/colorOnPrimarySurface = 0x7f0400f9
studio.takasaki.clubm:attr/colorScheme = 0x7f04010f
studio.takasaki.clubm:attr/alertDialogCenterButtons = 0x7f04002b
studio.takasaki.clubm:drawable/abc_scrubber_track_mtrl_alpha = 0x7f08009f
studio.takasaki.clubm:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f130331
studio.takasaki.clubm:attr/maxCharacterCount = 0x7f040317
studio.takasaki.clubm:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0701fd
studio.takasaki.clubm:styleable/SwipeRefreshLayout = 0x7f140088
studio.takasaki.clubm:attr/circleRadius = 0x7f0400cc
studio.takasaki.clubm:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f13022b
studio.takasaki.clubm:color/mtrl_tabs_legacy_text_color_selector = 0x7f060302
studio.takasaki.clubm:attr/colorAccent = 0x7f0400e7
studio.takasaki.clubm:style/Base.Theme.AppCompat.Dialog = 0x7f130050
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601bd
studio.takasaki.clubm:dimen/design_navigation_icon_size = 0x7f07007a
studio.takasaki.clubm:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0d0133
studio.takasaki.clubm:color/m3_ref_palette_error70 = 0x7f060119
studio.takasaki.clubm:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010026
studio.takasaki.clubm:attr/color = 0x7f0400e6
studio.takasaki.clubm:color/m3_ref_palette_error20 = 0x7f060114
studio.takasaki.clubm:attr/collapsingToolbarLayoutMediumSize = 0x7f0400e3
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f130302
studio.takasaki.clubm:color/material_harmonized_color_error = 0x7f060286
studio.takasaki.clubm:color/m3_dark_primary_text_disable_only = 0x7f060095
studio.takasaki.clubm:drawable/abc_list_divider_material = 0x7f08008a
studio.takasaki.clubm:id/open_search_view_edit_text = 0x7f09016b
studio.takasaki.clubm:attr/colorOnPrimaryFixed = 0x7f0400f7
studio.takasaki.clubm:drawable/mtrl_popupmenu_background = 0x7f080128
studio.takasaki.clubm:attr/cropBorderCornerColor = 0x7f040155
studio.takasaki.clubm:attr/showDelay = 0x7f0403d7
studio.takasaki.clubm:attr/animateNavigationIcon = 0x7f040033
studio.takasaki.clubm:drawable/abc_ic_menu_overflow_material = 0x7f080082
studio.takasaki.clubm:dimen/mtrl_navigation_rail_elevation = 0x7f0702d2
studio.takasaki.clubm:attr/collapseContentDescription = 0x7f0400db
studio.takasaki.clubm:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f07028f
studio.takasaki.clubm:drawable/common_google_signin_btn_icon_dark_focused = 0x7f0800c2
studio.takasaki.clubm:attr/hintEnabled = 0x7f04023a
studio.takasaki.clubm:styleable/MotionTelltales = 0x7f140068
studio.takasaki.clubm:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f08007e
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1301f3
studio.takasaki.clubm:color/m3_ref_palette_tertiary95 = 0x7f060168
studio.takasaki.clubm:attr/buttonBarPositiveButtonStyle = 0x7f04008f
studio.takasaki.clubm:dimen/material_helper_text_font_1_3_padding_top = 0x7f070243
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1302f9
studio.takasaki.clubm:attr/customBoolean = 0x7f04017e
studio.takasaki.clubm:color/design_dark_default_color_on_error = 0x7f060048
studio.takasaki.clubm:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07025a
studio.takasaki.clubm:attr/colorSurfaceContainer = 0x7f040117
studio.takasaki.clubm:attr/tabPaddingEnd = 0x7f040432
studio.takasaki.clubm:attr/percentX = 0x7f04037e
studio.takasaki.clubm:color/material_dynamic_tertiary0 = 0x7f060272
studio.takasaki.clubm:string/catalyst_copy_button = 0x7f120033
studio.takasaki.clubm:attr/buttonIconTint = 0x7f040095
studio.takasaki.clubm:attr/closeIconEnabled = 0x7f0400d4
studio.takasaki.clubm:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f13005d
studio.takasaki.clubm:id/errorDetails = 0x7f0900c1
studio.takasaki.clubm:drawable/ic_rotate_left_24 = 0x7f0800f8
studio.takasaki.clubm:attr/chipStrokeColor = 0x7f0400c7
studio.takasaki.clubm:attr/expandActivityOverflowButtonDrawable = 0x7f0401d2
studio.takasaki.clubm:attr/windowMinWidthMinor = 0x7f0404dc
studio.takasaki.clubm:attr/clockHandColor = 0x7f0400d0
studio.takasaki.clubm:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f070191
studio.takasaki.clubm:animator/fragment_fade_exit = 0x7f020006
studio.takasaki.clubm:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f130420
studio.takasaki.clubm:drawable/ic_clock_black_24dp = 0x7f0800e9
studio.takasaki.clubm:drawable/__node_modules_reactnavigation_elements_lib_module_assets_backiconmask = 0x7f08002e
studio.takasaki.clubm:attr/cropShowLabel = 0x7f040171
studio.takasaki.clubm:drawable/ic_arrow_back_black_24 = 0x7f0800e1
studio.takasaki.clubm:dimen/m3_extended_fab_end_padding = 0x7f0701b4
studio.takasaki.clubm:attr/chipMinHeight = 0x7f0400c0
studio.takasaki.clubm:string/catalyst_report_button = 0x7f120048
studio.takasaki.clubm:attr/chipIconVisible = 0x7f0400bf
studio.takasaki.clubm:attr/contentScrim = 0x7f04013a
studio.takasaki.clubm:attr/buttonBarButtonStyle = 0x7f04008c
studio.takasaki.clubm:attr/dropdownListPreferredItemHeight = 0x7f0401b1
studio.takasaki.clubm:attr/chipEndPadding = 0x7f0400b9
studio.takasaki.clubm:id/right_icon = 0x7f090195
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0601b0
studio.takasaki.clubm:style/Base.Widget.AppCompat.Button.Colored = 0x7f1300d7
studio.takasaki.clubm:attr/background = 0x7f04004b
studio.takasaki.clubm:integer/m3_sys_motion_path = 0x7f0a0021
studio.takasaki.clubm:attr/cropSaveBitmapToInstanceState = 0x7f04016d
studio.takasaki.clubm:layout/mtrl_picker_header_dialog = 0x7f0c0066
studio.takasaki.clubm:attr/actionModePopupWindowStyle = 0x7f040018
studio.takasaki.clubm:id/split_action_bar = 0x7f0901c9
studio.takasaki.clubm:color/m3_ref_palette_secondary90 = 0x7f06015a
studio.takasaki.clubm:anim/m3_motion_fade_enter = 0x7f010029
studio.takasaki.clubm:attr/motionDurationExtraLong1 = 0x7f04032e
studio.takasaki.clubm:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070303
studio.takasaki.clubm:dimen/browser_actions_context_menu_min_padding = 0x7f070054
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_settingsfilledinactiveicon = 0x7f080054
studio.takasaki.clubm:color/m3_ref_palette_neutral60 = 0x7f06012b
studio.takasaki.clubm:attr/checkedState = 0x7f0400b5
studio.takasaki.clubm:style/Base.AlertDialog.AppCompat = 0x7f13000e
studio.takasaki.clubm:attr/checkedIconTint = 0x7f0400b3
studio.takasaki.clubm:attr/shapeCornerFamily = 0x7f0403d2
studio.takasaki.clubm:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080085
studio.takasaki.clubm:style/Widget.AppCompat.Button.Colored = 0x7f13031f
studio.takasaki.clubm:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070112
studio.takasaki.clubm:attr/fontFamily = 0x7f04021a
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_container_shape = 0x7f0d00b1
studio.takasaki.clubm:integer/m3_btn_anim_delay_ms = 0x7f0a000c
studio.takasaki.clubm:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f080117
studio.takasaki.clubm:dimen/m3_comp_outlined_card_outline_width = 0x7f070157
studio.takasaki.clubm:styleable/Motion = 0x7f140064
studio.takasaki.clubm:color/design_default_color_primary = 0x7f060059
studio.takasaki.clubm:layout/abc_action_bar_title_item = 0x7f0c0000
studio.takasaki.clubm:attr/rangeFillColor = 0x7f04039c
studio.takasaki.clubm:style/Widget.Material3.Chip.Input = 0x7f130399
studio.takasaki.clubm:attr/chainUseRtl = 0x7f0400a7
studio.takasaki.clubm:macro/m3_comp_snackbar_container_color = 0x7f0d0113
studio.takasaki.clubm:attr/helperTextTextAppearance = 0x7f040232
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f13006c
studio.takasaki.clubm:color/m3_ref_palette_neutral30 = 0x7f060126
studio.takasaki.clubm:style/Base.AlertDialog.AppCompat.Light = 0x7f13000f
studio.takasaki.clubm:attr/badgeWithTextRadius = 0x7f040064
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_background = 0x7f06019e
studio.takasaki.clubm:drawable/abc_btn_check_material = 0x7f080069
studio.takasaki.clubm:string/mtrl_checkbox_state_description_unchecked = 0x7f1200cb
studio.takasaki.clubm:attr/cardForegroundColor = 0x7f0400a0
studio.takasaki.clubm:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f130063
studio.takasaki.clubm:attr/colorOnSurfaceVariant = 0x7f040100
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f13011a
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Display4 = 0x7f130021
studio.takasaki.clubm:attr/labelBehavior = 0x7f04027f
studio.takasaki.clubm:attr/cropCornerRadius = 0x7f04015d
studio.takasaki.clubm:attr/drawableRightCompat = 0x7f0401a6
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f0601a3
studio.takasaki.clubm:attr/cardElevation = 0x7f04009f
studio.takasaki.clubm:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
studio.takasaki.clubm:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
studio.takasaki.clubm:dimen/abc_edit_text_inset_top_material = 0x7f07002e
studio.takasaki.clubm:color/primary_dark_material_light = 0x7f06030e
studio.takasaki.clubm:id/startToEnd = 0x7f0901d3
studio.takasaki.clubm:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060110
studio.takasaki.clubm:attr/chipIcon = 0x7f0400bb
studio.takasaki.clubm:styleable/Capability = 0x7f14001a
studio.takasaki.clubm:attr/badgeText = 0x7f04005d
studio.takasaki.clubm:attr/tabIndicatorFullWidth = 0x7f040429
studio.takasaki.clubm:attr/cardCornerRadius = 0x7f04009e
studio.takasaki.clubm:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f13048e
studio.takasaki.clubm:layout/bottom_sheet = 0x7f0c001e
studio.takasaki.clubm:attr/fastScrollVerticalThumbDrawable = 0x7f0401f4
studio.takasaki.clubm:attr/buttonStyleSmall = 0x7f04009a
studio.takasaki.clubm:attr/dayInvalidStyle = 0x7f040187
studio.takasaki.clubm:id/BOTTOM_START = 0x7f090002
studio.takasaki.clubm:attr/paddingEnd = 0x7f04036b
studio.takasaki.clubm:attr/bottomAppBarStyle = 0x7f04007a
studio.takasaki.clubm:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07012b
studio.takasaki.clubm:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
studio.takasaki.clubm:attr/textAppearanceBodyMedium = 0x7f040445
studio.takasaki.clubm:attr/flow_padding = 0x7f040213
studio.takasaki.clubm:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0702f6
studio.takasaki.clubm:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070124
studio.takasaki.clubm:attr/flow_verticalStyle = 0x7f040217
studio.takasaki.clubm:id/useLogo = 0x7f090213
studio.takasaki.clubm:attr/buttonCompat = 0x7f040091
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601d7
studio.takasaki.clubm:drawable/m3_selection_control_ripple = 0x7f080103
studio.takasaki.clubm:anim/rns_fade_out = 0x7f010038
studio.takasaki.clubm:id/rn_redbox_report_button = 0x7f09019d
studio.takasaki.clubm:attr/collapseIcon = 0x7f0400dc
studio.takasaki.clubm:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f13042e
studio.takasaki.clubm:style/Widget.AppCompat.Button.Borderless = 0x7f13031c
studio.takasaki.clubm:dimen/m3_btn_elevation = 0x7f0700d4
studio.takasaki.clubm:attr/expandedTitleMargin = 0x7f0401d6
studio.takasaki.clubm:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f130408
studio.takasaki.clubm:attr/boxStrokeWidthFocused = 0x7f04008a
studio.takasaki.clubm:color/primary_text_disabled_material_dark = 0x7f060313
studio.takasaki.clubm:attr/layout_goneMarginRight = 0x7f0402be
studio.takasaki.clubm:color/m3_ref_palette_error90 = 0x7f06011b
studio.takasaki.clubm:attr/boxStrokeWidth = 0x7f040089
studio.takasaki.clubm:dimen/design_fab_border_width = 0x7f070071
studio.takasaki.clubm:layout/notification_media_action = 0x7f0c0071
studio.takasaki.clubm:attr/drawableBottomCompat = 0x7f0401a3
studio.takasaki.clubm:string/item_view_role_description = 0x7f120095
studio.takasaki.clubm:dimen/m3_chip_dragged_translation_z = 0x7f0700fa
studio.takasaki.clubm:attr/windowFixedWidthMajor = 0x7f0404d9
studio.takasaki.clubm:attr/layout_constraintHorizontal_bias = 0x7f0402a2
studio.takasaki.clubm:attr/maxVelocity = 0x7f04031c
studio.takasaki.clubm:attr/actionDropDownStyle = 0x7f04000c
studio.takasaki.clubm:layout/abc_popup_menu_item_layout = 0x7f0c0013
studio.takasaki.clubm:attr/splitTrack = 0x7f0403f2
studio.takasaki.clubm:color/m3_ref_palette_secondary10 = 0x7f060151
studio.takasaki.clubm:style/Base.V22.Theme.AppCompat = 0x7f1300b3
studio.takasaki.clubm:attr/autoTransition = 0x7f040044
studio.takasaki.clubm:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f13030e
studio.takasaki.clubm:dimen/mtrl_slider_halo_radius = 0x7f0702e9
studio.takasaki.clubm:attr/borderWidth = 0x7f040078
studio.takasaki.clubm:attr/cardPreventCornerOverlap = 0x7f0400a2
studio.takasaki.clubm:attr/behavior_significantVelocityThreshold = 0x7f040076
studio.takasaki.clubm:attr/cropperLabelTextColor = 0x7f040176
studio.takasaki.clubm:color/material_personalized_color_secondary_text = 0x7f0602b1
studio.takasaki.clubm:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07011b
studio.takasaki.clubm:color/material_timepicker_button_stroke = 0x7f0602cf
studio.takasaki.clubm:color/m3_sys_color_light_secondary_container = 0x7f0601fe
studio.takasaki.clubm:style/Platform.V25.AppCompat = 0x7f130150
studio.takasaki.clubm:attr/backgroundTintMode = 0x7f040056
studio.takasaki.clubm:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701a2
studio.takasaki.clubm:attr/borderlessButtonStyle = 0x7f040079
studio.takasaki.clubm:attr/behavior_halfExpandedRatio = 0x7f040071
studio.takasaki.clubm:attr/altSrc = 0x7f040031
studio.takasaki.clubm:color/common_google_signin_btn_tint = 0x7f060042
studio.takasaki.clubm:attr/cropperLabelText = 0x7f040175
studio.takasaki.clubm:dimen/m3_navigation_item_vertical_padding = 0x7f0701ca
studio.takasaki.clubm:anim/catalyst_fade_out = 0x7f010019
studio.takasaki.clubm:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f130387
studio.takasaki.clubm:color/button_material_light = 0x7f06002d
studio.takasaki.clubm:attr/cursorErrorColor = 0x7f04017c
studio.takasaki.clubm:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f130380
studio.takasaki.clubm:dimen/abc_text_size_large_material = 0x7f070048
studio.takasaki.clubm:attr/behavior_draggable = 0x7f04006e
studio.takasaki.clubm:attr/behavior_autoHide = 0x7f04006c
studio.takasaki.clubm:attr/cardBackgroundColor = 0x7f04009d
studio.takasaki.clubm:style/Widget.MaterialComponents.Chip.Entry = 0x7f13043c
studio.takasaki.clubm:attr/collapsingToolbarLayoutStyle = 0x7f0400e5
studio.takasaki.clubm:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1300ad
studio.takasaki.clubm:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700cc
studio.takasaki.clubm:dimen/m3_appbar_size_large = 0x7f0700ae
studio.takasaki.clubm:attr/materialSearchViewPrefixStyle = 0x7f04030b
studio.takasaki.clubm:attr/boxCornerRadiusTopStart = 0x7f040086
studio.takasaki.clubm:id/SHIFT = 0x7f09000a
studio.takasaki.clubm:attr/chipSpacing = 0x7f0400c2
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_threefingerlongpressicon = 0x7f08005b
studio.takasaki.clubm:string/mtrl_checkbox_button_icon_path_checked = 0x7f1200c1
studio.takasaki.clubm:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f07017a
studio.takasaki.clubm:attr/touchRegionId = 0x7f0404ac
studio.takasaki.clubm:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0d005f
studio.takasaki.clubm:drawable/abc_vector_test = 0x7f0800b4
studio.takasaki.clubm:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f130358
studio.takasaki.clubm:drawable/mtrl_switch_thumb_pressed = 0x7f08012e
studio.takasaki.clubm:attr/badgeWidth = 0x7f040062
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f130450
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1302ba
studio.takasaki.clubm:color/abc_secondary_text_material_dark = 0x7f060011
studio.takasaki.clubm:attr/trackDecoration = 0x7f0404b2
studio.takasaki.clubm:color/m3_ref_palette_neutral99 = 0x7f060135
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_extensionsfilledactiveicon = 0x7f08003f
studio.takasaki.clubm:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
studio.takasaki.clubm:color/design_default_color_secondary_variant = 0x7f06005d
studio.takasaki.clubm:style/TextAppearance.Compat.Notification = 0x7f1301d4
studio.takasaki.clubm:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f13014c
studio.takasaki.clubm:string/common_google_play_services_update_text = 0x7f12005c
studio.takasaki.clubm:attr/actionBarTabBarStyle = 0x7f040006
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1302f6
studio.takasaki.clubm:attr/fontProviderAuthority = 0x7f04021b
studio.takasaki.clubm:color/m3_sys_color_secondary_fixed = 0x7f060212
studio.takasaki.clubm:id/autoCompleteToEnd = 0x7f090060
studio.takasaki.clubm:attr/badgeHeight = 0x7f040058
studio.takasaki.clubm:attr/targetId = 0x7f04043d
studio.takasaki.clubm:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0702f4
studio.takasaki.clubm:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f13029a
studio.takasaki.clubm:string/mtrl_checkbox_state_description_checked = 0x7f1200c9
studio.takasaki.clubm:attr/badgeGravity = 0x7f040057
studio.takasaki.clubm:dimen/mtrl_calendar_text_input_padding_top = 0x7f070297
studio.takasaki.clubm:attr/tabIndicatorAnimationMode = 0x7f040427
studio.takasaki.clubm:dimen/design_fab_image_size = 0x7f070073
studio.takasaki.clubm:layout/abc_select_dialog_material = 0x7f0c001a
studio.takasaki.clubm:id/buttonPanel = 0x7f090074
studio.takasaki.clubm:drawable/m3_tabs_rounded_line_indicator = 0x7f080106
studio.takasaki.clubm:anim/design_snackbar_in = 0x7f010020
studio.takasaki.clubm:attr/autoSizeStepGranularity = 0x7f040042
studio.takasaki.clubm:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070255
studio.takasaki.clubm:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0d0092
studio.takasaki.clubm:attr/motionDurationShort3 = 0x7f04033c
studio.takasaki.clubm:color/material_slider_active_track_color = 0x7f0602c9
studio.takasaki.clubm:attr/colorOnTertiaryContainer = 0x7f040102
studio.takasaki.clubm:attr/backgroundImage = 0x7f04004d
studio.takasaki.clubm:id/rn_redbox_report_label = 0x7f09019e
studio.takasaki.clubm:attr/textLocale = 0x7f040472
studio.takasaki.clubm:id/exitUntilCollapsed = 0x7f0900c9
studio.takasaki.clubm:attr/materialCardViewOutlinedStyle = 0x7f0402ff
studio.takasaki.clubm:attr/chipStyle = 0x7f0400c9
studio.takasaki.clubm:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1302e1
studio.takasaki.clubm:id/navigation_bar_item_active_indicator_view = 0x7f090154
studio.takasaki.clubm:anim/rns_slide_out_to_left = 0x7f01004a
studio.takasaki.clubm:string/abc_toolbar_collapse_description = 0x7f12001c
studio.takasaki.clubm:macro/m3_comp_divider_color = 0x7f0d0028
studio.takasaki.clubm:attr/dropDownBackgroundTint = 0x7f0401af
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0d00b8
studio.takasaki.clubm:dimen/material_textinput_default_width = 0x7f070245
studio.takasaki.clubm:attr/backgroundColor = 0x7f04004c
studio.takasaki.clubm:attr/actionModeShareDrawable = 0x7f04001a
studio.takasaki.clubm:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
studio.takasaki.clubm:attr/colorOnContainer = 0x7f0400f1
studio.takasaki.clubm:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1300fd
studio.takasaki.clubm:attr/textAppearanceHeadline2 = 0x7f04044d
studio.takasaki.clubm:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080013
studio.takasaki.clubm:color/mtrl_chip_background_color = 0x7f0602df
studio.takasaki.clubm:attr/cardViewStyle = 0x7f0400a4
studio.takasaki.clubm:attr/autoCompleteTextViewStyle = 0x7f04003d
studio.takasaki.clubm:dimen/m3_timepicker_display_stroke_width = 0x7f070222
studio.takasaki.clubm:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
studio.takasaki.clubm:string/m3_ref_typeface_brand_medium = 0x7f120098
studio.takasaki.clubm:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0d00d6
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0600d9
studio.takasaki.clubm:attr/itemShapeInsetStart = 0x7f040270
studio.takasaki.clubm:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
studio.takasaki.clubm:animator/fragment_open_enter = 0x7f020007
studio.takasaki.clubm:attr/actionBarWidgetTheme = 0x7f04000a
studio.takasaki.clubm:attr/alphabeticModifiers = 0x7f040030
studio.takasaki.clubm:anim/rns_fade_to_bottom = 0x7f010039
studio.takasaki.clubm:styleable/StateSet = 0x7f140087
studio.takasaki.clubm:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
studio.takasaki.clubm:id/postLayout = 0x7f090185
studio.takasaki.clubm:attr/contentPaddingLeft = 0x7f040136
studio.takasaki.clubm:color/m3_ref_palette_dynamic_primary50 = 0x7f0600f0
studio.takasaki.clubm:attr/attributeName = 0x7f04003b
studio.takasaki.clubm:color/m3_sys_color_dark_inverse_on_surface = 0x7f060179
studio.takasaki.clubm:color/m3_slider_active_track_color_legacy = 0x7f06016e
studio.takasaki.clubm:color/m3_ref_palette_tertiary50 = 0x7f060163
studio.takasaki.clubm:style/TextAppearance.Material3.HeadlineLarge = 0x7f130200
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0601ac
studio.takasaki.clubm:style/Base.Widget.AppCompat.ProgressBar = 0x7f1300f3
studio.takasaki.clubm:attr/logoAdjustViewBounds = 0x7f0402de
studio.takasaki.clubm:attr/iconPadding = 0x7f040245
studio.takasaki.clubm:string/fab_transformation_sheet_behavior = 0x7f120072
studio.takasaki.clubm:attr/logoDescription = 0x7f0402df
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601be
studio.takasaki.clubm:string/m3_ref_typeface_brand_regular = 0x7f120099
studio.takasaki.clubm:attr/actionLayout = 0x7f04000d
studio.takasaki.clubm:color/m3_ref_palette_error99 = 0x7f06011d
studio.takasaki.clubm:attr/state_with_icon = 0x7f040405
studio.takasaki.clubm:style/Theme.FullScreenDialog = 0x7f130246
studio.takasaki.clubm:color/m3_textfield_indicator_text_color = 0x7f060220
studio.takasaki.clubm:id/match_parent = 0x7f09011a
studio.takasaki.clubm:attr/flow_horizontalStyle = 0x7f04020d
studio.takasaki.clubm:color/material_personalized_color_on_primary_container = 0x7f0602a0
studio.takasaki.clubm:attr/animationMode = 0x7f040035
studio.takasaki.clubm:attr/layout_constrainedHeight = 0x7f04028e
studio.takasaki.clubm:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f130179
studio.takasaki.clubm:string/material_minute_selection = 0x7f1200ac
studio.takasaki.clubm:id/view_tree_lifecycle_owner = 0x7f090219
studio.takasaki.clubm:id/accessibility_custom_action_13 = 0x7f09001b
studio.takasaki.clubm:attr/itemTextAppearanceActiveBoldEnabled = 0x7f040277
studio.takasaki.clubm:color/m3_ref_palette_secondary60 = 0x7f060157
studio.takasaki.clubm:attr/rippleColor = 0x7f0403a9
studio.takasaki.clubm:attr/contentInsetLeft = 0x7f04012f
studio.takasaki.clubm:attr/alertDialogTheme = 0x7f04002d
studio.takasaki.clubm:attr/animate_relativeTo = 0x7f040034
studio.takasaki.clubm:color/material_dynamic_secondary95 = 0x7f060270
studio.takasaki.clubm:attr/alpha = 0x7f04002f
studio.takasaki.clubm:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f13021f
studio.takasaki.clubm:attr/tintNavigationIcon = 0x7f040490
studio.takasaki.clubm:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0d00a0
studio.takasaki.clubm:dimen/m3_comp_fab_primary_large_container_height = 0x7f07011f
studio.takasaki.clubm:style/ThemeOverlay.Material3.Snackbar = 0x7f1302df
studio.takasaki.clubm:dimen/mtrl_progress_circular_inset_medium = 0x7f0702da
studio.takasaki.clubm:style/Base.Theme.SplashScreen = 0x7f13007b
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0d00b3
studio.takasaki.clubm:id/decelerateAndComplete = 0x7f09009d
studio.takasaki.clubm:attr/cropScaleType = 0x7f04016e
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600dc
studio.takasaki.clubm:attr/cornerFamily = 0x7f04013f
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600e3
studio.takasaki.clubm:attr/useDrawerArrowDrawable = 0x7f0404c5
studio.takasaki.clubm:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
studio.takasaki.clubm:attr/ratingBarStyle = 0x7f04039d
studio.takasaki.clubm:string/pick_image_chooser_title = 0x7f120104
studio.takasaki.clubm:attr/actionOverflowMenuStyle = 0x7f040020
studio.takasaki.clubm:attr/activityChooserViewStyle = 0x7f040025
studio.takasaki.clubm:styleable/PopupWindowBackgroundState = 0x7f140070
studio.takasaki.clubm:string/mtrl_picker_text_input_day_abbr = 0x7f1200ec
studio.takasaki.clubm:attr/layout_constraintHeight_min = 0x7f0402a0
studio.takasaki.clubm:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1301b3
studio.takasaki.clubm:attr/behavior_hideable = 0x7f040072
studio.takasaki.clubm:attr/verticalOffset = 0x7f0404c8
studio.takasaki.clubm:attr/cropGuidelinesThickness = 0x7f040163
studio.takasaki.clubm:attr/actionModeWebSearchDrawable = 0x7f04001e
studio.takasaki.clubm:style/Widget.Material3.Snackbar = 0x7f130401
studio.takasaki.clubm:macro/m3_comp_badge_large_label_text_type = 0x7f0d0004
studio.takasaki.clubm:attr/materialSearchBarStyle = 0x7f04030a
studio.takasaki.clubm:attr/contentInsetEnd = 0x7f04012d
studio.takasaki.clubm:dimen/fastscroll_margin = 0x7f070094
studio.takasaki.clubm:attr/layout_behavior = 0x7f04028b
studio.takasaki.clubm:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07017d
studio.takasaki.clubm:color/m3_filled_icon_button_container_color_selector = 0x7f0600a7
studio.takasaki.clubm:attr/iconTintMode = 0x7f040249
studio.takasaki.clubm:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1302bc
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Medium = 0x7f130028
studio.takasaki.clubm:color/m3_sys_color_on_tertiary_fixed = 0x7f06020e
studio.takasaki.clubm:attr/contentInsetStart = 0x7f040131
studio.takasaki.clubm:color/mtrl_choice_chip_ripple_color = 0x7f0602e4
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0d00ba
studio.takasaki.clubm:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0600e2
studio.takasaki.clubm:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f130070
studio.takasaki.clubm:attr/barrierAllowsGoneWidgets = 0x7f040069
studio.takasaki.clubm:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
studio.takasaki.clubm:attr/badgeWidePadding = 0x7f040061
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.Light = 0x7f13025e
studio.takasaki.clubm:attr/bottomNavigationStyle = 0x7f04007c
studio.takasaki.clubm:anim/abc_slide_out_bottom = 0x7f010008
studio.takasaki.clubm:attr/actionModeSelectAllDrawable = 0x7f040019
studio.takasaki.clubm:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f13042b
studio.takasaki.clubm:attr/collapsingToolbarLayoutLargeSize = 0x7f0400e1
studio.takasaki.clubm:style/Widget.AppCompat.Button.Small = 0x7f130320
studio.takasaki.clubm:integer/mtrl_badge_max_character_count = 0x7f0a002f
studio.takasaki.clubm:color/m3_sys_color_light_inverse_on_surface = 0x7f0601eb
studio.takasaki.clubm:attr/actionModePasteDrawable = 0x7f040017
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f130308
studio.takasaki.clubm:attr/layout_constraintGuide_end = 0x7f04029c
studio.takasaki.clubm:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1300ba
studio.takasaki.clubm:attr/dividerThickness = 0x7f04019d
studio.takasaki.clubm:color/common_google_signin_btn_text_light_focused = 0x7f060040
studio.takasaki.clubm:styleable/LinearLayoutCompat = 0x7f140049
studio.takasaki.clubm:attr/actionModeCloseContentDescription = 0x7f040012
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1302fc
studio.takasaki.clubm:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f0800bb
studio.takasaki.clubm:attr/colorOnPrimaryFixedVariant = 0x7f0400f8
studio.takasaki.clubm:attr/thumbIcon = 0x7f040479
studio.takasaki.clubm:attr/motionStagger = 0x7f04034e
studio.takasaki.clubm:attr/colorBackgroundFloating = 0x7f0400e8
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601c9
studio.takasaki.clubm:attr/titleMargin = 0x7f040495
studio.takasaki.clubm:style/Widget.AppCompat.DrawerArrowToggle = 0x7f130326
studio.takasaki.clubm:id/action_mode_bar_stub = 0x7f09004c
studio.takasaki.clubm:attr/behavior_saveFlags = 0x7f040075
studio.takasaki.clubm:animator/m3_chip_state_list_anim = 0x7f02000e
studio.takasaki.clubm:attr/autofillInlineSuggestionSubtitle = 0x7f040048
studio.takasaki.clubm:style/Widget.Material3.Slider.Legacy.Label = 0x7f130400
studio.takasaki.clubm:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080074
studio.takasaki.clubm:dimen/m3_comp_outlined_card_icon_size = 0x7f070156
studio.takasaki.clubm:attr/fontStyle = 0x7f040222
studio.takasaki.clubm:attr/actionButtonStyle = 0x7f04000b
studio.takasaki.clubm:id/fitToContents = 0x7f0900da
studio.takasaki.clubm:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07023c
studio.takasaki.clubm:attr/triggerSlack = 0x7f0404c2
studio.takasaki.clubm:attr/dragThreshold = 0x7f0401a1
studio.takasaki.clubm:dimen/m3_chip_checked_hovered_translation_z = 0x7f0700f7
studio.takasaki.clubm:string/path_password_eye = 0x7f1200ff
studio.takasaki.clubm:dimen/abc_control_padding_material = 0x7f07001a
studio.takasaki.clubm:attr/actionTextColorAlpha = 0x7f040022
studio.takasaki.clubm:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1303c0
studio.takasaki.clubm:color/m3_ref_palette_neutral94 = 0x7f060131
studio.takasaki.clubm:attr/fontProviderFetchTimeout = 0x7f04021e
studio.takasaki.clubm:id/open_search_view_toolbar_container = 0x7f090172
studio.takasaki.clubm:attr/itemShapeInsetEnd = 0x7f04026f
studio.takasaki.clubm:attr/motionDurationMedium1 = 0x7f040336
studio.takasaki.clubm:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1302b7
studio.takasaki.clubm:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080024
studio.takasaki.clubm:dimen/m3_comp_filled_card_icon_size = 0x7f07012c
studio.takasaki.clubm:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f130467
studio.takasaki.clubm:attr/floatingActionButtonSmallSurfaceStyle = 0x7f040201
studio.takasaki.clubm:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f07009f
studio.takasaki.clubm:color/material_personalized_color_primary_container = 0x7f0602ab
studio.takasaki.clubm:string/call_notification_answer_action = 0x7f12002b
studio.takasaki.clubm:color/m3_ref_palette_secondary100 = 0x7f060152
studio.takasaki.clubm:attr/showMarker = 0x7f0403d9
studio.takasaki.clubm:attr/cropShape = 0x7f04016f
studio.takasaki.clubm:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f130456
studio.takasaki.clubm:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702af
studio.takasaki.clubm:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0d014b
studio.takasaki.clubm:attr/layout_constraintStart_toStartOf = 0x7f0402ac
studio.takasaki.clubm:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f130123
studio.takasaki.clubm:macro/m3_comp_outlined_card_container_color = 0x7f0d00a8
studio.takasaki.clubm:anim/rns_ios_from_right_foreground_close = 0x7f010040
studio.takasaki.clubm:attr/liftOnScrollColor = 0x7f0402c8
studio.takasaki.clubm:attr/autoShowKeyboard = 0x7f04003e
studio.takasaki.clubm:color/m3_sys_color_dark_inverse_primary = 0x7f06017a
studio.takasaki.clubm:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0d015f
studio.takasaki.clubm:attr/subheaderTextAppearance = 0x7f04040f
studio.takasaki.clubm:styleable/SearchView = 0x7f14007a
studio.takasaki.clubm:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
studio.takasaki.clubm:dimen/mtrl_calendar_header_height = 0x7f070285
studio.takasaki.clubm:attr/layoutDescription = 0x7f040286
studio.takasaki.clubm:styleable/SimpleDraweeView = 0x7f14007f
studio.takasaki.clubm:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
studio.takasaki.clubm:attr/actionBarSplitStyle = 0x7f040004
studio.takasaki.clubm:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0d0159
studio.takasaki.clubm:dimen/mtrl_switch_track_width = 0x7f0702ff
studio.takasaki.clubm:attr/backgroundStacked = 0x7f040054
studio.takasaki.clubm:attr/topInsetScrimEnabled = 0x7f0404a9
studio.takasaki.clubm:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f13047b
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601c4
studio.takasaki.clubm:attr/layout_constraintEnd_toEndOf = 0x7f040299
studio.takasaki.clubm:animator/mtrl_chip_state_list_anim = 0x7f020018
studio.takasaki.clubm:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f0800b1
studio.takasaki.clubm:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1302f3
studio.takasaki.clubm:drawable/abc_cab_background_internal_bg = 0x7f080075
studio.takasaki.clubm:id/matrix = 0x7f09012f
studio.takasaki.clubm:color/mtrl_chip_text_color = 0x7f0602e2
studio.takasaki.clubm:style/Widget.MaterialComponents.Slider = 0x7f130472
studio.takasaki.clubm:anim/m3_bottom_sheet_slide_in = 0x7f010027
studio.takasaki.clubm:dimen/abc_search_view_preferred_height = 0x7f070036
studio.takasaki.clubm:animator/m3_extended_fab_state_list_animator = 0x7f020014
studio.takasaki.clubm:attr/tabIndicator = 0x7f040425
studio.takasaki.clubm:anim/mtrl_bottom_sheet_slide_out = 0x7f010030
studio.takasaki.clubm:color/m3_dynamic_default_color_secondary_text = 0x7f06009e
studio.takasaki.clubm:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0d0070
studio.takasaki.clubm:attr/css = 0x7f040179
studio.takasaki.clubm:anim/linear_indeterminate_line2_head_interpolator = 0x7f010025
studio.takasaki.clubm:attr/touchAnchorId = 0x7f0404aa
studio.takasaki.clubm:style/TextAppearance.AppCompat.Display3 = 0x7f1301ab
studio.takasaki.clubm:attr/textAppearanceLabelLarge = 0x7f040455
studio.takasaki.clubm:style/Base.Widget.AppCompat.ListView = 0x7f1300ed
studio.takasaki.clubm:layout/select_dialog_item_material = 0x7f0c0082
studio.takasaki.clubm:attr/colorPrimaryFixed = 0x7f04010a
studio.takasaki.clubm:dimen/material_clock_face_margin_top = 0x7f07022a
studio.takasaki.clubm:attr/colorOnSecondaryFixed = 0x7f0400fc
studio.takasaki.clubm:attr/listPreferredItemHeight = 0x7f0402d6
studio.takasaki.clubm:anim/rns_ios_from_right_background_open = 0x7f01003f
studio.takasaki.clubm:color/material_personalized_color_on_error = 0x7f06029d
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0601a7
studio.takasaki.clubm:color/m3_sys_color_dynamic_dark_on_surface = 0x7f0601a5
studio.takasaki.clubm:layout/mtrl_calendar_months = 0x7f0c005d
studio.takasaki.clubm:color/m3_ref_palette_neutral6 = 0x7f06012a
studio.takasaki.clubm:attr/iconSize = 0x7f040246
studio.takasaki.clubm:array/keyguard_biometric_and_credential_exclude_vendors = 0x7f030005
studio.takasaki.clubm:attr/roundWithOverlayColor = 0x7f0403b5
studio.takasaki.clubm:attr/backHandlingEnabled = 0x7f04004a
studio.takasaki.clubm:attr/maxActionInlineWidth = 0x7f040315
studio.takasaki.clubm:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1303ac
studio.takasaki.clubm:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1200eb
studio.takasaki.clubm:animator/m3_card_state_list_anim = 0x7f02000d
studio.takasaki.clubm:string/fingerprint_error_hw_not_present = 0x7f12007c
studio.takasaki.clubm:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0d0122
studio.takasaki.clubm:drawable/mtrl_dropdown_arrow = 0x7f08011e
studio.takasaki.clubm:anim/rns_fade_from_bottom = 0x7f010036
studio.takasaki.clubm:attr/dividerInsetStart = 0x7f04019b
studio.takasaki.clubm:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f130191
studio.takasaki.clubm:attr/badgeWithTextHeight = 0x7f040063
studio.takasaki.clubm:attr/cropGuidelines = 0x7f040161
studio.takasaki.clubm:attr/motionPath = 0x7f04034b
studio.takasaki.clubm:animator/fragment_close_exit = 0x7f020004
studio.takasaki.clubm:id/touch_outside = 0x7f090201
studio.takasaki.clubm:attr/itemShapeInsetBottom = 0x7f04026e
studio.takasaki.clubm:layout/m3_alert_dialog = 0x7f0c003c
studio.takasaki.clubm:dimen/cardview_default_radius = 0x7f070057
studio.takasaki.clubm:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401e0
studio.takasaki.clubm:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0d004e
studio.takasaki.clubm:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
studio.takasaki.clubm:styleable/BottomSheetBehavior_Layout = 0x7f140018
studio.takasaki.clubm:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
studio.takasaki.clubm:attr/helperText = 0x7f040230
studio.takasaki.clubm:attr/colorOnBackground = 0x7f0400f0
studio.takasaki.clubm:styleable/Insets = 0x7f14003f
studio.takasaki.clubm:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400e4
studio.takasaki.clubm:attr/layout_insetEdge = 0x7f0402c1
studio.takasaki.clubm:styleable/CardView = 0x7f14001b
studio.takasaki.clubm:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f130253
studio.takasaki.clubm:attr/roundBottomRight = 0x7f0403ae
studio.takasaki.clubm:attr/actionMenuTextColor = 0x7f04000f
studio.takasaki.clubm:macro/m3_comp_fab_primary_large_container_shape = 0x7f0d0039
studio.takasaki.clubm:attr/actionBarStyle = 0x7f040005
studio.takasaki.clubm:style/Base.V21.Theme.AppCompat.Light = 0x7f1300a9
studio.takasaki.clubm:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0d00fe
studio.takasaki.clubm:attr/colorOnContainerUnchecked = 0x7f0400f2
studio.takasaki.clubm:attr/checkboxStyle = 0x7f0400ab
studio.takasaki.clubm:anim/rns_default_enter_in = 0x7f010032
studio.takasaki.clubm:color/dev_launcher_colorPrimaryDark = 0x7f06006b
studio.takasaki.clubm:attr/contentPaddingBottom = 0x7f040134
studio.takasaki.clubm:layout/ime_base_split_test_activity = 0x7f0c003a
studio.takasaki.clubm:integer/mtrl_btn_anim_delay_ms = 0x7f0a0030
studio.takasaki.clubm:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701c3
studio.takasaki.clubm:dimen/m3_appbar_size_medium = 0x7f0700af
studio.takasaki.clubm:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f130026
studio.takasaki.clubm:attr/checkedIconGravity = 0x7f0400b0
studio.takasaki.clubm:anim/abc_fade_out = 0x7f010001
studio.takasaki.clubm:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0d00c4
studio.takasaki.clubm:integer/mtrl_card_anim_duration_ms = 0x7f0a0036
studio.takasaki.clubm:dimen/m3_btn_icon_only_icon_padding = 0x7f0700d9
studio.takasaki.clubm:attr/closeIconEndPadding = 0x7f0400d5
studio.takasaki.clubm:drawable/_expodevclientcomponents_assets_terminalicon = 0x7f08005a
studio.takasaki.clubm:style/Widget.Material3.Button.TextButton.Icon = 0x7f13038c
studio.takasaki.clubm:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
studio.takasaki.clubm:dimen/mtrl_navigation_rail_default_width = 0x7f0702d1
studio.takasaki.clubm:attr/animateMenuItems = 0x7f040032
studio.takasaki.clubm:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07014b
studio.takasaki.clubm:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070307
studio.takasaki.clubm:attr/titleEnabled = 0x7f040494
studio.takasaki.clubm:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1302c9
studio.takasaki.clubm:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0601c2
studio.takasaki.clubm:id/customPanel = 0x7f090098
studio.takasaki.clubm:attr/bottomSheetStyle = 0x7f04007f
studio.takasaki.clubm:macro/m3_comp_filled_text_field_container_shape = 0x7f0d004c
studio.takasaki.clubm:attr/titleMarginEnd = 0x7f040497
studio.takasaki.clubm:dimen/m3_comp_assist_chip_container_height = 0x7f0700fe
studio.takasaki.clubm:attr/autofillInlineSuggestionChip = 0x7f040045
studio.takasaki.clubm:style/CardView = 0x7f13012b
studio.takasaki.clubm:id/confirm_button = 0x7f09008a
studio.takasaki.clubm:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
studio.takasaki.clubm:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0d016e
studio.takasaki.clubm:anim/m3_side_sheet_exit_to_right = 0x7f01002e
studio.takasaki.clubm:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f13010a
studio.takasaki.clubm:dimen/mtrl_progress_circular_inset = 0x7f0702d8
studio.takasaki.clubm:attr/editTextStyle = 0x7f0401b6
studio.takasaki.clubm:attr/errorContentDescription = 0x7f0401ca
studio.takasaki.clubm:attr/colorSurfaceBright = 0x7f040116
studio.takasaki.clubm:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702e7
studio.takasaki.clubm:attr/searchPrefixText = 0x7f0403c1
studio.takasaki.clubm:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f13025f
studio.takasaki.clubm:attr/carousel_alignment = 0x7f0400a5
studio.takasaki.clubm:attr/subheaderColor = 0x7f04040c
studio.takasaki.clubm:string/call_notification_answer_video_action = 0x7f12002c
studio.takasaki.clubm:attr/layout_constraintLeft_creator = 0x7f0402a5
studio.takasaki.clubm:anim/m3_bottom_sheet_slide_out = 0x7f010028
studio.takasaki.clubm:anim/catalyst_push_up_in = 0x7f01001a
studio.takasaki.clubm:attr/labelStyle = 0x7f040280
studio.takasaki.clubm:anim/fragment_fast_out_extra_slow_in = 0x7f010022
studio.takasaki.clubm:attr/srcCompat = 0x7f0403f3
studio.takasaki.clubm:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0d0095
studio.takasaki.clubm:attr/fastScrollHorizontalThumbDrawable = 0x7f0401f2
studio.takasaki.clubm:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080090
studio.takasaki.clubm:attr/actualImageResource = 0x7f040026
studio.takasaki.clubm:animator/mtrl_fab_show_motion_spec = 0x7f02001f
studio.takasaki.clubm:styleable/ClockHandView = 0x7f140022
studio.takasaki.clubm:attr/tickMarkTintMode = 0x7f04048a
studio.takasaki.clubm:attr/hideOnScroll = 0x7f040238
studio.takasaki.clubm:dimen/mtrl_calendar_header_divider_thickness = 0x7f070284
studio.takasaki.clubm:anim/rns_default_exit_in = 0x7f010034
studio.takasaki.clubm:color/design_dark_default_color_on_surface = 0x7f06004b
studio.takasaki.clubm:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f13019c
studio.takasaki.clubm:color/accent_material_dark = 0x7f060019
studio.takasaki.clubm:attr/flow_lastVerticalStyle = 0x7f040211
studio.takasaki.clubm:attr/clockIcon = 0x7f0400d1
studio.takasaki.clubm:attr/indicatorSize = 0x7f040254
studio.takasaki.clubm:attr/indicatorTrackGapSize = 0x7f040255
studio.takasaki.clubm:drawable/abc_list_selector_disabled_holo_light = 0x7f080093
studio.takasaki.clubm:color/iconBackground = 0x7f06007b
studio.takasaki.clubm:string/catalyst_debug_error = 0x7f120035
studio.takasaki.clubm:drawable/m3_popupmenu_background_overlay = 0x7f080101
studio.takasaki.clubm:attr/tabPadding = 0x7f040430
studio.takasaki.clubm:string/mtrl_picker_text_input_month_abbr = 0x7f1200ed
studio.takasaki.clubm:id/error_viewPager = 0x7f0900c7
studio.takasaki.clubm:attr/materialCardViewFilledStyle = 0x7f0402fe
studio.takasaki.clubm:macro/m3_comp_fab_tertiary_container_color = 0x7f0d003f
studio.takasaki.clubm:animator/m3_btn_state_list_anim = 0x7f02000b
studio.takasaki.clubm:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1301f6
studio.takasaki.clubm:animator/fragment_open_exit = 0x7f020008
studio.takasaki.clubm:anim/abc_slide_in_top = 0x7f010007
studio.takasaki.clubm:color/mtrl_fab_ripple_color = 0x7f0602e9
studio.takasaki.clubm:attr/thumbIconTint = 0x7f04047b
studio.takasaki.clubm:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700b2
studio.takasaki.clubm:attr/menuAlignmentMode = 0x7f040320
studio.takasaki.clubm:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
