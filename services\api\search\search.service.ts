/**
 * Global Search Service for ClubM
 * Handles comprehensive search functionality across all entity types
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {PaginationResponse} from "@/models/api/common.models";

// Search result interfaces
export interface SearchResult {
  id: string;
  name: string;
  subtitle: string;
  type:
    | "member"
    | "product"
    | "event"
    | "magazine"
    | "chat"
    | "partner"
    | "opportunity";
  avatar?: string;
  imageUrl?: string;
  description?: string;
  metadata?: Record<string, any>;
  originalData?: any; // Store original API response data for navigation
}

export interface GlobalSearchParams {
  search: string;
  page?: number;
  pageSize?: number;
}

export interface GlobalSearchResponse
  extends PaginationResponse<SearchResult> {}

// Individual entity search params
export interface EntitySearchParams {
  search: string;
  page?: number;
  pageSize?: number;
}

// Raw API response structure based on the actual /api/app/home/<USER>
export interface RawSearchItem {
  user?: any;
  event?: any;
  product?: any;
  magazine?: any;
  partner?: any;
  opportunity?: any;
  chat?: any;
}

export interface RawSearchResponse {
  data: RawSearchItem[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export class SearchService {
  private static readonly GLOBAL_SEARCH_PATH = "/api/app/home/<USER>";
  private static readonly USERS_PATH = "/api/app/users";
  private static readonly EVENTS_PATH = "/api/app/events";
  private static readonly PRODUCTS_PATH = "/api/app/products/catalog";
  private static readonly MAGAZINES_PATH = "/api/app/magazines";
  private static readonly CHATS_PATH = "/api/app/chats";
  private static readonly PARTNERS_PATH = "/api/app/partners";
  private static readonly OPPORTUNITIES_PATH = "/api/app/opportunities";

  /**
   * Perform global search across all entity types using the /api/app/home/<USER>
   */
  static async globalSearch(
    params: GlobalSearchParams
  ): Promise<GlobalSearchResponse> {
    try {
      ApiLogger.info("Performing global search", params);

      // Use the correct parameter names expected by the API
      const apiParams = {
        Search: params.search,
        Page: params.page || 1,
        PageSize: params.pageSize || 20
      };

      const rawResponse = await firstValueFrom(
        apiClient.get<RawSearchResponse>(this.GLOBAL_SEARCH_PATH, apiParams)
      );

      // Debug logging to understand the actual API response
      console.log(
        "🔍 [SEARCH-SERVICE] Raw API response:",
        JSON.stringify(rawResponse, null, 2)
      );
      console.log(
        "🔍 [SEARCH-SERVICE] Raw response data length:",
        rawResponse.data?.length || 0
      );

      if (rawResponse.data && rawResponse.data.length > 0) {
        rawResponse.data.forEach((item, index) => {
          console.log(`🔍 [SEARCH-SERVICE] Item ${index}:`, Object.keys(item));
          Object.keys(item).forEach((key) => {
            if ((item as any)[key]) {
              console.log(`  - ${key}:`, (item as any)[key]);
            }
          });
        });
      }

      // Transform the raw API response to our expected format
      const transformedData = rawResponse.data
        .map((item, index) => {
          const transformed = this.transformSearchItem(item);
          console.log(
            `🔍 [SEARCH-SERVICE] Transformed item ${index}:`,
            transformed
          );
          return transformed;
        })
        .filter(Boolean) as SearchResult[];

      const response: GlobalSearchResponse = {
        data: transformedData,
        pagination: {
          currentPage: rawResponse.currentPage,
          pageSize: rawResponse.pageSize,
          totalCount: rawResponse.totalItems,
          totalPages: rawResponse.totalPages,
          hasNextPage: rawResponse.hasNextPage,
          hasPreviousPage: rawResponse.hasPreviousPage
        }
      };

      ApiLogger.info(
        `Global search completed: ${response.data.length} results found`
      );
      return response;
    } catch (error) {
      ApiLogger.error("Error performing global search", error as Error, params);
      throw error;
    }
  }

  /**
   * Transform raw search item from API to SearchResult format
   */
  private static transformSearchItem(item: RawSearchItem): SearchResult | null {
    // Handle user results
    if (item.user) {
      return {
        id: item.user.id.toString(),
        name: item.user.name || item.user.fullName || "Unknown User",
        subtitle: item.user.email || item.user.document || "",
        description: `Membro desde ${new Date(
          item.user.createdAt
        ).toLocaleDateString()}`,
        type: "member" as const,
        avatar: item.user.profilePicture || item.user.avatar,
        originalData: item.user
      };
    }

    // Handle event results
    if (item.event) {
      return {
        id: item.event.id.toString(),
        name: item.event.title || item.event.name,
        subtitle: item.event.location || "Evento",
        description: `Data: ${new Date(
          item.event.date || item.event.startDate
        ).toLocaleDateString()}`,
        type: "event" as const,
        imageUrl: item.event.imageUrl || item.event.banner,
        originalData: item.event
      };
    }

    // Handle product results
    if (item.product) {
      return {
        id: item.product.id.toString(),
        name: item.product.name || item.product.title,
        subtitle: "Produto",
        description: item.product.value
          ? `Valor: R$ ${(item.product.value / 100).toFixed(2)}`
          : item.product.description,
        type: "product" as const,
        imageUrl: item.product.imageUrl || item.product.image,
        originalData: item.product
      };
    }

    // Handle magazine results
    if (item.magazine) {
      return {
        id: item.magazine.id.toString(),
        name: item.magazine.title || item.magazine.name,
        subtitle: "Revista",
        description: item.magazine.description,
        type: "magazine" as const,
        imageUrl: item.magazine.coverImage || item.magazine.image,
        originalData: item.magazine
      };
    }

    // Handle partner results
    if (item.partner) {
      return {
        id: item.partner.id.toString(),
        name: item.partner.name || item.partner.companyName,
        subtitle: "Parceiro",
        description: item.partner.description,
        type: "partner" as const,
        imageUrl: item.partner.logo || item.partner.image,
        originalData: item.partner
      };
    }

    // Handle opportunity results
    if (item.opportunity) {
      return {
        id: item.opportunity.id.toString(),
        name: item.opportunity.title || item.opportunity.name,
        subtitle: "Oportunidade",
        description: item.opportunity.description,
        type: "opportunity" as const,
        originalData: item.opportunity
      };
    }

    // Handle chat results
    if (item.chat) {
      return {
        id: item.chat.id.toString(),
        name: item.chat.name || item.chat.title || "Chat",
        subtitle: "Conversa",
        description: item.chat.lastMessage || "Conversa",
        type: "chat" as const,
        avatar: item.chat.avatar,
        originalData: item.chat
      };
    }

    // Return null for unrecognized items
    return null;
  }

  /**
   * Search members/users
   */
  static async searchMembers(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching members", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.USERS_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform user data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (user: any) => ({
          id: user.id.toString(),
          name: user.name || user.fullName || "Unknown User",
          subtitle: user.email || user.document || "",
          type: "member" as const,
          avatar: user.profilePicture || user.avatar,
          metadata: {
            document: user.document,
            status: user.status,
            createdAt: user.createdAt
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching members", error as Error, params);
      throw error;
    }
  }

  /**
   * Search events
   */
  static async searchEvents(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching events", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.EVENTS_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform event data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (event: any) => ({
          id: event.id.toString(),
          name: event.title || event.name,
          subtitle: event.location || "Eventos",
          type: "event" as const,
          imageUrl: event.imageUrl || event.banner,
          description: event.description,
          metadata: {
            startDate: event.startDate,
            endDate: event.endDate,
            status: event.status,
            category: event.category
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching events", error as Error, params);
      throw error;
    }
  }

  /**
   * Search products
   */
  static async searchProducts(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching products", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.PRODUCTS_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform product data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (product: any) => ({
          id: product.id.toString(),
          name: product.name || product.title,
          subtitle: "Produtos",
          type: "product" as const,
          imageUrl: product.imageUrl || product.image,
          description: product.description,
          metadata: {
            price: product.price,
            category: product.category,
            status: product.status
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching products", error as Error, params);
      throw error;
    }
  }

  /**
   * Search magazines
   */
  static async searchMagazines(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching magazines", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.MAGAZINES_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform magazine data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (magazine: any) => ({
          id: magazine.id.toString(),
          name: magazine.title || magazine.name,
          subtitle: "Revistas",
          type: "magazine" as const,
          imageUrl: magazine.coverImage || magazine.image,
          description: magazine.description,
          metadata: {
            publishedAt: magazine.publishedAt,
            category: magazine.category
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching magazines", error as Error, params);
      throw error;
    }
  }

  /**
   * Search chats
   */
  static async searchChats(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching chats", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.CHATS_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform chat data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (chat: any) => ({
          id: chat.id.toString(),
          name: chat.name || chat.title || "Chat",
          subtitle: "Conversas",
          type: "chat" as const,
          avatar: chat.avatar,
          metadata: {
            lastMessage: chat.lastMessage,
            participantsCount: chat.participantsCount,
            updatedAt: chat.updatedAt
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching chats", error as Error, params);
      throw error;
    }
  }

  /**
   * Search partners
   */
  static async searchPartners(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching partners", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.PARTNERS_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform partner data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (partner: any) => ({
          id: partner.id.toString(),
          name: partner.name || partner.companyName,
          subtitle: "Parceiros",
          type: "partner" as const,
          imageUrl: partner.logo || partner.image,
          description: partner.description,
          metadata: {
            category: partner.category,
            status: partner.status
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching partners", error as Error, params);
      throw error;
    }
  }

  /**
   * Search opportunities
   */
  static async searchOpportunities(
    params: EntitySearchParams
  ): Promise<PaginationResponse<SearchResult>> {
    try {
      ApiLogger.info("Searching opportunities", params);

      const response = await firstValueFrom(
        apiClient.get<any>(this.OPPORTUNITIES_PATH, {
          Search: params.search,
          Page: params.page,
          PageSize: params.pageSize
        })
      );

      // Transform opportunity data to SearchResult format
      const transformedData: SearchResult[] = response.data.map(
        (opportunity: any) => ({
          id: opportunity.id.toString(),
          name: opportunity.title || opportunity.name,
          subtitle: "Oportunidades",
          type: "opportunity" as const,
          description: opportunity.description,
          metadata: {
            segment: opportunity.segment,
            currentStage: opportunity.currentStage,
            status: opportunity.status,
            createdAt: opportunity.createdAt
          }
        })
      );

      return {
        data: transformedData,
        pagination: response.pagination
      };
    } catch (error) {
      ApiLogger.error("Error searching opportunities", error as Error, params);
      throw error;
    }
  }
}
