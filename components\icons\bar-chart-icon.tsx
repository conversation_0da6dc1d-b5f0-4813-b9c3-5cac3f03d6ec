import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface BarChartIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const BarChartIcon: React.FC<BarChartIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#F2F4F7",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M18.333 18.333H1.667V1.667"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M5 15V10M10 15V7.5M15 15V12.5"
      />
    </Svg>
  );
};

export default BarChartIcon;
