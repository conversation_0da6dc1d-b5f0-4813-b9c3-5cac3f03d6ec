@echo off
REM Club M Android Build Script for Windows
REM Builds production APK for Android

setlocal enabledelayedexpansion

echo [INFO] Starting Android production build...

REM Check if we're in the project root
if not exist "package.json" (
    echo [ERROR] This script must be run from the project root directory
    exit /b 1
)

REM Check if Android directory exists
if not exist "android" (
    echo [ERROR] Android directory not found. Make sure this is a React Native project.
    exit /b 1
)

REM Setup production environment
echo [INFO] Setting up production environment...
node scripts/setup-production.js

REM Clean previous builds
echo [INFO] Cleaning previous builds...
cd android
call gradlew.bat clean

REM Build the APK
echo [INFO] Building production APK...
call gradlew.bat app:assembleRelease

if %errorlevel% equ 0 (
    echo [SUCCESS] Android APK build completed successfully!

    REM Create builds directory
    cd ..
    if not exist "builds\android" mkdir builds\android

    REM Find and copy APK
    for /r "android\app\build\outputs\apk\release" %%f in (*.apk) do (
        copy "%%f" "builds\android\club-m-production.apk"
        echo [SUCCESS] APK copied to: builds\android\club-m-production.apk
        goto :found
    )

    :found
    echo [SUCCESS] Android build process completed!
) else (
    echo [ERROR] Android build failed!
    exit /b 1
)

endlocal
