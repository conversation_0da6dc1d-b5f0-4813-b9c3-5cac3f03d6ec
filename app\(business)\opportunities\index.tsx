import React, {useState, useCallback, useMemo} from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl
} from "react-native";
import {useTranslation} from "react-i18next";
import {router} from "expo-router";
import ScreenWithHeader from "@/components/screen-with-header";
import Search from "@/components/search";
import OpportunityCard from "@/components/user/opportunity-card";
import OpportunityFilterComponent, {
  OpportunityFilter
} from "@/components/opportunities/opportunity-filter";
import stylesConstants from "@/styles/styles-constants";
import {useInfiniteOpportunities} from "@/hooks/api/use-opportunities";
import {useErrorHandling} from "@/hooks/use-error-handling";
import {OpportunityFilters} from "@/services/api/opportunities/opportunities.service";

const OpportunitiesList: React.FC = () => {
  const {t} = useTranslation();
  const {handleError} = useErrorHandling();

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [refreshing, setRefreshing] = useState(false);
  const [opportunityFilters, setOpportunityFilters] =
    useState<OpportunityFilter>({});

  // Build filters for API
  const filters: Omit<OpportunityFilters, "page"> = useMemo(() => {
    return {
      search: searchTerm.trim() || undefined,
      segmentId: opportunityFilters.segmentId,
      currentStageId: opportunityFilters.currentStageId,
      pageSize: 10
    };
  }, [searchTerm, opportunityFilters]);

  // Fetch opportunities with infinite scroll
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch
  } = useInfiniteOpportunities(filters);

  // Handle errors
  React.useEffect(() => {
    if (isError && error) {
      handleError(error, {
        customMessage: t(
          "opportunities.error.loadFailed",
          "Erro ao carregar oportunidades"
        )
      });
    }
  }, [isError, error, handleError, t]);

  // Flatten paginated data
  const opportunities = useMemo(() => {
    if (!data) return [];
    // Cast to any to handle the InfiniteData type from React Query
    const infiniteData = data as any;
    if (!infiniteData.pages) return [];
    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [data]);

  // Handle search
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: OpportunityFilter) => {
    setOpportunityFilters(newFilters);
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      handleError(error as any);
    } finally {
      setRefreshing(false);
    }
  }, [refetch, handleError]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Handle opportunity press
  const handleOpportunityPress = useCallback((opportunityId: number) => {
    router.push(`/(business)/opportunities/${opportunityId}`);
  }, []);

  // Render opportunity item
  const renderOpportunityItem = useCallback(
    ({item}: {item: any}) => (
      <View style={{marginBottom: 16}}>
        <OpportunityCard
          id={item.id}
          title={item.title}
          userName={item.user?.name || ""}
          user={item.user}
          description={item.description}
          imageUrl={item.imageUrl}
          imageId={item.imageId}
          value={item.value}
          createdAt={item.createdAt}
          onPress={() => handleOpportunityPress(item.id)}
        />
      </View>
    ),
    [handleOpportunityPress]
  );

  // Render loading footer
  const renderFooter = useCallback(() => {
    if (!isFetchingNextPage) return null;

    return (
      <View style={{padding: 20, alignItems: "center"}}>
        <ActivityIndicator
          size="small"
          color={stylesConstants.colors.primary500}
        />
        <Text
          style={{
            marginTop: 8,
            color: stylesConstants.colors.gray100,
            fontSize: 12
          }}
        >
          {t("opportunities.loadingMore", "Carregando mais...")}
        </Text>
      </View>
    );
  }, [isFetchingNextPage, t]);

  // Render empty state
  const renderEmpty = useCallback(() => {
    if (isLoading) return null;

    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          padding: 32,
          minHeight: 200
        }}
      >
        <Text
          style={{
            fontSize: 18,
            fontWeight: "600",
            color: stylesConstants.colors.white,
            textAlign: "center",
            marginBottom: 16
          }}
        >
          {searchTerm
            ? t("opportunities.noResults", "Nenhuma oportunidade encontrada")
            : t("opportunities.empty", "Nenhuma oportunidade disponível")}
        </Text>
        <Text
          style={{
            fontSize: 14,
            color: stylesConstants.colors.gray25,
            textAlign: "center"
          }}
        >
          {searchTerm
            ? t(
                "opportunities.noResultsDescription",
                "Tente ajustar sua busca ou remover filtros."
              )
            : t(
                "opportunities.emptyDescription",
                "Novas oportunidades aparecerão aqui quando disponíveis."
              )}
        </Text>
      </View>
    );
  }, [isLoading, searchTerm, t]);

  // Show loading state
  if (isLoading && !refreshing) {
    return (
      <ScreenWithHeader
        screenTitle={t("opportunities.title", "Oportunidades")}
        backButton
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            padding: 32
          }}
        >
          <ActivityIndicator
            size="large"
            color={stylesConstants.colors.primary500}
          />
          <Text
            style={{
              marginTop: 16,
              color: stylesConstants.colors.gray100,
              textAlign: "center"
            }}
          >
            {t("opportunities.loading", "Carregando oportunidades...")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle={t("opportunities.title", "Oportunidades")}
      backButton
      disableScrollView={true}
    >
      <Search
        searchBarValue={searchTerm}
        onSearchBarChange={handleSearchChange}
        searchBarPlaceholder={t(
          "opportunities.searchPlaceholder",
          "Buscar oportunidades..."
        )}
        style={{marginBottom: 16}}
      />

      <OpportunityFilterComponent
        filters={opportunityFilters}
        onFiltersChange={handleFiltersChange}
      />

      <FlatList
        data={opportunities}
        renderItem={renderOpportunityItem}
        keyExtractor={(item) => `opportunity-${item.id}`}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 20
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={stylesConstants.colors.primary500}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        removeClippedSubviews={true}
        maxToRenderPerBatch={8}
        windowSize={8}
        initialNumToRender={6}
        updateCellsBatchingPeriod={50}
      />
    </ScreenWithHeader>
  );
};

export default OpportunitiesList;
