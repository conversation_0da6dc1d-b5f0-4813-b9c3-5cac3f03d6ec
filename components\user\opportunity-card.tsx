import React, {useMemo, useState, useEffect} from "react";
import {View, Text} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import stylesConstants from "../../styles/styles-constants";
import Divider from "../divider";
import Button from "../button";
import styles from "../../styles/components/user/opportunity-card.style";
import Avatar from "./avatar";
import useCurrentLocalization from "../../hooks/use-localization";
import {
  getOpportunityImageUrl,
  isValidImageUrl
} from "../../utils/opportunity-image";
import {apiClient} from "../../services/api/base/api-client";
import {firstValueFrom} from "rxjs";

export interface OpportunityCardProps {
  id: number;
  title: string;
  userName: string;
  description: string;
  imageUrl?: string;
  imageId?: string;
  value: number;
  createdAt?: Date | string;
  onPress?: () => void;
  user?: {
    avatar?: string;
    avatarId?: string;
  };
}

const OpportunityCard: React.FC<OpportunityCardProps> = (props) => {
  const {t} = useTranslation();
  const localization = useCurrentLocalization();
  const [isExpanded, setIsExpanded] = useState(false);
  const [imageUri, setImageUri] = useState<string | undefined>(undefined);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [hasImageError, setHasImageError] = useState(false);

  const value = useMemo(() => {
    return props.value
      ? (props.value / 100).toLocaleString(localization, {
          maximumFractionDigits: 2
        })
      : 0;
  }, [props.value, localization]);

  const truncatedDescription = useMemo(() => {
    return props.description.length > 150
      ? props.description.substring(0, 150) + "..."
      : props.description;
  }, [props.description]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  // Determine the image URL to use
  // Priority: imageUrl prop (if valid) > imageId to construct URL > fallback to undefined
  const opportunityImageUrl = useMemo(() => {
    return getOpportunityImageUrl({
      imageUrl: props.imageUrl,
      imageId: props.imageId
    });
  }, [props.imageUrl, props.imageId]);

  // Function to fetch image with auth headers
  const fetchImageWithAuth = async (imageUrl: string) => {
    try {
      setIsImageLoading(true);
      setHasImageError(false);

      // Extract the endpoint from the full URL
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl || !imageUrl.startsWith(baseUrl)) {
        // If it's not our API URL, use it directly
        setImageUri(imageUrl);
        return;
      }

      const endpoint = imageUrl.replace(baseUrl, "");

      // Use apiClient to fetch with auth headers
      const response = await firstValueFrom(
        apiClient.request<ArrayBuffer>("GET", endpoint, undefined, {
          responseType: "arraybuffer"
        })
      );

      // Convert ArrayBuffer to base64
      const base64 = btoa(
        new Uint8Array(response).reduce(
          (data, byte) => data + String.fromCharCode(byte),
          ""
        )
      );

      // Create data URI
      const dataUri = `data:image/webp;base64,${base64}`;
      setImageUri(dataUri);
    } catch (error) {
      setHasImageError(true);
      // Fallback to direct URL
      setImageUri(imageUrl);
    } finally {
      setIsImageLoading(false);
    }
  };

  // Effect to load image when opportunityImageUrl changes
  useEffect(() => {
    if (opportunityImageUrl && isValidImageUrl(opportunityImageUrl)) {
      fetchImageWithAuth(opportunityImageUrl);
    } else {
      setImageUri(undefined);
    }
  }, [opportunityImageUrl]);

  return (
    <View style={styles.container}>
      <Avatar
        size={32}
        borderSize={2}
        user={props.user}
        name={props.userName}
      />
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{props.userName}</Text>
          <Text style={styles.label}>{t("user.publishedOpportunity")}</Text>
          {props.createdAt && (
            <Text style={styles.date}>
              {new Date(props.createdAt).toLocaleDateString()}
              {", às "}
              {new Date(props.createdAt).toLocaleTimeString()}
            </Text>
          )}
        </View>
        {!!props.title && <Text style={styles.title}>{props.title}</Text>}
        <View>
          <Text
            style={isExpanded ? styles.description : styles.descriptionFree}
          >
            {props.description}
            <Text onPress={handleSeeMore} style={styles.seeMoreText}>
              {" "}
              {isExpanded ? "Ver Menos" : "Ver Mais"}
            </Text>
          </Text>
          {imageUri ? (
            <Image style={styles.image} source={imageUri} />
          ) : isImageLoading ? (
            <View style={[styles.image, styles.imagePlaceholder]}>
              <Text style={styles.loadingText}>Carregando...</Text>
            </View>
          ) : (
            <View style={[styles.image, styles.imagePlaceholder]}>
              <Text style={styles.placeholderText}>📷</Text>
            </View>
          )}
          <Divider />
          <View style={styles.footer}>
            <View style={styles.valueContainer}>
              <Text style={styles.description}>
                {t("user.investmentValue")}
              </Text>
              <Text style={styles.value}>
                {t("user.price", {
                  price: value
                })}
              </Text>
            </View>
            <Button
              text={t("user.seeDetails")}
              backgroundColor={"transparent"}
              borderColor={stylesConstants.colors.gray25}
              style={styles.viewOpportunityButton}
              onPress={props.onPress}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default OpportunityCard;
