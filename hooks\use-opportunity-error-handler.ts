/**
 * Hook personalizado para tratamento de erros específicos de oportunidades
 * Fornece validações e mensagens de erro contextualizadas
 */

import {useCallback} from "react";
import {
  showErrorToast,
  showSuccessToast,
  extractErrorDetails
} from "@/utils/error-handler";

export interface OpportunityFormData {
  title: string;
  description: string;
  value: string;
  segment: string;
  currentStage: string;
  targetMarket: string;
  addressState: string;
  addressCity: string;
  addressNeighborhood: string;
  addressStreet: string;
  addressNumber: string;
  addressZip: string;
  addressComplement: string;
}

export interface ValidationError {
  field: keyof OpportunityFormData;
  message: string;
}

export const useOpportunityErrorHandler = () => {
  /**
   * Valida os dados do formulário de oportunidade
   */
  const validateOpportunityForm = useCallback(
    (formData: OpportunityFormData): ValidationError[] => {
      const errors: ValidationError[] = [];

      // Validação do título
      if (!formData.title.trim()) {
        errors.push({
          field: "title",
          message: "O título da oportunidade é obrigatório."
        });
      } else if (formData.title.trim().length < 3) {
        errors.push({
          field: "title",
          message: "O título deve ter pelo menos 3 caracteres."
        });
      } else if (formData.title.trim().length > 150) {
        errors.push({
          field: "title",
          message: "O título deve ter no máximo 150 caracteres."
        });
      }

      // Validação da descrição
      if (!formData.description.trim()) {
        errors.push({
          field: "description",
          message: "A descrição da oportunidade é obrigatória."
        });
      } else if (formData.description.trim().length > 250) {
        errors.push({
          field: "description",
          message: "A descrição deve ter no máximo 250 caracteres."
        });
      }

      // Validação do segmento
      if (!formData.segment) {
        errors.push({
          field: "segment",
          message: "Por favor, selecione um segmento para a oportunidade."
        });
      }

      // Validação do estágio atual
      if (!formData.currentStage) {
        errors.push({
          field: "currentStage",
          message: "Por favor, selecione o estágio atual da oportunidade."
        });
      }

      // Validação do mercado alvo
      if (!formData.targetMarket.trim()) {
        errors.push({
          field: "targetMarket",
          message: "O mercado alvo é obrigatório."
        });
      } else if (formData.targetMarket.trim().length < 3) {
        errors.push({
          field: "targetMarket",
          message: "O mercado alvo deve ter pelo menos 3 caracteres."
        });
      }

      // Validação do CEP
      if (!formData.addressZip.trim()) {
        errors.push({
          field: "addressZip",
          message: "O CEP é obrigatório para localização da oportunidade."
        });
      } else if (!/^\d{8}$/.test(formData.addressZip.replace(/\D/g, ""))) {
        errors.push({
          field: "addressZip",
          message: "Por favor, insira um CEP válido (8 dígitos)."
        });
      }

      // Validação do estado
      if (!formData.addressState.trim()) {
        errors.push({
          field: "addressState",
          message: "Por favor, selecione o estado."
        });
      }

      // Validação da cidade
      if (!formData.addressCity.trim()) {
        errors.push({
          field: "addressCity",
          message: "Por favor, insira a cidade."
        });
      }

      // Validação do valor (se preenchido)
      if (formData.value && formData.value.trim()) {
        const numericValue = parseFloat(
          formData.value.replace(/[^\d,]/g, "").replace(",", ".")
        );
        if (isNaN(numericValue) || numericValue < 0) {
          errors.push({
            field: "value",
            message: "Por favor, insira um valor válido para a oportunidade."
          });
        }
      }

      return errors;
    },
    []
  );

  /**
   * Exibe o primeiro erro de validação encontrado
   */
  const showValidationError = useCallback((errors: ValidationError[]) => {
    if (errors.length > 0) {
      const firstError = errors[0];
      showErrorToast(
        {status: 400, message: "Dados inválidos"},
        firstError.message
      );
      return true;
    }
    return false;
  }, []);

  /**
   * Valida e exibe erro se necessário
   */
  const validateAndShowError = useCallback(
    (formData: OpportunityFormData): boolean => {
      const errors = validateOpportunityForm(formData);
      return showValidationError(errors);
    },
    [validateOpportunityForm, showValidationError]
  );

  /**
   * Trata erros específicos da API de oportunidades
   */
  const handleOpportunityApiError = useCallback((error: any) => {
    const errorDetails = extractErrorDetails(error);

    // Mensagens específicas baseadas no status e conteúdo do erro
    let customMessage: string | undefined;

    if (errorDetails.status === 400) {
      const message = errorDetails.message.toLowerCase();

      if (message.includes("título") || message.includes("title")) {
        customMessage =
          "Erro no título: verifique se está preenchido corretamente e tem entre 3 e 150 caracteres.";
      } else if (
        message.includes("descrição") ||
        message.includes("description")
      ) {
        customMessage =
          "Erro na descrição: verifique se está preenchida e tem no máximo 250 caracteres.";
      } else if (message.includes("segmento") || message.includes("segment")) {
        customMessage =
          "Erro no segmento: por favor, selecione um segmento válido da lista.";
      } else if (message.includes("estágio") || message.includes("stage")) {
        customMessage =
          "Erro no estágio: por favor, selecione um estágio válido da lista.";
      } else if (
        message.includes("targetmarket") ||
        message.includes("mercado")
      ) {
        customMessage =
          "Erro no mercado alvo: por favor, preencha o mercado que deseja atingir.";
      } else if (message.includes("valor") || message.includes("value")) {
        customMessage =
          "Erro no valor: verifique se o valor informado é um número válido.";
      } else if (message.includes("cep") || message.includes("zip")) {
        customMessage =
          "Erro no CEP: verifique se o CEP informado é válido (8 dígitos).";
      } else if (message.includes("endereço") || message.includes("address")) {
        customMessage =
          "Erro no endereço: verifique se todos os campos de localização estão preenchidos corretamente.";
      } else if (message.includes("termos") || message.includes("terms")) {
        customMessage =
          "É necessário aceitar os termos e condições para criar a oportunidade.";
      }
    } else if (errorDetails.status === 401) {
      customMessage =
        "Sua sessão expirou. Faça login novamente para criar a oportunidade.";
    } else if (errorDetails.status === 403) {
      customMessage =
        "Você não tem permissão para editar esta oportunidade. Apenas o criador pode fazer alterações.";
    } else if (errorDetails.status === 409) {
      customMessage =
        "Já existe uma oportunidade similar. Tente com dados diferentes.";
    } else if (errorDetails.status === 422) {
      customMessage =
        "Alguns dados fornecidos são inválidos. Verifique as informações e tente novamente.";
    } else if (errorDetails.status >= 500) {
      customMessage =
        "Erro interno do servidor. Tente novamente em alguns minutos.";
    } else if (errorDetails.isNetworkError) {
      customMessage =
        "Erro de conexão. Verifique sua internet e tente novamente.";
    }

    showErrorToast(error, customMessage);
  }, []);

  /**
   * Exibe mensagem de sucesso para criação de oportunidade
   */
  const showOpportunityCreatedSuccess = useCallback(
    (opportunityTitle: string) => {
      showSuccessToast(
        "Oportunidade Criada!",
        `"${opportunityTitle}" foi criada com sucesso. Redirecionando para o pagamento...`
      );
    },
    []
  );

  /**
   * Exibe mensagem de sucesso para publicação de oportunidade
   */
  const showOpportunityPublishedSuccess = useCallback(
    (opportunityTitle: string) => {
      showSuccessToast(
        "Oportunidade Publicada!",
        `"${opportunityTitle}" foi publicada com sucesso e já está disponível para outros membros.`
      );
    },
    []
  );

  return {
    validateOpportunityForm,
    validateAndShowError,
    handleOpportunityApiError,
    showOpportunityCreatedSuccess,
    showOpportunityPublishedSuccess,
    showValidationError
  };
};
