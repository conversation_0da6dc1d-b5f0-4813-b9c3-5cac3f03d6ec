import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: 6,
    minWidth: 50,
    minHeight: 60,
    justifyContent: "center",
    paddingVertical: 4
  },
  iconContainer: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: 6,
    borderRadius: 4,
    width: 32,
    height: 32
  },
  title: {
    fontSize: 12,
    fontFamily: stylesConstants.fonts.openSans,
    fontStyle: "normal",
    fontWeight: "400",
    lineHeight: 16,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 1
  },
  selectedContainer: {
    opacity: 1,
    transform: [{scale: 1.05}]
  },
  selectedIconContainer: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderWidth: 2,
    borderColor: stylesConstants.colors.fullWhite
  },
  selectedTitle: {
    fontWeight: "600",
    color: "#FFFFFF",
    fontSize: 12,
    textAlign: "center",
    opacity: 1
  }
});

export default styles;
