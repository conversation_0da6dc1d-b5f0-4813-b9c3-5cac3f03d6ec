/**
 * Opportunity Segment Selector Component
 * Reusable component for selecting opportunity segments
 */

import React, {useState, useCallback} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import {useOpportunitySegmentsList} from "@/hooks/api";
import {OpportunitySegmentViewModel} from "@/services/api/opportunity-segments/opportunity-segments.service";
import CheckIcon from "@/components/icons/check-icon";

interface OpportunitySegmentSelectorProps {
  selectedSegmentIds?: number[];
  onSegmentSelect: (segment: OpportunitySegmentViewModel) => void;
  onSegmentDeselect: (segmentId: number) => void;
  multiSelect?: boolean;
  style?: any;
}

const OpportunitySegmentSelector: React.FC<OpportunitySegmentSelectorProps> = ({
  selectedSegmentIds = [],
  onSegmentSelect,
  onSegmentDeselect,
  multiSelect = true,
  style
}) => {
  const {t} = useTranslation();
  const [page, setPage] = useState(1);

  // Fetch opportunity segments
  const {
    data: segmentsResponse,
    isLoading,
    error,
    refetch
  } = useOpportunitySegmentsList({
    page,
    pageSize: 50 // Get more segments since they're usually fewer
  });

  const segments = segmentsResponse?.data || [];

  const handleSegmentPress = useCallback(
    (segment: OpportunitySegmentViewModel) => {
      const isSelected = selectedSegmentIds.includes(segment.id);
      
      if (isSelected) {
        onSegmentDeselect(segment.id);
      } else {
        onSegmentSelect(segment);
      }
    },
    [selectedSegmentIds, onSegmentSelect, onSegmentDeselect]
  );

  const handleRetry = useCallback(() => {
    refetch();
  }, [refetch]);

  const renderSegmentItem = useCallback(
    ({item}: {item: OpportunitySegmentViewModel}) => {
      const isSelected = selectedSegmentIds.includes(item.id);
      
      return (
        <TouchableOpacity
          style={{
            backgroundColor: isSelected ? 'rgba(0, 122, 255, 0.1)' : 'rgba(255, 255, 255, 0.1)',
            borderWidth: isSelected ? 2 : 1,
            borderColor: isSelected ? '#007AFF' : 'rgba(255, 255, 255, 0.3)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
          onPress={() => handleSegmentPress(item)}
          activeOpacity={0.7}
        >
          <View style={{flex: 1}}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#FFFFFF',
              marginBottom: 4
            }}>
              {item.name}
            </Text>
            {item.description && (
              <Text style={{
                fontSize: 14,
                color: 'rgba(255, 255, 255, 0.8)'
              }}>
                {item.description}
              </Text>
            )}
          </View>
          {isSelected && (
            <View style={{
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: '#007AFF',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 12
            }}>
              <CheckIcon width={16} height={16} color="#FFFFFF" />
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [selectedSegmentIds, handleSegmentPress]
  );

  if (isLoading) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={{
          marginTop: 12,
          fontSize: 16,
          color: '#FFFFFF',
          textAlign: 'center'
        }}>
          {t("opportunitySegmentSelector.loading", "Carregando segmentos...")}
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <Text style={{
          fontSize: 16,
          color: '#FF6B6B',
          textAlign: 'center',
          marginBottom: 16
        }}>
          {t("opportunitySegmentSelector.error", "Erro ao carregar segmentos")}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: '#007AFF',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8
          }}
          onPress={handleRetry}
        >
          <Text style={{
            color: '#FFFFFF',
            fontSize: 16,
            fontWeight: '600'
          }}>
            {t("opportunitySegmentSelector.retry", "Tentar novamente")}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (segments.length === 0) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <Text style={{
          fontSize: 16,
          color: 'rgba(255, 255, 255, 0.8)',
          textAlign: 'center'
        }}>
          {t("opportunitySegmentSelector.noSegments", "Nenhum segmento disponível")}
        </Text>
      </View>
    );
  }

  return (
    <View style={style}>
      <Text style={{
        fontSize: 18,
        fontWeight: '600',
        color: '#FFFFFF',
        marginBottom: 8
      }}>
        {t("opportunitySegmentSelector.title", "Segmentos de interesse")}
      </Text>
      {multiSelect && (
        <Text style={{
          fontSize: 14,
          color: 'rgba(255, 255, 255, 0.8)',
          marginBottom: 16
        }}>
          {t("opportunitySegmentSelector.subtitle", "Selecione um ou mais segmentos do seu interesse")}
        </Text>
      )}
      <FlatList
        data={segments}
        renderItem={renderSegmentItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
      {multiSelect && selectedSegmentIds.length > 0 && (
        <Text style={{
          fontSize: 12,
          color: 'rgba(255, 255, 255, 0.6)',
          marginTop: 8,
          textAlign: 'center'
        }}>
          {t("opportunitySegmentSelector.selectedCount", "{{count}} segmento(s) selecionado(s)", {
            count: selectedSegmentIds.length
          })}
        </Text>
      )}
    </View>
  );
};

export default OpportunitySegmentSelector;
