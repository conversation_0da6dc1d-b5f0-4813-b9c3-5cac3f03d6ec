import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.background,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24
  },
  content: {
    width: "100%",
    maxWidth: 400,
    alignItems: "center"
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: "center"
  },
  textContainer: {
    marginBottom: 48,
    alignItems: "center"
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    fontFamily: stylesConstants.fonts.inter,
    color: stylesConstants.colors.textPrimary,
    textAlign: "center",
    marginBottom: 12
  },
  description: {
    fontSize: 16,
    fontWeight: "400",
    fontFamily: stylesConstants.fonts.inter,
    color: stylesConstants.colors.textSecondary,
    textAlign: "center",
    lineHeight: 24
  },
  buttonsContainer: {
    width: "100%",
    gap: 16
  },
  biometricButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary,
    shadowColor: stylesConstants.colors.fullBlack,
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  skipButton: {
    backgroundColor: "transparent",
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: stylesConstants.colors.gray300
  },
  buttonDisabled: {
    opacity: 0.6
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12
  },
  biometricButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 16,
    fontWeight: "600",
    fontFamily: stylesConstants.fonts.inter,
    textAlign: "center"
  },
  skipButtonText: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontWeight: "500",
    fontFamily: stylesConstants.fonts.inter,
    textAlign: "center"
  }
});

export default styles;
