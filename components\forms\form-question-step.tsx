import React, {useState, useCallback} from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView
} from "react-native";
import {useTranslation} from "react-i18next";
import {FormQuestionStepProps} from "@/models/api/forms.models";
import CheckIcon from "../icons/check-icon";
import styles from "@/styles/components/forms/form-question-step.style";

const FormQuestionStep: React.FC<FormQuestionStepProps> = ({
  question,
  answer = "",
  onAnswerChange,
  isRequired = false,
  pageTitle
}) => {
  const {t} = useTranslation();
  const [selectedOption, setSelectedOption] = useState<string>(answer);

  const handleTextChange = useCallback(
    (text: string) => {
      onAnswerChange(text);
    },
    [onAnswerChange]
  );

  const handleOptionSelect = useCallback(
    (option: any) => {
      const optionValue =
        typeof option === "string"
          ? option
          : option.option || option.text || String(option);

      // Extract option ID if available
      const optionId =
        typeof option === "object" && option.id ? option.id : undefined;

      console.log("🎯 Option selected:", {
        option,
        optionValue,
        optionId,
        questionId: question.id
      });

      setSelectedOption(optionValue);
      onAnswerChange(optionValue, optionId);
    },
    [onAnswerChange, question.id]
  );

  const renderTextInput = () => (
    <View style={styles.inputContainer}>
      <TextInput
        style={styles.textInput}
        value={answer}
        onChangeText={handleTextChange}
        placeholder={
          question.placeholder ||
          t("formSurvey.enterAnswer", "Digite sua resposta...")
        }
        placeholderTextColor="rgba(223, 233, 240, 0.5)"
        multiline
        numberOfLines={4}
        textAlignVertical="top"
      />
    </View>
  );

  const renderMultipleChoice = () => {
    if (!question.options || question.options.length === 0) {
      return renderTextInput();
    }

    return (
      <View style={styles.optionsContainer}>
        {question.options.map((option, index) => {
          const optionValue =
            typeof option === "string"
              ? option
              : option.option || option.text || `Option ${index + 1}`;
          const isSelected = selectedOption === optionValue;

          return (
            <TouchableOpacity
              key={`${question.id}-option-${index}`}
              style={[
                styles.optionItem,
                isSelected && styles.optionItemSelected
              ]}
              onPress={() => handleOptionSelect(option)}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text
                  style={[
                    styles.optionText,
                    isSelected && styles.optionTextSelected
                  ]}
                >
                  {optionValue}
                </Text>

                {isSelected && (
                  <View style={styles.checkIconContainer}>
                    <CheckIcon width={20} height={20} stroke="#FFFFFF" />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}

        {/* Text input for "Other" option */}
        <View style={styles.otherOptionContainer}>
          <Text style={styles.otherOptionLabel}>
            {t("formSurvey.other", "Outro:")}
          </Text>
          <TextInput
            style={styles.otherOptionInput}
            value={
              selectedOption && !question.options?.includes(selectedOption)
                ? selectedOption
                : ""
            }
            onChangeText={handleTextChange}
            placeholder={t("formSurvey.specifyOther", "Especifique...")}
            placeholderTextColor="rgba(223, 233, 240, 0.5)"
          />
        </View>
      </View>
    );
  };

  const renderQuestionContent = () => {
    // For now, treat all questions as either multiple choice (if options exist) or text input
    if (question.options && question.options.length > 0) {
      return renderMultipleChoice();
    }

    return renderTextInput();
  };

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      {/* Page Title (if available) */}
      {pageTitle && (
        <View style={styles.pageTitleContainer}>
          <Text style={styles.pageTitle}>{pageTitle}</Text>
        </View>
      )}

      {/* Question Label */}
      <View style={styles.questionHeader}>
        <Text style={styles.questionTitle}>
          {question.label}
          {isRequired && <Text style={styles.requiredIndicator}> *</Text>}
        </Text>
      </View>

      {/* Question Content */}
      <View style={styles.questionContent}>{renderQuestionContent()}</View>

      {/* Required Notice */}
      {isRequired && (
        <View style={styles.requiredNotice}>
          <Text style={styles.requiredNoticeText}>
            {t("formSurvey.requiredField", "* Campo obrigatório")}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

export default FormQuestionStep;
