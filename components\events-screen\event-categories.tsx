import React from "react";
import {useTranslation} from "react-i18next";
import {View, Text, ScrollView} from "react-native";
import EventCategoryButton from "./event-category-button";
import HandshakeIcon from "../icons/handshake-icon";
import BuildingIcon from "../icons/building-icon";
import CodepenIcon from "../icons/codepen-icon";
import TargetIcon from "../icons/target-icon";
import ChartBreakoutIcon from "../icons/chart-breakout-icon";
import BookIcon from "../icons/book-icon";
import styles from "../../styles/components/events-screen/event-categories.style";
import {EventCategory} from "../../models/api/events.models";
export interface EventCategoriesProps {
  selectedCategory?: EventCategory;
  onCategorySelect?: (category: EventCategory) => void;
}

const EventCategories: React.FC<EventCategoriesProps> = ({
  selectedCategory,
  onCategorySelect
}) => {
  const {t} = useTranslation();

  // APENAS categorias que funcionam com a API - SEM EDUCATION!
  const categories: EventCategory[] = [
    {
      id: "business",
      name: t("eventList.categories.business", "Negócios"),
      slug: "business",
      color: "#34C759"
    },
    {
      id: "technology",
      name: t("eventList.categories.technology", "Tecnologia"),
      slug: "technology",
      color: "#5856D6"
    },
    {
      id: "marketing",
      name: t("eventList.categories.marketing", "Marketing"),
      slug: "marketing",
      color: "#FF9500"
    }
  ];

  // Mapeamento de ícones para categorias
  const getCategoryIcon = (category: EventCategory) => {
    const name = category.name.toLowerCase();
    const slug = category.slug.toLowerCase();

    if (
      name.includes("negóc") ||
      name.includes("business") ||
      slug.includes("business")
    )
      return <HandshakeIcon width={20} height={20} />;
    if (name.includes("startup") || slug.includes("startup"))
      return <BuildingIcon width={20} height={20} />;
    if (
      name.includes("tecnolog") ||
      name.includes("tech") ||
      slug.includes("technology")
    )
      return <CodepenIcon width={20} height={20} />;
    if (name.includes("marketing") || slug.includes("marketing"))
      return <TargetIcon width={20} height={20} />;
    if (
      name.includes("sustent") ||
      name.includes("sustain") ||
      slug.includes("sustain")
    )
      return <ChartBreakoutIcon width={20} height={20} />;
    if (
      name.includes("educação") ||
      name.includes("education") ||
      slug.includes("education")
    )
      return <BookIcon width={20} height={20} />;
    if (name.includes("networking") || slug.includes("networking"))
      return <HandshakeIcon width={20} height={20} />;

    return <HandshakeIcon width={20} height={20} />; // Default icon
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("eventList.category")}</Text>
      <View style={styles.scrollWrapper}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          style={styles.scrollView}
          scrollEnabled={true}
          bounces={false}
          alwaysBounceHorizontal={false}
          decelerationRate="normal"
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={false}
          directionalLockEnabled={true}
          pagingEnabled={false}
          removeClippedSubviews={false}
          scrollEventThrottle={16}
          overScrollMode="never"
        >
          {categories.map((category) => (
            <EventCategoryButton
              key={category.id}
              title={category.name}
              icon={getCategoryIcon(category)}
              category={category}
              isSelected={selectedCategory?.id === category.id}
              onPress={onCategorySelect}
            />
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

export default React.memo(EventCategories);
