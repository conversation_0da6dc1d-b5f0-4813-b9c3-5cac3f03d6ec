import React, {useMemo} from "react";
import {View, Text, TouchableOpacity, Image} from "react-native";
import {
  Notification,
  NotificationType,
  NotificationStatus
} from "@/models/api/notifications.models";
import CalendarIcon from "@/components/icons/calendar-icon";
import AnnounceIcon from "@/components/icons/announce-icon";
import GeneralNotificationsIcon from "@/components/icons/general-notifications-icon";
import styles from "@/styles/notifications/notifications.style";

interface NotificationItemProps {
  notification: Notification;
  onPress?: (notification: Notification) => void;
  onActionPress?: (notification: Notification) => void;
  showSeparator?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onPress,
  onActionPress,
  showSeparator = true
}) => {
  // Função para obter o ícone baseado no tipo
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.EVENT:
        return <CalendarIcon width={20} height={20} />;
      case NotificationType.ANNOUNCEMENT:
        return <AnnounceIcon width={20} height={20} />;
      default:
        return <GeneralNotificationsIcon width={20} height={20} />;
    }
  };

  // Função para formatar o texto da notificação
  const renderNotificationText = () => {
    if (notification.type === NotificationType.EVENT && notification.imageUrl) {
      // Event notification with avatar - format: "Phoenix Baker enviou um convite para o evento Encontro..."
      const parts = notification.title.split(
        " enviou um convite para o evento "
      );
      if (parts.length === 2) {
        return (
          <Text style={styles.notificationText}>
            <Text style={styles.boldText}>{parts[0]}</Text>
            <Text> enviou um convite para o evento </Text>
            <Text style={styles.boldText}>{parts[1]}</Text>
          </Text>
        );
      }
    } else if (notification.type === NotificationType.ANNOUNCEMENT) {
      // Survey notification - format: "Club M Alphaville publicou uma nova enquete, participe!"
      const parts = notification.title.split(" publicou");
      if (parts.length === 2) {
        return (
          <Text style={styles.notificationText}>
            <Text style={styles.boldText}>{parts[0]}</Text>
            <Text> publicou{parts[1]}</Text>
          </Text>
        );
      }
    }

    // Default formatting
    return <Text style={styles.notificationText}>{notification.title}</Text>;
  };

  // Formatar timestamp
  const formattedTime = useMemo(() => {
    return new Date(notification.createdAt).toLocaleString("pt-BR", {
      weekday: "long",
      hour: "2-digit",
      minute: "2-digit"
    });
  }, [notification.createdAt]);

  // Verificar se é não lida
  const isUnread = notification.status === NotificationStatus.UNREAD;

  return (
    <TouchableOpacity
      style={styles.notificationItem}
      onPress={() => onPress?.(notification)}
      activeOpacity={0.7}
    >
      <View style={styles.notificationRow}>
        <View style={styles.avatarContainer}>
          {notification.imageUrl ? (
            <Image
              source={{uri: notification.imageUrl}}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.iconContainer}>
              {getNotificationIcon(notification.type)}
            </View>
          )}
        </View>

        <View style={styles.contentContainer}>
          {renderNotificationText()}
        </View>

        <View
          style={isUnread ? styles.unreadIndicator : styles.readIndicator}
        />
      </View>

      {notification.actionUrl && notification.actionText && (
        <View style={styles.actionButtonContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onActionPress?.(notification)}
          >
            <Text style={styles.actionButtonText}>
              {notification.actionText}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.timestampContainer}>
        <Text style={styles.timestampText}>{formattedTime}</Text>
      </View>

      {showSeparator && <View style={styles.separator} />}
    </TouchableOpacity>
  );
};

export default NotificationItem;
