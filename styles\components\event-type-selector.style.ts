import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-end"
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: stylesConstants.colors.gray900
  },
  backdropTouchable: {
    flex: 1
  },
  drawer: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderWidth: 1.5,
    borderColor: "#282A2E",
    borderBottomWidth: 0,
    maxHeight: "80%",
    minHeight: 600
  },
  safeArea: {
    flex: 1
  },
  handleBar: {
    width: 44,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    alignSelf: "center",
    marginTop: 8,
    marginBottom: 20
  },
  title: {
    fontSize: 16,
    fontWeight: "400",
    color: "#DFE9F0",
    textAlign: "center",
    marginBottom: 24,
    paddingHorizontal: 20,
    lineHeight: 24
  },
  // Tabs
  tabsContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#667085",
    paddingHorizontal: 24,
    gap: 8
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 32
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#FCFCFD"
  },
  tabText: {
    fontSize: 14,
    fontWeight: "400",
    color: "#FCFCFD",
    lineHeight: 20
  },
  activeTabText: {
    fontWeight: "700"
  },

  // Tab Content
  tabContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24
  },

  // Presential Content
  presentialContent: {
    flex: 1
  },
  searchContainer: {
    marginBottom: 16
  },
  searchInputContainer: {
    backgroundColor: "#1C2230",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    shadowColor: "#101828",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  searchIcon: {
    fontSize: 16,
    color: "#F2F4F7"
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontWeight: "400",
    color: "#F2F4F7",
    lineHeight: 20
  },
  // Locations
  locationsSection: {
    flex: 1,
    gap: 16
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "400",
    color: "#DFE9F0",
    lineHeight: 18
  },
  locationsList: {
    backgroundColor: "#202938",
    borderRadius: 8,
    paddingVertical: 6,
    maxHeight: 400
  },
  locationItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12
  },
  locationItemWithBorder: {
    borderBottomWidth: 1,
    borderBottomColor: "#2B3342"
  },
  locationName: {
    flex: 1,
    fontSize: 14,
    fontWeight: "400",
    color: "#FFFFFF",
    lineHeight: 20
  },
  chevronIcon: {
    fontSize: 20,
    color: "#FFFFFF",
    fontWeight: "400"
  },

  // Virtual Content
  virtualContent: {
    flex: 1,
    gap: 24
  },
  linkSection: {
    gap: 6
  },
  linkLabel: {
    fontSize: 14,
    fontWeight: "400",
    color: "#FCFCFD",
    lineHeight: 20
  },
  linkInputContainer: {
    backgroundColor: "#1C2230",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    shadowColor: "#101828",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  linkIcon: {
    fontSize: 16,
    color: "#F2F4F7"
  },
  linkInput: {
    flex: 1,
    fontSize: 14,
    fontWeight: "400",
    color: "#F2F4F7",
    lineHeight: 20
  },
  saveLinkButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#0F7C4D",
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    height: 48,
    shadowColor: "#101828",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    marginTop: "auto"
  },
  saveLinkButtonDisabled: {
    backgroundColor: "#374151",
    borderColor: "#374151"
  },
  saveLinkButtonText: {
    fontSize: 16,
    fontWeight: "400",
    color: "#FCFCFD",
    lineHeight: 24
  },

  // Back Button
  backButton: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    height: 48,
    marginTop: 16,
    marginHorizontal: 24,
    marginBottom: 20
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "400",
    color: "#FCFCFD",
    lineHeight: 24
  }
});

export default styles;
