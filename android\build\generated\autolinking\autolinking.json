{"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app", "reactNativePath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/app": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-firebase\\app", "name": "@react-native-firebase/app", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-firebase\\app\\android", "packageImportPath": "import io.invertase.firebase.app.ReactNativeFirebaseAppPackage;", "packageInstance": "new ReactNativeFirebaseAppPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-firebase/app/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/auth": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-firebase\\auth", "name": "@react-native-firebase/auth", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-firebase\\auth\\android", "packageImportPath": "import io.invertase.firebase.auth.ReactNativeFirebaseAuthPackage;", "packageInstance": "new ReactNativeFirebaseAuthPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-firebase/auth/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pager-view": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pager-view", "name": "react-native-pager-view", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pager-view\\android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-svg\\android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "studio.takasaki.clubm", "sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\android"}}}