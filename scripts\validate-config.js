#!/usr/bin/env node

/**
 * Build Configuration Validation Script
 * Validates that all production build configurations are correct
 */

const fs = require("fs");
const path = require("path");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  purple: "\x1b[35m"
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`[INFO] ${message}`, "blue");
}

function logSuccess(message) {
  log(`[SUCCESS] ${message}`, "green");
}

function logWarning(message) {
  log(`[WARNING] ${message}`, "yellow");
}

function logError(message) {
  log(`[ERROR] ${message}`, "red");
}

function logHeader(message) {
  log(`[VALIDATE] ${message}`, "purple");
}

let validationErrors = 0;
let validationWarnings = 0;

function addError(message) {
  logError(message);
  validationErrors++;
}

function addWarning(message) {
  logWarning(message);
  validationWarnings++;
}

// Validate environment configuration
function validateEnvironmentConfig() {
  logHeader("Validating environment configuration...");

  // Check .env.production file
  const envProdPath = path.join(process.cwd(), ".env.production");
  if (!fs.existsSync(envProdPath)) {
    addError(".env.production file not found");
    return;
  }

  const envContent = fs.readFileSync(envProdPath, "utf8");

  // Check API URL
  if (
    !envContent.includes(
      'EXPO_PUBLIC_API_BASE_URL="https://apiclubm.grupow.dev"'
    )
  ) {
    addError("Production API URL not correctly configured in .env.production");
  } else {
    logSuccess("✓ Production API URL configured correctly");
  }

  // Check register URL
  if (!envContent.includes("EXPO_PUBLIC_REGISTER_URL")) {
    addWarning("Register URL not found in .env.production");
  } else {
    logSuccess("✓ Register URL configured");
  }
}

// Validate Firebase configuration
function validateFirebaseConfig() {
  logHeader("Validating Firebase configuration...");

  // Check production Firebase files
  const prodAndroidConfig = path.join(
    process.cwd(),
    "keys",
    "production",
    "google-services.json"
  );
  const prodIosConfig = path.join(
    process.cwd(),
    "keys",
    "production",
    "GoogleService-Info.plist"
  );
  const prodServiceAccount = path.join(
    process.cwd(),
    "keys",
    "production",
    "clubm-970aa-firebase-adminsdk-fbsvc-be4c5592a9.json"
  );

  // Validate Android config
  if (!fs.existsSync(prodAndroidConfig)) {
    addError(
      "Production Android Firebase config not found: keys/production/google-services.json"
    );
  } else {
    try {
      const androidConfig = JSON.parse(
        fs.readFileSync(prodAndroidConfig, "utf8")
      );
      if (androidConfig.project_info?.project_id === "clubm-970aa") {
        logSuccess("✓ Production Android Firebase config is valid");
      } else {
        addError("Android Firebase config has incorrect project ID");
      }
    } catch (error) {
      addError("Android Firebase config is not valid JSON");
    }
  }

  // Validate iOS config
  if (!fs.existsSync(prodIosConfig)) {
    addError(
      "Production iOS Firebase config not found: keys/production/GoogleService-Info.plist"
    );
  } else {
    const iosConfig = fs.readFileSync(prodIosConfig, "utf8");
    if (iosConfig.includes("clubm-970aa")) {
      logSuccess("✓ Production iOS Firebase config is valid");
    } else {
      addError("iOS Firebase config has incorrect project ID");
    }
  }

  // Validate service account
  if (!fs.existsSync(prodServiceAccount)) {
    addWarning(
      "Production Firebase service account not found (optional for client builds)"
    );
  } else {
    logSuccess("✓ Production Firebase service account found");
  }
}

// Validate app configuration
function validateAppConfig() {
  logHeader("Validating app configuration...");

  const appConfigPath = path.join(process.cwd(), "app.config.js");
  if (!fs.existsSync(appConfigPath)) {
    addError("app.config.js not found");
    return;
  }

  const appConfigContent = fs.readFileSync(appConfigPath, "utf8");

  // Check environment detection
  if (
    !appConfigContent.includes("process.env.NODE_ENV") ||
    !appConfigContent.includes("process.env.EXPO_PUBLIC_ENV")
  ) {
    addError("Environment detection not properly configured in app.config.js");
  } else {
    logSuccess("✓ Environment detection configured in app.config.js");
  }

  // Check Firebase config paths
  if (!appConfigContent.includes("keys/production/")) {
    addError("Production Firebase paths not configured in app.config.js");
  } else {
    logSuccess("✓ Production Firebase paths configured in app.config.js");
  }
}

// Validate build scripts
function validateBuildScripts() {
  logHeader("Validating build scripts...");

  const scripts = [
    "scripts/build-android.sh",
    "scripts/build-android.bat",
    "scripts/build-ios.sh",
    "scripts/build.sh",
    "scripts/build.bat",
    "scripts/setup-production.js"
  ];

  scripts.forEach((script) => {
    const scriptPath = path.join(process.cwd(), script);
    if (!fs.existsSync(scriptPath)) {
      addError(`Build script not found: ${script}`);
    } else {
      logSuccess(`✓ Build script found: ${script}`);
    }
  });

  // Check package.json scripts
  const packageJsonPath = path.join(process.cwd(), "package.json");
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
    const requiredScripts = ["build", "build:android", "build:ios"];

    requiredScripts.forEach((script) => {
      if (!packageJson.scripts[script]) {
        addError(`Package.json script not found: ${script}`);
      } else {
        logSuccess(`✓ Package.json script found: ${script}`);
      }
    });
  }
}

// Validate project structure
function validateProjectStructure() {
  logHeader("Validating project structure...");

  const requiredDirs = ["android", "ios", "keys", "scripts"];
  const optionalDirs = ["builds"];

  requiredDirs.forEach((dir) => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      addError(`Required directory not found: ${dir}`);
    } else {
      logSuccess(`✓ Required directory found: ${dir}`);
    }
  });

  optionalDirs.forEach((dir) => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      logInfo(`Optional directory will be created during build: ${dir}`);
    } else {
      logSuccess(`✓ Optional directory found: ${dir}`);
    }
  });
}

// Validate dependencies
function validateDependencies() {
  logHeader("Validating dependencies...");

  const packageJsonPath = path.join(process.cwd(), "package.json");
  if (!fs.existsSync(packageJsonPath)) {
    addError("package.json not found");
    return;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

  // Check for cross-env
  if (!packageJson.devDependencies?.["cross-env"]) {
    addError("cross-env dependency not found in devDependencies");
  } else {
    logSuccess("✓ cross-env dependency found");
  }

  // Check for required Expo/React Native dependencies
  const requiredDeps = ["expo", "react-native", "expo-router"];
  requiredDeps.forEach((dep) => {
    if (!packageJson.dependencies[dep]) {
      addError(`Required dependency not found: ${dep}`);
    } else {
      logSuccess(`✓ Required dependency found: ${dep}`);
    }
  });
}

// Main validation function
function main() {
  logHeader("Club M Production Configuration Validation");
  logInfo(`Validation started at: ${new Date().toLocaleString()}`);
  logInfo(`Platform: ${process.platform}`);

  try {
    validateEnvironmentConfig();
    validateFirebaseConfig();
    validateAppConfig();
    validateBuildScripts();
    validateProjectStructure();
    validateDependencies();

    console.log("\n" + "=".repeat(60));
    logHeader("Validation Summary");

    if (validationErrors === 0 && validationWarnings === 0) {
      logSuccess(
        "🎉 All validations passed! Production build configuration is ready."
      );
    } else {
      if (validationErrors > 0) {
        logError(`❌ ${validationErrors} error(s) found that must be fixed`);
      }
      if (validationWarnings > 0) {
        logWarning(`⚠️  ${validationWarnings} warning(s) found`);
      }

      if (validationErrors === 0) {
        logSuccess("✅ No critical errors found. You can proceed with builds.");
      }
    }

    console.log("=".repeat(60));

    if (validationErrors > 0) {
      process.exit(1);
    }
  } catch (error) {
    logError(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run validation if called directly
if (require.main == module) {
  main();
}

module.exports = {
  validateEnvironmentConfig,
  validateFirebaseConfig,
  validateAppConfig,
  validateBuildScripts,
  validateProjectStructure,
  validateDependencies
};
