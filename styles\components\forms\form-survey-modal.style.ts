import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0A0E1A"
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)"
  },
  backButton: {
    width: 44,
    height: 44,
    alignItems: "center",
    justifyContent: "center"
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 16
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  stepIndicator: {
    fontSize: 12,
    fontWeight: "400",
    color: "rgba(223, 233, 240, 0.7)",
    fontFamily: stylesConstants.fonts.openSans,
    marginTop: 2
  },
  headerRight: {
    width: 44,
    height: 44
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  progressBar: {
    height: 4,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 2,
    overflow: "hidden"
  },
  progressFill: {
    height: "100%",
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 2
  },
  content: {
    flex: 1,
    paddingHorizontal: 16
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)"
  },
  nextButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center"
  },
  nextButtonDisabled: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    opacity: 0.6
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans
  },
  submitButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center"
  },
  submitButtonDisabled: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    opacity: 0.6
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans
  }
});
