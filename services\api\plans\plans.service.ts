/**
 * Plans Service for Club M
 * Handles operations for subscription plans management
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import axios from "axios";

// Types based on swagger.json schemas
export interface PlanViewModel {
  id: number;
  name: string;
  description?: string;
  price?: number;
  period?: string;
  features?: string[];
  isPopular?: boolean;
  status: PlanStatus;
  createdAt: string;
  updatedAt: string;
}

export interface FranchiseViewModel {
  id: number;
  name: string;
  description?: string;
  location?: string;
  contactInfo?: string;
  status: FranchiseStatus;
  createdAt: string;
  updatedAt: string;
}

export interface PlanListResponse {
  data: PlanViewModel[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface FranchiseListResponse {
  data: FranchiseViewModel[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export enum PlanStatus {
  Active = 1,
  Inactive = 0
}

export enum FranchiseStatus {
  Active = 1,
  Inactive = 0
}

export interface PlansListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export interface FranchisesListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export class PlansService {
  private static readonly PLANS_PATH = "/api/app/plans";

  /**
   * Get all subscription plans
   */
  static async getPlans(params?: PlansListParams): Promise<PlanListResponse> {
    try {
      ApiLogger.info("Fetching subscription plans", params);

      const response = await firstValueFrom(
        apiClient.get<PlanListResponse>(this.PLANS_PATH, {
          params: {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || ""
          }
        })
      );

      ApiLogger.info(`Found ${response.totalItems} subscription plans`);
      return response;
    } catch (error) {
      ApiLogger.error("Error fetching subscription plans", error as Error);
      throw error;
    }
  }

  /**
   * Get all subscription plans without authentication (for registration)
   */
  static async getPlansPublic(
    params?: PlansListParams
  ): Promise<PlanListResponse> {
    try {
      ApiLogger.info("Fetching subscription plans (public)", params);

      const baseURL = process.env["EXPO_PUBLIC_API_BASE_URL"] ?? "";
      const response = await axios.get<PlanListResponse>(
        `${baseURL}${this.PLANS_PATH}`,
        {
          params: {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || ""
          },
          headers: {
            Accept: "application/json",
            "Accept-Language": "pt-br"
          }
        }
      );

      ApiLogger.info(
        `Found ${response.data.totalItems} subscription plans (public)`
      );
      return response.data;
    } catch (error) {
      ApiLogger.error(
        "Error fetching subscription plans (public)",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Get franchises for a specific plan
   */
  static async getFranchisesByPlan(
    planId: string | number,
    params?: FranchisesListParams
  ): Promise<FranchiseListResponse> {
    try {
      ApiLogger.info("Fetching franchises for plan", {planId, params});

      const response = await firstValueFrom(
        apiClient.get<FranchiseListResponse>(
          `${this.PLANS_PATH}/${planId}/franchises`,
          {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || ""
          }
        )
      );

      ApiLogger.info(
        `Found ${response.data.length} franchises for plan ${planId}`
      );
      return response;
    } catch (error) {
      ApiLogger.error(
        `Error fetching franchises for plan ${planId}`,
        error as Error
      );
      throw error;
    }
  }

  /**
   * Get a specific plan by ID
   */
  static async getPlanById(planId: string | number): Promise<PlanViewModel> {
    try {
      ApiLogger.info("Fetching plan by ID", {planId});

      const response = await firstValueFrom(
        apiClient.get<PlanViewModel>(`${this.PLANS_PATH}/${planId}`)
      );

      ApiLogger.info(`Found plan: ${response.name}`);
      return response;
    } catch (error) {
      ApiLogger.error(`Error fetching plan ${planId}`, error as Error);
      throw error;
    }
  }
}

export default PlansService;
