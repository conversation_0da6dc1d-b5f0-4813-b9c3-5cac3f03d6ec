/**
 * SearchResultItem Component
 * Reusable component for displaying search results with consistent styling
 */

import React, {useCallback} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle
} from "react-native";
import {useRouter} from "expo-router";
import {SearchResult} from "@/services/api/search/search.service";
import styles from "@/styles/components/search-result-item.style";
import MailIcon from "./icons/mail-icon";
import AnnounceIcon from "./icons/announce-icon";
import CloseIcon from "./icons/close-icon";

export interface SearchResultItemProps {
  result: SearchResult;
  onPress?: (result: SearchResult) => void;
  onRemove?: (result: SearchResult) => void;
  showRemoveButton?: boolean;
  style?: StyleProp<ViewStyle>;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({
  result,
  onPress,
  onRemove,
  showRemoveButton = false,
  style
}) => {
  const router = useRouter();

  const getInitials = useCallback((name: string) => {
    // Safe handling of name parameter
    if (!name || typeof name !== "string") {
      return "??";
    }
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }, []);

  const getTypeIcon = useCallback(() => {
    switch (result.type) {
      case "product":
        return <MailIcon width={20} height={20} />;
      case "event":
        return <AnnounceIcon width={20} height={20} />;
      case "member":
      case "chat":
      case "magazine":
      case "partner":
      case "opportunity":
      default:
        return null;
    }
  }, [result.type]);

  const handlePress = useCallback(() => {
    if (onPress) {
      onPress(result);
      return;
    }

    // Default navigation logic based on result type
    switch (result.type) {
      case "member":
        router.push(`/(main)/user-profile/${result.id}`);
        break;
      case "event":
        // Navigate to event sale page with the correct parameter name
        router.push({
          pathname: "/(events)/event-sale",
          params: {
            id: result.id
          }
        });
        break;
      case "product":
        // Navigate to product page with the correct parameter name
        router.push({
          pathname: "/(logged-stack)/product-page",
          params: {
            id: result.id
          }
        });
        break;
      case "magazine":
        router.push(`/(magazine)/magazine-details/${result.id}`);
        break;
      case "chat":
        router.push(`/(logged-stack)/chat?chatId=${result.id}`);
        break;
      case "partner":
        router.push(`/(main)/partners/${result.id}`);
        break;
      case "opportunity":
        router.push(`/(business)/opportunities/${result.id}`);
        break;
      default:
        console.warn(`No navigation defined for result type: ${result.type}`);
    }
  }, [result, onPress, router]);

  const handleRemove = useCallback(() => {
    if (onRemove) {
      onRemove(result);
    }
  }, [result, onRemove]);

  const renderAvatar = useCallback(() => {
    // Render avatar from URL if available (no default image fallback)
    if (result.avatar || result.imageUrl) {
      return (
        <Image
          source={{uri: result.avatar || result.imageUrl}}
          style={styles.avatar}
        />
      );
    }

    // Show initials for members or type icon for other entities
    if (result.type === "member") {
      return (
        <View style={styles.avatarPlaceholder}>
          <Text style={styles.avatarInitials}>{getInitials(result.name)}</Text>
        </View>
      );
    }

    // Show type icon for non-member entities
    const typeIcon = getTypeIcon();
    if (typeIcon) {
      return <View style={styles.iconContainer}>{typeIcon}</View>;
    }

    // Fallback to initials
    return (
      <View style={styles.avatarPlaceholder}>
        <Text style={styles.avatarInitials}>{getInitials(result.name)}</Text>
      </View>
    );
  }, [result, getInitials, getTypeIcon]);

  // Return null if result is invalid
  if (!result?.id || !result?.name || typeof result.name !== "string") {
    console.warn("SearchResultItem: Invalid result data", result);
    return null;
  }

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {renderAvatar()}

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
          {result.name || ""}
        </Text>
        <Text style={styles.subtitle} numberOfLines={1} ellipsizeMode="tail">
          {result.subtitle || ""}
        </Text>
        {result.description && typeof result.description === "string" && (
          <Text
            style={styles.description}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {result.description}
          </Text>
        )}
      </View>

      {showRemoveButton && (
        <TouchableOpacity
          onPress={handleRemove}
          style={styles.removeButton}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
        >
          <CloseIcon width={24} height={24} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

export default SearchResultItem;
