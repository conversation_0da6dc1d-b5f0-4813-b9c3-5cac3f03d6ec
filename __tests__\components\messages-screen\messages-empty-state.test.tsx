import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import MessagesEmptyState from "../../../components/messages-screen/messages-empty-state";

describe("MessagesEmptyState", () => {
  it("renders correctly", () => {
    const {getByText} = render(<MessagesEmptyState />);

    expect(getByText("Você ainda não possui mensagens")).toBeTruthy();
    expect(
      getByText(
        "Que tal dar uma olhada nas novidades e interagir com os membros do aplicativo?"
      )
    ).toBeTruthy();
    expect(getByText("Iniciar uma conversa")).toBeTruthy();
  });

  it("calls onStartConversation when button is pressed", () => {
    const mockOnStartConversation = jest.fn();
    const {getByText} = render(
      <MessagesEmptyState onStartConversation={mockOnStartConversation} />
    );

    const button = getByText("Iniciar uma conversa");
    fireEvent.press(button);

    expect(mockOnStartConversation).toHaveBeenCalledTimes(1);
  });

  it("renders without onStartConversation prop", () => {
    const {getByText} = render(<MessagesEmptyState />);

    const button = getByText("Iniciar uma conversa");
    fireEvent.press(button);

    // Should not crash when onStartConversation is undefined
    expect(button).toBeTruthy();
  });
});
