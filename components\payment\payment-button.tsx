/**
 * Componente de botão de pagamento reutilizável
 * Usado em produtos, oportunidades e eventos
 */

import React, {useCallback} from "react";
import {TouchableOpacity, Text, View} from "react-native";
import {useTranslation} from "react-i18next";
import {router} from "expo-router";
import {PaymentEntity} from "@/models/api/payments.models";
import ShoppingBagIcon from "@/components/icons/shopping-bag-icon";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import styles from "@/styles/components/payment/payment-button.style";

export interface PaymentButtonProps {
  // Dados da entidade
  entityId: number;
  entityType: PaymentEntity;
  entityTitle: string;
  entityDescription?: string;
  entityImageUrl?: string;
  amount: number;

  // Configurações visuais
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
  fullWidth?: boolean;
  showIcon?: boolean;

  // Estados
  disabled?: boolean;
  loading?: boolean;

  // Textos customizados
  text?: string;

  // Callbacks
  onPress?: () => void;
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
  entityId,
  entityType,
  entityTitle,
  entityDescription,
  entityImageUrl,
  amount,
  variant = "primary",
  size = "medium",
  fullWidth = false,
  showIcon = true,
  disabled = false,
  loading = false,
  text,
  onPress
}) => {
  const {t} = useTranslation();

  // Texto padrão baseado no tipo de entidade
  const getDefaultText = useCallback(() => {
    if (text) return text;

    switch (entityType) {
      case PaymentEntity.Product:
        return amount > 0
          ? t("payment.button.buyProduct")
          : t("payment.button.getProduct");
      case PaymentEntity.Event:
        return t("payment.button.buyEvent");
      case PaymentEntity.Opportunity:
        return t("payment.button.buyOpportunity");
      default:
        return t("payment.button.buy");
    }
  }, [entityType, amount, text, t]);

  // Ícone baseado no tipo de entidade
  const getIcon = useCallback(() => {
    if (!showIcon) return null;

    switch (entityType) {
      case PaymentEntity.Product:
        return <ShoppingBagIcon width={20} height={20} />;
      case PaymentEntity.Event:
      case PaymentEntity.Opportunity:
      default:
        return <CreditCardIcon width={20} height={20} />;
    }
  }, [entityType, showIcon]);

  // Handler de clique
  const handlePress = useCallback(() => {
    if (disabled || loading) return;

    if (onPress) {
      onPress();
      return;
    }

    // Navegação padrão para tela de pagamento
    const params = {
      eventId: entityId.toString(), // Usando eventId como entityId genérico
      entity: entityType.toString(),
      entityTitle,
      entityDescription: entityDescription || "",
      entityImageUrl: entityImageUrl || "",
      amount: amount.toString()
    };

    console.log("💰 [PAYMENT-BUTTON] Navegando para pagamento:", params);

    router.push({
      pathname: "/(logged-stack)/payment",
      params
    });
  }, [
    disabled,
    loading,
    onPress,
    entityId,
    entityType,
    entityTitle,
    entityDescription,
    entityImageUrl,
    amount
  ]);

  // Formatação do valor
  const formatPrice = useCallback(
    (value: number) => {
      if (value === 0) return t("payment.free");

      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL"
      }).format(value);
    },
    [t]
  );

  // Classes de estilo dinâmicas
  const buttonStyle = [
    styles.button,
    styles[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`],
    fullWidth && styles.buttonFullWidth,
    disabled && styles.buttonDisabled,
    loading && styles.buttonLoading
  ];

  const textStyle = [
    styles.text,
    styles[`text${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`text${size.charAt(0).toUpperCase() + size.slice(1)}`],
    disabled && styles.textDisabled,
    loading && styles.textLoading
  ];

  const priceStyle = [
    styles.price,
    styles[`price${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    disabled && styles.priceDisabled
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {getIcon()}

        <View style={styles.textContainer}>
          <Text style={textStyle}>
            {loading ? t("payment.button.loading") : getDefaultText()}
          </Text>

          <Text style={priceStyle}>{formatPrice(amount)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default PaymentButton;
