import React from "react";
import {render, fireEvent, waitFor} from "@testing-library/react-native";
import BiometricLoginButton from "@/components/biometric-login-button";

// Mock dos hooks
jest.mock("@/hooks/use-biometric-availability", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    isAvailable: true,
    isEnrolled: true,
    supportedTypes: [],
    isLoading: false
  }))
}));

jest.mock("@/hooks/use-biometric-login", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    isLoading: false,
    error: null,
    authenticateWithBiometrics: jest.fn(),
    clearError: jest.fn()
  }))
}));

// Mock da tradução
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "login.biometricLogin": "Entrar com Biometria"
      };
      return translations[key] || key;
    }
  })
}));

describe("BiometricLoginButton", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve renderizar o botão quando biometria está disponível", () => {
    const {getByText} = render(<BiometricLoginButton />);
    
    expect(getByText("Entrar com Biometria")).toBeTruthy();
  });

  it("não deve renderizar quando biometria não está disponível", () => {
    const useBiometricAvailability = require("@/hooks/use-biometric-availability").default;
    useBiometricAvailability.mockReturnValue({
      isAvailable: false,
      isEnrolled: false,
      supportedTypes: [],
      isLoading: false
    });

    const {queryByText} = render(<BiometricLoginButton />);
    
    expect(queryByText("Entrar com Biometria")).toBeNull();
  });

  it("deve chamar authenticateWithBiometrics quando pressionado", async () => {
    const mockAuthenticate = jest.fn();
    const useBiometricLogin = require("@/hooks/use-biometric-login").default;
    useBiometricLogin.mockReturnValue({
      isLoading: false,
      error: null,
      authenticateWithBiometrics: mockAuthenticate,
      clearError: jest.fn()
    });

    const {getByText} = render(<BiometricLoginButton />);
    const button = getByText("Entrar com Biometria");
    
    fireEvent.press(button);
    
    await waitFor(() => {
      expect(mockAuthenticate).toHaveBeenCalledTimes(1);
    });
  });

  it("deve mostrar loading quando autenticação está em progresso", () => {
    const useBiometricLogin = require("@/hooks/use-biometric-login").default;
    useBiometricLogin.mockReturnValue({
      isLoading: true,
      error: null,
      authenticateWithBiometrics: jest.fn(),
      clearError: jest.fn()
    });

    const {getByTestId} = render(<BiometricLoginButton />);
    
    // O ActivityIndicator deve estar presente
    expect(getByTestId).toBeDefined();
  });

  it("deve estar desabilitado quando prop disabled é true", () => {
    const {getByText} = render(<BiometricLoginButton disabled={true} />);
    const button = getByText("Entrar com Biometria");
    
    // Verificar se o botão tem o estilo de desabilitado
    expect(button.parent?.props.accessibilityState?.disabled).toBe(true);
  });
});
