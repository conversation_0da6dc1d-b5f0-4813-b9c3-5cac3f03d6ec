import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.border,
    backgroundColor: stylesConstants.colors.cardBackground
  },
  headerTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.secondaryBackground
  },
  content: {
    flex: 1
  },
  scrollContent: {
    paddingBottom: 24
  },
  imageContainer: {
    alignItems: "center",
    paddingVertical: 32,
    paddingHorizontal: 24
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 16
  },
  iconPlaceholder: {
    width: 200,
    height: 200,
    borderRadius: 16,
    backgroundColor: stylesConstants.colors.cardBackground,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },
  infoSection: {
    paddingHorizontal: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: "600",
    lineHeight: 32,
    marginBottom: 12,
    textAlign: "center"
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 32
  },
  termsSection: {
    backgroundColor: stylesConstants.colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },
  termsTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    marginBottom: 8
  },
  termsText: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  validitySection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },
  validityLabel: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  validityDate: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.border,
    backgroundColor: stylesConstants.colors.cardBackground
  },
  redeemButton: {
    backgroundColor: stylesConstants.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: "center"
  },
  redeemButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22
  }
});

export default styles;
