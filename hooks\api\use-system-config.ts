/**
 * React Query hooks for system configuration management
 */

import {useQuery, UseQueryOptions} from "@tanstack/react-query";
import {
  SystemConfigResponse,
  InstallmentConfig
} from "@/services/api/system/system-config.service";
import {ProductsCatalogService} from "@/services/api/products/products-catalog.service";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys for system config
export const systemConfigKeys = {
  all: ["system-config"] as const,
  config: () => [...systemConfigKeys.all, "config"] as const,
  installments: () => [...systemConfigKeys.all, "installments"] as const
};

/**
 * Hook to fetch system configuration
 */
export const useSystemConfig = (
  options?: UseQueryOptions<SystemConfigResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: systemConfigKeys.config(),
    queryFn: () => SystemConfigService.getSystemConfig(),
    staleTime: 10 * 60 * 1000, // 10 minutes - config doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch installment configuration from products catalog
 */
export const useInstallmentConfig = (
  options?: UseQueryOptions<InstallmentConfig, BaseApiError>
) => {
  return useQuery({
    queryKey: systemConfigKeys.installments(),
    queryFn: () => ProductsCatalogService.getInstallmentConfig(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        console.warn(
          "⚠️ [PRODUCTS-CATALOG] Erro de permissão ao buscar configuração de parcelamento, usando valores padrão"
        );
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Provide default values if API fails
    placeholderData: {
      maxInstallmentsWithoutFee: 6,
      maxInstallmentsWithFee: 12,
      feePercentage: 2.99
    },
    ...options
  });
};
