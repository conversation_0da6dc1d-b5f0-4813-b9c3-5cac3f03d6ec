/**
 * Hooks para configurações de parcelamento
 * Gerencia configurações de installments por entidade (produtos, eventos, oportunidades)
 */

import {useQuery, UseQueryOptions} from "@tanstack/react-query";
import {ProductsCatalogService} from "@/services/api/products/products-catalog.service";
import {EventsService} from "@/services/api/events/events.service";
import {BaseApiError} from "@/services/api/base/api-errors";

export interface InstallmentConfig {
  maxInstallmentsWithoutFee: number;
  maxInstallmentsWithFee: number;
  feePercentage: number;
}

// Query keys para cache
export const installmentConfigKeys = {
  all: ["installment-config"] as const,
  products: () => [...installmentConfigKeys.all, "products"] as const,
  events: () => [...installmentConfigKeys.all, "events"] as const,
  opportunities: () => [...installmentConfigKeys.all, "opportunities"] as const
};

/**
 * Hook to fetch installment configuration from products catalog
 * Use this specifically for products
 */
export const useProductInstallmentConfig = (
  options?: UseQueryOptions<InstallmentConfig, BaseApiError>
) => {
  return useQuery({
    queryKey: installmentConfigKeys.products(),
    queryFn: () => ProductsCatalogService.getInstallmentConfig(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        console.warn(
          "⚠️ [PRODUCTS-CATALOG] Erro de permissão ao buscar configuração de parcelamento, usando valores padrão"
        );
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch installment configuration for events
 * Use this specifically for events
 */
export const useEventInstallmentConfig = (
  options?: UseQueryOptions<InstallmentConfig, BaseApiError>
) => {
  return useQuery({
    queryKey: installmentConfigKeys.events(),
    queryFn: () => EventsService.getEventInstallmentConfig(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        console.warn(
          "⚠️ [EVENTS-SERVICE] Erro de permissão ao buscar configuração de parcelamento, usando valores padrão"
        );
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Legacy hook for backward compatibility
 * @deprecated Use useProductInstallmentConfig, useEventInstallmentConfig, or useOpportunityInstallmentConfig instead
 */
export const useInstallmentConfig = (
  options?: UseQueryOptions<InstallmentConfig, BaseApiError>
) => {
  console.warn(
    "⚠️ useInstallmentConfig is deprecated. Use entity-specific hooks instead."
  );
  return useProductInstallmentConfig(options);
};
