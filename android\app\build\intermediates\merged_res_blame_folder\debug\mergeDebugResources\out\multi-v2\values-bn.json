{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5965", "endColumns": "151", "endOffsets": "6112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7268,7938,8040,8149", "endColumns": "105,101,108,105", "endOffsets": "7369,8035,8144,8250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "33,51,94,96,97,99,113,114,115,162,163,164,166,171,172,173,174,175,176,177,178,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,4907,9967,10105,10173,10314,11324,11391,11465,15295,15378,15458,15647,16048,16128,16203,16291,16378,16453,16529,16604,16800,16876,16953,17023", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3119,4979,10034,10168,10248,10377,11386,11460,11537,15373,15453,15523,15721,16123,16198,16286,16373,16448,16524,16599,16694,16871,16948,17018,17091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4984,5093,5252,5380,5491,5627,5749,5861,6117,6260,6369,6525,6653,6786,6934,6994,7061", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "5088,5247,5375,5486,5622,5744,5856,5960,6255,6364,6520,6648,6781,6929,6989,7056,7146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1475,1539,1606,1667,1736,1798,1852,1959,2018,2079,2133,2207,2327,2412,2502,2608,2698,2782,2917,2988,3058,3190,3277,3360,3418,3474,3540,3613,3693,3764,3846,3915,3991,4071,4140,4249,4344,4427,4517,4612,4686,4760,4853,4907,4992,5059,5145,5230,5292,5356,5419,5485,5587,5686,5779,5878,5940,6000,6080,6163,6242", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1470,1534,1601,1662,1731,1793,1847,1954,2013,2074,2128,2202,2322,2407,2497,2603,2693,2777,2912,2983,3053,3185,3272,3355,3413,3469,3535,3608,3688,3759,3841,3910,3986,4066,4135,4244,4339,4422,4512,4607,4681,4755,4848,4902,4987,5054,5140,5225,5287,5351,5414,5480,5582,5681,5774,5873,5935,5995,6075,6158,6237,6310"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3409,3507,3600,3683,3784,4605,4709,4826,7463,7524,7590,10039,10253,10382,10472,10536,10603,10664,10733,10795,10849,10956,11015,11076,11130,11204,11542,11627,11717,11823,11913,11997,12132,12203,12273,12405,12492,12575,12633,12689,12755,12828,12908,12979,13061,13130,13206,13286,13355,13464,13559,13642,13732,13827,13901,13975,14068,14122,14207,14274,14360,14445,14507,14571,14634,14700,14802,14901,14994,15093,15155,15215,15813,15896,15975", "endLines": "5,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "316,3502,3595,3678,3779,3871,4704,4821,4902,7519,7585,7676,10100,10309,10467,10531,10598,10659,10728,10790,10844,10951,11010,11071,11125,11199,11319,11622,11712,11818,11908,11992,12127,12198,12268,12400,12487,12570,12628,12684,12750,12823,12903,12974,13056,13125,13201,13281,13350,13459,13554,13637,13727,13822,13896,13970,14063,14117,14202,14269,14355,14440,14502,14566,14629,14695,14797,14896,14989,15088,15150,15210,15290,15891,15970,16043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,340,457,546,689,803,917,1038,1177,1311,1440,1564,1711,1814,1971,2099,2236,2381,2515,2634,2740,2875,2965,3084,3188,3321", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "207,335,452,541,684,798,912,1033,1172,1306,1435,1559,1706,1809,1966,2094,2231,2376,2510,2629,2735,2870,2960,3079,3183,3316,3420"}, "to": {"startLines": "34,35,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,165,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3124,3281,7151,7374,7681,7824,8255,8369,8490,8629,8763,8892,9016,9163,9266,9423,9551,9688,9833,15528,17096,17202,17337,17427,17546,17650,17783", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "3276,3404,7263,7458,7819,7933,8364,8485,8624,8758,8887,9011,9158,9261,9418,9546,9683,9828,9962,15642,17197,17332,17422,17541,17645,17778,17882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,15726", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,15808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "41,42,43,44,45,46,47,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3876,3975,4077,4179,4282,4383,4485,16699", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3970,4072,4174,4277,4378,4480,4600,16795"}}]}]}