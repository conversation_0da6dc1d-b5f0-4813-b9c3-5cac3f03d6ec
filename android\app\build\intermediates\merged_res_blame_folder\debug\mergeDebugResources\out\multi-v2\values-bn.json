{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "73,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7494,8164,8266,8375", "endColumns": "105,101,108,105", "endOffsets": "7595,8261,8370,8476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "33,53,96,98,99,101,115,116,117,164,165,166,168,173,174,175,176,177,178,179,180,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3051,5133,10193,10331,10399,10540,11550,11617,11691,15521,15604,15684,15873,16274,16354,16429,16517,16604,16679,16755,16830,17026,17102,17179,17249", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3119,5205,10260,10394,10474,10603,11612,11686,11763,15599,15679,15749,15947,16349,16424,16512,16599,16674,16750,16825,16920,17097,17174,17244,17317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5210,5319,5478,5606,5717,5853,5975,6087,6343,6486,6595,6751,6879,7012,7160,7220,7287", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "5314,5473,5601,5712,5848,5970,6082,6186,6481,6590,6746,6874,7007,7155,7215,7282,7372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6191", "endColumns": "151", "endOffsets": "6338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1475,1539,1606,1667,1736,1798,1852,1959,2018,2079,2133,2207,2327,2412,2502,2608,2698,2782,2917,2988,3058,3190,3277,3360,3418,3474,3540,3613,3693,3764,3846,3915,3991,4071,4140,4249,4344,4427,4517,4612,4686,4760,4853,4907,4992,5059,5145,5230,5292,5356,5419,5485,5587,5686,5779,5878,5940,6000,6080,6163,6242", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1470,1534,1601,1662,1731,1793,1847,1954,2013,2074,2128,2202,2322,2407,2497,2603,2693,2777,2912,2983,3053,3185,3272,3355,3413,3469,3535,3608,3688,3759,3841,3910,3986,4066,4135,4244,4339,4422,4512,4607,4681,4755,4848,4902,4987,5054,5140,5225,5287,5351,5414,5480,5582,5681,5774,5873,5935,5995,6075,6158,6237,6310"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3635,3733,3826,3909,4010,4831,4935,5052,7689,7750,7816,10265,10479,10608,10698,10762,10829,10890,10959,11021,11075,11182,11241,11302,11356,11430,11768,11853,11943,12049,12139,12223,12358,12429,12499,12631,12718,12801,12859,12915,12981,13054,13134,13205,13287,13356,13432,13512,13581,13690,13785,13868,13958,14053,14127,14201,14294,14348,14433,14500,14586,14671,14733,14797,14860,14926,15028,15127,15220,15319,15381,15441,16039,16122,16201", "endLines": "5,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "316,3728,3821,3904,4005,4097,4930,5047,5128,7745,7811,7902,10326,10535,10693,10757,10824,10885,10954,11016,11070,11177,11236,11297,11351,11425,11545,11848,11938,12044,12134,12218,12353,12424,12494,12626,12713,12796,12854,12910,12976,13049,13129,13200,13282,13351,13427,13507,13576,13685,13780,13863,13953,14048,14122,14196,14289,14343,14428,14495,14581,14666,14728,14792,14855,14921,15023,15122,15215,15314,15376,15436,15516,16117,16196,16269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,340,457,546,689,803,917,1038,1177,1311,1440,1564,1711,1814,1971,2099,2236,2381,2515,2634,2740,2875,2965,3084,3188,3321", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "207,335,452,541,684,798,912,1033,1172,1306,1435,1559,1706,1809,1966,2094,2231,2376,2510,2629,2735,2870,2960,3079,3183,3316,3420"}, "to": {"startLines": "36,37,72,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,167,186,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3350,3507,7377,7600,7907,8050,8481,8595,8716,8855,8989,9118,9242,9389,9492,9649,9777,9914,10059,15754,17322,17428,17563,17653,17772,17876,18009", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "3502,3630,7489,7684,8045,8159,8590,8711,8850,8984,9113,9237,9384,9487,9644,9772,9909,10054,10188,15868,17423,17558,17648,17767,17871,18004,18108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,111", "endOffsets": "164,276"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3124,3238", "endColumns": "113,111", "endOffsets": "3233,3345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,15952", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,16034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "43,44,45,46,47,48,49,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4102,4201,4303,4405,4508,4609,4711,16925", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "4196,4298,4400,4503,4604,4706,4826,17021"}}]}]}