import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface GiftIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const GiftIcon: React.FC<GiftIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg viewBox="0 0 21 20" fill="none" {...props}>
      <Path
        d="M10.2498 5.83317H6.49984C5.9473 5.83317 5.4174 5.61368 5.0267 5.22298C4.636 4.83228 4.4165 4.30237 4.4165 3.74984C4.4165 3.1973 4.636 2.6674 5.0267 2.2767C5.4174 1.886 5.9473 1.6665 6.49984 1.6665C9.4165 1.6665 10.2498 5.83317 10.2498 5.83317ZM10.2498 5.83317H13.9998C14.5524 5.83317 15.0823 5.61368 15.473 5.22298C15.8637 4.83228 16.0832 4.30237 16.0832 3.74984C16.0832 3.1973 15.8637 2.6674 15.473 2.2767C15.0823 1.886 14.5524 1.6665 13.9998 1.6665C11.0832 1.6665 10.2498 5.83317 10.2498 5.83317ZM10.2498 5.83317L10.2498 18.3332M1.9165 11.6665H18.5832M1.9165 8.49984L1.9165 15.6665C1.9165 16.5999 1.9165 17.0666 2.09816 17.4232C2.25795 17.7368 2.51292 17.9917 2.82652 18.1515C3.18304 18.3332 3.64975 18.3332 4.58317 18.3332L15.9165 18.3332C16.8499 18.3332 17.3166 18.3332 17.6732 18.1515C17.9868 17.9917 18.2417 17.7368 18.4015 17.4232C18.5832 17.0666 18.5832 16.5999 18.5832 15.6665V8.49984C18.5832 7.56642 18.5832 7.09971 18.4015 6.74319C18.2417 6.42958 17.9868 6.17462 17.6732 6.01483C17.3166 5.83317 16.8499 5.83317 15.9165 5.83317L4.58317 5.83317C3.64975 5.83317 3.18304 5.83317 2.82652 6.01483C2.51292 6.17462 2.25795 6.42958 2.09816 6.74319C1.9165 7.09971 1.9165 7.56642 1.9165 8.49984Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default GiftIcon;
