/**
 * Opportunities Service for Club M
 * Handles operations for user opportunities management
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {FormDataClient} from "../base/form-data-client";
import {firstValueFrom} from "rxjs";

// Types based on swagger.json schemas
export interface SafeOpportunityViewModel {
  id: number;
  title: string;
  description: string;
  value: number;
  imageUrl?: string;
  imageId?: string;
  status: OpportunityStatus;
  createdAt: string;
  updatedAt: string;
  user: SafeUserViewModel;
  segment: SafeOpportunitySegmentViewModel;
  currentStage: SafeOpportunityCurrentStageViewModel;
}

export interface SafeUserViewModel {
  id: number;
  name: string;
  avatar?: string;
  avatarId?: string;
}

export interface SafeOpportunitySegmentViewModel {
  id: number;
  name: string;
  createdAt: string;
}

export interface SafeOpportunityCurrentStageViewModel {
  id: number;
  name: string;
  createdAt: string;
}

export interface OpportunityPaginateViewModel {
  data: SafeOpportunityViewModel[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export enum OpportunityStatus {
  PendingPayment = 0,
  PendingModeration = 1,
  Approved = 2,
  Rejected = 3,
  Inactive = 4
}

export interface OpportunityFilters {
  status?: OpportunityStatus;
  moderatedAtStart?: string;
  moderatedAtEnd?: string;
  userId?: string | number;
  createdAtStart?: string;
  createdAtEnd?: string;
  search?: string;
  segmentId?: number;
  currentStageId?: number;
  city?: string;
  state?: string;
  page?: number;
  pageSize?: number;
}

export interface CreateOpportunityRequest {
  title: string;
  description: string;
  value: number;
  segmentId: number;
  currentStageId: number;
  targetMarket: string;
  addressState: string;
  addressCity: string;
  addressNeighborhood: string;
  addressStreet: string;
  addressNumber: string;
  addressZip: string;
  addressComplement: string;
  acceptTerms: boolean;
  image?: string; // Campo opcional para imagem em formato base64 (WebP)
}

export interface UpdateOpportunityRequest {
  title: string;
  description: string;
  value: number;
  segmentId: number;
  currentStageId: number;
  targetMarket: string;
  addressState: string;
  addressCity: string;
  addressNeighborhood: string;
  addressStreet: string;
  addressNumber: string;
  addressZip: string;
  addressComplement: string;
  image?: string; // Campo opcional para imagem em formato base64 (WebP)
}

export interface UpdateOpportunityStatusRequest {
  status: OpportunityStatus;
}

export interface InstallmentOption {
  value: number;
  installments: number;
  withFee: boolean;
  feePercentage?: number;
  total: number;
}

export interface OpportunityValueResponse {
  id: number;
  value: number;
  installments: InstallmentOption[];
}

export class OpportunitiesService {
  private static readonly BASE_PATH = "api/app/opportunities";
  private static readonly SEGMENTS_PATH = "api/app/opportunity-segment";
  private static readonly STAGES_PATH = "api/app/opportunity-current-stage";

  /**
   * Get all opportunities (public)
   */
  static async getOpportunities(
    filters?: OpportunityFilters
  ): Promise<OpportunityPaginateViewModel> {
    try {
      ApiLogger.info("Fetching opportunities", filters);

      const params = new URLSearchParams();
      if (filters?.status !== undefined)
        params.append("Status", filters.status.toString());
      if (filters?.moderatedAtStart)
        params.append("ModeratedAtStart", filters.moderatedAtStart);
      if (filters?.moderatedAtEnd)
        params.append("ModeratedAtEnd", filters.moderatedAtEnd);
      if (filters?.userId !== undefined)
        params.append("UserId", filters.userId.toString());
      if (filters?.createdAtStart)
        params.append("CreatedAtStart", filters.createdAtStart);
      if (filters?.createdAtEnd)
        params.append("CreatedAtEnd", filters.createdAtEnd);
      if (filters?.search) params.append("Search", filters.search);
      if (filters?.segmentId)
        params.append("SegmentId", filters.segmentId.toString());
      if (filters?.currentStageId)
        params.append("CurrentStageId", filters.currentStageId.toString());
      if (filters?.city) params.append("City", filters.city);
      if (filters?.state) params.append("State", filters.state);
      if (filters?.page) params.append("Page", filters.page.toString());
      if (filters?.pageSize)
        params.append("PageSize", filters.pageSize.toString());

      const url = params.toString()
        ? `${this.BASE_PATH}?${params}`
        : this.BASE_PATH;

      const response = await firstValueFrom(
        apiClient.get<OpportunityPaginateViewModel>(url)
      );

      ApiLogger.info("Opportunities fetched successfully", {
        totalItems: response.totalItems,
        currentPage: response.currentPage
      });

      return response;
    } catch (error: any) {
      // Se for 403 (Forbidden), retornar resposta vazia em vez de erro
      if (error?.status === 403 || error?.response?.status === 403) {
        ApiLogger.warn(
          "Acesso negado para oportunidades, retornando resposta vazia"
        );
        return {
          data: [],
          totalItems: 0,
          currentPage: 1,
          pageSize: 20,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false
        };
      }

      ApiLogger.error("Error fetching opportunities", error as Error);
      throw error;
    }
  }

  /**
   * Get user's own opportunities
   */
  static async getUserOpportunities(
    filters?: OpportunityFilters
  ): Promise<OpportunityPaginateViewModel> {
    try {
      ApiLogger.info("Fetching user opportunities", filters);

      const params = new URLSearchParams();
      if (filters?.status !== undefined)
        params.append("Status", filters.status.toString());
      if (filters?.moderatedAtStart)
        params.append("ModeratedAtStart", filters.moderatedAtStart);
      if (filters?.moderatedAtEnd)
        params.append("ModeratedAtEnd", filters.moderatedAtEnd);
      if (filters?.createdAtStart)
        params.append("CreatedAtStart", filters.createdAtStart);
      if (filters?.createdAtEnd)
        params.append("CreatedAtEnd", filters.createdAtEnd);
      if (filters?.search) params.append("Search", filters.search);
      if (filters?.segmentId)
        params.append("SegmentId", filters.segmentId.toString());
      if (filters?.currentStageId)
        params.append("CurrentStageId", filters.currentStageId.toString());
      if (filters?.city) params.append("City", filters.city);
      if (filters?.state) params.append("State", filters.state);
      if (filters?.page) params.append("Page", filters.page.toString());
      if (filters?.pageSize)
        params.append("PageSize", filters.pageSize.toString());

      const url = params.toString()
        ? `${this.BASE_PATH}/@me?${params}`
        : `${this.BASE_PATH}/@me`;

      const response = await firstValueFrom(
        apiClient.get<OpportunityPaginateViewModel>(url)
      );

      // Log status information for debugging
      const statusCounts = response.data.reduce(
        (acc: any, opp: SafeOpportunityViewModel) => {
          const statusName = this.getOpportunityStatusName(opp.status);
          acc[statusName] = (acc[statusName] || 0) + 1;
          return acc;
        },
        {}
      );

      console.log("🔍 [OPPORTUNITIES-STATUS] Status breakdown:", statusCounts);

      // Log first few opportunities with their status for debugging
      const firstFew = response.data
        .slice(0, 3)
        .map((opp: SafeOpportunityViewModel) => ({
          id: opp.id,
          title: opp.title.substring(0, 30) + "...",
          status: opp.status,
          statusName: this.getOpportunityStatusName(opp.status)
        }));

      console.log(
        "📋 [OPPORTUNITIES-SAMPLE] First few opportunities:",
        firstFew
      );

      ApiLogger.info("User opportunities fetched successfully", {
        totalItems: response.totalItems,
        currentPage: response.currentPage,
        statusBreakdown: statusCounts
      });

      // Log status da request no console
      console.log(`✅ API Status: GET ${url} - 200 OK`);
      console.log(
        `💼 Opportunities Found: ${response.totalItems} opportunities`
      );

      return response;
    } catch (error: any) {
      // Se for 403 (Forbidden), retornar resposta vazia em vez de erro
      if (error?.status === 403 || error?.response?.status === 403) {
        ApiLogger.warn(
          "Acesso negado para oportunidades do usuário, retornando resposta vazia"
        );
        console.log(
          `⚠️ API Status: GET ${this.BASE_PATH}/@me - 403 Forbidden (Acesso negado)`
        );
        return {
          data: [],
          totalItems: 0,
          currentPage: 1,
          pageSize: 20,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false
        };
      }

      ApiLogger.error("Error fetching user opportunities", error as Error);
      console.log(
        `❌ API Status: GET ${this.BASE_PATH}/@me - ${
          error?.status || "Unknown"
        } Error`
      );
      throw error;
    }
  }

  /**
   * Get opportunity by ID
   */
  static async getOpportunityById(
    id: number
  ): Promise<SafeOpportunityViewModel> {
    try {
      ApiLogger.info("Fetching opportunity by ID", {id});

      const response = await firstValueFrom(
        apiClient.get<SafeOpportunityViewModel>(`${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info("Opportunity fetched successfully", {id: response.id});

      return response;
    } catch (error) {
      ApiLogger.error("Error fetching opportunity by ID", error as Error);
      throw error;
    }
  }

  /**
   * Create a new opportunity
   */
  static async createOpportunity(
    data: CreateOpportunityRequest
  ): Promise<SafeOpportunityViewModel> {
    try {
      ApiLogger.info("Creating opportunity", {title: data.title});

      // Create FormData for multipart/form-data request
      const formData = new FormData();

      // Add required fields to FormData
      formData.append("title", data.title);
      formData.append("description", data.description);
      formData.append("value", data.value.toString());
      formData.append("segmentId", data.segmentId.toString());
      formData.append("currentStageId", data.currentStageId.toString());
      formData.append("targetMarket", data.targetMarket);
      formData.append("addressState", data.addressState);
      formData.append("addressCity", data.addressCity);
      formData.append("addressNeighborhood", data.addressNeighborhood);
      formData.append("addressStreet", data.addressStreet);
      formData.append("addressNumber", data.addressNumber);
      formData.append("addressZip", data.addressZip);
      formData.append("addressComplement", data.addressComplement);
      formData.append("acceptTerms", data.acceptTerms.toString());

      // Add image if provided
      if (data.image) {
        formData.append("image", data.image);
      }

      // Use FormDataClient for proper FormData handling in React Native
      const response = await FormDataClient.post<SafeOpportunityViewModel>(
        this.BASE_PATH,
        formData
      );

      ApiLogger.info("Opportunity created successfully", {
        id: response.data.id,
        title: response.data.title
      });

      // Log status of the request in console
      console.log(
        `✅ API Status: POST ${this.BASE_PATH} - ${response.status} Created`
      );
      console.log(
        `📊 Opportunity Created: ${response.data.title} (ID: ${response.data.id})`
      );

      return response.data;
    } catch (error) {
      ApiLogger.error("Error creating opportunity", error as Error);

      // Log status of the request in console for debugging
      const axiosError = error as any;
      if (axiosError?.response?.status) {
        console.log(
          `❌ API Status: POST ${this.BASE_PATH} - ${axiosError.response.status} Error`
        );
      }

      throw error;
    }
  }

  /**
   * Get opportunity publication value and installment options
   */
  static async getOpportunityPublicationValue(
    id: number
  ): Promise<OpportunityValueResponse> {
    try {
      ApiLogger.info("Fetching opportunity publication value", {id});

      const response = await firstValueFrom(
        apiClient.get<OpportunityValueResponse>(`${this.BASE_PATH}/${id}/value`)
      );

      ApiLogger.info("Opportunity publication value fetched successfully", {
        id: response.id,
        value: response.value,
        installmentOptions: response.installments.length
      });

      return response;
    } catch (error) {
      ApiLogger.error(
        "Error fetching opportunity publication value",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Format opportunity value for display
   */
  static formatOpportunityValue(
    value: number,
    locale: string = "pt-BR"
  ): string {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100); // Assuming value is in cents
  }

  /**
   * Format opportunity date for display
   */
  static formatOpportunityDate(
    dateString: string,
    locale: string = "pt-BR"
  ): string {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale, {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }

  /**
   * Get opportunity status display name
   */
  static getOpportunityStatusName(status: OpportunityStatus): string {
    switch (status) {
      case OpportunityStatus.PendingPayment:
        return "Aguardando Pagamento";
      case OpportunityStatus.PendingModeration:
        return "Aguardando Moderação";
      case OpportunityStatus.Approved:
        return "Aprovada";
      case OpportunityStatus.Rejected:
        return "Rejeitada";
      case OpportunityStatus.Inactive:
        return "Inativa";
      default:
        return "Desconhecido";
    }
  }

  /**
   * Get opportunity segments
   */
  static async getOpportunitySegments(): Promise<
    SafeOpportunitySegmentViewModel[]
  > {
    try {
      ApiLogger.info("Fetching opportunity segments");

      const response = await firstValueFrom(
        apiClient.get<SafeOpportunitySegmentViewModel[]>(this.SEGMENTS_PATH)
      );

      ApiLogger.info("Opportunity segments fetched successfully", {
        count: response.length
      });

      return response;
    } catch (error: any) {
      // Se for 403 (Forbidden), retornar array vazio em vez de erro
      if (error?.status === 403 || error?.response?.status === 403) {
        ApiLogger.warn(
          "Acesso negado para segmentos de oportunidades, retornando array vazio"
        );
        return [];
      }

      ApiLogger.error("Error fetching opportunity segments", error as Error);
      throw error;
    }
  }

  /**
   * Get opportunity current stages
   */
  static async getOpportunityCurrentStages(): Promise<
    SafeOpportunityCurrentStageViewModel[]
  > {
    try {
      ApiLogger.info("Fetching opportunity current stages");

      const response = await firstValueFrom(
        apiClient.get<SafeOpportunityCurrentStageViewModel[]>(this.STAGES_PATH)
      );

      ApiLogger.info("Opportunity current stages fetched successfully", {
        count: response.length
      });

      return response;
    } catch (error: any) {
      // Se for 403 (Forbidden), retornar array vazio em vez de erro
      if (error?.status === 403 || error?.response?.status === 403) {
        ApiLogger.warn(
          "Acesso negado para estágios de oportunidades, retornando array vazio"
        );
        return [];
      }

      ApiLogger.error(
        "Error fetching opportunity current stages",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Update an existing opportunity
   */
  static async updateOpportunity(
    id: number,
    data: UpdateOpportunityRequest
  ): Promise<SafeOpportunityViewModel> {
    try {
      ApiLogger.info("Updating opportunity", {id, title: data.title});

      // Create FormData for multipart/form-data request
      const formData = new FormData();
      formData.append("title", data.title);
      formData.append("description", data.description);
      formData.append("value", data.value.toString());
      formData.append("segmentId", data.segmentId.toString());
      formData.append("currentStageId", data.currentStageId.toString());
      formData.append("targetMarket", data.targetMarket);
      formData.append("addressState", data.addressState);
      formData.append("addressCity", data.addressCity);
      formData.append("addressNeighborhood", data.addressNeighborhood);
      formData.append("addressStreet", data.addressStreet);
      formData.append("addressNumber", data.addressNumber);
      formData.append("addressZip", data.addressZip);
      formData.append("addressComplement", data.addressComplement);

      // Add image if provided
      if (data.image) {
        formData.append("image", data.image);
      }

      // Use FormDataClient for proper FormData handling in React Native
      const response = await FormDataClient.put<SafeOpportunityViewModel>(
        `${this.BASE_PATH}/${id}`,
        formData
      );

      ApiLogger.info("Opportunity updated successfully", {
        id: response.data.id,
        title: response.data.title
      });

      // Log status of the request in console
      console.log(
        `✅ API Status: PUT ${this.BASE_PATH}/${id} - ${response.status} Updated`
      );
      console.log(
        `📊 Opportunity Updated: ${response.data.title} (ID: ${response.data.id})`
      );

      return response.data;
    } catch (error) {
      ApiLogger.error("Error updating opportunity", error as Error, {id});
      throw error;
    }
  }

  /**
   * Update opportunity status
   */
  static async updateOpportunityStatus(
    id: number,
    data: UpdateOpportunityStatusRequest
  ): Promise<SafeOpportunityViewModel> {
    try {
      ApiLogger.info("Updating opportunity status", {id, status: data.status});

      // Determine the correct endpoint based on status
      let endpoint: string;
      if (data.status === OpportunityStatus.Approved) {
        endpoint = `${this.BASE_PATH}/${id}/active`;
      } else {
        endpoint = `${this.BASE_PATH}/${id}/inactive`;
      }

      // Log status of the request in console
      console.log(`🔄 API Request: PUT ${endpoint} - Updating Status`);
      console.log(
        `📝 Opportunity Status Update: ID ${id} to ${this.getOpportunityStatusName(
          data.status
        )}`
      );
      console.log(
        `🎯 [ENDPOINT-LOGIC] Status ${
          data.status
        } (${this.getOpportunityStatusName(data.status)}) → ${endpoint}`
      );

      const response = await firstValueFrom(
        apiClient.put<SafeOpportunityViewModel>(endpoint, {})
      );

      ApiLogger.info("Opportunity status updated successfully", {
        id: response.id,
        status: data.status
      });

      // Log status of the request in console
      console.log(`✅ API Status: PUT ${endpoint} - Status Updated`);
      console.log(
        `📊 Opportunity Status Updated: ${response.title} (ID: ${
          response.id
        }) - ${this.getOpportunityStatusName(data.status)}`
      );

      return response;
    } catch (error) {
      ApiLogger.error("Error updating opportunity status", error as Error, {
        id,
        status: data.status
      });
      throw error;
    }
  }
}
