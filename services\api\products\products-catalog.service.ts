/**
 * Products Catalog Service
 * Handles product catalog operations including fetching installment configuration from products
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {ApiResponse} from "../base/api-types";

export interface Product {
  id: number;
  name: string;
  description?: string;
  value: number;
  category: number;
  isActive: boolean;
  installmentsWithoutFee: number;
  installmentsWithFee: number;
  feePercentage: number;
  installments: ProductInstallment[];
}

export interface ProductInstallment {
  value: number;
  installments: number;
  withFee: boolean;
  feePercentage: number | null;
  total: number;
}

export interface ProductsCatalogResponse {
  data: Product[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  page: number;
  pageCount: number;
  pageSize: number;
  totalItems: number;
}

export interface InstallmentConfig {
  maxInstallmentsWithoutFee: number;
  maxInstallmentsWithFee: number;
  feePercentage: number;
}

export class ProductsCatalogService {
  private static readonly BASE_PATH = "/api/app/products/catalog";

  /**
   * Get products catalog
   */
  static async getProductsCatalog(params?: {
    search?: string;
    category?: string;
    page?: number;
    pageSize?: number;
  }): Promise<ApiResponse<Product[]>> {
    try {
      ApiLogger.info("Fetching products catalog", params);

      const response = await firstValueFrom(
        apiClient.get<ProductsCatalogResponse>(this.BASE_PATH, {
          params: {
            Search: params?.search,
            Category: params?.category,
            Page: params?.page,
            PageSize: params?.pageSize
          }
        })
      );

      ApiLogger.info("Products catalog fetched successfully", {
        totalItems: response.totalItems,
        page: response.page,
        productsCount: response.data?.length || 0
      });

      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      ApiLogger.error("Error fetching products catalog", error as Error);
      throw error;
    }
  }

  /**
   * Get installment configuration from products catalog
   * This method extracts installment configuration from the first available product
   * or returns default values if no products are available
   */
  static async getInstallmentConfig(): Promise<InstallmentConfig> {
    try {
      console.log(
        "🏪 [PRODUCTS-CATALOG] Iniciando busca de configuração de parcelamento"
      );

      // Fetch products catalog with minimal data (just first page, small size)
      const catalogResponse = await this.getProductsCatalog({
        page: 1,
        pageSize: 1
      });

      console.log("🏪 [PRODUCTS-CATALOG] Resposta do catálogo:", {
        success: catalogResponse.success,
        dataLength: catalogResponse.data?.length,
        firstProduct: catalogResponse.data?.[0]
      });

      // Try to get installment config from the first product
      const firstProduct = catalogResponse.data?.[0];

      if (firstProduct) {
        const installmentConfig = {
          maxInstallmentsWithoutFee: firstProduct.installmentsWithoutFee || 6,
          maxInstallmentsWithFee: firstProduct.installmentsWithFee || 12,
          feePercentage: firstProduct.feePercentage || 2.99
        };

        console.log(
          "🏪 [PRODUCTS-CATALOG] Configuração encontrada no produto:",
          installmentConfig
        );

        ApiLogger.info("Installment configuration found in product catalog", {
          maxWithoutFee: installmentConfig.maxInstallmentsWithoutFee,
          maxWithFee: installmentConfig.maxInstallmentsWithFee,
          feePercentage: installmentConfig.feePercentage
        });

        return installmentConfig;
      }

      // If no products found, return default values
      console.log(
        "🏪 [PRODUCTS-CATALOG] Nenhum produto encontrado, usando valores padrão"
      );
      ApiLogger.warn(
        "No products found in catalog, using default installment values"
      );

      const defaultConfig = {
        maxInstallmentsWithoutFee: 6,
        maxInstallmentsWithFee: 12,
        feePercentage: 2.99
      };

      console.log("🏪 [PRODUCTS-CATALOG] Valores padrão:", defaultConfig);
      return defaultConfig;
    } catch (error) {
      const apiError = error as any;

      console.log("🏪 [PRODUCTS-CATALOG] Erro ao buscar configuração:", {
        error: error,
        status: apiError?.response?.status,
        message: apiError?.message
      });

      // Handle permission errors gracefully
      if (apiError?.response?.status === 403) {
        ApiLogger.warn(
          "Access denied to products catalog - using default installment values",
          {
            status: 403,
            endpoint: this.BASE_PATH
          }
        );
      } else if (apiError?.response?.status === 401) {
        ApiLogger.warn(
          "Authentication required for products catalog - using default installment values",
          {
            status: 401,
            endpoint: this.BASE_PATH
          }
        );
      } else {
        ApiLogger.error(
          "Error getting installment configuration from products catalog",
          error as Error
        );
      }

      // Return default values if API fails
      const defaultConfig = {
        maxInstallmentsWithoutFee: 6,
        maxInstallmentsWithFee: 12,
        feePercentage: 2.99
      };

      console.log(
        "🏪 [PRODUCTS-CATALOG] Retornando valores padrão após erro:",
        defaultConfig
      );
      return defaultConfig;
    }
  }

  /**
   * Get real installment options for a specific product
   * This method returns the actual installment options calculated by the backend
   */
  static async getProductInstallmentOptions(
    productId: number
  ): Promise<ProductInstallment[]> {
    try {
      console.log(
        "🏪 [PRODUCTS-CATALOG] Buscando opções de parcelamento para produto:",
        productId
      );

      // For now, we'll get the product from the catalog
      // TODO: Replace with /api/app/products/{id} when available
      const catalogResponse = await this.getProductsCatalog({
        page: 1,
        pageSize: 10 // Get more products to find the specific one
      });

      const product = catalogResponse.data?.find((p) => p.id === productId);

      if (product?.installments && Array.isArray(product.installments)) {
        console.log(
          "🏪 [PRODUCTS-CATALOG] Opções de parcelamento encontradas:",
          product.installments
        );

        // Validar se as opções têm a estrutura esperada
        const validInstallments = product.installments.filter((option) => {
          const isValid =
            option &&
            typeof option.value === "number" &&
            typeof option.total === "number" &&
            typeof option.installments === "number";

          if (!isValid) {
            console.warn(
              "🏪 [PRODUCTS-CATALOG] Opção de parcelamento inválida:",
              option
            );
          }

          return isValid;
        });

        console.log("🏪 [PRODUCTS-CATALOG] Opções válidas:", validInstallments);

        ApiLogger.info("Product installment options found", {
          productId,
          totalOptionsCount: product.installments.length,
          validOptionsCount: validInstallments.length
        });

        return validInstallments;
      }

      console.log(
        "🏪 [PRODUCTS-CATALOG] Produto não encontrado ou sem opções de parcelamento"
      );
      ApiLogger.warn("Product not found or no installment options available", {
        productId
      });

      return [];
    } catch (error) {
      console.log(
        "🏪 [PRODUCTS-CATALOG] Erro ao buscar opções de parcelamento:",
        {
          productId,
          error: error
        }
      );

      ApiLogger.error(
        "Error getting product installment options",
        error as Error
      );

      return [];
    }
  }
}
