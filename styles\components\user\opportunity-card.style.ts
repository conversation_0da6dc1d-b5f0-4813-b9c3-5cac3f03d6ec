import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    width: "100%"
  },
  content: {
    paddingLeft: 12,
    paddingRight: 24,
    gap: 8
  },
  header: {
    flexDirection: "row",
    gap: 4,
    flexWrap: "wrap"
  },
  name: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: 700,
    lineHeight: 18
  },
  label: {
    color: stylesConstants.colors.gray50,
    fontSize: 11,
    fontWeight: 400,
    lineHeight: 18
  },
  date: {
    color: stylesConstants.colors.gray50,
    fontSize: 11,
    fontWeight: 900,
    lineHeight: 18
  },
  title: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginTop: 4
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    overflow: "hidden",
    height: 36,
    flexWrap: "wrap"
  },
  descriptionFree: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    flexWrap: "wrap"
  },

  seeMoreText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 700,
    lineHeight: 18,
    color: stylesConstants.colors.secondaryBrand200
  },
  image: {
    width: "100%",
    height: 120,
    borderRadius: 4,
    marginTop: 10,
    marginBottom: 12
  },
  imagePlaceholder: {
    backgroundColor: stylesConstants.colors.gray10,
    justifyContent: "center",
    alignItems: "center"
  },
  loadingText: {
    color: stylesConstants.colors.gray50,
    fontSize: 12,
    fontWeight: 400
  },
  placeholderText: {
    fontSize: 24,
    opacity: 0.5
  },
  value: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  valueContainer: {
    gap: 0
  },
  footer: {
    marginTop: 6,
    flexDirection: "row",
    justifyContent: "space-between"
  },
  viewOpportunityButton: {
    borderWidth: 1.5,
    paddingHorizontal: 8,
    paddingVertical: 4,
    height: 28
  }
});

export default styles;
