/**
 * Modelos TypeScript para formulários baseados na API ClubM
 */

import {z} from "zod";

// Enums
export enum FormStatus {
  Draft = "Draft",
  Published = "Published",
  Archived = "Archived"
}

// Interfaces base
export interface FormViewModel {
  id: number;
  title: string;
  description?: string;
  status: FormStatus;
  createdAt: string;
  updatedAt?: string;
  pages?: FormPageViewModel[];
}

export interface FormPageViewModel {
  id: number;
  title: string;
  description?: string;
  order: number;
  questions?: FormQuestionViewModel[];
}

export interface FormQuestionOption {
  id: number;
  formQuestionId: number;
  option: string;
}

export interface FormQuestionViewModel {
  id: number;
  formId: number;
  pageId: number;
  label: string;
  placeholder?: string;
  required: boolean;
  type: number;
  options?: (string | FormQuestionOption)[];
}

export interface FormAnswerViewModel {
  id: number;
  questionId: number;
  answer: string;
  createdAt: string;
}

// Response types
export interface FormViewModelPaginateViewModel {
  data: FormViewModel[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface FormAnswersResponse {
  data: FormAnswerViewModel[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Request types
export interface CreateFormAnswersViewModel {
  answers: CreateFormAnswerViewModel[];
}

export interface CreateFormAnswerViewModel {
  questionId: number;
  questionOptionId?: number;
  answer: string;
}

// Schemas de validação
export const CreateFormAnswerSchema = z.object({
  questionId: z.number().min(1, "ID da questão é obrigatório"),
  questionOptionId: z.number().optional(),
  answer: z.string().min(1, "Resposta é obrigatória")
});

export const CreateFormAnswersSchema = z.object({
  answers: z
    .array(CreateFormAnswerSchema)
    .min(1, "Pelo menos uma resposta é obrigatória")
});

// Types derivados dos schemas
export type CreateFormAnswerData = z.infer<typeof CreateFormAnswerSchema>;
export type CreateFormAnswersData = z.infer<typeof CreateFormAnswersSchema>;

// Parâmetros para listagem
export interface FormsListParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface FormAnswersListParams {
  formId: number;
}

// Tipos para o componente de formulário
export interface FormStepData {
  questionId: number;
  question: string;
  description?: string;
  type: string;
  required: boolean;
  options?: string[];
  answer?: string;
}

export interface FormSurveyData {
  form: FormViewModel;
  currentStep: number;
  totalSteps: number;
  answers: Record<number, string>;
}

// Estados do formulário
export interface FormState {
  isVisible: boolean;
  currentForm?: FormViewModel;
  currentStep: number;
  answers: Record<number, string>;
  isSubmitting: boolean;
}

// Props para componentes
export interface FormSurveyDrawerProps {
  visible: boolean;
  onClose: () => void;
  onStartSurvey: () => void;
  formTitle?: string;
}

export interface FormSurveyModalProps {
  visible: boolean;
  onClose: () => void;
  form: FormViewModel;
  onSubmit: (answers: CreateFormAnswersData) => void;
  isSubmitting?: boolean;
}

export interface FormQuestionStepProps {
  question: FormQuestionViewModel;
  answer?: string;
  onAnswerChange: (answer: string, optionId?: number) => void;
  isRequired?: boolean;
  pageTitle?: string;
}
