{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3312,3389,3468,3549,3648,4470,4578,4690,7412,7468,7532,9965,10183,10309,10394,10457,10519,10577,10641,10702,10756,10870,10928,10988,11042,11112,11456,11537,11627,11726,11823,11902,12037,12113,12190,12319,12403,12485,12540,12595,12661,12730,12807,12878,12957,13025,13101,13171,13236,13338,13433,13506,13600,13693,13767,13836,13930,13986,14069,14136,14220,14308,14370,14434,14497,14564,14661,14767,14858,14960,15019,15078,15666,15751,15827", "endLines": "5,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "308,3384,3463,3544,3643,3732,4573,4685,4768,7463,7527,7619,10029,10237,10389,10452,10514,10572,10636,10697,10751,10865,10923,10983,11037,11107,11234,11532,11622,11721,11818,11897,12032,12108,12185,12314,12398,12480,12535,12590,12656,12725,12802,12873,12952,13020,13096,13166,13231,13333,13428,13501,13595,13688,13762,13831,13925,13981,14064,14131,14215,14303,14365,14429,14492,14559,14656,14762,14853,14955,15014,15073,15150,15746,15822,15895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4850,4957,5130,5260,5369,5516,5645,5758,6012,6174,6283,6456,6588,6741,6902,6967,7033", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "4952,5125,5255,5364,5511,5640,5753,5856,6169,6278,6451,6583,6736,6897,6962,7028,7110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7220,7876,7977,8091", "endColumns": "104,100,113,102", "endOffsets": "7320,7972,8086,8189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,203,274,343,423,490,557,631,707,790,869,937,1015,1098,1172,1256,1344,1419,1490,1561,1647,1716,1790,1859", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,198,269,338,418,485,552,626,702,785,864,932,1010,1093,1167,1251,1339,1414,1485,1556,1642,1711,1785,1854,1927"}, "to": {"startLines": "33,51,94,96,97,99,113,114,115,162,163,164,166,171,172,173,174,175,176,177,178,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,4773,9894,10034,10103,10242,11239,11306,11380,15155,15238,15317,15508,15900,15983,16057,16141,16229,16304,16375,16446,16633,16702,16776,16845", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3041,4845,9960,10098,10178,10304,11301,11375,11451,15233,15312,15380,15581,15978,16052,16136,16224,16299,16370,16441,16527,16697,16771,16840,16913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5861", "endColumns": "150", "endOffsets": "6007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,321,426,513,650,765,878,1000,1129,1261,1409,1536,1689,1788,1938,2058,2198,2335,2465,2588,2683,2808,2900,3014,3111,3238", "endColumns": "147,117,104,86,136,114,112,121,128,131,147,126,152,98,149,119,139,136,129,122,94,124,91,113,96,126,95", "endOffsets": "198,316,421,508,645,760,873,995,1124,1256,1404,1531,1684,1783,1933,2053,2193,2330,2460,2583,2678,2803,2895,3009,3106,3233,3329"}, "to": {"startLines": "34,35,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,165,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3046,3194,7115,7325,7624,7761,8194,8307,8429,8558,8690,8838,8965,9118,9217,9367,9487,9627,9764,15385,16918,17013,17138,17230,17344,17441,17568", "endColumns": "147,117,104,86,136,114,112,121,128,131,147,126,152,98,149,119,139,136,129,122,94,124,91,113,96,126,95", "endOffsets": "3189,3307,7215,7407,7756,7871,8302,8424,8553,8685,8833,8960,9113,9212,9362,9482,9622,9759,9889,15503,17008,17133,17225,17339,17436,17563,17659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,15586", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,15661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "41,42,43,44,45,46,47,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3737,3835,3937,4040,4141,4243,4341,16532", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3830,3932,4035,4136,4238,4336,4465,16628"}}]}]}