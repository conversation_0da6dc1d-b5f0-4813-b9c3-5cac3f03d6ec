{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,16206", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,16284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "33,51,94,96,97,99,113,114,115,162,163,164,166,171,172,173,174,175,176,177,178,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,4911,10195,10336,10406,10549,11626,11693,11767,15768,15849,15933,16126,16537,16619,16699,16781,16867,16945,17018,17090,17287,17360,17440,17508", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3137,4986,10265,10401,10484,10613,11688,11762,11837,15844,15928,15997,16201,16614,16694,16776,16862,16940,17013,17085,17181,17355,17435,17503,17576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1084,1149,1248,1314,1374,1476,1538,1614,1672,1750,1815,1869,1986,2050,2114,2168,2248,2382,2468,2555,2658,2754,2843,2979,3064,3152,3304,3399,3482,3540,3592,3658,3737,3819,3890,3977,4053,4130,4207,4278,4388,4495,4575,4672,4772,4846,4927,5032,5090,5178,5245,5336,5428,5490,5554,5617,5686,5789,5896,6001,6106,6168,6224,6308,6402,6480", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "266,346,429,516,622,721,815,925,1017,1079,1144,1243,1309,1369,1471,1533,1609,1667,1745,1810,1864,1981,2045,2109,2163,2243,2377,2463,2550,2653,2749,2838,2974,3059,3147,3299,3394,3477,3535,3587,3653,3732,3814,3885,3972,4048,4125,4202,4273,4383,4490,4570,4667,4767,4841,4922,5027,5085,5173,5240,5331,5423,5485,5549,5612,5681,5784,5891,5996,6101,6163,6219,6303,6397,6475,6551"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3423,3503,3586,3673,3779,4615,4709,4819,7665,7727,7792,10270,10489,10618,10720,10782,10858,10916,10994,11059,11113,11230,11294,11358,11412,11492,11842,11928,12015,12118,12214,12303,12439,12524,12612,12764,12859,12942,13000,13052,13118,13197,13279,13350,13437,13513,13590,13667,13738,13848,13955,14035,14132,14232,14306,14387,14492,14550,14638,14705,14796,14888,14950,15014,15077,15146,15249,15356,15461,15566,15628,15684,16289,16383,16461", "endLines": "5,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,168,169,170", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "316,3498,3581,3668,3774,3873,4704,4814,4906,7722,7787,7886,10331,10544,10715,10777,10853,10911,10989,11054,11108,11225,11289,11353,11407,11487,11621,11923,12010,12113,12209,12298,12434,12519,12607,12759,12854,12937,12995,13047,13113,13192,13274,13345,13432,13508,13585,13662,13733,13843,13950,14030,14127,14227,14301,14382,14487,14545,14633,14700,14791,14883,14945,15009,15072,15141,15244,15351,15456,15561,15623,15679,15763,16378,16456,16532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6049", "endColumns": "159", "endOffsets": "6204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7467,8147,8250,8361", "endColumns": "108,102,110,103", "endOffsets": "7571,8245,8356,8460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4991,5110,5289,5428,5549,5713,5838,5943,6209,6391,6507,6681,6817,6966,7127,7191,7260", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "5105,5284,5423,5544,5708,5833,5938,6044,6386,6502,6676,6812,6961,7122,7186,7255,7347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,336,451,540,684,796,910,1033,1161,1290,1420,1556,1697,1799,1958,2092,2251,2397,2526,2650,2755,2891,2981,3103,3209,3346", "endColumns": "166,113,114,88,143,111,113,122,127,128,129,135,140,101,158,133,158,145,128,123,104,135,89,121,105,136,107", "endOffsets": "217,331,446,535,679,791,905,1028,1156,1285,1415,1551,1692,1794,1953,2087,2246,2392,2521,2645,2750,2886,2976,3098,3204,3341,3449"}, "to": {"startLines": "34,35,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,165,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3142,3309,7352,7576,7891,8035,8465,8579,8702,8830,8959,9089,9225,9366,9468,9627,9761,9920,10066,16002,17581,17686,17822,17912,18034,18140,18277", "endColumns": "166,113,114,88,143,111,113,122,127,128,129,135,140,101,158,133,158,145,128,123,104,135,89,121,105,136,107", "endOffsets": "3304,3418,7462,7660,8030,8142,8574,8697,8825,8954,9084,9220,9361,9463,9622,9756,9915,10061,10190,16121,17681,17817,17907,18029,18135,18272,18380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "41,42,43,44,45,46,47,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3878,3980,4083,4185,4289,4392,4493,17186", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3975,4078,4180,4284,4387,4488,4610,17282"}}]}]}