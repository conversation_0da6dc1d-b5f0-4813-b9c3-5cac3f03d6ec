{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,16433", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,16511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "33,53,96,98,99,101,115,116,117,164,165,166,168,173,174,175,176,177,178,179,180,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,5138,10422,10563,10633,10776,11853,11920,11994,15995,16076,16160,16353,16764,16846,16926,17008,17094,17172,17245,17317,17514,17587,17667,17735", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3137,5213,10492,10628,10711,10840,11915,11989,12064,16071,16155,16224,16428,16841,16921,17003,17089,17167,17240,17312,17408,17582,17662,17730,17803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6276", "endColumns": "159", "endOffsets": "6431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1084,1149,1248,1314,1374,1476,1538,1614,1672,1750,1815,1869,1986,2050,2114,2168,2248,2382,2468,2555,2658,2754,2843,2979,3064,3152,3304,3399,3482,3540,3592,3658,3737,3819,3890,3977,4053,4130,4207,4278,4388,4495,4575,4672,4772,4846,4927,5032,5090,5178,5245,5336,5428,5490,5554,5617,5686,5789,5896,6001,6106,6168,6224,6308,6402,6480", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "266,346,429,516,622,721,815,925,1017,1079,1144,1243,1309,1369,1471,1533,1609,1667,1745,1810,1864,1981,2045,2109,2163,2243,2377,2463,2550,2653,2749,2838,2974,3059,3147,3299,3394,3477,3535,3587,3653,3732,3814,3885,3972,4048,4125,4202,4273,4383,4490,4570,4667,4767,4841,4922,5027,5085,5173,5240,5331,5423,5485,5549,5612,5681,5784,5891,5996,6101,6163,6219,6303,6397,6475,6551"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3650,3730,3813,3900,4006,4842,4936,5046,7892,7954,8019,10497,10716,10845,10947,11009,11085,11143,11221,11286,11340,11457,11521,11585,11639,11719,12069,12155,12242,12345,12441,12530,12666,12751,12839,12991,13086,13169,13227,13279,13345,13424,13506,13577,13664,13740,13817,13894,13965,14075,14182,14262,14359,14459,14533,14614,14719,14777,14865,14932,15023,15115,15177,15241,15304,15373,15476,15583,15688,15793,15855,15911,16516,16610,16688", "endLines": "5,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "316,3725,3808,3895,4001,4100,4931,5041,5133,7949,8014,8113,10558,10771,10942,11004,11080,11138,11216,11281,11335,11452,11516,11580,11634,11714,11848,12150,12237,12340,12436,12525,12661,12746,12834,12986,13081,13164,13222,13274,13340,13419,13501,13572,13659,13735,13812,13889,13960,14070,14177,14257,14354,14454,14528,14609,14714,14772,14860,14927,15018,15110,15172,15236,15299,15368,15471,15578,15683,15788,15850,15906,15990,16605,16683,16759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "73,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7694,8374,8477,8588", "endColumns": "108,102,110,103", "endOffsets": "7798,8472,8583,8687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5218,5337,5516,5655,5776,5940,6065,6170,6436,6618,6734,6908,7044,7193,7354,7418,7487", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "5332,5511,5650,5771,5935,6060,6165,6271,6613,6729,6903,7039,7188,7349,7413,7482,7574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,113", "endOffsets": "163,277"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3142,3255", "endColumns": "112,113", "endOffsets": "3250,3364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,336,451,540,684,796,910,1033,1161,1290,1420,1556,1697,1799,1958,2092,2251,2397,2526,2650,2755,2891,2981,3103,3209,3346", "endColumns": "166,113,114,88,143,111,113,122,127,128,129,135,140,101,158,133,158,145,128,123,104,135,89,121,105,136,107", "endOffsets": "217,331,446,535,679,791,905,1028,1156,1285,1415,1551,1692,1794,1953,2087,2246,2392,2521,2645,2750,2886,2976,3098,3204,3341,3449"}, "to": {"startLines": "36,37,72,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,167,186,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3369,3536,7579,7803,8118,8262,8692,8806,8929,9057,9186,9316,9452,9593,9695,9854,9988,10147,10293,16229,17808,17913,18049,18139,18261,18367,18504", "endColumns": "166,113,114,88,143,111,113,122,127,128,129,135,140,101,158,133,158,145,128,123,104,135,89,121,105,136,107", "endOffsets": "3531,3645,7689,7887,8257,8369,8801,8924,9052,9181,9311,9447,9588,9690,9849,9983,10142,10288,10417,16348,17908,18044,18134,18256,18362,18499,18607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "43,44,45,46,47,48,49,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4105,4207,4310,4412,4516,4619,4720,17413", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "4202,4305,4407,4511,4614,4715,4837,17509"}}]}]}