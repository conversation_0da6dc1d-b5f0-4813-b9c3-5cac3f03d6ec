/**
 * Hook React Query para criação de eventos
 */

import {useMutation, useQueryClient} from "@tanstack/react-query";
import {
  CreateEventFormData,
  convertFormDataToApiModel
} from "@/models/api/app-create-event.models";
import EventsService from "@/services/api/events/events.service";
import {Event} from "@/models/api/events.models";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";

interface UseCreateEventOptions {
  onSuccess?: (event: Event) => void;
  onError?: (error: BaseApiError) => void;
}

interface CreateEventMutationData {
  formData: CreateEventFormData;
  userIds?: number[];
}

/**
 * Hook para criar eventos com React Query
 */
export const useCreateEvent = (options?: UseCreateEventOptions) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateEventMutationData): Promise<Event> => {
      const {formData, userIds = []} = data;

      ApiLogger.info("Iniciando criação de evento", {
        title: formData.title,
        type: formData.type,
        category: formData.category,
        userIds: userIds
      });

      // Converter dados do formulário para o formato da API
      const eventRequest = convertFormDataToApiModel(formData);

      // Adicionar userIds ao request
      eventRequest.userIds = userIds;

      // Chamar serviço para criar evento usando AppCreateEventViewModel
      const createdEvent = await EventsService.createAppEvent(eventRequest);

      ApiLogger.info("Evento criado com sucesso", {
        eventId: createdEvent.id,
        name: createdEvent.name,
        invitedUsers: userIds.length
      });

      return createdEvent;
    },
    onSuccess: (event) => {
      // Invalidar cache de eventos para atualizar listas
      queryClient.invalidateQueries({queryKey: ["events"]});
      queryClient.invalidateQueries({queryKey: ["events", "my-events"]});
      queryClient.invalidateQueries({queryKey: ["events", "upcoming"]});
      queryClient.invalidateQueries({queryKey: ["events", "featured"]});

      // Adicionar evento ao cache
      queryClient.setQueryData(["events", event.id], event);

      ApiLogger.info("Cache de eventos invalidado após criação");

      // Chamar callback de sucesso se fornecido
      options?.onSuccess?.(event);
    },
    onError: (error: BaseApiError) => {
      ApiLogger.error("Erro ao criar evento", error);

      // Chamar callback de erro se fornecido
      options?.onError?.(error);
    },
    retry: (failureCount, error) => {
      // Não tentar novamente para erros de validação (400) ou autorização (401, 403)
      if (
        error?.status === 400 ||
        error?.status === 401 ||
        error?.status === 403
      ) {
        return false;
      }

      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
};

/**
 * Hook para obter estado de loading global de criação de eventos
 */
export const useCreateEventStatus = () => {
  const queryClient = useQueryClient();

  // Verificar se há alguma mutação de criação de evento em andamento
  const mutations = queryClient.getMutationCache().getAll();
  const createEventMutations = mutations.filter(
    (mutation) => mutation.options.mutationKey?.[0] === "createEvent"
  );

  const isCreating = createEventMutations.some(
    (mutation) => mutation.state.status === "pending"
  );
  const hasError = createEventMutations.some(
    (mutation) => mutation.state.status === "error"
  );
  const hasSuccess = createEventMutations.some(
    (mutation) => mutation.state.status === "success"
  );

  return {
    isCreating,
    hasError,
    hasSuccess,
    mutationsCount: createEventMutations.length
  };
};

/**
 * Chaves de query para eventos
 */
export const eventKeys = {
  all: ["events"] as const,
  lists: () => [...eventKeys.all, "list"] as const,
  list: (filters: Record<string, any>) =>
    [...eventKeys.lists(), {filters}] as const,
  details: () => [...eventKeys.all, "detail"] as const,
  detail: (id: string) => [...eventKeys.details(), id] as const,
  myEvents: () => [...eventKeys.all, "my-events"] as const,
  upcoming: () => [...eventKeys.all, "upcoming"] as const,
  featured: () => [...eventKeys.all, "featured"] as const,
  categories: () => [...eventKeys.all, "categories"] as const,
  perDay: (date: string) => [...eventKeys.all, "per-day", date] as const,
  forMonth: (year: number, month: number) =>
    [...eventKeys.all, "for-month", year, month] as const
};
