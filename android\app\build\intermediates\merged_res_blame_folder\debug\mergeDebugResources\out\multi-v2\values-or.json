{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5011,5122,5283,5415,5532,5687,5822,5936,6186,6353,6466,6627,6760,6910,7067,7132,7204", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "5117,5278,5410,5527,5682,5817,5931,6041,6348,6461,6622,6755,6905,7062,7127,7199,7286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6046", "endColumns": "139", "endOffsets": "6181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,433,543,650,736,840,960,1039,1120,1211,1304,1405,1500,1600,1693,1788,1884,1975,2065,2154,2264,2368,2474,2585,2687,2805,2968,14967", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "428,538,645,731,835,955,1034,1115,1206,1299,1400,1495,1595,1688,1783,1879,1970,2060,2149,2259,2363,2469,2580,2682,2800,2963,3069,15052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1017,1082,1171,1236,1295,1381,1445,1509,1572,1645,1709,1763,1875,1933,1995,2049,2121,2243,2330,2406,2498,2580,2666,2806,2883,2964,3091,3182,3259,3313,3364,3430,3500,3577,3648,3723,3794,3871,3940,4009,4116,4207,4279,4368,4457,4531,4603,4689,4739,4818,4884,4964,5048,5110,5174,5237,5306,5406,5501,5593,5685,5743,5798,5882,5963,6038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "267,349,427,504,590,674,768,873,952,1012,1077,1166,1231,1290,1376,1440,1504,1567,1640,1704,1758,1870,1928,1990,2044,2116,2238,2325,2401,2493,2575,2661,2801,2878,2959,3086,3177,3254,3308,3359,3425,3495,3572,3643,3718,3789,3866,3935,4004,4111,4202,4274,4363,4452,4526,4598,4684,4734,4813,4879,4959,5043,5105,5169,5232,5301,5401,5496,5588,5680,5738,5793,5877,5958,6033,6108"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,73,74,75,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3591,3673,3751,3828,3914,4733,4827,4932,7609,7669,7734,10127,10192,10251,10337,10401,10465,10528,10601,10665,10719,10831,10889,10951,11005,11077,11199,11286,11362,11454,11536,11622,11762,11839,11920,12047,12138,12215,12269,12320,12386,12456,12533,12604,12679,12750,12827,12896,12965,13072,13163,13235,13324,13413,13487,13559,13645,13695,13774,13840,13920,14004,14066,14130,14193,14262,14362,14457,14549,14641,14699,14754,15057,15138,15213", "endLines": "5,37,38,39,40,41,49,50,51,73,74,75,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,157,158,159", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "317,3668,3746,3823,3909,3993,4822,4927,5006,7664,7729,7818,10187,10246,10332,10396,10460,10523,10596,10660,10714,10826,10884,10946,11000,11072,11194,11281,11357,11449,11531,11617,11757,11834,11915,12042,12133,12210,12264,12315,12381,12451,12528,12599,12674,12745,12822,12891,12960,13067,13158,13230,13319,13408,13482,13554,13640,13690,13769,13835,13915,13999,14061,14125,14188,14257,14357,14452,14544,14636,14694,14749,14833,15133,15208,15283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,111", "endOffsets": "162,274"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3074,3186", "endColumns": "111,111", "endOffsets": "3181,3293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,348,468,556,706,823,932,1050,1177,1306,1442,1573,1720,1822,1984,2113,2251,2395,2533,2662,2769,2909,3001,3126,3230,3367", "endColumns": "162,129,119,87,149,116,108,117,126,128,135,130,146,101,161,128,137,143,137,128,106,139,91,124,103,136,105", "endOffsets": "213,343,463,551,701,818,927,1045,1172,1301,1437,1568,1715,1817,1979,2108,2246,2390,2528,2657,2764,2904,2996,3121,3225,3362,3468"}, "to": {"startLines": "35,36,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,155,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3298,3461,7291,7521,7823,7973,8417,8526,8644,8771,8900,9036,9167,9314,9416,9578,9707,9845,9989,14838,15389,15496,15636,15728,15853,15957,16094", "endColumns": "162,129,119,87,149,116,108,117,126,128,135,130,146,101,161,128,137,143,137,128,106,139,91,124,103,136,105", "endOffsets": "3456,3586,7406,7604,7968,8085,8521,8639,8766,8895,9031,9162,9309,9411,9573,9702,9840,9984,10122,14962,15491,15631,15723,15848,15952,16089,16195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7411,8090,8195,8308", "endColumns": "109,104,112,108", "endOffsets": "7516,8190,8303,8412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "42,43,44,45,46,47,48,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3998,4101,4203,4306,4411,4512,4614,15288", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "4096,4198,4301,4406,4507,4609,4728,15384"}}]}]}