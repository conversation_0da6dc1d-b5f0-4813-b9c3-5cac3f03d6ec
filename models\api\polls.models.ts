/**
 * Modelos de dados para Enquetes (Polls)
 * Baseado na API ClubM - Funcionalidade de Enquetes
 */

import {z} from "zod";

// Enums para Poll
export enum PollStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  EXPIRED = "EXPIRED",
  CLOSED = "CLOSED"
}

// Interfaces para Poll Option
export interface PollOption {
  id: number;
  text?: string;
  option?: string; // Campo que vem da API
  voteCount?: number;
  votes?: number; // Campo que vem da API
  createdAt?: string;
}

// Interface para User Vote
export interface UserVote {
  id: number;
  optionId: number;
  userId: number;
  createdAt: string;
}

// Interface principal para Poll
export interface Poll {
  id: number;
  title: string;
  description: string;
  status: number; // API retorna número, não enum
  createdAt: string;
  startDate: string;
  endDate?: string;
  user?: {
    id: number;
    name: string;
    avatar?: string;
  };
  options: PollOption[];
  userVote?: UserVote;
  totalVotes?: number;
  isExpired?: boolean;
}

// Interface para resposta paginada de polls
export interface PollPaginateViewModel {
  data: Poll[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  currentPage: number;
}

// Interface para filtros de busca
export interface PollFilters {
  search?: string;
  periodStart?: string;
  periodEnd?: string;
  page?: number;
  pageSize?: number;
}

// Interface para criar voto
export interface VoteRequest {
  pollId: number;
  optionId: number;
}

// Interface para resposta de voto
export interface VoteResponse {
  success?: boolean;
  message?: string;
  poll?: Poll;
  pollOptionId?: number;
  userId?: number;
  voteAt?: string;
}

// Schemas de validação com Zod
export const PollOptionSchema = z.object({
  id: z.number(),
  text: z.string().optional(),
  voteCount: z.number().optional(),
  createdAt: z.string().optional()
});

export const UserVoteSchema = z.object({
  id: z.number(),
  optionId: z.number(),
  userId: z.number(),
  createdAt: z.string()
});

export const PollSchema = z.object({
  id: z.number(),
  title: z.string(),
  description: z.string(),
  status: z.number(), // API retorna número
  createdAt: z.string(),
  startDate: z.string(),
  endDate: z.string().optional(),
  user: z
    .object({
      id: z.number(),
      name: z.string(),
      avatar: z.string().optional()
    })
    .optional(),
  options: z.array(PollOptionSchema),
  userVote: UserVoteSchema.optional(),
  totalVotes: z.number().optional(),
  isExpired: z.boolean().optional()
});

export const PollFiltersSchema = z.object({
  search: z.string().optional(),
  periodStart: z.string().optional(),
  periodEnd: z.string().optional(),
  page: z.number().optional(),
  pageSize: z.number().optional()
});

export const VoteRequestSchema = z.object({
  pollId: z.number(),
  optionId: z.number()
});

// Tipos derivados dos schemas (mantidos para compatibilidade futura)
// export type SafePollViewModel = z.infer<typeof PollSchema>;
// export type SafePollFilters = z.infer<typeof PollFiltersSchema>;
// export type SafeVoteRequest = z.infer<typeof VoteRequestSchema>;

// Utilitários para trabalhar com polls
export const PollUtils = {
  /**
   * Verifica se uma enquete está expirada
   */
  isExpired: (poll: any): boolean => {
    const expiryDate = poll.expiresAt || poll.endDate;
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  },

  /**
   * Calcula o tempo restante para expiração
   */
  getTimeRemaining: (
    poll: any
  ): {hours: number; minutes: number; seconds: number} | null => {
    const expiryDate = poll.expiresAt || poll.endDate;
    if (!expiryDate || PollUtils.isExpired(poll)) return null;

    const now = new Date().getTime();
    const expiry = new Date(expiryDate).getTime();
    const difference = expiry - now;

    if (difference <= 0) return null;

    const hours = Math.floor(difference / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return {hours, minutes, seconds};
  },

  /**
   * Formata o tempo restante como string
   */
  formatTimeRemaining: (poll: any): string => {
    const timeRemaining = PollUtils.getTimeRemaining(poll);
    if (!timeRemaining) return "Expirada";

    const {hours, minutes, seconds} = timeRemaining;
    return `${hours.toString().padStart(2, "0")}h${minutes
      .toString()
      .padStart(2, "0")}m${seconds.toString().padStart(2, "0")}s`;
  },

  /**
   * Verifica se o usuário já votou na enquete
   */
  hasUserVoted: (poll: any): boolean => {
    return !!poll.userVote;
  },

  /**
   * Obtém a opção votada pelo usuário
   */
  getUserVotedOption: (poll: any): PollOption | null => {
    if (!poll.userVote || !poll.options) return null;
    return (
      poll.options.find(
        (option: any) => option.id === poll.userVote!.optionId
      ) || null
    );
  },

  /**
   * Calcula a porcentagem de votos de uma opção
   */
  getOptionPercentage: (option: any, totalVotes: number): number => {
    const votes = option.votes || option.voteCount || 0;
    if (totalVotes === 0 || votes === 0) return 0;
    return Math.round((votes / totalVotes) * 100);
  }
};
