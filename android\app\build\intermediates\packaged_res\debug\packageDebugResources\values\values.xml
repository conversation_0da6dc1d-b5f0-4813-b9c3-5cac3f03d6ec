<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#0C111D</color>
    <color name="iconBackground">#FFFFFF</color>
    <color name="splashscreen_background">#0C111D</color>
    <integer name="react_native_dev_server_port">8082</integer>
    <string name="app_name">Club M</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="gcm_defaultSenderId" translatable="false">227334956891</string>
    <string name="google_api_key" translatable="false">AIzaSyCrHacOugPvTySDCXVAi1rdpNfTI6kRSDc</string>
    <string name="google_app_id" translatable="false">1:227334956891:android:09af0fd87f46e58cefdc7e</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCrHacOugPvTySDCXVAi1rdpNfTI6kRSDc</string>
    <string name="google_storage_bucket" translatable="false">clubm-cb480.firebasestorage.app</string>
    <string name="project_id" translatable="false">clubm-cb480</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#0C111D</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>