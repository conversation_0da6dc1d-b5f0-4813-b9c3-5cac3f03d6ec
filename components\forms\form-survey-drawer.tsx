import React, {useEffect, useRef} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions
} from "react-native";
import {useTranslation} from "react-i18next";
import {FormSurveyDrawerProps} from "@/models/api/forms.models";
import styles from "@/styles/components/forms/form-survey-drawer.style";

const FormSurveyDrawer: React.FC<FormSurveyDrawerProps> = ({
  visible,
  onClose,
  onStartSurvey,
  formTitle = "Formulário disponível"
}) => {
  const {t} = useTranslation();
  const slideAnim = useRef(new Animated.Value(0)).current;
  const screenHeight = Dimensions.get("window").height;

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [visible, slideAnim]);

  const handleBackdropPress = () => {
    onClose();
  };

  const handleYesPress = () => {
    onStartSurvey();
  };

  const handleNoPress = () => {
    onClose();
  };

  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [screenHeight, 0]
  });

  const backdropOpacity = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5]
  });

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, {opacity: backdropOpacity}]}>
          <TouchableOpacity
            style={styles.backdropTouchable}
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{translateY}]
            }
          ]}
        >
          {/* Handle Bar */}
          <View style={styles.handleBar} />

          {/* Content */}
          <View style={styles.content}>
            {/* Title */}
            <Text style={styles.title}>
              {t(
                "formSurvey.drawerTitle",
                "Gostaria de responder um formulário?"
              )}
            </Text>

            {/* Description */}
            <Text style={styles.description}>
              {formTitle
                ? t(
                    "formSurvey.drawerDescriptionWithTitle",
                    "Temos um formulário disponível para você:"
                  )
                : t(
                    "formSurvey.drawerDescription",
                    "Temos um formulário disponível para você. Gostaria de respondê-lo agora?"
                  )}
            </Text>

            {/* Form Title */}
            {!!formTitle && (
              <View style={styles.formTitleContainer}>
                <Text style={styles.formTitle}>"{formTitle}"</Text>
              </View>
            )}

            {/* Buttons */}
            <View style={styles.buttonsContainer}>
              <TouchableOpacity
                style={[styles.button, styles.noButton]}
                onPress={handleNoPress}
                activeOpacity={0.8}
              >
                <Text style={styles.noButtonText}>
                  {t("formSurvey.no", "Não")}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.yesButton]}
                onPress={handleYesPress}
                activeOpacity={0.8}
              >
                <Text style={styles.yesButtonText}>
                  {t("formSurvey.yes", "Sim")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default FormSurveyDrawer;
