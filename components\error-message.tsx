import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import ErrorIcon from "@/components/icons/error-icon";
import styles from "@/styles/components/error-message.style";

export interface ErrorMessageProps {
  message?: string;
  title?: string;
  onRetry?: () => void;
  retryText?: string;
  showIcon?: boolean;
}

/**
 * Error message component that displays error information
 * with optional retry functionality following the project's design patterns.
 */
const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  title,
  onRetry,
  retryText,
  showIcon = true
}) => {
  const {t} = useTranslation();

  const defaultTitle = title || t("common.error", "Erro");
  const defaultMessage = message || t("common.genericError", "Algo deu errado. Tente novamente.");
  const defaultRetryText = retryText || t("common.retry", "Tentar novamente");

  return (
    <View style={styles.container}>
      {showIcon && (
        <View style={styles.iconContainer}>
          <ErrorIcon width={48} height={48} color="#EF4444" />
        </View>
      )}
      
      <Text style={styles.title}>{defaultTitle}</Text>
      <Text style={styles.message}>{defaultMessage}</Text>
      
      {onRetry && (
        <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
          <Text style={styles.retryButtonText}>{defaultRetryText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ErrorMessage;
