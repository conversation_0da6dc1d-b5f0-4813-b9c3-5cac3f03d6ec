{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,162,240,314,386,439,496,567", "endColumns": "57,48,77,73,71,52,56,70,55", "endOffsets": "108,157,235,309,381,434,491,562,618"}, "to": {"startLines": "74,98,99,100,101,102,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7627,10409,10458,10536,10610,10682,16000,16057,16128", "endColumns": "57,48,77,73,71,52,56,70,55", "endOffsets": "7680,10453,10531,10605,10677,10730,16052,16123,16179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,76,77,78,103,106,107,108,109,110,111,112,113,114,115,116,117,118,119,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3605,3689,3770,3847,3946,4788,4887,5027,7780,7844,7910,10735,10980,11042,11130,11192,11261,11324,11397,11460,11514,11635,11692,11754,11808,11885,12186,12271,12351,12450,12536,12618,12753,12834,12915,13061,13152,13242,13297,13348,13414,13487,13567,13638,13718,13793,13870,13939,14016,14121,14209,14298,14391,14484,14558,14638,14732,14783,14867,14933,15017,15105,15167,15231,15294,15362,15477,15591,15697,15806,15865,15920,16657,16742,16821", "endLines": "5,38,39,40,41,42,50,51,52,76,77,78,103,106,107,108,109,110,111,112,113,114,115,116,117,118,119,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,176,177,178", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3684,3765,3842,3941,4036,4882,5022,5105,7839,7905,8000,10815,11037,11125,11187,11256,11319,11392,11455,11509,11630,11687,11749,11803,11880,12017,12266,12346,12445,12531,12613,12748,12829,12910,13056,13147,13237,13292,13343,13409,13482,13562,13633,13713,13788,13865,13934,14011,14116,14204,14293,14386,14479,14553,14633,14727,14778,14862,14928,15012,15100,15162,15226,15289,15357,15472,15586,15692,15801,15860,15915,15995,16737,16816,16898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,16575", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,16652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,352,463,558,715,825,940,1053,1188,1334,1490,1620,1778,1880,2047,2167,2304,2455,2580,2712,2812,2947,3033,3154,3250,3381", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "222,347,458,553,710,820,935,1048,1183,1329,1485,1615,1773,1875,2042,2162,2299,2450,2575,2707,2807,2942,3028,3149,3245,3376,3478"}, "to": {"startLines": "36,37,72,75,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,173,191,192,193,194,195,196,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3308,3480,7416,7685,8005,8162,8583,8698,8811,8946,9092,9248,9378,9536,9638,9805,9925,10062,10213,16355,17873,17973,18108,18194,18315,18411,18542", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "3475,3600,7522,7775,8157,8267,8693,8806,8941,9087,9243,9373,9531,9633,9800,9920,10057,10208,10333,16482,17968,18103,18189,18310,18406,18537,18639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3082,3194", "endColumns": "111,113", "endOffsets": "3189,3303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "43,44,45,46,47,48,49,187", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4041,4139,4241,4340,4442,4551,4658,17529", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "4134,4236,4335,4437,4546,4653,4783,17625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6193", "endColumns": "135", "endOffsets": "6324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5194,5299,5450,5575,5683,5841,5969,6089,6329,6486,6593,6747,6874,7030,7211,7278,7339", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "5294,5445,5570,5678,5836,5964,6084,6188,6481,6588,6742,6869,7025,7206,7273,7334,7411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,53,97,104,105,120,121,171,172,174,179,180,181,182,183,184,185,186,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,5110,10338,10820,10892,12022,12102,16184,16274,16487,16903,16989,17066,17146,17225,17300,17370,17439,17630,17705,17786", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,5189,10404,10887,10975,12097,12181,16269,16350,16570,16984,17061,17141,17220,17295,17365,17434,17524,17700,17781,17868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "73,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7527,8272,8370,8480", "endColumns": "99,97,109,102", "endOffsets": "7622,8365,8475,8578"}}]}]}