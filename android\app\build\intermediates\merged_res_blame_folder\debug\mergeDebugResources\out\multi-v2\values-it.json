{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,73,74,75,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3379,3463,3544,3621,3720,4562,4661,4801,7496,7560,7626,10125,10370,10432,10520,10582,10651,10714,10787,10850,10904,11025,11082,11144,11198,11275,11576,11661,11741,11840,11926,12008,12143,12224,12305,12451,12542,12632,12687,12738,12804,12877,12957,13028,13108,13183,13260,13329,13406,13511,13599,13688,13781,13874,13948,14028,14122,14173,14257,14323,14407,14495,14557,14621,14684,14752,14867,14981,15087,15196,15255,15310,15863,15948,16027", "endLines": "5,36,37,38,39,40,48,49,50,73,74,75,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,165,166,167", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3458,3539,3616,3715,3810,4656,4796,4879,7555,7621,7716,10205,10427,10515,10577,10646,10709,10782,10845,10899,11020,11077,11139,11193,11270,11407,11656,11736,11835,11921,12003,12138,12219,12300,12446,12537,12627,12682,12733,12799,12872,12952,13023,13103,13178,13255,13324,13401,13506,13594,13683,13776,13869,13943,14023,14117,14168,14252,14318,14402,14490,14552,14616,14679,14747,14862,14976,15082,15191,15250,15305,15385,15943,16022,16104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,15781", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,15858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,352,463,558,715,825,940,1053,1188,1334,1490,1620,1778,1880,2047,2167,2304,2455,2580,2712,2812,2947,3033,3154,3250,3381", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "222,347,458,553,710,820,935,1048,1183,1329,1485,1615,1773,1875,2042,2162,2299,2450,2575,2707,2807,2942,3028,3149,3245,3376,3478"}, "to": {"startLines": "34,35,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,162,180,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3082,3254,7190,7401,7721,7878,8299,8414,8527,8662,8808,8964,9094,9252,9354,9521,9641,9778,9929,15561,17079,17179,17314,17400,17521,17617,17748", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "3249,3374,7296,7491,7873,7983,8409,8522,8657,8803,8959,9089,9247,9349,9516,9636,9773,9924,10049,15688,17174,17309,17395,17516,17612,17743,17845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5967", "endColumns": "135", "endOffsets": "6098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4968,5073,5224,5349,5457,5615,5743,5863,6103,6260,6367,6521,6648,6804,6985,7052,7113", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "5068,5219,5344,5452,5610,5738,5858,5962,6255,6362,6516,6643,6799,6980,7047,7108,7185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "41,42,43,44,45,46,47,176", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3815,3913,4015,4114,4216,4325,4432,16735", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3908,4010,4109,4211,4320,4427,4557,16831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,51,94,96,97,112,113,160,161,163,168,169,170,171,172,173,174,175,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,4884,10054,10210,10282,11412,11492,15390,15480,15693,16109,16195,16272,16352,16431,16506,16576,16645,16836,16911,16992", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,4963,10120,10277,10365,11487,11571,15475,15556,15776,16190,16267,16347,16426,16501,16571,16640,16730,16906,16987,17074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7301,7988,8086,8196", "endColumns": "99,97,109,102", "endOffsets": "7396,8081,8191,8294"}}]}]}