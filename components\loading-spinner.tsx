import React from "react";
import {ActivityIndicator, ActivityIndicatorProps} from "react-native";
import stylesConstants from "@/styles/styles-constants";

export interface LoadingSpinnerProps extends ActivityIndicatorProps {
  size?: "small" | "large";
  color?: string;
}

/**
 * Loading spinner component that provides a consistent loading indicator
 * across the application following the project's design patterns.
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "large",
  color = stylesConstants.colors.primary,
  ...props
}) => {
  return (
    <ActivityIndicator
      size={size}
      color={color}
      {...props}
    />
  );
};

export default LoadingSpinner;
