/**
 * Utilitário para tratamento centralizado de erros da API
 * Extrai mensagens de erro detalhadas e fornece fallbacks apropriados
 */

import Toast from "react-native-toast-message";
import { BaseApiError } from "@/services/api/base/api-errors";

export interface ErrorDetails {
  title: string;
  message: string;
  code?: string;
  status?: number;
  isValidationError: boolean;
  isNetworkError: boolean;
  isAuthError: boolean;
  isForbiddenError: boolean;
  isServerError: boolean;
}

/**
 * Extrai detalhes do erro da API
 */
export const extractErrorDetails = (error: any): ErrorDetails => {
  // Valores padrão
  let title = "Erro";
  let message = "Ocorreu um erro inesperado. Tente novamente.";
  let code: string | undefined;
  let status: number | undefined;
  let isValidationError = false;
  let isNetworkError = false;
  let isAuthError = false;
  let isForbiddenError = false;
  let isServerError = false;

  // Se for uma instância de BaseApiError
  if (error instanceof BaseApiError) {
    status = error.status;
    code = error.code;
    message = error.message;
    
    // Classificar tipo de erro
    isValidationError = status === 400;
    isAuthError = status === 401;
    isForbiddenError = status === 403;
    isServerError = status >= 500;
    isNetworkError = status === 0;
  }
  // Se for um erro do Axios
  else if (error?.response) {
    status = error.response.status;
    
    // Tentar extrair mensagem da resposta
    const responseData = error.response.data;
    
    if (typeof responseData === 'string') {
      message = responseData;
    } else if (responseData?.message) {
      message = responseData.message;
    } else if (responseData?.error) {
      message = responseData.error;
    } else if (responseData?.errors && Array.isArray(responseData.errors)) {
      // Se for um array de erros de validação
      const validationErrors = responseData.errors
        .map((err: any) => {
          if (typeof err === 'string') return err;
          return err.message || err.errorMessage || err.description;
        })
        .filter(Boolean)
        .join(', ');
      
      if (validationErrors) {
        message = validationErrors;
      }
    } else if (responseData?.title && responseData?.detail) {
      // Formato ProblemDetails do ASP.NET Core
      title = responseData.title;
      message = responseData.detail;
    }
    
    // Classificar tipo de erro
    isValidationError = status === 400;
    isAuthError = status === 401;
    isForbiddenError = status === 403;
    isServerError = status >= 500;
  }
  // Se for um erro de rede
  else if (error?.code === 'NETWORK_ERROR' || !error?.response) {
    isNetworkError = true;
    title = "Erro de Conexão";
    message = "Verifique sua conexão com a internet e tente novamente.";
  }
  // Se for um erro genérico
  else if (error?.message) {
    message = error.message;
  }

  // Personalizar títulos baseado no tipo de erro
  if (isValidationError) {
    title = "Dados Inválidos";
  } else if (isAuthError) {
    title = "Erro de Autenticação";
    message = "Sua sessão expirou. Faça login novamente.";
  } else if (isForbiddenError) {
    title = "Acesso Negado";
    message = "Você não tem permissão para realizar esta ação.";
  } else if (isServerError) {
    title = "Erro do Servidor";
    message = "Ocorreu um erro interno. Tente novamente em alguns minutos.";
  } else if (isNetworkError) {
    title = "Erro de Conexão";
  }

  return {
    title,
    message,
    code,
    status,
    isValidationError,
    isNetworkError,
    isAuthError,
    isForbiddenError,
    isServerError
  };
};

/**
 * Exibe um toast de erro com detalhes extraídos automaticamente
 */
export const showErrorToast = (error: any, customMessage?: string) => {
  const errorDetails = extractErrorDetails(error);
  
  Toast.show({
    type: "error",
    text1: errorDetails.title,
    text2: customMessage || errorDetails.message,
    position: "top",
    topOffset: 60,
    visibilityTime: 5000,
  });
};

/**
 * Exibe um toast de sucesso
 */
export const showSuccessToast = (title: string, message: string) => {
  Toast.show({
    type: "success",
    text1: title,
    text2: message,
    position: "top",
    topOffset: 60,
    visibilityTime: 4000,
  });
};

/**
 * Exibe um toast de aviso
 */
export const showWarningToast = (title: string, message: string) => {
  Toast.show({
    type: "warning",
    text1: title,
    text2: message,
    position: "top",
    topOffset: 60,
    visibilityTime: 4000,
  });
};

/**
 * Mensagens de erro específicas para criação de oportunidades
 */
export const getOpportunityErrorMessage = (error: any): string => {
  const errorDetails = extractErrorDetails(error);
  
  // Mensagens específicas baseadas no status HTTP
  if (errorDetails.status === 400) {
    // Verificar se é um erro de validação específico
    const message = errorDetails.message.toLowerCase();
    
    if (message.includes('título') || message.includes('title')) {
      return 'O título da oportunidade é obrigatório e deve ter entre 3 e 150 caracteres.';
    }
    if (message.includes('descrição') || message.includes('description')) {
      return 'A descrição da oportunidade deve ter no máximo 250 caracteres.';
    }
    if (message.includes('segmento') || message.includes('segment')) {
      return 'Por favor, selecione um segmento válido para a oportunidade.';
    }
    if (message.includes('estágio') || message.includes('stage')) {
      return 'Por favor, selecione um estágio válido para a oportunidade.';
    }
    if (message.includes('valor') || message.includes('value')) {
      return 'O valor da oportunidade deve ser um número válido.';
    }
    if (message.includes('cep') || message.includes('zip')) {
      return 'Por favor, insira um CEP válido.';
    }
    if (message.includes('endereço') || message.includes('address')) {
      return 'Por favor, verifique os dados de endereço informados.';
    }
    
    // Retornar a mensagem original se não houver correspondência específica
    return errorDetails.message;
  }
  
  if (errorDetails.status === 401) {
    return 'Sua sessão expirou. Faça login novamente para criar a oportunidade.';
  }
  
  if (errorDetails.status === 403) {
    return 'Você não tem permissão para criar oportunidades. Verifique seu plano de assinatura.';
  }
  
  if (errorDetails.status === 409) {
    return 'Já existe uma oportunidade com essas características. Tente com dados diferentes.';
  }
  
  if (errorDetails.status >= 500) {
    return 'Erro interno do servidor. Tente novamente em alguns minutos.';
  }
  
  if (errorDetails.isNetworkError) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }
  
  // Fallback para outros erros
  return errorDetails.message || 'Não foi possível criar a oportunidade. Tente novamente.';
};
