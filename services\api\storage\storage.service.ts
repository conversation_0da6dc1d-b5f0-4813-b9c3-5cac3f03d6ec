/**
 * Serviço de Storage para ClubM
 * Implementa operações para buscar arquivos do storage
 */

import {apiClient} from "../base/api-client";
import {firstValueFrom} from "rxjs";
import {ApiLogger} from "../base/api-logger";
import {BaseApiError} from "../base/api-errors";

/**
 * Interface para resposta de arquivo do storage
 */
export interface StorageFileResponse {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  mimeType: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Interface para download de arquivo
 */
export interface FileDownloadResponse {
  blob: Blob;
  filename?: string;
  contentType?: string;
}

/**
 * Serviço para operações com storage de arquivos
 */
export class StorageService {
  private static readonly BASE_PATH = "/api/storage";

  /**
   * Busca informações de um arquivo por ID
   */
  static async getFileInfo(id: string): Promise<StorageFileResponse> {
    try {
      ApiLogger.info("📁 [STORAGE SERVICE] Buscando informações do arquivo:", {id});

      const response = await firstValueFrom(
        apiClient.request<StorageFileResponse>("GET", `${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info("📁 [STORAGE SERVICE] Arquivo encontrado:", {
        id,
        name: response.name,
        type: response.type,
        size: response.size
      });

      return response;
    } catch (error) {
      ApiLogger.error("📁 [STORAGE SERVICE] Erro ao buscar arquivo:", {
        id,
        error
      });
      throw error;
    }
  }

  /**
   * Faz download de um arquivo por ID
   * Retorna o blob do arquivo para ser usado com react-native-pdf ou similar
   */
  static async downloadFile(id: string): Promise<FileDownloadResponse> {
    try {
      ApiLogger.info("📁 [STORAGE SERVICE] Fazendo download do arquivo:", {id});

      // Fazer request para obter o arquivo como blob
      const response = await firstValueFrom(
        apiClient.request<ArrayBuffer>("GET", `${this.BASE_PATH}/${id}`, undefined, {
          responseType: "arraybuffer"
        })
      );

      // Converter ArrayBuffer para Blob
      const blob = new Blob([response], {
        type: "application/pdf" // Assumindo que é PDF para magazines
      });

      ApiLogger.info("📁 [STORAGE SERVICE] Download concluído:", {
        id,
        size: blob.size
      });

      return {
        blob,
        filename: `magazine-${id}.pdf`,
        contentType: "application/pdf"
      };
    } catch (error) {
      ApiLogger.error("📁 [STORAGE SERVICE] Erro ao fazer download:", {
        id,
        error
      });
      throw error;
    }
  }

  /**
   * Obtém URL temporária para visualização de arquivo
   * Útil para abrir PDFs em visualizadores externos
   */
  static async getFileUrl(id: string): Promise<string> {
    try {
      ApiLogger.info("📁 [STORAGE SERVICE] Obtendo URL do arquivo:", {id});

      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl) {
        throw new Error("EXPO_PUBLIC_API_BASE_URL não está configurado");
      }

      // Construir URL completa do arquivo
      const fileUrl = `${baseUrl}${this.BASE_PATH}/${id}`;

      ApiLogger.info("📁 [STORAGE SERVICE] URL construída:", {
        id,
        url: fileUrl
      });

      return fileUrl;
    } catch (error) {
      ApiLogger.error("📁 [STORAGE SERVICE] Erro ao obter URL:", {
        id,
        error
      });
      throw error;
    }
  }

  /**
   * Converte ArrayBuffer para base64 (útil para React Native)
   */
  static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Obtém arquivo como base64 para uso com react-native-pdf
   */
  static async getFileAsBase64(id: string): Promise<string> {
    try {
      ApiLogger.info("📁 [STORAGE SERVICE] Obtendo arquivo como base64:", {id});

      const response = await firstValueFrom(
        apiClient.request<ArrayBuffer>("GET", `${this.BASE_PATH}/${id}`, undefined, {
          responseType: "arraybuffer"
        })
      );

      const base64 = this.arrayBufferToBase64(response);
      const dataUri = `data:application/pdf;base64,${base64}`;

      ApiLogger.info("📁 [STORAGE SERVICE] Arquivo convertido para base64:", {
        id,
        size: base64.length
      });

      return dataUri;
    } catch (error) {
      ApiLogger.error("📁 [STORAGE SERVICE] Erro ao converter para base64:", {
        id,
        error
      });
      throw error;
    }
  }
}

export default StorageService;
