/**
 * Serviço de API para formulários
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {
  FormViewModelPaginateViewModel,
  FormAnswersResponse,
  CreateFormAnswersViewModel,
  FormsListParams,
  FormAnswersListParams
} from "@/models/api/forms.models";

class FormsService {
  private readonly baseUrl = "/api/app/forms";

  /**
   * Busca lista de formulários disponíveis
   */
  async getForms(
    params?: FormsListParams
  ): Promise<FormViewModelPaginateViewModel> {
    ApiLogger.info("Buscando formulários", {params});

    try {
      const queryParams = new URLSearchParams();

      if (params?.search) {
        queryParams.append("search", params.search);
      }
      if (params?.page) {
        queryParams.append("page", params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append("pageSize", params.pageSize.toString());
      }

      const url = queryParams.toString()
        ? `${this.baseUrl}?${queryParams.toString()}`
        : this.baseUrl;

      const response = await firstValueFrom(
        apiClient.get<FormViewModelPaginateViewModel>(url)
      );

      ApiLogger.info("Formulários obtidos com sucesso", {
        count: response.data?.length || 0,
        totalCount: response.totalCount
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar formulários", {error, params});
      throw error;
    }
  }

  /**
   * Busca respostas de um formulário
   */
  async getFormAnswers(
    params: FormAnswersListParams
  ): Promise<FormAnswersResponse> {
    ApiLogger.info("Buscando respostas do formulário", {params});

    try {
      const response = await firstValueFrom(
        apiClient.get<FormAnswersResponse>(
          `${this.baseUrl}/${params.formId}/answers`
        )
      );

      ApiLogger.info("Respostas do formulário obtidas com sucesso", {
        formId: params.formId,
        count: response.data?.length || 0
      });

      return response;
    } catch (error: any) {
      ApiLogger.error("Erro ao buscar respostas do formulário", {
        error,
        params
      });
      throw error;
    }
  }

  /**
   * Envia respostas de um formulário
   */
  async submitFormAnswers(
    formId: number,
    answers: CreateFormAnswersViewModel
  ): Promise<void> {
    ApiLogger.info("Enviando respostas do formulário", {
      formId,
      answersCount: answers.answers?.length || 0
    });

    try {
      await firstValueFrom(
        apiClient.post<void>(`${this.baseUrl}/${formId}/answers`, answers)
      );

      ApiLogger.info("Respostas do formulário enviadas com sucesso", {
        formId,
        answersCount: answers.answers?.length || 0
      });
    } catch (error: any) {
      ApiLogger.error("Erro ao enviar respostas do formulário", {
        error,
        formId,
        answers
      });
      throw error;
    }
  }

  /**
   * Verifica se há formulários disponíveis para o usuário
   */
  async hasAvailableForms(): Promise<boolean> {
    ApiLogger.info("Verificando formulários disponíveis");

    try {
      const response = await this.getForms({page: 1, pageSize: 1});
      const hasAvailable = (response.data?.length || 0) > 0;

      ApiLogger.info("Verificação de formulários disponíveis concluída", {
        hasAvailable,
        totalCount: response.totalCount
      });

      return hasAvailable;
    } catch (error: any) {
      ApiLogger.error("Erro ao verificar formulários disponíveis", {error});
      // Em caso de erro, assumir que não há formulários disponíveis
      return false;
    }
  }
}

export default new FormsService();
