/**
 * Hooks para operações com Storage
 * Implementa hooks React Query para gerenciar arquivos do storage
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import {
  StorageService,
  StorageFileResponse,
  FileDownloadResponse
} from "@/services/api/storage/storage.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";

/**
 * Chaves para cache do React Query
 */
export const storageKeys = {
  all: ["storage"] as const,
  files: () => [...storageKeys.all, "files"] as const,
  file: (id: string) => [...storageKeys.files(), id] as const,
  fileInfo: (id: string) => [...storageKeys.file(id), "info"] as const,
  fileDownload: (id: string) => [...storageKeys.file(id), "download"] as const,
  fileUrl: (id: string) => [...storageKeys.file(id), "url"] as const,
  fileBase64: (id: string) => [...storageKeys.file(id), "base64"] as const
};

/**
 * Hook para buscar informações de um arquivo
 */
export const useFileInfo = (
  id: string,
  options?: UseQueryOptions<StorageFileResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: storageKeys.fileInfo(id),
    queryFn: () => StorageService.getFileInfo(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    retry: (failureCount, error) => {
      // Não tentar novamente se for 404 (arquivo não encontrado)
      if (error?.status === 404) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para fazer download de arquivo
 */
export const useDownloadFile = (
  options?: UseMutationOptions<FileDownloadResponse, BaseApiError, string>
) => {
  return useMutation({
    mutationFn: (id: string) => StorageService.downloadFile(id),
    onSuccess: (data, id) => {
      ApiLogger.info("📁 [USE-STORAGE] Download concluído:", {
        id,
        size: data.blob.size,
        filename: data.filename
      });
    },
    onError: (error, id) => {
      ApiLogger.error("📁 [USE-STORAGE] Erro no download:", {
        id,
        error
      });
    },
    ...options
  });
};

/**
 * Hook para obter URL de arquivo
 */
export const useFileUrl = (
  id: string,
  options?: UseQueryOptions<string, BaseApiError>
) => {
  return useQuery({
    queryKey: storageKeys.fileUrl(id),
    queryFn: () => StorageService.getFileUrl(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
    retry: false, // URLs são construídas localmente, não precisam retry
    ...options
  });
};

/**
 * Hook para obter arquivo como base64
 */
export const useFileAsBase64 = (
  id: string,
  options?: UseQueryOptions<string, BaseApiError>
) => {
  return useQuery({
    queryKey: storageKeys.fileBase64(id),
    queryFn: () => StorageService.getFileAsBase64(id),
    enabled: !!id,
    staleTime: 30 * 60 * 1000, // 30 minutos (base64 é pesado)
    gcTime: 60 * 60 * 1000, // 1 hora
    retry: (failureCount, error) => {
      // Não tentar novamente se for 404 (arquivo não encontrado)
      if (error?.status === 404) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para obter arquivo como base64 via mutation (para controle manual)
 */
export const useGetFileAsBase64 = (
  options?: UseMutationOptions<string, BaseApiError, string>
) => {
  return useMutation({
    mutationFn: (id: string) => StorageService.getFileAsBase64(id),
    onSuccess: (data, id) => {
      ApiLogger.info("📁 [USE-STORAGE] Arquivo convertido para base64:", {
        id,
        size: data.length
      });
    },
    onError: (error, id) => {
      ApiLogger.error("📁 [USE-STORAGE] Erro ao converter para base64:", {
        id,
        error
      });
    },
    ...options
  });
};

/**
 * Hook para invalidar cache de storage
 */
export const useInvalidateStorage = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () =>
      queryClient.invalidateQueries({
        queryKey: storageKeys.all
      }),
    invalidateFile: (id: string) =>
      queryClient.invalidateQueries({
        queryKey: storageKeys.file(id)
      }),
    invalidateFileInfo: (id: string) =>
      queryClient.invalidateQueries({
        queryKey: storageKeys.fileInfo(id)
      }),
    invalidateFileBase64: (id: string) =>
      queryClient.invalidateQueries({
        queryKey: storageKeys.fileBase64(id)
      })
  };
};

/**
 * Hook para prefetch de arquivos
 */
export const usePrefetchStorage = () => {
  const queryClient = useQueryClient();

  return {
    prefetchFileInfo: (id: string) =>
      queryClient.prefetchQuery({
        queryKey: storageKeys.fileInfo(id),
        queryFn: () => StorageService.getFileInfo(id),
        staleTime: 10 * 60 * 1000
      }),
    prefetchFileBase64: (id: string) =>
      queryClient.prefetchQuery({
        queryKey: storageKeys.fileBase64(id),
        queryFn: () => StorageService.getFileAsBase64(id),
        staleTime: 30 * 60 * 1000
      })
  };
};
