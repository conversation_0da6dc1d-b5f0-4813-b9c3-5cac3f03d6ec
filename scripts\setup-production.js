#!/usr/bin/env node

/**
 * Cross-platform environment setup script for Club M builds
 * Sets up production environment variables and configuration
 */

const fs = require("fs");
const path = require("path");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  purple: "\x1b[35m"
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`[INFO] ${message}`, "blue");
}

function logSuccess(message) {
  log(`[SUCCESS] ${message}`, "green");
}

function logWarning(message) {
  log(`[WARNING] ${message}`, "yellow");
}

function logError(message) {
  log(`[ERROR] ${message}`, "red");
}

function logHeader(message) {
  log(`[SETUP] ${message}`, "purple");
}

// Check if we're in the project root
function checkProjectRoot() {
  const packageJsonPath = path.join(process.cwd(), "package.json");
  if (!fs.existsSync(packageJsonPath)) {
    logError("This script must be run from the project root directory");
    process.exit(1);
  }
}

// Set environment variables for production
function setupEnvironment() {
  logHeader("Setting up production environment...");

  // Set Node.js environment
  process.env.NODE_ENV = "production";
  process.env.EXPO_PUBLIC_ENV = "production";

  logInfo("Environment variables set:");
  logInfo("  NODE_ENV=production");
  logInfo("  EXPO_PUBLIC_ENV=production");
  logInfo("  API URL: https://apiclubm.grupow.dev");
  logInfo("  Firebase Config: Production");

  logSuccess("Environment setup completed");
}

// Verify Firebase configuration files exist
function verifyFirebaseConfig() {
  logHeader("Verifying Firebase configuration...");

  const prodAndroidConfig = path.join(
    process.cwd(),
    "keys",
    "production",
    "google-services.json"
  );
  const prodIosConfig = path.join(
    process.cwd(),
    "keys",
    "production",
    "GoogleService-Info.plist"
  );

  let configValid = true;

  if (!fs.existsSync(prodAndroidConfig)) {
    logError(
      "Production Android Firebase config not found: keys/production/google-services.json"
    );
    configValid = false;
  } else {
    logSuccess("✓ Production Android Firebase config found");
  }

  if (!fs.existsSync(prodIosConfig)) {
    logError(
      "Production iOS Firebase config not found: keys/production/GoogleService-Info.plist"
    );
    configValid = false;
  } else {
    logSuccess("✓ Production iOS Firebase config found");
  }

  if (!configValid) {
    logError("Firebase configuration is incomplete");
    process.exit(1);
  }

  logSuccess("Firebase configuration verified");
}

// Check build requirements
function checkBuildRequirements() {
  logHeader("Checking build requirements...");

  const platform = process.platform;
  logInfo(`Detected platform: ${platform}`);

  // Check for required directories
  const androidDir = path.join(process.cwd(), "android");
  const iosDir = path.join(process.cwd(), "ios");

  if (!fs.existsSync(androidDir)) {
    logError("Android directory not found");
    process.exit(1);
  }
  logSuccess("✓ Android directory found");

  if (!fs.existsSync(iosDir)) {
    logWarning("iOS directory not found");
  } else {
    logSuccess("✓ iOS directory found");
  }

  // Platform-specific checks
  if (platform === "darwin") {
    logInfo("macOS detected - both Android and iOS builds available");
  } else if (platform === "win32") {
    logInfo("Windows detected - only Android builds available");
    logWarning("iOS builds require macOS");
  } else if (platform === "linux") {
    logInfo("Linux detected - only Android builds available");
    logWarning("iOS builds require macOS");
  }

  logSuccess("Build requirements check completed");
}

// Create builds directory
function createBuildsDirectory() {
  const buildsDir = path.join(process.cwd(), "builds");
  const androidBuildsDir = path.join(buildsDir, "android");
  const iosBuildsDir = path.join(buildsDir, "ios");

  if (!fs.existsSync(buildsDir)) {
    fs.mkdirSync(buildsDir);
    logInfo("Created builds directory");
  }

  if (!fs.existsSync(androidBuildsDir)) {
    fs.mkdirSync(androidBuildsDir);
    logInfo("Created builds/android directory");
  }

  if (!fs.existsSync(iosBuildsDir)) {
    fs.mkdirSync(iosBuildsDir);
    logInfo("Created builds/ios directory");
  }
}

// Main setup function
function main() {
  logHeader("Club M Production Build Setup");
  logInfo(`Setup started at: ${new Date().toLocaleString()}`);

  try {
    checkProjectRoot();
    setupEnvironment();
    verifyFirebaseConfig();
    checkBuildRequirements();
    createBuildsDirectory();

    logSuccess("Setup completed successfully!");
    logInfo("You can now run production builds with:");
    logInfo("  bun run build              - Build both platforms");
    logInfo("  bun run build:android      - Build Android only");
    logInfo("  bun run build:ios          - Build iOS only (macOS)");
  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run setup if called directly
if (require.main == module) {
  main();
}

module.exports = {
  setupEnvironment,
  verifyFirebaseConfig,
  checkBuildRequirements,
  createBuildsDirectory
};
