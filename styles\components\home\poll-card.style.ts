import { StyleSheet } from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  pollCard: {
    padding: 20,
    marginBottom: 16
  },

  loadingCard: {
    justifyContent: "center",
    alignItems: "center",
    height: 200
  },

  pollHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    marginBottom: 12
  },

  pollContent: {
    flex: 1,
    gap: 2
  },

  pollUserInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    flexWrap: "wrap"
  },

  userName: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 18
  },

  pollActionText: {
    color: "#F9FAFB",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 11,
    fontWeight: "400",
    lineHeight: 18
  },

  pollDateInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    flexWrap: "wrap"
  },

  pollDatePrefix: {
    color: "#F9FAFB",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 11,
    fontWeight: "400",
    lineHeight: 18
  },

  pollDate: {
    color: "#F9FAFB",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 11,
    fontWeight: "600",
    lineHeight: 18
  },

  pollQuestion: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 18,
    marginBottom: 16,
    marginTop: 4
  },

  pollOptions: {
    gap: 8,
    marginBottom: 16
  },

  pollOption: {
    backgroundColor: "#1E1E2D",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2
  },

  pollOptionSelected: {
    borderColor: "#007AFF",
    backgroundColor: "#1E1E2D"
  },

  pollOptionDisabled: {
    opacity: 0.6
  },

  pollOptionText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 18,
    flex: 1
  },

  pollOptionTextSelected: {
    color: "#FFFFFF"
  },

  pollOptionRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },

  pollOptionPercentage: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    minWidth: 35,
    textAlign: "right"
  },

  pollOptionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    backgroundColor: "#F2F4F7",
    justifyContent: "center",
    alignItems: "center"
  },

  pollOptionIndicatorSelected: {
    backgroundColor: "#FFFFFF",
    borderColor: "#007AFF"
  },

  pollOptionIndicatorLoading: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF"
  },

  pollTimer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginTop: 4
  },

  pollTimerText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },

  pollTimerValue: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },

  pollExpired: {
    marginTop: 8,
    padding: 8,
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderRadius: 6,
    alignItems: "center"
  },

  pollExpiredText: {
    color: "#EF4444",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  }
});

export default styles;
