import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderWidth: 1.5,
    borderColor: "#282A2E",
    borderBottomWidth: 0,
    display: "flex",
    flexDirection: "column",
    alignItems: "stretch",
    overflow: "hidden",
    paddingTop: 8,
    justifyContent: "space-between"
  },

  contentWrapper: {
    flex: 1
  },

  handleContainer: {
    alignItems: "center",
    marginBottom: 20
  },

  handle: {
    width: 44,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2
  },

  headerContainer: {
    alignItems: "center",
    marginBottom: 16,
    paddingHorizontal: 24
  },

  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center"
  },

  coverContainer: {
    alignItems: "center",
    marginBottom: 24
  },

  coverImage: {
    width: 229.1,
    height: 300,
    borderRadius: 2,
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 3
  },

  contentContainer: {
    alignItems: "center",
    paddingHorizontal: 24,
    marginBottom: 28
  },

  badge: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    marginBottom: 4
  },

  title: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "700",
    lineHeight: 30,
    textAlign: "center",
    marginBottom: 4
  },

  descriptionContainer: {
    width: 327,
    alignItems: "center"
  },

  description: {
    textAlign: "center"
  },

  descriptionNormal: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },

  descriptionBold: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },

  buttonsContainer: {
    paddingHorizontal: 24,
    gap: 6,
    paddingBottom: 34
  },

  primaryButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.4)",
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 120.5,
    paddingVertical: 12,
    shadowColor: "rgba(16, 24, 40, 0.15)",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4
  },

  primaryButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24
  },

  secondaryButton: {
    borderRadius: 8,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 137,
    paddingVertical: 12
  },

  secondaryButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
