/**
 * Tests for FormDataClient
 */

import {FormDataClient} from "../../services/api/base/form-data-client";

// Mock fetch
global.fetch = jest.fn();

// Mock environment
process.env.EXPO_PUBLIC_API_BASE_URL = "https://api.test.com";

describe("FormDataClient", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();

    // Set up auth provider for tests
    FormDataClient.setAuthProvider(() => ({
      accessToken: "test-token",
      tokenType: "Bearer",
      expiresIn: 3600
    }));
  });

  describe("POST requests", () => {
    it("should send POST request with FormData", async () => {
      const mockResponse = {
        ok: true,
        status: 201,
        statusText: "Created",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({id: 1, title: "Test"})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append("title", "Test Title");
      formData.append("description", "Test Description");

      const result = await FormDataClient.post("/api/test", formData);

      expect(fetch).toHaveBeenCalledWith("https://api.test.com/api/test", {
        method: "POST",
        body: formData,
        headers: {
          "Accept-Language": "pt-br",
          Authorization: "Bearer test-token"
        }
      });

      expect(result.data).toEqual({id: 1, title: "Test"});
      expect(result.status).toBe(201);
    });

    it("should handle request without authentication", async () => {
      // Mock apiClient without token
      jest.doMock("../../services/api/base/api-client", () => ({
        apiClient: {tokenData: null}
      }));

      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({success: true})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append("data", "test");

      const result = await FormDataClient.post("/api/public", formData);

      expect(fetch).toHaveBeenCalledWith("https://api.test.com/api/public", {
        method: "POST",
        body: formData,
        headers: {
          "Accept-Language": "pt-br"
        }
      });

      expect(result.data).toEqual({success: true});
    });
  });

  describe("PUT requests", () => {
    it("should send PUT request with FormData", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({id: 1, updated: true})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append("name", "Updated Name");

      const result = await FormDataClient.put("/api/users/1", formData);

      expect(fetch).toHaveBeenCalledWith("https://api.test.com/api/users/1", {
        method: "PUT",
        body: formData,
        headers: {
          "Accept-Language": "pt-br",
          Authorization: "Bearer test-token"
        }
      });

      expect(result.data).toEqual({id: 1, updated: true});
    });
  });

  describe("Error handling", () => {
    it("should handle HTTP errors", async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        statusText: "Bad Request",
        text: jest.fn().mockResolvedValue("Invalid data")
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append("invalid", "data");

      await expect(FormDataClient.post("/api/test", formData)).rejects.toThrow(
        "HTTP 400: Bad Request"
      );
    });

    it("should handle network errors", async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error("Network Error"));

      const formData = new FormData();
      formData.append("data", "test");

      await expect(FormDataClient.post("/api/test", formData)).rejects.toThrow(
        "Network Error"
      );
    });

    it("should handle timeout", async () => {
      // Mock a slow response
      (fetch as jest.Mock).mockImplementation(
        () => new Promise((resolve) => setTimeout(resolve, 2000))
      );

      const formData = new FormData();
      formData.append("data", "test");

      await expect(
        FormDataClient.post("/api/test", formData, {timeout: 1000})
      ).rejects.toThrow("Request timeout after 1000ms");
    });
  });

  describe("URL building", () => {
    it("should handle endpoints with leading slash", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      await FormDataClient.post("/api/test", formData);

      expect(fetch).toHaveBeenCalledWith(
        "https://api.test.com/api/test",
        expect.any(Object)
      );
    });

    it("should handle endpoints without leading slash", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      await FormDataClient.post("api/test", formData);

      expect(fetch).toHaveBeenCalledWith(
        "https://api.test.com/api/test",
        expect.any(Object)
      );
    });

    it("should handle base URL with trailing slash", async () => {
      process.env.EXPO_PUBLIC_API_BASE_URL = "https://api.test.com/";

      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({})
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = new FormData();
      await FormDataClient.post("/api/test", formData);

      expect(fetch).toHaveBeenCalledWith(
        "https://api.test.com/api/test",
        expect.any(Object)
      );
    });
  });
});
