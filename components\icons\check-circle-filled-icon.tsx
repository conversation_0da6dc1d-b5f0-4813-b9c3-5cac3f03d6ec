import React from "react";
import Svg, { Defs, ClipP<PERSON>, <PERSON>, <PERSON> } from "react-native-svg";

interface CheckCircleFilledIconProps {
  width?: number;
  height?: number;
  replaceColor?: string;
}

const CheckCircleFilledIcon: React.FC<CheckCircleFilledIconProps> = ({
  width = 24,
  height = 24,
  replaceColor = "rgb(207, 212, 220)"
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24">
      <Defs>
        <ClipPath id="clipPath8088956494">
          <Path d="M0 0L24 0L24 24L0 24L0 0Z" fillRule="nonzero" transform="matrix(1 0 0 1 0 0)" />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath8088956494)">
        <Path
          d="M21 10Q21 12.2373 20.1352 14.282Q19.3001 16.2562 17.7782 17.7782Q16.2562 19.3001 14.282 20.1352Q12.2373 21 10 21Q7.76272 21 5.718 20.1352Q3.74379 19.3001 2.22183 17.7782Q0.699868 16.2562 -0.135154 14.282Q-0.999999 12.2373 -0.999999 10Q-1 7.76272 -0.135155 5.718Q0.699867 3.74378 2.22183 2.22183Q3.74378 0.699869 5.718 -0.135154Q7.76272 -0.999999 10 -0.999999Q12.2373 -1 14.282 -0.135155Q16.2562 0.699866 17.7782 2.22183Q19.3001 3.74378 20.1352 5.718Q21 7.76272 21 10ZM19 10Q19 8.1683 18.2931 6.4971Q17.6101 4.88216 16.364 3.63604Q15.1178 2.38991 13.5029 1.70685Q11.8317 1 10 1Q8.16829 1 6.4971 1.70686Q4.88216 2.38992 3.63604 3.63604Q2.38992 4.88216 1.70685 6.4971Q1 8.16829 1 10Q1 11.8317 1.70686 13.5029Q2.38992 15.1178 3.63604 16.364Q4.88217 17.6101 6.4971 18.2931Q8.16829 19 10 19Q11.8317 19 13.5029 18.2931Q15.1178 17.6101 16.364 16.364Q17.6101 15.1178 18.2931 13.5029Q19 11.8317 19 10ZM15.2929 7.29289C15.6834 6.90237 16.3166 6.90237 16.7071 7.29289C17.0976 7.68341 17.0976 8.31659 16.7071 8.70711L13.2728 12.1414Q13.0707 12.3435 12.9758 12.4241Q12.7297 12.633 12.4635 12.7195Q12 12.8701 11.5365 12.7195Q11.2703 12.633 11.0242 12.4241Q10.9293 12.3435 10.7272 12.1414L8 9.41421L4.70711 12.7071C4.31659 13.0976 3.68341 13.0976 3.29289 12.7071C2.90237 12.3166 2.90237 11.6834 3.29289 11.2929L6.72721 7.85858Q6.9293 7.65648 7.02417 7.57595Q7.27028 7.36701 7.53647 7.28052Q8 7.12991 8.46352 7.28052Q8.72972 7.36701 8.97583 7.57595Q9.0707 7.65648 9.27279 7.85858L12 10.5858L15.2929 7.29289Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 2 2)"
          fill={replaceColor}
        />
      </G>
    </Svg>
  );
};

export default CheckCircleFilledIcon;
