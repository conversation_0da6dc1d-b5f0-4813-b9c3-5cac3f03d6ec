import React, {useState, useEffect, useRef} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
  SafeAreaView,
  TextInput
} from "react-native";
import {AppEventType} from "@/models/api/app-create-event.models";
import styles from "@/styles/components/event-type-selector.style";

interface EventTypeSelectorProps {
  visible: boolean;
  selectedType?: AppEventType;
  onSelect: (type: AppEventType, locationData?: any) => void;
  onClose: () => void;
}

interface LocationOption {
  id: string;
  name: string;
  city: string;
}

const EventTypeSelector: React.FC<EventTypeSelectorProps> = ({
  visible,
  selectedType,
  onSelect,
  onClose
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [activeTab, setActiveTab] = useState<"presential" | "virtual">(
    "presential"
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [virtualLink, setVirtualLink] = useState("");

  const screenHeight = Dimensions.get("window").height;

  // Mock data for nearby locations
  const nearbyLocations: LocationOption[] = [
    {id: "1", name: "Praia dos Amores", city: "Santa Catarina"},
    {id: "2", name: "Cabeçudas", city: "Santa Catarina"},
    {id: "3", name: "Pioneiros", city: "Balneário Camboriú"},
    {id: "4", name: "Ariribá", city: "Balneário Camboriú"},
    {id: "5", name: "Estados", city: "Balneário Camboriú"},
    {id: "6", name: "Nações", city: "Balneário Camboriú"}
  ];

  useEffect(() => {
    if (visible) {
      // Reset states when opening
      setActiveTab("presential");
      setSearchQuery("");
      setVirtualLink("");
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [visible, slideAnim]);

  const handleLocationSelect = (location: LocationOption) => {
    onSelect(AppEventType.PRESENTIAL, {
      type: "location",
      location: location
    });
    onClose();
  };

  const handleSaveLink = () => {
    if (virtualLink.trim()) {
      onSelect(AppEventType.ONLINE, {
        type: "link",
        link: virtualLink.trim()
      });
      onClose();
    }
  };

  const handleBackdropPress = () => {
    onClose();
  };

  const filteredLocations = nearbyLocations.filter(
    (location) =>
      location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.city.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [screenHeight, 0]
  });

  const backdropOpacity = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5]
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, {opacity: backdropOpacity}]}>
          <TouchableOpacity
            style={styles.backdropTouchable}
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{translateY}]
            }
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Handle Bar */}
            <View style={styles.handleBar} />

            {/* Title */}
            <Text style={styles.title}>Tipo de evento</Text>

            {/* Tabs */}
            <View style={styles.tabsContainer}>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === "presential" && styles.activeTab
                ]}
                onPress={() => setActiveTab("presential")}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === "presential" && styles.activeTabText
                  ]}
                >
                  Presencial
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === "virtual" && styles.activeTab
                ]}
                onPress={() => setActiveTab("virtual")}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === "virtual" && styles.activeTabText
                  ]}
                >
                  Virtual
                </Text>
              </TouchableOpacity>
            </View>

            {/* Tab Content */}
            <View style={styles.tabContent}>
              {activeTab === "presential" ? (
                <View style={styles.presentialContent}>
                  {/* Search Input */}
                  <View style={styles.searchContainer}>
                    <View style={styles.searchInputContainer}>
                      <Text style={styles.searchIcon}>🔍</Text>
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Pesquisar localização"
                        placeholderTextColor="#F2F4F7"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                      />
                    </View>
                  </View>

                  {/* Nearby Locations */}
                  <View style={styles.locationsSection}>
                    <Text style={styles.sectionTitle}>
                      Localizações próximas
                    </Text>
                    <ScrollView
                      style={styles.locationsList}
                      showsVerticalScrollIndicator={false}
                    >
                      {filteredLocations.map((location, index) => (
                        <TouchableOpacity
                          key={location.id}
                          style={[
                            styles.locationItem,
                            index < filteredLocations.length - 1 &&
                              styles.locationItemWithBorder
                          ]}
                          onPress={() => handleLocationSelect(location)}
                        >
                          <Text style={styles.locationName}>
                            {location.name}, {location.city}
                          </Text>
                          <Text style={styles.chevronIcon}>›</Text>
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  </View>
                </View>
              ) : (
                <View style={styles.virtualContent}>
                  {/* Link Input */}
                  <View style={styles.linkSection}>
                    <Text style={styles.linkLabel}>Link do evento</Text>
                    <View style={styles.linkInputContainer}>
                      <Text style={styles.linkIcon}>🔗</Text>
                      <TextInput
                        style={styles.linkInput}
                        placeholder="Insira o link da plataforma"
                        placeholderTextColor="#F2F4F7"
                        value={virtualLink}
                        onChangeText={setVirtualLink}
                      />
                    </View>
                  </View>

                  {/* Save Link Button */}
                  <TouchableOpacity
                    style={[
                      styles.saveLinkButton,
                      !virtualLink.trim() && styles.saveLinkButtonDisabled
                    ]}
                    onPress={handleSaveLink}
                    disabled={!virtualLink.trim()}
                  >
                    <Text style={styles.saveLinkButtonText}>Salvar link</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Back Button */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <Text style={styles.backButtonText}>Voltar</Text>
            </TouchableOpacity>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default EventTypeSelector;
