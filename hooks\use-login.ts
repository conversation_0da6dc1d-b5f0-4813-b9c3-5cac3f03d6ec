import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import {LoginRequest, LoginRequestSchema, LoginResponse} from "@/models/login";
import {formatZodError} from "@/utils/zod-utils";
import {cleanCpf} from "@/utils/cpf";
import LoginService from "@/services/login.service";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import {useLoading} from "@/contexts/loading-context";
import useNotification from "./use-notification";
import BiometricService from "@/services/biometric.service";
import FirebaseService from "@/services/firebase.service";
import {concatMap, map, from} from "rxjs";
import {SessionData, useSession} from "@/contexts/session.context";
import {useRouter} from "expo-router";
import * as SecureStore from "expo-secure-store";
import ApiService from "@/services/api.service";

const STORAGE_KEY_NAME: string = "user-login-data";

function useLogin() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [restoreRequest, setRestoreRequest] = useState<boolean>(false);
  const {t} = useTranslation();
  const {firebaseToken} = useNotification();
  const errorActions = useErrorMessage();
  const loadingAction = useLoading();
  const session = useSession();
  const router = useRouter();

  const login = useCallback(
    (request: LoginRequest) => {
      ApiService.onExpiredToken = () => {
        handleLogout();
      };
      const validation = LoginRequestSchema.safeParse(request);
      if (!validation.success) {
        // Format and translate Zod errors
        const zodErrors = formatZodError(validation.error);
        const translatedErrors: Record<string, string> = {};

        Object.entries(zodErrors).forEach(([field, errorKey]) => {
          translatedErrors[field] = t(errorKey, errorKey);
        });

        setErrors(translatedErrors);
        errorActions.emitError({
          title: t("errors.emptyFields"),
          description: t("errors.emptyFieldsDescription"),
          errorType: ErrorType.Warning
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);

      // Check if Firebase is configured, then get auth token
      const isFirebaseConfigured = FirebaseService.isFirebaseConfigured();
      console.log("🔥 Firebase configured:", isFirebaseConfigured);

      const firebaseTokenObservable = isFirebaseConfigured
        ? FirebaseService.getFirebaseAuthToken()
        : from(Promise.resolve(null));

      firebaseTokenObservable
        .pipe(
          concatMap((firebaseAuthToken) => {
            console.log(
              "🔥 Firebase Auth Token obtained:",
              !!firebaseAuthToken
            );

            if (!firebaseAuthToken && isFirebaseConfigured) {
              console.warn(
                "⚠️ Firebase is configured but no token was obtained"
              );
            }

            return BiometricService.hasBiometric(
              BiometricService.createBiometricConfig(t)
            ).pipe(
              map((hasBiometric) => ({
                hasBiometric,
                firebaseAuthToken
              }))
            );
          }),
          concatMap(({hasBiometric, firebaseAuthToken}) => {
            // Limpar CPF antes de enviar para a API
            const cleanedRequest = {
              ...request,
              document: request.document
                ? cleanCpf(request.document)
                : request.document,
              activatedBiometrics: hasBiometric,
              firebaseToken: firebaseAuthToken ?? undefined, // Use Firebase auth token
              pushNotificationToken: firebaseToken ?? undefined // Keep push token separate
            };

            console.log("🔍 Login - Enviando para API:", {
              document: cleanedRequest.document,
              hasPassword: !!cleanedRequest.password,
              hasFirebaseToken: !!firebaseAuthToken,
              hasPushToken: !!firebaseToken
            });

            return LoginService.login(cleanedRequest).pipe(
              map((data: LoginResponse) => ({
                response: data,
                hasBiometric: hasBiometric
              }))
            );
          })
        )
        .subscribe({
          // eslint-disable-next-line @typescript-eslint/no-misused-promises
          next: async (response) => {
            const sessionData = {
              token: response.response.accessToken,
              email: "<EMAIL>",
              name: "placeholder name"
            } satisfies SessionData;

            if (response.hasBiometric) {
              await SecureStore.setItemAsync(
                STORAGE_KEY_NAME,
                JSON.stringify(sessionData)
              );
            }
            session.setLoginData?.(sessionData);
            router.navigate("(tabs)/home");
          },
          error: (error) => {
            console.error("🚨 Login error:", error);
            errorActions.emitError({
              title: t("errors.emptyFields"),
              description: t("errors.tryLater"),
              errorType: ErrorType.Error
            });
            loadingAction.setCurrentLoading?.(false);
          },
          complete: () => {
            loadingAction.setCurrentLoading?.(false);
          }
        });
    },
    [errorActions, t, loadingAction, firebaseToken, session, router]
  );

  const restoreSession = useCallback(() => {
    // NOTA: A restauração automática de sessão agora é gerenciada pelo AuthContext
    // que intercepta o login automático e exige biometria quando disponível.
    // Este hook agora serve apenas como fallback para dispositivos sem biometria.

    ApiService.onExpiredToken = () => {
      handleLogout();
    };
    console.log("Restoring session (fallback)", restoreRequest);
    if (restoreRequest) return;

    SecureStore.getItemAsync(STORAGE_KEY_NAME).then((rawSessionInfo) => {
      if (!rawSessionInfo) return;

      // Verificar se biometria está disponível
      BiometricService.hasBiometric(
        BiometricService.createBiometricConfig(t)
      ).subscribe({
        next: (hasBiometric) => {
          if (hasBiometric) {
            // Se biometria está disponível, não fazer restauração automática
            // O AuthContext já deve ter interceptado e exigido biometria
            console.log(
              "Biometria disponível - não restaurando sessão automaticamente"
            );
            return;
          }

          // Apenas restaurar automaticamente se biometria NÃO estiver disponível
          const sessionData = JSON.parse(rawSessionInfo) satisfies SessionData;
          session.setLoginData?.(sessionData);
          router.navigate("/(tabs)/home");
          setRestoreRequest(true);
        }
      });
    });
  }, [router, t, session, restoreRequest]);

  const handleLogout = useCallback(async () => {
    session.setLoginData?.(null);
    await SecureStore.deleteItemAsync(STORAGE_KEY_NAME);
    router.replace("/(auth)/login");
  }, [router, session]);

  return {
    login,
    errors,
    restoreSession
  };
}

export default useLogin;
