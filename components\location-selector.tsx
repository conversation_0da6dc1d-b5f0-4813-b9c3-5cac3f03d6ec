import React, {useState, useCallback} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  SafeAreaView
} from "react-native";
import {useTranslation} from "react-i18next";
import stylesConstants from "@/styles/styles-constants";
import SearchIcon from "@/components/icons/search-icon";
import CloseIcon from "@/components/icons/close-icon";
import CheckIcon from "@/components/icons/check-icon";

interface LocationSelectorProps {
  visible: boolean;
  selectedCity?: string;
  selectedState?: string;
  onLocationSelect: (city: string, state: string) => void;
  onClose: () => void;
}

// Lista de cidades brasileiras principais
const BRAZILIAN_CITIES = [
  {city: "Itajaí", state: "SC"},
  {city: "Balneário Camboriú", state: "SC"},
  {city: "Florianópolis", state: "SC"},
  {city: "Blumenau", state: "SC"},
  {city: "Joinville", state: "SC"},
  {city: "São Paulo", state: "SP"},
  {city: "Rio de Janeiro", state: "RJ"},
  {city: "Belo Horizonte", state: "MG"},
  {city: "Brasília", state: "DF"},
  {city: "Salvador", state: "BA"},
  {city: "Fortaleza", state: "CE"},
  {city: "Recife", state: "PE"},
  {city: "Porto Alegre", state: "RS"},
  {city: "Curitiba", state: "PR"},
  {city: "Goiânia", state: "GO"},
  {city: "Manaus", state: "AM"},
  {city: "Belém", state: "PA"},
  {city: "Vitória", state: "ES"},
  {city: "Natal", state: "RN"},
  {city: "João Pessoa", state: "PB"},
  {city: "Aracaju", state: "SE"},
  {city: "Maceió", state: "AL"},
  {city: "Teresina", state: "PI"},
  {city: "São Luís", state: "MA"},
  {city: "Palmas", state: "TO"},
  {city: "Boa Vista", state: "RR"},
  {city: "Macapá", state: "AP"},
  {city: "Rio Branco", state: "AC"},
  {city: "Porto Velho", state: "RO"},
  {city: "Cuiabá", state: "MT"},
  {city: "Campo Grande", state: "MS"}
];

const LocationSelector: React.FC<LocationSelectorProps> = ({
  visible,
  selectedCity,
  selectedState,
  onLocationSelect,
  onClose
}) => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCities = BRAZILIAN_CITIES.filter(
    (location) =>
      location.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.state.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLocationPress = useCallback(
    (city: string, state: string) => {
      onLocationSelect(city, state);
    },
    [onLocationSelect]
  );

  const isSelected = useCallback(
    (city: string, state: string) => {
      return selectedCity === city && selectedState === state;
    },
    [selectedCity, selectedState]
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)"
        }}
      >
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: stylesConstants.colors.gray900,
            marginTop: 50,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: stylesConstants.colors.gray800
            }}
          >
            <Text
              style={{
                fontSize: 18,
                fontWeight: "600",
                color: stylesConstants.colors.white
              }}
            >
              {t("locationSelector.title", "Selecionar localização")}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <CloseIcon width={24} height={24} />
            </TouchableOpacity>
          </View>

          {/* Search */}
          <View
            style={{
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: stylesConstants.colors.gray800
            }}
          >
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: stylesConstants.colors.gray800,
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 12
              }}
            >
              <SearchIcon width={20} height={20} />
              <TextInput
                style={{
                  flex: 1,
                  marginLeft: 12,
                  fontSize: 16,
                  color: stylesConstants.colors.white
                }}
                value={searchTerm}
                onChangeText={setSearchTerm}
                placeholder={t(
                  "locationSelector.searchPlaceholder",
                  "Buscar cidade..."
                )}
                placeholderTextColor={stylesConstants.colors.gray25}
                autoCapitalize="words"
              />
            </View>
          </View>

          {/* Cities List */}
          <ScrollView style={{flex: 1}}>
            {filteredCities.map((location, index) => (
              <TouchableOpacity
                key={`${location.city}-${location.state}`}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: 20,
                  borderBottomWidth: index < filteredCities.length - 1 ? 1 : 0,
                  borderBottomColor: stylesConstants.colors.gray800
                }}
                onPress={() =>
                  handleLocationPress(location.city, location.state)
                }
              >
                <View>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: "500",
                      color: stylesConstants.colors.white
                    }}
                  >
                    {location.city}
                  </Text>
                  <Text
                    style={{
                      fontSize: 14,
                      color: stylesConstants.colors.gray25,
                      marginTop: 2
                    }}
                  >
                    {location.state}
                  </Text>
                </View>
                {isSelected(location.city, location.state) && (
                  <CheckIcon width={20} height={20} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

export default LocationSelector;
