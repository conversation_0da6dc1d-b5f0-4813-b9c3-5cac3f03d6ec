import React, {use<PERSON><PERSON>back, useMemo, useState, useEffect} from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  ActivityIndicator
} from "react-native";
import {useRouter} from "expo-router";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import styles from "@/styles/tabs/home.style";
import stylesConstants from "@/styles/styles-constants";
import Avatar from "@/components/user/avatar";
import BellIcon from "@/components/icons/bell-icon";
import SearchIcon from "@/components/icons/search-icon";
import MessageChatSquareIcon from "@/components/icons/message-chat-square-icon";
import BookIcon from "@/components/icons/book-icon";
import HandshakeIcon from "@/components/icons/handshake-icon";
import PublicationCard from "@/components/home/<USER>";
import EventCard from "@/components/home/<USER>";
import PollCard from "@/components/home/<USER>";
import HomeLoading from "@/components/home/<USER>";
import ReferalIcon from "@/components/icons/referal-icon";
import {EventType, EventStatus, LocationType} from "@/models/api/events.models";

// API Hooks
import {useOpportunities} from "@/hooks/api/use-opportunities";
import {useCurrentUser} from "@/hooks/api/use-users";
import {useUnreadNotifications} from "@/hooks/api/use-notifications";
import {useEvents} from "@/hooks/api/use-events";
import {useHomeFeed} from "@/hooks/api/use-home";
import {usePolls} from "@/hooks/api/use-polls";
import {
  useAvailableForms,
  useForms,
  useSubmitFormAnswers
} from "@/hooks/api/use-forms";
import {OpportunityStatus} from "@/services/api/opportunities/opportunities.service";
import CenterIcon from "@/components/icons/center-icon";
import FormSurveyDrawer from "@/components/forms/form-survey-drawer";
import FormSurveyModal from "@/components/forms/form-survey-modal";
import {FormViewModel, CreateFormAnswersData} from "@/models/api/forms.models";
const Home: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);

  // Form survey state
  const [showFormDrawer, setShowFormDrawer] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [selectedForm, setSelectedForm] = useState<FormViewModel | null>(null);

  // API Hooks
  const {
    data: currentUser,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchCurrentUser
  } = useCurrentUser();

  // Use home feed for opportunities section
  const {
    data: homeFeedData,
    isLoading: isHomeFeedLoading,
    error: homeFeedError,
    refetch: refetchHomeFeed
  } = useHomeFeed({
    page: 1,
    pageSize: 10
  });

  // Keep the original opportunities hook as fallback
  const {
    data: opportunitiesData,
    isLoading: isOpportunitiesLoading,
    error: opportunitiesError,
    refetch: refetchOpportunities
  } = useOpportunities({
    status: OpportunityStatus.Published,
    page: 1,
    pageSize: 10
  });

  const {data: unreadNotifications, refetch: refetchUnreadNotifications} =
    useUnreadNotifications();

  const {
    data: eventsData,
    isLoading: isEventsLoading,
    error: eventsError,
    refetch: refetchEvents
  } = useEvents({
    page: 1,
    pageSize: 5
  });

  const {
    data: pollsData,
    isLoading: isPollsLoading,
    error: pollsError,
    refetch: refetchPolls
  } = usePolls({
    page: 1,
    pageSize: 5
  });

  // Forms hooks
  const {data: hasAvailableForms} = useAvailableForms();

  const {data: formsData} = useForms({page: 1, pageSize: 1}, {
    enabled: !!hasAvailableForms
  } as any);

  const submitFormAnswers = useSubmitFormAnswers({
    onSuccess: () => {
      setShowFormModal(false);
      setSelectedForm(null);
    }
  });

  // Computed values
  const notificationCount = useMemo(() => {
    return unreadNotifications?.length || 0;
  }, [unreadNotifications]);

  // Use home feed data if available, otherwise fallback to opportunities data
  const opportunities = useMemo(() => {
    if (homeFeedData?.data) {
      // Filter only opportunity items from home feed
      return homeFeedData.data
        .filter((item) => item.type === "opportunity")
        .map((item, index) => {
          // Debug logs para capturar dados do usuário do home feed
          console.log(`🔍 [HOME-DEBUG] Item ${index}:`, {
            itemId: item.id,
            itemType: item.type,
            hasUser: !!item.user,
            hasMetadata: !!item.metadata,
            hasOpportunity: !!item.metadata?.opportunity,
            hasOpportunityUser: !!item.metadata?.opportunity?.user,
            topLevelUser: item.user,
            opportunityUser: item.metadata?.opportunity?.user,
            fullItemKeys: Object.keys(item),
            fullItem: item
          });

          return {
            id: item.id,
            title: item.title,
            description: item.description,
            value: item.metadata?.value || 0,
            imageUrl: item.imageUrl,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt || item.createdAt,
            user:
              item.user ||
              (item.metadata?.opportunity?.user
                ? {
                    id: item.metadata.opportunity.user.id,
                    name: item.metadata.opportunity.user.name,
                    avatar:
                      item.metadata.opportunity.user.avatar ||
                      item.metadata.opportunity.user.avatarId
                  }
                : {
                    id: 0,
                    name: "Usuário",
                    avatar: undefined
                  }),
            segment: item.metadata?.segment || {
              id: 0,
              name: "Geral",
              createdAt: item.createdAt
            },
            currentStage: item.metadata?.currentStage || {
              id: 0,
              name: "Publicado",
              createdAt: item.createdAt
            }
          };
        });
    }

    // Debug logs para oportunidades da API separada
    if (opportunitiesData?.data) {
      console.log(
        `🔍 [OPPORTUNITIES-DEBUG] Total opportunities:`,
        opportunitiesData.data.length
      );
      opportunitiesData.data.forEach((opportunity, index) => {
        console.log(`🔍 [OPPORTUNITIES-DEBUG] Opportunity ${index}:`, {
          id: opportunity.id,
          title: opportunity.title,
          hasUser: !!opportunity.user,
          userId: opportunity.user?.id,
          userName: opportunity.user?.name,
          userAvatar: opportunity.user?.avatar,
          fullUser: opportunity.user,
          fullOpportunity: opportunity
        });
      });
    }

    return opportunitiesData?.data || [];
  }, [homeFeedData, opportunitiesData]);

  // Use home feed data for events if available, otherwise use events data
  const events = useMemo(() => {
    if (homeFeedData?.data) {
      // Filter only event items from home feed
      const eventItems = homeFeedData.data.filter(
        (item) => item.type === "event"
      );
      if (eventItems.length > 0) {
        return eventItems.map((item) => ({
          id: item.id.toString(),
          title: item.title,
          description: item.description,
          shortDescription: item.description,
          category: {
            id: "general",
            name: "Geral",
            slug: "general",
            color: "#007AFF"
          },
          type: EventType.MEETING,
          status: EventStatus.PUBLISHED,
          startDate: item.createdAt,
          endDate: item.createdAt,
          location: {
            type: LocationType.PHYSICAL,
            name: item.metadata?.location || "Local não informado"
          },
          capacity: 100,
          registeredCount: 0,
          waitingListCount: 0,
          images: item.imageUrl
            ? [
                {
                  id: "1",
                  url: item.imageUrl,
                  thumbnailUrl: item.imageUrl,
                  alt: item.title,
                  isPrimary: true,
                  order: 1
                }
              ]
            : [],
          organizer: {
            id: item.user?.id?.toString() || "0",
            name: item.user?.name || "Organizador",
            email: "<EMAIL>",
            avatar: item.user?.avatar || ""
          },
          tags: [],
          isRegistered: false,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt || item.createdAt
        }));
      }
    }

    // Fallback to events data or empty array
    return eventsData?.data || [];
  }, [homeFeedData, eventsData]);

  // Combined publications (opportunities, events, and polls) in chronological order
  const combinedPublications = useMemo(() => {
    const publications: Array<{
      type: "opportunity" | "event" | "poll";
      data: any;
      createdAt: string;
    }> = [];

    // Add opportunities
    opportunities.forEach((opportunity) => {
      publications.push({
        type: "opportunity",
        data: opportunity,
        createdAt: opportunity.createdAt
      });
    });

    // Add events
    events.forEach((event) => {
      publications.push({
        type: "event",
        data: event,
        createdAt: event.createdAt
      });
    });

    // Add polls
    const polls = pollsData?.data || [];
    polls.forEach((poll) => {
      if (poll && poll.id) {
        publications.push({
          type: "poll",
          data: poll,
          createdAt: poll.createdAt
        });
      }
    });

    // Sort by creation date (most recent first)
    return publications.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [opportunities, events, pollsData]);

  // Event handlers
  const handleSearchPress = useCallback(() => {
    router.push("/(main)/search");
  }, [router]);

  const handleNotificationPress = useCallback(() => {
    router.push("/(main)/notifications");
  }, [router]);

  const handleQuickActionPress = useCallback(
    (action: string) => {
      switch (action) {
        case "central-negócios":
          router.push("/(business)/business-center");
          break;
        case "carteira-digital":
          router.push("/(wallet)/wallet");
          break;
        case "revista-digital":
          router.push("/(magazine)/magazine-list");
          break;
        case "indicação-amigos":
          router.push("/(main)/referral");
          break;
        default:
          console.log("Quick action pressed:", action);
      }
    },
    [router]
  );

  const handleInterestPress = useCallback(
    (opportunityId: number) => {
      // Navigate to opportunity details
      router.push(`/(business)/opportunities/${opportunityId}`);
    },
    [router]
  );

  const handleUserPress = useCallback(
    (userId: number) => {
      // Navigate to user profile with the specific user ID
      console.log("User pressed:", userId);
      router.push(`/(main)/user-profile/${userId}`);
    },
    [router]
  );

  // Form survey handlers
  const handleStartSurvey = useCallback(() => {
    const firstForm = formsData?.data?.[0];
    if (firstForm) {
      setSelectedForm(firstForm);
      setShowFormDrawer(false);
      setShowFormModal(true);
    }
  }, [formsData]);

  const handleCloseFormDrawer = useCallback(() => {
    setShowFormDrawer(false);
  }, []);

  const handleCloseFormModal = useCallback(() => {
    setShowFormModal(false);
    setSelectedForm(null);
  }, []);

  const handleSubmitForm = useCallback(
    (answers: CreateFormAnswersData) => {
      if (selectedForm) {
        submitFormAnswers.mutate({
          formId: selectedForm.id,
          answers
        });
      }
    },
    [selectedForm, submitFormAnswers]
  );

  // Show form drawer automatically when forms are available
  useEffect(() => {
    if (hasAvailableForms && formsData?.data && formsData.data.length > 0) {
      setShowFormDrawer(true);
    }
  }, [hasAvailableForms, formsData]);

  const handleEventPress = useCallback(
    (eventId: string) => {
      // Navigate to event details
      router.push(`/(events)/event-sale?id=${eventId}`);
    },
    [router]
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Recarregar todos os dados da tela home em paralelo
      await Promise.allSettled([
        refetchCurrentUser(),
        refetchHomeFeed(),
        refetchOpportunities(), // Keep as fallback
        refetchUnreadNotifications(),
        refetchEvents(),
        refetchPolls()
      ]);
    } finally {
      setRefreshing(false);
    }
  }, [
    refetchCurrentUser,
    refetchHomeFeed,
    refetchOpportunities,
    refetchUnreadNotifications,
    refetchEvents,
    refetchPolls
  ]);

  // Render combined publications section
  const renderCombinedPublications = useCallback(() => {
    // Check loading states for all data sources
    const isLoading =
      isHomeFeedLoading ||
      (homeFeedError && isOpportunitiesLoading) ||
      isEventsLoading ||
      isPollsLoading;

    if (isLoading) {
      return (
        <View style={{alignItems: "center", paddingVertical: 20}}>
          <ActivityIndicator size="large" color="#FFFFFF" />
          <Text style={styles.publicationText}>Carregando publicações...</Text>
        </View>
      );
    }

    if (combinedPublications.length > 0) {
      return combinedPublications.map((publication, index) => {
        const key = `${publication.type}-${publication.data.id}-${index}`;

        switch (publication.type) {
          case "opportunity":
            return (
              <PublicationCard
                key={key}
                opportunity={publication.data}
                onInterestPress={handleInterestPress}
                onUserPress={handleUserPress}
              />
            );
          case "event":
            return (
              <EventCard
                key={key}
                event={publication.data}
                onEventPress={handleEventPress}
              />
            );
          case "poll":
            return (
              <PollCard
                key={key}
                poll={publication.data}
                onUserPress={handleUserPress}
              />
            );
          default:
            return null;
        }
      });
    }

    return (
      <View style={{alignItems: "center", paddingVertical: 40}}>
        <Text style={styles.publicationText}>
          Nenhuma publicação encontrada
        </Text>
      </View>
    );
  }, [
    isHomeFeedLoading,
    homeFeedError,
    isOpportunitiesLoading,
    isEventsLoading,
    isPollsLoading,
    combinedPublications,
    handleInterestPress,
    handleUserPress,
    handleEventPress
  ]);

  // Loading and error states
  const isInitialLoading =
    isUserLoading &&
    (isHomeFeedLoading || isOpportunitiesLoading) &&
    isEventsLoading &&
    isPollsLoading;
  const hasError =
    userError ||
    (homeFeedError && opportunitiesError) ||
    eventsError ||
    pollsError;

  // Show loading screen only on initial load
  if (isInitialLoading) {
    return <HomeLoading testID="home-loading" />;
  }

  if (hasError) {
    return (
      <View
        style={[
          styles.container,
          {justifyContent: "center", alignItems: "center", padding: 20}
        ]}
      >
        <Text
          style={[
            styles.publicationText,
            {textAlign: "center", marginBottom: 20}
          ]}
        >
          Erro ao carregar dados. Verifique sua conexão e tente novamente.
        </Text>
        <TouchableOpacity
          onPress={handleRefresh}
          style={{
            backgroundColor: stylesConstants.colors.green400,
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8
          }}
        >
          <Text style={{color: "#FFFFFF", fontWeight: "600"}}>
            Tentar Novamente
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#02343B" />

      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#FFFFFF"
          />
        }
      >
        {/* Header */}
        <View
          style={[styles.green, {height: 265 + Math.max(insets.top - 10, 0)}]}
        >
          <View
            style={[styles.header, {paddingTop: Math.max(insets.top + 10, 20)}]}
          >
            <TouchableOpacity
              onPress={() => router.push("/(tabs)/user")}
              style={styles.avatarButton}
            >
              <Avatar
                user={currentUser}
                name={currentUser?.name}
                size={40}
                borderSize={2}
              />
            </TouchableOpacity>

            <View style={styles.searchContainer}>
              <TouchableOpacity
                style={styles.searchInputContainer}
                onPress={handleSearchPress}
              >
                <SearchIcon width={22} height={22} replaceColor="#ffffff" />
                <Text style={styles.searchInput}>Buscar membros e etc...</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={handleNotificationPress}
              style={styles.notificationButton}
              testID="notification-button"
            >
              <BellIcon width={24} height={24} replaceColor="#FFFFFF" />
              {notificationCount > 0 && (
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationBadgeText}>
                    {notificationCount > 99 ? "99+" : notificationCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickActionItem}
              onPress={() => handleQuickActionPress("central-negócios")}
            >
              <View style={styles.quickActionIcon}>
                <CenterIcon width={24} height={24} stroke="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>
                Central de{"\n"}Negócios
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionItem}
              onPress={() => handleQuickActionPress("carteira-digital")}
            >
              <View style={styles.quickActionIcon}>
                <MessageChatSquareIcon
                  width={24}
                  height={24}
                  stroke="#FFFFFF"
                />
              </View>
              <Text style={styles.quickActionText}>Carteira{"\n"}Digital</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionItem}
              onPress={() => handleQuickActionPress("revista-digital")}
            >
              <View style={styles.quickActionIcon}>
                <BookIcon width={24} height={24} stroke="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>Revista{"\n"}Digital</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionItem}
              onPress={() => handleQuickActionPress("indicação-amigos")}
            >
              <View style={styles.quickActionIcon}>
                <ReferalIcon width={24} height={24} stroke="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>
                Indique o{"\n"}Aplicativo
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Combined Publications - Unified section with chronological order */}
        <View style={[styles.publicationsSection, {marginTop: -120}]}>
          <Text style={styles.sectionTitle}>
            Confira as publicações recentes
          </Text>

          {renderCombinedPublications()}
        </View>

        {/* Statistics - Movido para baixo */}
        {/* <View style={styles.greetingSection}>
          <HomeStatistics
            isLoading={isUserLoading}
            title="Oportunidades visualizadas"
            mainValue={2420}
            percentageChange={40}
            comparisonText="comparado ao ultimo mês"
          />
        </View> */}
      </ScrollView>

      {/* Form Survey Drawer */}
      <FormSurveyDrawer
        visible={showFormDrawer}
        onClose={handleCloseFormDrawer}
        onStartSurvey={handleStartSurvey}
        formTitle={formsData?.data?.[0]?.title}
      />

      {/* Form Survey Modal */}
      {selectedForm && (
        <FormSurveyModal
          visible={showFormModal}
          onClose={handleCloseFormModal}
          form={selectedForm}
          onSubmit={handleSubmitForm}
          isSubmitting={submitFormAnswers.isPending}
        />
      )}
    </>
  );
};

export default Home;
