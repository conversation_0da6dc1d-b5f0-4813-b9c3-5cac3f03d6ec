/**
 * System Configuration Service
 * Handles system-wide configuration settings including payment installment options
 */

import {firstValueFrom} from "rxjs";
import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";

export interface SystemConfigResponse {
  applicationUseTermId?: number;
  titleTermId?: number;
  titleValue?: number;
  installmentsWithoutFee?: number;
  installmentsWithFee?: number;
  feePercentage?: number;
}

export interface InstallmentConfig {
  maxInstallmentsWithoutFee: number;
  maxInstallmentsWithFee: number;
  feePercentage: number;
}

export class SystemConfigService {
  private static readonly BASE_PATH = "/api/system-config";

  /**
   * Get system configuration
   */
  static async getSystemConfig(): Promise<SystemConfigResponse> {
    try {
      ApiLogger.info("Fetching system configuration");

      const response = await firstValueFrom(
        apiClient.get<SystemConfigResponse>(this.BASE_PATH)
      );

      ApiLogger.info("System configuration fetched successfully", {
        installmentsWithoutFee: response.installmentsWithoutFee,
        installmentsWithFee: response.installmentsWithFee,
        feePercentage: response.feePercentage
      });

      return response;
    } catch (error) {
      ApiLogger.error("Error fetching system configuration", error as Error);
      throw error;
    }
  }

  /**
   * Get installment configuration from system config
   */
  static async getInstallmentConfig(): Promise<InstallmentConfig> {
    try {
      console.log(
        "🔧 [SYSTEM-CONFIG] Iniciando busca de configuração de parcelamento"
      );
      const systemConfig = await this.getSystemConfig();

      console.log("🔧 [SYSTEM-CONFIG] Configuração do sistema recebida:", {
        installmentsWithoutFee: systemConfig.installmentsWithoutFee,
        installmentsWithFee: systemConfig.installmentsWithFee,
        feePercentage: systemConfig.feePercentage,
        fullConfig: systemConfig
      });

      const installmentConfig = {
        maxInstallmentsWithoutFee: systemConfig.installmentsWithoutFee || 6,
        maxInstallmentsWithFee: systemConfig.installmentsWithFee || 12,
        feePercentage: systemConfig.feePercentage || 2.99
      };

      console.log(
        "🔧 [SYSTEM-CONFIG] Configuração de parcelamento gerada:",
        installmentConfig
      );
      return installmentConfig;
    } catch (error) {
      const apiError = error as any;

      console.log("🔧 [SYSTEM-CONFIG] Erro ao buscar configuração:", {
        error: error,
        status: apiError?.response?.status,
        message: apiError?.message
      });

      // Log different types of errors differently
      if (apiError?.response?.status === 403) {
        ApiLogger.warn(
          "Access denied to system configuration - using default installment values",
          {
            status: 403,
            endpoint: this.BASE_PATH
          }
        );
      } else if (apiError?.response?.status === 401) {
        ApiLogger.warn(
          "Authentication required for system configuration - using default installment values",
          {
            status: 401,
            endpoint: this.BASE_PATH
          }
        );
      } else {
        ApiLogger.error(
          "Error getting installment configuration",
          error as Error
        );
      }

      // Return default values if API fails
      const defaultConfig = {
        maxInstallmentsWithoutFee: 6,
        maxInstallmentsWithFee: 12,
        feePercentage: 2.99
      };

      console.log(
        "🔧 [SYSTEM-CONFIG] Retornando valores padrão após erro:",
        defaultConfig
      );
      return defaultConfig;
    }
  }
}
