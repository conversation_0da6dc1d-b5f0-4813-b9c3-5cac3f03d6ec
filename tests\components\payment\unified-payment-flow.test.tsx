/**
 * Testes para o componente UnifiedPaymentFlow
 * Verifica se o sistema de pagamento unificado funciona corretamente
 */

import React from "react";
import {render, fireEvent, waitFor} from "@testing-library/react-native";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import UnifiedPaymentFlow from "@/components/payment/unified-payment-flow";
import {PaymentEntity} from "@/models/api/payments.models";

// Mock dos hooks
jest.mock("@/hooks/api/use-payments", () => ({
  useCreditCards: () => ({
    data: [],
    isLoading: false,
    error: null
  }),
  useCreatePayment: () => ({
    mutateAsync: jest.fn().mockResolvedValue({id: "payment-123"}),
    isPending: false
  }),
  useCreateCreditCard: () => ({
    mutateAsync: jest.fn().mockResolvedValue({id: 1}),
    isPending: false
  })
}));

// Mock do router
jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn()
  }
}));

// Mock do i18n
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      },
      mutations: {
        retry: false
      }
    }
  });

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe("UnifiedPaymentFlow", () => {
  const defaultProps = {
    entityId: 123,
    entityType: PaymentEntity.Product,
    entityTitle: "Produto Teste",
    entityDescription: "Descrição do produto teste",
    amount: 99.99
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve renderizar corretamente para produto", () => {
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...defaultProps} />
    );

    expect(getByText("Produto Teste")).toBeTruthy();
    expect(getByText("payment.button.confirm")).toBeTruthy();
  });

  it("deve renderizar corretamente para evento", () => {
    const eventProps = {
      ...defaultProps,
      entityType: PaymentEntity.Event,
      entityTitle: "Evento Teste"
    };

    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...eventProps} />
    );

    expect(getByText("Evento Teste")).toBeTruthy();
  });

  it("deve renderizar corretamente para oportunidade", () => {
    const opportunityProps = {
      ...defaultProps,
      entityType: PaymentEntity.Opportunity,
      entityTitle: "Oportunidade Teste"
    };

    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...opportunityProps} />
    );

    expect(getByText("Oportunidade Teste")).toBeTruthy();
  });

  it("deve exibir o valor formatado corretamente", () => {
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...defaultProps} />
    );

    // Verifica se o valor está formatado como moeda brasileira
    expect(getByText(/R\$.*99,99/)).toBeTruthy();
  });

  it("deve chamar onPaymentSuccess quando pagamento for bem-sucedido", async () => {
    const onPaymentSuccess = jest.fn();
    
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow 
        {...defaultProps} 
        onPaymentSuccess={onPaymentSuccess}
      />
    );

    // Simula seleção de método de pagamento PIX
    const pixButton = getByText(/PIX/i);
    fireEvent.press(pixButton);

    // Simula confirmação do pagamento
    const confirmButton = getByText("payment.button.confirm");
    fireEvent.press(confirmButton);

    await waitFor(() => {
      expect(onPaymentSuccess).toHaveBeenCalledWith("payment-123");
    });
  });

  it("deve chamar onPaymentError quando pagamento falhar", async () => {
    // Mock para simular erro
    jest.doMock("@/hooks/api/use-payments", () => ({
      useCreditCards: () => ({
        data: [],
        isLoading: false,
        error: null
      }),
      useCreatePayment: () => ({
        mutateAsync: jest.fn().mockRejectedValue(new Error("Erro de pagamento")),
        isPending: false
      }),
      useCreateCreditCard: () => ({
        mutateAsync: jest.fn().mockResolvedValue({id: 1}),
        isPending: false
      })
    }));

    const onPaymentError = jest.fn();
    
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow 
        {...defaultProps} 
        onPaymentError={onPaymentError}
      />
    );

    // Simula seleção de método de pagamento PIX
    const pixButton = getByText(/PIX/i);
    fireEvent.press(pixButton);

    // Simula confirmação do pagamento
    const confirmButton = getByText("payment.button.confirm");
    fireEvent.press(confirmButton);

    await waitFor(() => {
      expect(onPaymentError).toHaveBeenCalledWith("Erro de pagamento");
    });
  });

  it("deve chamar onCancel quando cancelar", () => {
    const onCancel = jest.fn();
    
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow 
        {...defaultProps} 
        onCancel={onCancel}
      />
    );

    const cancelButton = getByText("common.cancel");
    fireEvent.press(cancelButton);

    expect(onCancel).toHaveBeenCalled();
  });

  it("deve desabilitar botão de confirmação quando nenhum método estiver selecionado", () => {
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...defaultProps} />
    );

    const confirmButton = getByText("payment.button.confirm");
    expect(confirmButton.props.accessibilityState?.disabled).toBe(true);
  });

  it("deve habilitar botão de confirmação quando método PIX estiver selecionado", () => {
    const {getByText} = renderWithQueryClient(
      <UnifiedPaymentFlow {...defaultProps} />
    );

    // Seleciona PIX
    const pixButton = getByText(/PIX/i);
    fireEvent.press(pixButton);

    const confirmButton = getByText("payment.button.confirm");
    expect(confirmButton.props.accessibilityState?.disabled).toBe(false);
  });
});
