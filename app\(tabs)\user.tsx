import React, {use<PERSON><PERSON>back, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {ScrollView, View, Text, RefreshControl} from "react-native";
import {useRouter} from "expo-router";
import OpportunityCard from "../../components/user/opportunity-card";
import UserHeader from "../../components/user/user-header";
import UserTabs from "../../components/user/user-tabs";
import UserProfile from "../../components/user/user-profile";
import {useUserOpportunities} from "../../hooks/api/use-opportunities";
import {useUserBadges} from "../../hooks/api/use-users";
import styles from "../../styles/tabs/user.style";
import Seals from "../../components/user/seals";
import Objectives from "../../components/user/objectives";
import AboutMe from "../(profile)/about-me";

enum UserTabsEnum {
  Opportunities = 1,
  ClaimedBadges = 2,
  AboutMe = 3
}

const User: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(UserTabsEnum.Opportunities);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch user opportunities from API with error handling
  const {
    data: opportunitiesData,
    isLoading: opportunitiesLoading,
    error: opportunitiesError,
    refetch: refetchOpportunities
  } = useUserOpportunities({
    pageSize: 10,
    page: 1
  });

  // Fetch user badges from API with error handling
  const {
    data: badgesData,
    isLoading: badgesLoading,
    error: badgesError,
    refetch: refetchBadges
  } = useUserBadges({
    pageSize: 20,
    page: 1
  });

  const tabs = [
    {title: t("user.opportunities"), id: UserTabsEnum.Opportunities},
    {title: t("user.claimedBadges"), id: UserTabsEnum.ClaimedBadges},
    {title: t("user.aboutMe"), id: UserTabsEnum.AboutMe}
  ];

  const onTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetchOpportunities(), refetchBadges()]);
    } finally {
      setRefreshing(false);
    }
  }, [refetchOpportunities, refetchBadges]);

  // Mock data for fallback when API fails
  const mockOpportunities = useMemo(() => {
    const list = [];
    for (let i = 0; i < 3; i++) {
      list.push({
        id: i + 1,
        title: `Oportunidade de Negócio ${i + 1}`,
        user: {
          id: 1,
          name: "Mozart",
          avatar:
            "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg"
        },
        description:
          "Oportunidade de negócio interessante com potencial de crescimento. Ideal para investidores que buscam diversificar seu portfólio.",
        value: (i + 1) * 1000,
        imageUrl:
          "https://upload.wikimedia.org/wikipedia/commons/0/01/Steinway_Vienna_002.JPG",
        createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    return list;
  }, []);

  const opportunities = useMemo(() => {
    if (opportunitiesLoading) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t("common.loading")}</Text>
        </View>
      );
    }

    // If there's an error and no data, show error with mock data as fallback
    if (
      opportunitiesError &&
      (!opportunitiesData?.data || opportunitiesData.data.length === 0)
    ) {
      return (
        <View>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {t("user.opportunitiesTab.loadError")}
            </Text>
            <Text style={styles.errorSubText}>Mostrando dados de exemplo</Text>
          </View>
          {mockOpportunities.map((opportunity) => (
            <OpportunityCard
              key={opportunity.id}
              id={opportunity.id}
              title={opportunity.title}
              avatarUrl={opportunity.user.avatar || ""}
              userName={opportunity.user.name}
              createdAt={opportunity.createdAt}
              description={opportunity.description}
              value={opportunity.value}
              imageUrl={opportunity.imageUrl}
              onPress={() => {
                router.push(`/(business)/opportunities/${opportunity.id}`);
              }}
            />
          ))}
        </View>
      );
    }

    // If API returned successfully but with no opportunities, show empty message
    if (opportunitiesData?.data && opportunitiesData.data.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {t("user.opportunitiesTab.noOpportunities")}
          </Text>
        </View>
      );
    }

    // Use API data if available, otherwise use mock data
    const dataToUse =
      opportunitiesData?.data && opportunitiesData.data.length > 0
        ? opportunitiesData.data
        : mockOpportunities;

    return dataToUse.map((opportunity) => (
      <OpportunityCard
        key={opportunity.id}
        id={opportunity.id}
        title={opportunity.title}
        avatarUrl={opportunity.user.avatar || ""}
        userName={opportunity.user.name}
        createdAt={opportunity.createdAt}
        description={opportunity.description}
        value={opportunity.value}
        imageUrl={opportunity.imageUrl}
        onPress={() => {
          router.push(`/(business)/opportunities/${opportunity.id}`);
        }}
      />
    ));
  }, [
    opportunitiesData,
    opportunitiesLoading,
    opportunitiesError,
    mockOpportunities,
    t
  ]);

  const seals = useMemo(() => {
    const list = [];
    const sealTypes = ["Ouro", "Prata", "Bronze", "Diamante", "Platina"];
    for (let i = 0; i < 8; i++) {
      list.push({
        id: i + 1,
        title: sealTypes[i % sealTypes.length],
        description: `Selo ${sealTypes[i % sealTypes.length]} conquistado`,
        earnedAt: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        category: "achievement"
      });
    }
    return list;
  }, []);

  const content = useMemo(() => {
    switch (activeTab) {
      case UserTabsEnum.Opportunities:
        return <View style={styles.opportunitiesList}>{opportunities}</View>;
      case UserTabsEnum.ClaimedBadges:
        return (
          <View style={styles.objectivesContainer}>
            <Seals
              badges={badgesData?.data}
              loading={badgesLoading}
              error={badgesError?.message}
              seals={seals} // Fallback for when API data is not available
            />
            <Objectives
              opportunities={opportunitiesData?.data}
              loading={opportunitiesLoading}
              error={opportunitiesError?.message}
            />
          </View>
        );
      case UserTabsEnum.AboutMe:
        return <AboutMe />;
    }
  }, [activeTab]);

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor="#FFFFFF"
        />
      }
    >
      <UserHeader />
      <View style={styles.mainContainer}>
        <UserTabs
          tabs={tabs}
          style={styles.tabsContainer}
          onTabChange={onTabChange}
        />
        <View style={styles.contentContainer}>{content}</View>
      </View>
    </ScrollView>
  );
};
export default User;
