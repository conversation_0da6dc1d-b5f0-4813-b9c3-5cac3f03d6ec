import React, {useMemo, useState, useRef, useCallback} from "react";
import {
  StyleProp,
  Text,
  View,
  ViewStyle,
  Dimensions,
  FlatList,
  TouchableOpacity
} from "react-native";
import {useRouter} from "expo-router";
import PartnerCard from "./partner-card";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";
import styles from "../../styles/components/products/partners.style";
import {usePartners} from "@/hooks/api/use-partners";

export interface PartnersProps {
  style?: StyleProp<ViewStyle>;
}

const Partners: React.FC<PartnersProps> = (props) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  // Buscar parceiros da API
  const {
    data: partnersResponse,
    isLoading,
    error
  } = usePartners({
    page: 1,
    pageSize: 8, // Limitar a 8 parceiros para o carrossel
    status: 1
  });

  // Define the Partner type based on expected API response
  interface Partner {
    id: number;
    name: string;
    logoUrl?: string;
    // add other fields if needed
  }

  const partners = useMemo(() => {
    const data = (partnersResponse?.data ?? []) as Partner[];
    console.log("🏢 [PARTNERS] Partners data:", {
      isLoading,
      error: error?.message,
      partnersCount: data.length,
      partners: data.map((p: Partner) => ({id: p.id, name: p.name}))
    });
    return data;
  }, [partnersResponse, isLoading, error]);

  const screenWidth = Dimensions.get("window").width;
  const itemWidth = 72;
  const gap = 12;
  const containerPadding = 24; // Account for container padding
  const availableWidth = screenWidth - containerPadding;
  const itemsPerPage = Math.floor(availableWidth / (itemWidth + gap));
  const totalPages = Math.ceil(partners.length / itemsPerPage);

  const handleScroll = (event: any) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const pageWidth = itemsPerPage * (itemWidth + gap);
    const page = Math.round(scrollPosition / pageWidth);
    setCurrentPage(Math.max(0, Math.min(page, totalPages - 1)));
  };

  const scrollToPage = useCallback(
    (page: number) => {
      if (flatListRef.current && page >= 0 && page < totalPages) {
        const pageWidth = itemsPerPage * (itemWidth + gap);
        const scrollPosition = page * pageWidth;
        flatListRef.current.scrollToOffset({
          offset: scrollPosition,
          animated: true
        });
        setCurrentPage(page);
      }
    },
    [itemsPerPage, itemWidth, gap, totalPages]
  );

  const handlePrevious = useCallback(() => {
    scrollToPage(currentPage - 1);
  }, [currentPage, scrollToPage]);

  const handleNext = useCallback(() => {
    scrollToPage(currentPage + 1);
  }, [currentPage, scrollToPage]);

  const handleViewAll = useCallback(() => {
    router.push("/(main)/partners");
  }, [router]);

  const handlePartnerPress = useCallback(
    (partnerId: number) => {
      router.push(`/(main)/partners/${partnerId}`);
    },
    [router]
  );

  const renderPaginationDots = () => {
    return (
      <View style={styles.paginationContainer}>
        <TouchableOpacity
          onPress={handlePrevious}
          disabled={currentPage === 0}
          style={[
            styles.arrowButton,
            currentPage === 0 && styles.disabledArrow
          ]}
        >
          <ChevronLeftIcon />
        </TouchableOpacity>
        <View style={styles.dotsContainer}>
          {Array.from({length: totalPages}, (_, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => scrollToPage(index)}
              style={[
                styles.dot,
                currentPage === index ? styles.activeDot : styles.inactiveDot
              ]}
            />
          ))}
        </View>
        <TouchableOpacity
          onPress={handleNext}
          disabled={currentPage === totalPages - 1}
          style={[
            styles.arrowButton,
            currentPage === totalPages - 1 && styles.disabledArrow
          ]}
        >
          <ChevronRightIcon />
        </TouchableOpacity>
      </View>
    );
  };

  // Se não há parceiros ou está carregando, não renderizar a seção
  if (isLoading || error || partners.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, props.style]}>
      <View style={styles.header}>
        <Text style={styles.title}>Parceiros e benefícios</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAllText}>Ver todos</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.content}>
        <FlatList
          ref={flatListRef}
          data={partners}
          contentContainerStyle={styles.flatListContainer}
          renderItem={({item}) => (
            <PartnerCard
              name={item.name}
              logo={
                item.logoUrl ||
                "https://via.placeholder.com/72x72/4A90E2/FFFFFF?text=" +
                  item.name.charAt(0)
              }
              onPress={() => handlePartnerPress(item.id)}
            />
          )}
          keyExtractor={(item) => item.id.toString()}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          scrollEnabled={true}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          pagingEnabled={false}
          snapToInterval={itemsPerPage * (itemWidth + gap)}
          snapToAlignment="start"
          decelerationRate="fast"
          bounces={false}
        />
        {totalPages > 1 && renderPaginationDots()}
      </View>
    </View>
  );
};

export default Partners;
