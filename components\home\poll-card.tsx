import React, {useCallback, useMemo, useState, useEffect} from "react";
import {View, Text, TouchableOpacity, ActivityIndicator} from "react-native";
import Avatar from "@/components/user/avatar";
import styles from "@/styles/components/home/<USER>";
import {PollUtils} from "@/services/api/polls/polls.service";
import {Poll} from "@/models/api/polls.models";
import {useVoteOnPoll, usePolls} from "@/hooks/api/use-polls";
import ClockIcon from "@/components/icons/clock-icon";

export interface PollCardProps {
  poll: Poll;
  onUserPress?: (userId: number) => void;
  isLoading?: boolean;
}

const PollCard: React.FC<PollCardProps> = ({
  poll,
  onUserPress,
  isLoading = false
}) => {
  const [timeRemaining, setTimeRemaining] = useState<string>("");
  const [selectedOptionId, setSelectedOptionId] = useState<number | null>(null);
  const [hasVoted, setHasVoted] = useState<boolean>(false);
  const voteOnPollMutation = useVoteOnPoll();

  // Buscar enquetes da API para obter as opções reais
  const {data: pollsData} = usePolls({page: 1, pageSize: 10});

  // Encontrar a enquete atual na lista de enquetes da API
  const pollWithOptions = pollsData?.data?.find((p) => p.id === poll?.id);

  // Usar opções da API se disponíveis, senão usar as do feed
  const pollOptions = pollWithOptions?.options || poll?.options;

  // Log para debug das opções carregadas
  useEffect(() => {
    if (pollWithOptions && poll?.id) {
      console.log(`🔍 [POLL-CARD] Opções carregadas para enquete ${poll.id}:`, {
        pollId: poll.id,
        optionsCount: pollOptions?.length || 0,
        options: pollOptions?.map((opt) => ({
          id: opt?.id,
          text: opt?.option || opt?.text,
          votes: opt?.votes || opt?.voteCount || 0
        }))
      });
    }
  }, [pollWithOptions, poll?.id, pollOptions]);

  // Como não temos informação de voto do usuário na API,
  // vamos manter apenas o estado local após votar
  // O estado será reiniciado apenas quando o componente for remontado

  // Atualizar timer a cada segundo
  useEffect(() => {
    if (!poll) return;

    const updateTimer = () => {
      setTimeRemaining(PollUtils.formatTimeRemaining(poll));
    };

    updateTimer(); // Atualização inicial
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [poll]);

  const formattedDate = useMemo(() => {
    if (!poll?.createdAt) return "Data não disponível";

    const date = new Date(poll.createdAt);
    return (
      date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) +
      ", às " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      }) +
      " PM"
    );
  }, [poll?.createdAt]);

  const isExpired = useMemo(() => {
    return PollUtils.isExpired(poll);
  }, [poll]);

  const canVote = useMemo(() => {
    return !hasVoted && !isExpired && !voteOnPollMutation.isPending;
  }, [hasVoted, isExpired, voteOnPollMutation.isPending]);

  const handleUserPress = useCallback(() => {
    if (poll?.user?.id) {
      onUserPress?.(poll.user.id);
    }
  }, [poll?.user?.id, onUserPress]);

  const handleVote = useCallback(
    (optionId: number) => {
      if (!canVote || !optionId || !poll?.id) return;

      // Atualizar estado local imediatamente para feedback visual
      setSelectedOptionId(optionId);
      setHasVoted(true);

      voteOnPollMutation.mutate(
        {
          pollId: poll.id,
          optionId
        },
        {
          onSuccess: () => {
            // Manter estado local - não há sincronização com API
            // pois a API não retorna informação de voto do usuário
            console.log(
              "✅ [POLL-CARD] Voto enviado com sucesso, mantendo estado local"
            );
          },
          onError: () => {
            // Reverter estado local em caso de erro
            console.log(
              "❌ [POLL-CARD] Erro ao votar, revertendo estado local"
            );
            setSelectedOptionId(null);
            setHasVoted(false);
          }
        }
      );
    },
    [canVote, poll?.id, voteOnPollMutation]
  );

  // Verificação de segurança após todos os hooks
  if (!poll) {
    return (
      <View style={[styles.pollCard, styles.loadingCard]}>
        <Text style={styles.pollOptionText}>Enquete não encontrada</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={[styles.pollCard, styles.loadingCard]}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  return (
    <View style={styles.pollCard}>
      {/* Header */}
      <View style={styles.pollHeader}>
        <TouchableOpacity
          onPress={poll?.user?.id ? handleUserPress : undefined}
        >
          <Avatar
            user={poll?.user}
            name={poll?.user?.name}
            size={40}
            borderSize={2}
          />
        </TouchableOpacity>
        <View style={styles.pollContent}>
          <View style={styles.pollUserInfo}>
            <Text style={styles.userName}>
              {poll?.user?.name || "Usuário Anônimo"}
            </Text>
            <Text style={styles.pollActionText}>publicou uma nova enquete</Text>
          </View>
          <View style={styles.pollDateInfo}>
            <Text style={styles.pollDatePrefix}>em</Text>
            <Text style={styles.pollDate}>{formattedDate}</Text>
          </View>
        </View>
      </View>

      {/* Pergunta da enquete */}
      <Text style={styles.pollQuestion}>
        {poll?.title || poll?.description}
      </Text>

      {/* Opções da enquete */}
      <View style={styles.pollOptions}>
        {pollOptions && pollOptions.length > 0 ? (
          pollOptions
            .filter((option) => option)
            .map((option, index) => {
              if (!option) return null;

              const isSelected = selectedOptionId === option?.id;

              // Calcular total de votos somando todas as opções
              const totalVotes =
                pollOptions?.reduce(
                  (sum, opt) => sum + (opt?.votes || opt?.voteCount || 0),
                  0
                ) || 0;

              const percentage = PollUtils.getOptionPercentage(
                option,
                totalVotes
              );

              return (
                <TouchableOpacity
                  key={option?.id || `option-${index}`}
                  style={[
                    styles.pollOption,
                    isSelected && styles.pollOptionSelected,
                    !canVote && styles.pollOptionDisabled
                  ]}
                  onPress={() => option.id && handleVote(option.id)}
                  disabled={!canVote}
                >
                  <Text
                    style={[
                      styles.pollOptionText,
                      isSelected && styles.pollOptionTextSelected
                    ]}
                  >
                    {option?.option || option?.text || `Opção ${index + 1}`}
                  </Text>

                  <View style={styles.pollOptionRight}>
                    {hasVoted && (
                      <Text style={styles.pollOptionPercentage}>
                        {percentage}%
                      </Text>
                    )}
                    <View
                      style={[
                        styles.pollOptionIndicator,
                        isSelected && styles.pollOptionIndicatorSelected,
                        voteOnPollMutation.isPending &&
                          isSelected &&
                          styles.pollOptionIndicatorLoading
                      ]}
                    >
                      {voteOnPollMutation.isPending && isSelected ? (
                        <ActivityIndicator size="small" color="#FFFFFF" />
                      ) : null}
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })
        ) : (
          <Text style={styles.pollOptionText}>Nenhuma opção disponível</Text>
        )}
      </View>

      {/* Timer de expiração */}
      {!isExpired && poll?.endDate && (
        <View style={styles.pollTimer}>
          <ClockIcon width={16} height={16} stroke="#FCFCFD" />
          <Text style={styles.pollTimerText}>A enquete expira em:</Text>
          <Text style={styles.pollTimerValue}>{timeRemaining}</Text>
        </View>
      )}

      {/* Status de expirada */}
      {isExpired && (
        <View style={styles.pollExpired}>
          <Text style={styles.pollExpiredText}>Esta enquete expirou</Text>
        </View>
      )}
    </View>
  );
};

export default PollCard;
