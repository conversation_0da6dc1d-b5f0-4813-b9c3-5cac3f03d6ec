import React, {useCallback, useMemo, useState, useEffect} from "react";
import {View, Text, TouchableOpacity, ActivityIndicator} from "react-native";
import Avatar from "@/components/user/avatar";
import styles from "@/styles/components/home/<USER>";
import {PollUtils} from "@/services/api/polls/polls.service";
import {Poll} from "@/models/api/polls.models";
import {useVoteOnPoll} from "@/hooks/api/use-polls";
import ClockIcon from "@/components/icons/clock-icon";

export interface PollCardProps {
  poll: Poll;
  onUserPress?: (userId: number) => void;
  isLoading?: boolean;
}

const PollCard: React.FC<PollCardProps> = ({
  poll,
  onUserPress,
  isLoading = false
}) => {
  const [timeRemaining, setTimeRemaining] = useState<string>("");
  const voteOnPollMutation = useVoteOnPoll();

  // Atualizar timer a cada segundo
  useEffect(() => {
    if (!poll) return;

    const updateTimer = () => {
      setTimeRemaining(PollUtils.formatTimeRemaining(poll));
    };

    updateTimer(); // Atualização inicial
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [poll]);

  const formattedDate = useMemo(() => {
    if (!poll?.createdAt) return "Data não disponível";

    const date = new Date(poll.createdAt);
    return (
      date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) +
      ", às " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      }) +
      " PM"
    );
  }, [poll?.createdAt]);

  const hasUserVoted = useMemo(() => {
    return PollUtils.hasUserVoted(poll);
  }, [poll]);

  const userVotedOption = useMemo(() => {
    return PollUtils.getUserVotedOption(poll);
  }, [poll]);

  const isExpired = useMemo(() => {
    return PollUtils.isExpired(poll);
  }, [poll]);

  const canVote = useMemo(() => {
    return !hasUserVoted && !isExpired && !voteOnPollMutation.isPending;
  }, [hasUserVoted, isExpired, voteOnPollMutation.isPending]);

  const handleUserPress = useCallback(() => {
    if (poll.user?.id) {
      onUserPress?.(poll.user.id);
    }
  }, [poll.user?.id, onUserPress]);

  const handleVote = useCallback(
    (optionId: number) => {
      if (!canVote || !optionId || !poll?.id) return;

      voteOnPollMutation.mutate({
        pollId: poll.id,
        optionId
      });
    },
    [canVote, poll?.id, voteOnPollMutation]
  );

  // Verificação de segurança após todos os hooks
  if (!poll) {
    return (
      <View style={[styles.pollCard, styles.loadingCard]}>
        <Text style={styles.pollOptionText}>Enquete não encontrada</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={[styles.pollCard, styles.loadingCard]}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  return (
    <View style={styles.pollCard}>
      {/* Header */}
      <View style={styles.pollHeader}>
        <TouchableOpacity onPress={poll.user?.id ? handleUserPress : undefined}>
          <Avatar
            user={poll.user}
            name={poll.user?.name}
            size={40}
            borderSize={2}
          />
        </TouchableOpacity>
        <View style={styles.pollContent}>
          <View style={styles.pollUserInfo}>
            <Text style={styles.userName}>{poll.user?.name || "Usuário"}</Text>
            <Text style={styles.pollActionText}>publicou uma nova enquete</Text>
          </View>
          <View style={styles.pollDateInfo}>
            <Text style={styles.pollDatePrefix}>em</Text>
            <Text style={styles.pollDate}>{formattedDate}</Text>
          </View>
        </View>
      </View>

      {/* Pergunta da enquete */}
      <Text style={styles.pollQuestion}>
        {poll.question || poll.description}
      </Text>

      {/* Opções da enquete */}
      <View style={styles.pollOptions}>
        {poll.options && poll.options.length > 0 ? (
          poll.options
            .filter((option) => option)
            .map((option, index) => {
              if (!option) return null;

              const isSelected = userVotedOption?.id === option?.id;
              const percentage = PollUtils.getOptionPercentage(
                option,
                poll.totalVotes || 0
              );

              return (
                <TouchableOpacity
                  key={option?.id || `option-${index}`}
                  style={[
                    styles.pollOption,
                    isSelected && styles.pollOptionSelected,
                    !canVote && styles.pollOptionDisabled
                  ]}
                  onPress={() => option.id && handleVote(option.id)}
                  disabled={!canVote}
                >
                  <Text
                    style={[
                      styles.pollOptionText,
                      isSelected && styles.pollOptionTextSelected
                    ]}
                  >
                    {option?.text || `Opção ${index + 1}`}
                  </Text>

                  <View style={styles.pollOptionRight}>
                    {hasUserVoted && (
                      <Text style={styles.pollOptionPercentage}>
                        {percentage}%
                      </Text>
                    )}
                    <View
                      style={[
                        styles.pollOptionIndicator,
                        isSelected && styles.pollOptionIndicatorSelected,
                        voteOnPollMutation.isPending &&
                          styles.pollOptionIndicatorLoading
                      ]}
                    >
                      {voteOnPollMutation.isPending && isSelected ? (
                        <ActivityIndicator size="small" color="#FFFFFF" />
                      ) : null}
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })
        ) : (
          <Text style={styles.pollOptionText}>Nenhuma opção disponível</Text>
        )}
      </View>

      {/* Timer de expiração */}
      {!isExpired && poll.expiresAt && (
        <View style={styles.pollTimer}>
          <ClockIcon width={16} height={16} stroke="#FCFCFD" />
          <Text style={styles.pollTimerText}>A enquete expira em:</Text>
          <Text style={styles.pollTimerValue}>{timeRemaining}</Text>
        </View>
      )}

      {/* Status de expirada */}
      {isExpired && (
        <View style={styles.pollExpired}>
          <Text style={styles.pollExpiredText}>Esta enquete expirou</Text>
        </View>
      )}
    </View>
  );
};

export default PollCard;
