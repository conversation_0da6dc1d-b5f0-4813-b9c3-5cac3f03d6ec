import React, {useState, useMemo, useCallback} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/notifications/notifications.style";
import NotificationItem from "../../components/notifications/notification-item";
import {
  useNotifications,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead
} from "../../hooks/api/use-notifications";
import {
  Notification as ApiNotification,
  NotificationStatus,
  NotificationType,
  NotificationPriority,
  NotificationChannel
} from "../../models/api/notifications.models";
import stylesConstants from "../../styles/styles-constants";

type NotificationFilter = "all" | "read" | "unread";

const Notifications: React.FC = () => {
  const {t} = useTranslation();
  const [activeFilter, setActiveFilter] = useState<NotificationFilter>("all");

  // Hooks para buscar notificações
  const {
    data: notificationsResponse,
    isLoading,
    error,
    refetch
  } = useNotifications({
    page: 1,
    pageSize: 50
  });

  // Hook para marcar notificação como lida
  const markAsReadMutation = useMarkNotificationAsRead();

  // Hook para marcar todas como lidas
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();

  // Função para mapear tipos numéricos para enums
  const mapNotificationType = (typeNumber: number): NotificationType => {
    switch (typeNumber) {
      case 1:
        return NotificationType.EVENT;
      case 2:
        return NotificationType.ANNOUNCEMENT;
      case 3:
        return NotificationType.SYSTEM;
      case 4:
        return NotificationType.PROMOTION;
      case 5:
        return NotificationType.REMINDER;
      case 6:
        return NotificationType.CHAT;
      case 7:
        return NotificationType.MESSAGE;
      case 8:
        return NotificationType.PRODUCT;
      default:
        return NotificationType.GENERAL;
    }
  };

  const mapNotificationStatus = (statusNumber: number): NotificationStatus => {
    switch (statusNumber) {
      case 1:
        return NotificationStatus.UNREAD;
      case 2:
        return NotificationStatus.read;
      case 3:
        return NotificationStatus.ARCHIVED;
      case 4:
        return NotificationStatus.UNREAD; // Assumindo que 4 também é UNREAD
      default:
        return NotificationStatus.UNREAD;
    }
  };

  // Mapear os dados da API para extrair os objetos notification
  const notifications = useMemo(() => {
    const rawData = notificationsResponse?.data || [];
    console.log(
      "🔍 [NOTIFICATIONS] Raw data from API:",
      JSON.stringify(rawData, null, 2)
    );

    // A API retorna um wrapper com { id, notification, notificationId, sentAt, userId }
    // Usamos o 'id' do wrapper (relação usuário-notificação) para operações de leitura
    const mappedNotifications = rawData.map((item: any, index: number) => {
      console.log(
        `📦 [NOTIFICATIONS] Item ${index}:`,
        JSON.stringify(item, null, 2)
      );

      if (item.notification) {
        console.log(
          `📋 [NOTIFICATIONS] Notification object inside item ${index}:`,
          JSON.stringify(item.notification, null, 2)
        );

        const notification = item.notification;
        const mappedNotification = {
          id:
            item.id?.toString() ||
            item.notificationId?.toString() ||
            notification.id?.toString(),
          userId:
            item.userId?.toString() || notification.userId?.toString() || "",
          type: mapNotificationType(notification.type),
          priority: NotificationPriority.NORMAL, // Default priority
          status: mapNotificationStatus(notification.status),
          title: notification.title || "",
          message: notification.body || notification.message || "", // API usa 'body' em vez de 'message'
          data: notification.data || {},
          imageUrl: notification.imageUrl,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText,
          channels: [NotificationChannel.IN_APP], // Default channel
          scheduledAt: notification.scheduledAt,
          sentAt: item.sentAt || notification.sentAt,
          readAt: notification.readAt,
          expiresAt: notification.expiresAt,
          createdAt: notification.createdAt || item.sentAt,
          updatedAt:
            notification.updatedAt || notification.createdAt || item.sentAt
        };

        console.log(
          `✅ [NOTIFICATIONS] Mapped notification ${index}:`,
          JSON.stringify(mappedNotification, null, 2)
        );
        return mappedNotification;
      }

      // Fallback caso não tenha a estrutura esperada
      console.log(
        `⚠️ [NOTIFICATIONS] Item ${index} doesn't have notification property, using as is`
      );
      return item;
    });

    console.log(
      "🎯 [NOTIFICATIONS] Final mapped notifications:",
      JSON.stringify(mappedNotifications, null, 2)
    );
    return mappedNotifications;
  }, [notificationsResponse?.data]);

  // Função para lidar com ação da notificação
  const handleNotificationAction = useCallback(
    (notification: ApiNotification) => {
      // Marcar como lida se não estiver
      if (notification.status === NotificationStatus.UNREAD) {
        markAsReadMutation.mutate(notification.id);
      }

      // Navegar para URL da ação se existir
      if (notification.actionUrl) {
        // Implementar navegação baseada na URL
        console.log("Navegando para:", notification.actionUrl);
      }
    },
    [markAsReadMutation]
  );

  // Função para marcar notificação como lida ao tocar
  const handleNotificationPress = useCallback(
    (notification: ApiNotification) => {
      if (notification.status === NotificationStatus.UNREAD) {
        markAsReadMutation.mutate(notification.id);
      }
    },
    [markAsReadMutation]
  );

  const filteredNotifications = useMemo(() => {
    switch (activeFilter) {
      case "read":
        return notifications.filter(
          (n) => n.status === NotificationStatus.READ
        );
      case "unread":
        return notifications.filter(
          (n) => n.status === NotificationStatus.UNREAD
        );
      default:
        return notifications;
    }
  }, [notifications, activeFilter]);

  const renderFilterTabs = () => (
    <View style={styles.tabContainer}>
      <View style={styles.tabList}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "all" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("all")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "all" && styles.tabButtonTextActive
            ]}
          >
            Todas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "read" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("read")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "read" && styles.tabButtonTextActive
            ]}
          >
            Lidas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "unread" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("unread")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "unread" && styles.tabButtonTextActive
            ]}
          >
            Não lidas
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <ScreenWithHeader
      screenTitle={t("notifications.title", "Notificações")}
      backButton
    >
      <View style={styles.container}>
        {renderFilterTabs()}

        {/* Loading state */}
        {isLoading && (
          <View style={{padding: 20, alignItems: "center"}}>
            <ActivityIndicator
              size="large"
              color={stylesConstants.colors.primary500}
            />
            <Text
              style={{
                marginTop: 10,
                color: stylesConstants.colors.textSecondary
              }}
            >
              {t("notifications.loading", "Carregando notificações...")}
            </Text>
          </View>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <View style={{padding: 20, alignItems: "center"}}>
            <Text
              style={{
                color: stylesConstants.colors.error500,
                textAlign: "center"
              }}
            >
              {t(
                "notifications.error",
                "Erro ao carregar notificações. Toque para tentar novamente."
              )}
            </Text>
            <TouchableOpacity
              onPress={() => refetch()}
              style={{
                marginTop: 10,
                padding: 10,
                backgroundColor: stylesConstants.colors.primary500,
                borderRadius: 8
              }}
            >
              <Text style={{color: "white"}}>
                {t("notifications.retry", "Tentar novamente")}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Notifications list */}
        {!isLoading && !error && (
          <ScrollView style={styles.notificationsList}>
            {filteredNotifications.map((notification, index) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onPress={handleNotificationPress}
                onActionPress={handleNotificationAction}
                showSeparator={index < filteredNotifications.length - 1}
              />
            ))}

            {filteredNotifications.length === 0 && (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateText}>
                  {(() => {
                    if (activeFilter === "all") {
                      return t("notifications.empty", "Nenhuma notificação");
                    } else if (activeFilter === "read") {
                      return t(
                        "notifications.emptyRead",
                        "Nenhuma notificação lida"
                      );
                    } else {
                      return t(
                        "notifications.emptyUnread",
                        "Nenhuma notificação não lida"
                      );
                    }
                  })()}
                </Text>
              </View>
            )}
          </ScrollView>
        )}
      </View>
    </ScreenWithHeader>
  );
};

export default Notifications;
