import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import * as LocalAuthentication from "expo-local-authentication";
import * as SecureStore from "expo-secure-store";
import {useRouter} from "expo-router";
import {useAuth} from "@/contexts/AuthContext";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import BiometricService from "@/services/biometric.service";
import {tokenManager} from "@/services/api/auth/token-manager";
import {ApiLogger} from "@/services/api/base/api-logger";

// Chave para armazenar dados de sessão para biometria (mesma do AuthContext)
const BIOMETRIC_SESSION_KEY = "session_data";

export interface StartupBiometricAuthState {
  isChecking: boolean;
  shouldPromptBiometric: boolean;
  isAuthenticating: boolean;
  hasCompletedCheck: boolean;
}

/**
 * Hook para gerenciar autenticação biométrica automática no startup do app
 */
export const useStartupBiometricAuth = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const auth = useAuth();
  const errorActions = useErrorMessage();

  const [state, setState] = useState<StartupBiometricAuthState>({
    isChecking: false,
    shouldPromptBiometric: false,
    isAuthenticating: false,
    hasCompletedCheck: false
  });

  /**
   * Verificar se há dados de sessão biométrica salvos
   */
  const checkBiometricSessionData = useCallback(async (): Promise<boolean> => {
    try {
      const savedSessionData = await SecureStore.getItemAsync(
        BIOMETRIC_SESSION_KEY
      );

      if (!savedSessionData) {
        return false;
      }

      // Verificar se os dados são válidos
      const sessionData = JSON.parse(savedSessionData);
      if (!sessionData.token || !sessionData.user) {
        return false;
      }

      // Verificar se o token não expirou
      const expiresAt = new Date(sessionData.expiresAt);
      if (expiresAt <= new Date()) {
        // Token expirado, remover dados
        await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
        return false;
      }

      return true;
    } catch (error) {
      ApiLogger.error(
        "Erro ao verificar dados de sessão biométrica",
        error as Error
      );
      return false;
    }
  }, []);

  /**
   * Verificar se o dispositivo suporta biometria
   */
  const checkBiometricAvailability = useCallback(async (): Promise<boolean> => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      return hasHardware && isEnrolled;
    } catch (error) {
      ApiLogger.error(
        "Erro ao verificar disponibilidade biométrica",
        error as Error
      );
      return false;
    }
  }, []);

  /**
   * Realizar autenticação biométrica e restaurar sessão
   */
  const authenticateAndRestoreSession =
    useCallback(async (): Promise<boolean> => {
      try {
        setState((prev) => ({...prev, isAuthenticating: true}));

        // Configurar opções de autenticação biométrica
        const biometricConfig = BiometricService.createBiometricConfig(t);

        // Realizar autenticação biométrica
        const authResult = await LocalAuthentication.authenticateAsync(
          biometricConfig
        );

        if (!authResult.success) {
          if (authResult.error === "user_cancel") {
            ApiLogger.info("Autenticação biométrica cancelada pelo usuário");
          } else {
            ApiLogger.warn(
              "Falha na autenticação biométrica",
              authResult.error
            );
          }
          return false;
        }

        // Autenticação bem-sucedida, restaurar sessão
        const savedSessionData = await SecureStore.getItemAsync(
          BIOMETRIC_SESSION_KEY
        );

        if (!savedSessionData) {
          throw new Error("Dados de sessão não encontrados após autenticação");
        }

        const sessionData = JSON.parse(savedSessionData);

        // Configurar token manager com os dados salvos
        const tokenData = {
          accessToken: sessionData.token,
          refreshToken: sessionData.refreshToken,
          tokenType: "Bearer",
          expiresIn: Math.floor(
            (new Date(sessionData.expiresAt).getTime() - Date.now()) / 1000
          ),
          expiresAt: new Date(sessionData.expiresAt)
        };

        await tokenManager.setTokenData(tokenData);

        // Atualizar contexto de autenticação
        if (auth?.refreshUser) {
          await auth.refreshUser();
        }

        ApiLogger.info("Sessão restaurada com sucesso via biometria");
        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Erro na autenticação biométrica";

        ApiLogger.error(
          "Erro na autenticação biométrica de startup",
          error as Error
        );

        errorActions.emitError({
          title: t("login.biometricErrorTitle", "Erro na Biometria"),
          description: errorMessage,
          errorType: ErrorType.Error
        });

        return false;
      } finally {
        setState((prev) => ({...prev, isAuthenticating: false}));
      }
    }, [t, auth, errorActions]);

  /**
   * Verificar se deve solicitar autenticação biométrica no startup
   */
  const checkShouldPromptBiometric = useCallback(async () => {
    if (state.hasCompletedCheck || auth?.isAuthenticated || state.isChecking) {
      return;
    }

    setState((prev) => ({...prev, isChecking: true}));

    try {
      // Verificar se há dados de sessão salvos
      const hasSessionData = await checkBiometricSessionData();

      if (!hasSessionData) {
        setState((prev) => ({
          ...prev,
          isChecking: false,
          hasCompletedCheck: true,
          shouldPromptBiometric: false
        }));
        return;
      }

      // Verificar se dispositivo suporta biometria
      const hasBiometric = await checkBiometricAvailability();

      if (!hasBiometric) {
        setState((prev) => ({
          ...prev,
          isChecking: false,
          hasCompletedCheck: true,
          shouldPromptBiometric: false
        }));
        return;
      }

      // Tudo OK, deve solicitar biometria
      setState((prev) => ({
        ...prev,
        isChecking: false,
        shouldPromptBiometric: true
      }));
    } catch (error) {
      ApiLogger.error(
        "Erro ao verificar necessidade de biometria",
        error as Error
      );
      setState((prev) => ({
        ...prev,
        isChecking: false,
        hasCompletedCheck: true,
        shouldPromptBiometric: false
      }));
    }
  }, [
    state.hasCompletedCheck,
    auth?.isAuthenticated,
    checkBiometricSessionData,
    checkBiometricAvailability
  ]);

  /**
   * Executar autenticação biométrica
   */
  const promptBiometricAuth = useCallback(async () => {
    const success = await authenticateAndRestoreSession();

    setState((prev) => ({
      ...prev,
      shouldPromptBiometric: false,
      hasCompletedCheck: true
    }));

    if (success) {
      // Redirecionar para home será feito automaticamente pelo AuthContext
      ApiLogger.info("Redirecionamento automático para home após biometria");
    } else {
      // Redirecionar para login
      router.replace("/(auth)/login");
    }
  }, [authenticateAndRestoreSession, router]);

  /**
   * Pular autenticação biométrica e ir para login
   */
  const skipBiometricAuth = useCallback(() => {
    setState((prev) => ({
      ...prev,
      shouldPromptBiometric: false,
      hasCompletedCheck: true
    }));

    router.replace("/(auth)/login");
  }, [router]);

  return {
    isChecking: state.isChecking || false,
    shouldPromptBiometric: state.shouldPromptBiometric || false,
    isAuthenticating: state.isAuthenticating || false,
    hasCompletedCheck: state.hasCompletedCheck || false,
    checkShouldPromptBiometric,
    promptBiometricAuth,
    skipBiometricAuth
  };
};

export default useStartupBiometricAuth;
