import React, {useCallback, useMemo, useState, useEffect} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import Svg, {Path} from "react-native-svg";
import Avatar from "@/components/user/avatar";
import Button from "@/components/button";
import styles from "@/styles/components/home/<USER>";
import {SafeOpportunityViewModel} from "@/services/api/opportunities/opportunities.service";
import {
  getOpportunityImageUrl,
  isValidImageUrl
} from "@/utils/opportunity-image";
import {apiClient} from "@/services/api/base/api-client";
import {firstValueFrom} from "rxjs";

export interface PublicationCardProps {
  opportunity: SafeOpportunityViewModel;
  onInterestPress?: (opportunityId: number) => void;
  onUserPress?: (userId: number) => void;
  isLoading?: boolean;
}

interface PollComponentProps {
  pollData: {
    question: string;
    options: string[];
  };
  onVote?: (option: string) => void;
}

const PollComponent: React.FC<PollComponentProps> = ({pollData, onVote}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const handleVote = useCallback(
    (option: string) => {
      setSelectedOption(option);
      onVote?.(option);
    },
    [onVote]
  );

  return (
    <View style={styles.pollContainer}>
      <Text style={styles.pollQuestion}>{pollData.question}</Text>
      {pollData.options.map((option, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.pollOption,
            selectedOption === option && styles.pollOptionSelected
          ]}
          onPress={() => handleVote(option)}
        >
          <Text style={styles.pollOptionText}>{option}</Text>
          <View
            style={[
              styles.pollOptionIndicator,
              selectedOption === option && styles.pollOptionIndicatorSelected
            ]}
          />
        </TouchableOpacity>
      ))}
      <View style={styles.pollTimer}>
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <Path
            d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
            stroke="#FFFFFF"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Svg>
        <Text style={styles.pollTimerText}>A enquete expira em:</Text>
        <Text style={styles.pollTimerValue}>08h45m36s</Text>
      </View>
    </View>
  );
};

const PublicationCard: React.FC<PublicationCardProps> = ({
  opportunity,
  onInterestPress,
  onUserPress,
  isLoading = false
}) => {
  const {t} = useTranslation();
  const [imageUri, setImageUri] = useState<string | undefined>(undefined);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [hasImageError, setHasImageError] = useState(false);

  const formattedDate = useMemo(() => {
    const date = new Date(opportunity.createdAt);
    return (
      date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) +
      ", às " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      })
    );
  }, [opportunity.createdAt]);

  const formattedValue = useMemo(() => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(opportunity.value / 100);
  }, [opportunity.value]);

  const handleInterestPress = useCallback(() => {
    onInterestPress?.(opportunity.id);
  }, [opportunity.id, onInterestPress]);

  const handleUserPress = useCallback(() => {
    onUserPress?.(opportunity.user.id);
  }, [opportunity.user.id, onUserPress]);

  // Determine the image URL to use
  const opportunityImageUrl = useMemo(() => {
    return getOpportunityImageUrl({
      imageUrl: opportunity.imageUrl,
      imageId: opportunity.imageId
    });
  }, [opportunity.imageUrl, opportunity.imageId]);

  // Function to fetch image with auth headers
  const fetchImageWithAuth = async (imageUrl: string) => {
    try {
      setIsImageLoading(true);
      setHasImageError(false);

      // Extract the endpoint from the full URL
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl || !imageUrl.startsWith(baseUrl)) {
        // If it's not our API URL, use it directly
        setImageUri(imageUrl);
        return;
      }

      const endpoint = imageUrl.replace(baseUrl, "");

      // Use apiClient to fetch with auth headers
      const response = await firstValueFrom(
        apiClient.request<ArrayBuffer>("GET", endpoint, undefined, {
          responseType: "arraybuffer"
        })
      );

      // Convert ArrayBuffer to base64
      const base64 = btoa(
        new Uint8Array(response).reduce(
          (data, byte) => data + String.fromCharCode(byte),
          ""
        )
      );

      // Create data URI
      const dataUri = `data:image/webp;base64,${base64}`;
      setImageUri(dataUri);
    } catch (error) {
      setHasImageError(true);
      // Fallback to direct URL
      setImageUri(imageUrl);
    } finally {
      setIsImageLoading(false);
    }
  };

  // Effect to load image when opportunityImageUrl changes
  useEffect(() => {
    if (opportunityImageUrl && isValidImageUrl(opportunityImageUrl)) {
      fetchImageWithAuth(opportunityImageUrl);
    } else {
      setImageUri(undefined);
    }
  }, [opportunityImageUrl]);

  if (isLoading) {
    return (
      <View style={[styles.publicationCard, styles.loadingCard]}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  return (
    <View style={styles.publicationCard}>
      <View style={styles.publicationHeader}>
        <TouchableOpacity onPress={handleUserPress}>
          <Avatar
            url={opportunity.user?.avatar}
            name={opportunity.user?.name}
            size={40}
            borderSize={2}
          />
        </TouchableOpacity>
        <View style={styles.publicationContent}>
          <Text style={styles.publicationText}>
            <Text style={styles.userName}>
              {opportunity.user?.name || "Usuário"}
            </Text>
            {" publicou uma nova oportunidade de negócio em " + formattedDate}
          </Text>
        </View>
      </View>

      <Text style={styles.publicationDescription}>
        {opportunity.description}
      </Text>

      {imageUri ? (
        <Image
          source={{uri: imageUri}}
          style={styles.publicationImg}
          resizeMode="cover"
        />
      ) : isImageLoading ? (
        <View style={[styles.publicationImg, styles.imagePlaceholder]}>
          <ActivityIndicator size="small" color="#666" />
        </View>
      ) : opportunityImageUrl ? (
        <View style={[styles.publicationImg, styles.imagePlaceholder]}>
          <Text style={styles.placeholderText}>📷</Text>
        </View>
      ) : null}

      <View style={styles.divider} />

      <View style={styles.publicationActions}>
        <View style={styles.valueContainer}>
          <Text style={styles.publicationText}>Valor de Investimento</Text>
          <Text style={styles.publicationTextClock}>{formattedValue}</Text>
        </View>
        <Button
          text="Tenho interesse"
          onPress={handleInterestPress}
          style={styles.interestButton}
          textStyle={styles.interestButtonText}
        />
      </View>
    </View>
  );
};

export default PublicationCard;
