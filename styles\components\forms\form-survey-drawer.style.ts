import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-end"
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)"
  },
  backdropTouchable: {
    flex: 1
  },
  drawer: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: 34,
    minHeight: 280
  },
  handleBar: {
    width: 44,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    alignSelf: "center",
    marginTop: 12,
    marginBottom: 24
  },
  content: {
    paddingHorizontal: 24,
    gap: 16
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 24
  },
  description: {
    fontSize: 14,
    fontWeight: "400",
    color: "rgba(223, 233, 240, 0.8)",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 8
  },
  formTitleContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8
  },
  formTitle: {
    fontSize: 14,
    fontWeight: "500",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 20
  },
  buttonsContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: 8
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center"
  },
  noButton: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)"
  },
  yesButton: {
    backgroundColor: stylesConstants.colors.brand.primary
  },
  noButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans
  },
  yesButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans
  }
});
