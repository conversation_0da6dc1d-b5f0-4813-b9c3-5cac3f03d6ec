/**
 * Search hooks for ClubM
 * Provides React Query hooks for comprehensive search functionality
 */

import {
  useQuery,
  useInfiniteQuery,
  UseQueryOptions,
  UseInfiniteQueryOptions
} from "@tanstack/react-query";
import {BaseApiError} from "@/services/api/base/api-errors";
import {
  SearchService,
  SearchResult,
  GlobalSearchParams,
  GlobalSearchResponse,
  EntitySearchParams
} from "@/services/api/search/search.service";
import {PaginationResponse} from "@/models/api/common.models";

// Query keys for caching
export const searchKeys = {
  all: ["search"] as const,
  global: (params: GlobalSearchParams) =>
    [...searchKeys.all, "global", params] as const,
  members: (params: EntitySearchParams) =>
    [...searchKeys.all, "members", params] as const,
  events: (params: EntitySearchParams) =>
    [...searchKeys.all, "events", params] as const,
  products: (params: EntitySearchParams) =>
    [...searchKeys.all, "products", params] as const,
  magazines: (params: EntitySearchParams) =>
    [...searchKeys.all, "magazines", params] as const,
  chats: (params: EntitySearchParams) =>
    [...searchKeys.all, "chats", params] as const,
  partners: (params: EntitySearchParams) =>
    [...searchKeys.all, "partners", params] as const,
  opportunities: (params: EntitySearchParams) =>
    [...searchKeys.all, "opportunities", params] as const,
  recent: () => [...searchKeys.all, "recent"] as const
};

/**
 * Hook for global search across all entity types
 */
export const useGlobalSearch = (
  searchTerm: string,
  params?: Omit<GlobalSearchParams, "search">,
  options?: UseQueryOptions<GlobalSearchResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.global({search: searchTerm, ...params}),
    queryFn: () => SearchService.globalSearch({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2, // Only search with 2+ characters
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for infinite global search with pagination
 */
export const useInfiniteGlobalSearch = (
  searchTerm: string,
  params?: Omit<GlobalSearchParams, "search" | "page">,
  pageSize: number = 20,
  options?: UseInfiniteQueryOptions<GlobalSearchResponse, BaseApiError>
) => {
  return useInfiniteQuery({
    queryKey: searchKeys.global({search: searchTerm, ...params, pageSize}),
    queryFn: ({pageParam = 1}) =>
      SearchService.globalSearch({
        search: searchTerm,
        ...params,
        page: pageParam as number,
        pageSize
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Safe access to pagination data using optional chaining
      return lastPage?.pagination?.hasNextPage
        ? lastPage.pagination.currentPage + 1
        : undefined;
    },
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook for searching members
 */
export const useSearchMembers = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.members({search: searchTerm, ...params}),
    queryFn: () => SearchService.searchMembers({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching events
 */
export const useSearchEvents = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.events({search: searchTerm, ...params}),
    queryFn: () => SearchService.searchEvents({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching products
 */
export const useSearchProducts = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.products({search: searchTerm, ...params}),
    queryFn: () =>
      SearchService.searchProducts({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching magazines
 */
export const useSearchMagazines = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.magazines({search: searchTerm, ...params}),
    queryFn: () =>
      SearchService.searchMagazines({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching chats
 */
export const useSearchChats = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.chats({search: searchTerm, ...params}),
    queryFn: () => SearchService.searchChats({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching partners
 */
export const useSearchPartners = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.partners({search: searchTerm, ...params}),
    queryFn: () =>
      SearchService.searchPartners({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for searching opportunities
 */
export const useSearchOpportunities = (
  searchTerm: string,
  params?: Omit<EntitySearchParams, "search">,
  options?: UseQueryOptions<PaginationResponse<SearchResult>, BaseApiError>
) => {
  return useQuery({
    queryKey: searchKeys.opportunities({search: searchTerm, ...params}),
    queryFn: () =>
      SearchService.searchOpportunities({search: searchTerm, ...params}),
    enabled:
      !!searchTerm && typeof searchTerm === "string" && searchTerm.length >= 2,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};
