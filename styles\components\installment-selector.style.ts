import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    marginBottom: 16
  },

  // Selector Button Styles
  selectorButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.inputBackground,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    minHeight: 48
  },

  selectorButtonDisabled: {
    opacity: 0.5
  },

  selectorButtonExpanded: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: "transparent"
  },

  selectorText: {
    flex: 1,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    color: stylesConstants.colors.textPrimary,
    lineHeight: 20
  },

  selectorTextDisabled: {
    color: stylesConstants.colors.gray700
  },

  chevronIcon: {
    transform: [{rotate: "0deg"}]
  },

  chevronIconRotated: {
    transform: [{rotate: "180deg"}]
  },

  // Options Container Styles
  optionsContainer: {
    backgroundColor: "#202938",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    padding: 16,
    gap: 12
  },

  optionsTitle: {
    fontSize: 12,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    marginBottom: 4
  },

  optionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    justifyContent: "space-between"
  },

  // Option Button Styles
  optionButton: {
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
    width: "48%",
    minHeight: 60
  },

  optionButtonSelected: {
    borderColor: stylesConstants.colors.brand.primary,
    borderWidth: 1.5,
    backgroundColor: "rgba(54, 166, 122, 0.1)"
  },

  optionText: {
    textAlign: "center",
    marginBottom: 4
  },

  optionInstallments: {
    fontSize: 12,
    fontWeight: "700",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans
  },

  optionOf: {
    fontSize: 12,
    fontWeight: "400",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans
  },

  optionAmount: {
    fontSize: 12,
    fontWeight: "700",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans
  },

  optionLabel: {
    fontSize: 10,
    fontWeight: "400",
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  }
});

export default styles;
