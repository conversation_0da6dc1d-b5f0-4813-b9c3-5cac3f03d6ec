/**
 * Test script to verify Firebase authentication token generation
 * Run this to test if Firebase is properly configured and generating tokens
 */

import FirebaseService from '../services/firebase.service';

async function testFirebaseAuth() {
  console.log('🔥 Testing Firebase Authentication...');
  
  try {
    // Test getting Firebase auth token
    console.log('📝 Getting Firebase auth token...');
    const token = await FirebaseService.getFirebaseAuthToken().toPromise();
    
    if (token) {
      console.log('✅ Firebase auth token obtained successfully!');
      console.log('🔑 Token length:', token.length);
      console.log('🔑 Token preview:', token.substring(0, 50) + '...');
      
      // Verify token structure (JWT should have 3 parts separated by dots)
      const tokenParts = token.split('.');
      if (tokenParts.length === 3) {
        console.log('✅ Token has valid JWT structure (3 parts)');
      } else {
        console.log('❌ Token does not have valid JWT structure');
      }
      
    } else {
      console.log('❌ No Firebase auth token obtained');
    }
    
    // Test getting current user
    console.log('👤 Getting current Firebase user...');
    const currentUser = FirebaseService.getCurrentUser();
    
    if (currentUser) {
      console.log('✅ Current user found:');
      console.log('   - UID:', currentUser.uid);
      console.log('   - Anonymous:', currentUser.isAnonymous);
      console.log('   - Provider:', currentUser.providerData.length > 0 ? currentUser.providerData[0].providerId : 'anonymous');
    } else {
      console.log('❌ No current user found');
    }
    
  } catch (error) {
    console.error('🚨 Firebase authentication test failed:', error);
  }
}

// Run the test
testFirebaseAuth()
  .then(() => {
    console.log('🏁 Firebase authentication test completed');
  })
  .catch((error) => {
    console.error('💥 Test execution failed:', error);
  });
