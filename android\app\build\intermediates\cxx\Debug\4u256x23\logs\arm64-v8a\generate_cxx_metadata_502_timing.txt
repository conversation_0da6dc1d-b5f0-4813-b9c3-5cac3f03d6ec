# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 25ms
  generate-prefab-packages
    [gap of 42ms]
    exec-prefab 1780ms
    [gap of 47ms]
  generate-prefab-packages completed in 1869ms
  execute-generate-process
    exec-configure 1473ms
    [gap of 64ms]
  execute-generate-process completed in 1539ms
  remove-unexpected-so-files 14ms
  [gap of 62ms]
generate_cxx_metadata completed in 3532ms

