import {useCallback, useState} from "react";
import {
  ActivateAccountRequest,
  ActivateAccountRequestSchema
} from "@/models/login";
import {formatZodError} from "@/utils/zod-utils";
import {useTranslation} from "react-i18next";
import LoginService from "@/services/login.service";
import {useRouter} from "expo-router";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import {useLoading} from "@/contexts/loading-context";

function useAccountActivation() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const errorAction = useErrorMessage();
  const loadingAction = useLoading();
  const router = useRouter();
  const {t} = useTranslation();

  const activateAccount = useCallback(
    (request: ActivateAccountRequest) => {
      const validation = ActivateAccountRequestSchema.safeParse(request);
      if (!validation.success) {
        setErrors(formatZodError(validation.error));
        errorAction.emitError({
          errorType: ErrorType.Warning,
          title: t("errors.invalidData"),
          description: t("errors.accountActivationDescription")
        });
        return;
      }

      setErrors({});

      loadingAction.setCurrentLoading?.(true);
      LoginService.activateAccount(request).subscribe({
        next: () => {
          router.replace("/(auth)/account-activation/activation-success");
        },
        error: (error) => {
          console.error("Account activation error:", error);
          errorAction.emitError({
            errorType: ErrorType.Error,
            title: t("errors.activationFailed"),
            description: t("errors.tryLater")
          });
          loadingAction.setCurrentLoading?.(false);
        },
        complete: () => {
          loadingAction.setCurrentLoading?.(false);
        }
      });
    },
    [errorAction, t, loadingAction, router]
  );

  const resendActivationCode = useCallback(
    (document: string) => {
      if (!document) {
        errorAction.emitError({
          errorType: ErrorType.Error,
          title: t("errors.invalidData"),
          description: t("errors.requiredField")
        });
        return;
      }

      loadingAction.setCurrentLoading?.(true);

      // Use the forgot password endpoint to resend activation code
      LoginService.forgetPassword({document}).subscribe({
        next: () => {
          errorAction.emitError({
            errorType: ErrorType.Warning,
            title: t("accountActivation.codeResent"),
            description: t("accountActivation.codeResentDescription")
          });
        },
        error: (error) => {
          console.error("Resend activation code error:", error);
          errorAction.emitError({
            errorType: ErrorType.Error,
            title: t("errors.resendFailed"),
            description: t("errors.tryLater")
          });
          loadingAction.setCurrentLoading?.(false);
        },
        complete: () => {
          loadingAction.setCurrentLoading?.(false);
        }
      });
    },
    [errorAction, t, loadingAction]
  );

  return {
    activateAccount,
    resendActivationCode,
    errors
  };
}

export default useAccountActivation;
