{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "73,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7471,8190,8296,8403", "endColumns": "99,105,106,105", "endOffsets": "7566,8291,8398,8504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,16306", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,16381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3076,3185", "endColumns": "108,120", "endOffsets": "3180,3301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,366,472,564,709,822,935,1054,1183,1308,1452,1569,1712,1807,1966,2092,2216,2357,2495,2620,2715,2843,2934,3058,3156,3287", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "224,361,467,559,704,817,930,1049,1178,1303,1447,1564,1707,1802,1961,2087,2211,2352,2490,2615,2710,2838,2929,3053,3151,3282,3382"}, "to": {"startLines": "36,37,72,75,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,174,193,194,195,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3306,3480,7365,7625,7932,8077,8509,8622,8741,8870,8995,9139,9256,9399,9494,9653,9779,9903,10044,16098,17661,17756,17884,17975,18099,18197,18328", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "3475,3612,7466,7712,8072,8185,8617,8736,8865,8990,9134,9251,9394,9489,9648,9774,9898,10039,10177,16218,17751,17879,17970,18094,18192,18323,18423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6130", "endColumns": "146", "endOffsets": "6272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "43,44,45,46,47,48,49,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4034,4131,4233,4331,4428,4530,4636,17259", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4126,4228,4326,4423,4525,4631,4742,17355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,76,77,78,103,106,108,109,110,111,112,113,114,115,116,117,118,119,120,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3617,3692,3767,3844,3943,4747,4843,4955,7717,7777,7841,10561,10791,10923,11014,11077,11140,11199,11268,11331,11385,11493,11551,11613,11667,11740,12085,12169,12249,12348,12432,12523,12663,12740,12816,12947,13034,13110,13163,13217,13283,13353,13430,13501,13581,13652,13727,13805,13876,13977,14062,14151,14246,14339,14411,14483,14579,14631,14717,14784,14868,14958,15020,15084,15147,15217,15311,15413,15502,15602,15659,15717,16386,16470,16545", "endLines": "5,38,39,40,41,42,50,51,52,76,77,78,103,106,108,109,110,111,112,113,114,115,116,117,118,119,120,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "310,3687,3762,3839,3938,4029,4838,4950,5032,7772,7836,7927,10633,10847,11009,11072,11135,11194,11263,11326,11380,11488,11546,11608,11662,11735,11856,12164,12244,12343,12427,12518,12658,12735,12811,12942,13029,13105,13158,13212,13278,13348,13425,13496,13576,13647,13722,13800,13871,13972,14057,14146,14241,14334,14406,14478,14574,14626,14712,14779,14863,14953,15015,15079,15142,15212,15306,15408,15497,15597,15654,15712,15791,16465,16540,16614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5114,5224,5379,5515,5620,5767,5897,6024,6277,6449,6556,6713,6847,6992,7159,7221,7285", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "5219,5374,5510,5615,5762,5892,6019,6125,6444,6551,6708,6842,6987,7154,7216,7280,7360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,109,154,225,294,360,417", "endColumns": "53,44,70,68,65,56,66", "endOffsets": "104,149,220,289,355,412,479"}, "to": {"startLines": "74,98,99,100,101,102,170", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7571,10253,10298,10369,10438,10504,15796", "endColumns": "53,44,70,68,65,56,66", "endOffsets": "7620,10293,10364,10433,10499,10556,15858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "33,53,97,104,105,107,121,122,123,171,172,173,175,180,181,182,183,184,185,186,187,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,5037,10182,10638,10708,10852,11861,11928,12005,15863,15948,16028,16223,16619,16704,16779,16864,16950,17027,17101,17172,17360,17430,17509,17584", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "3071,5109,10248,10703,10786,10918,11923,12000,12080,15943,16023,16093,16301,16699,16774,16859,16945,17022,17096,17167,17254,17425,17504,17579,17656"}}]}]}