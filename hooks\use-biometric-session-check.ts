import {useEffect, useState} from "react";
import * as SecureStore from "expo-secure-store";
import {ApiLogger} from "@/services/api/base/api-logger";

// Chave para armazenar dados de sessão para biometria (mesma do AuthContext)
const BIOMETRIC_SESSION_KEY = "session_data";

export interface BiometricSessionCheck {
  hasSessionData: boolean;
  isChecking: boolean;
}

/**
 * Hook para verificar se há dados de sessão biométrica salvos
 */
export const useBiometricSessionCheck = (): BiometricSessionCheck => {
  const [state, setState] = useState<BiometricSessionCheck>({
    hasSessionData: false,
    isChecking: true
  });

  useEffect(() => {
    const checkSessionData = async () => {
      try {
        setState(prev => ({...prev, isChecking: true}));

        const savedSessionData = await SecureStore.getItemAsync(BIOMETRIC_SESSION_KEY);
        
        if (!savedSessionData) {
          setState({hasSessionData: false, isChecking: false});
          return;
        }

        // Verificar se os dados são válidos
        const sessionData = JSON.parse(savedSessionData);
        if (!sessionData.token || !sessionData.user) {
          setState({hasSessionData: false, isChecking: false});
          return;
        }

        // Verificar se o token não expirou
        const expiresAt = new Date(sessionData.expiresAt);
        if (expiresAt <= new Date()) {
          // Token expirado, remover dados
          await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
          setState({hasSessionData: false, isChecking: false});
          return;
        }

        setState({hasSessionData: true, isChecking: false});
      } catch (error) {
        ApiLogger.error("Erro ao verificar dados de sessão biométrica", error as Error);
        setState({hasSessionData: false, isChecking: false});
      }
    };

    checkSessionData();
  }, []);

  return state;
};

export default useBiometricSessionCheck;
