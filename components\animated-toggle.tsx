import React, {useRef, useEffect} from "react";
import {
  TouchableOpacity,
  View,
  Animated,
  StyleSheet,
  ViewStyle
} from "react-native";

interface AnimatedToggleProps {
  isEnabled: boolean;
  onToggle: () => void;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  activeColor?: string;
  inactiveColor?: string;
  disabledColor?: string;
  style?: ViewStyle;
}

const AnimatedToggle: React.FC<AnimatedToggleProps> = ({
  isEnabled,
  onToggle,
  disabled = false,
  size = "medium",
  activeColor = "#1F9464",
  inactiveColor = "#CFD4DC",
  disabledColor = "#475467",
  style
}) => {
  const animatedValue = useRef(new Animated.Value(isEnabled ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isEnabled ? 1 : 0,
      duration: 200,
      useNativeDriver: false
    }).start();
  }, [isEnabled, animatedValue]);

  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          container: {width: 36, height: 20, borderRadius: 10, padding: 1.5},
          button: {width: 17, height: 17, borderRadius: 8.5}
        };
      case "large":
        return {
          container: {width: 52, height: 28, borderRadius: 14, padding: 2.5},
          button: {width: 23, height: 23, borderRadius: 11.5}
        };
      default: // medium
        return {
          container: {width: 44, height: 24, borderRadius: 12, padding: 2},
          button: {width: 20, height: 20, borderRadius: 10}
        };
    }
  };

  const sizeStyles = getSizeStyles();

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      disabled ? disabledColor : inactiveColor,
      disabled ? disabledColor : activeColor
    ]
  });

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, sizeStyles.container.width - sizeStyles.button.width - sizeStyles.container.padding * 2]
  });

  if (disabled) {
    return (
      <View style={[styles.container, sizeStyles.container, {backgroundColor: disabledColor}, style]}>
        <View style={[styles.button, sizeStyles.button, styles.disabledButton]} />
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.container, sizeStyles.container, style]}
      onPress={onToggle}
      activeOpacity={0.8}
    >
      <Animated.View style={[styles.container, sizeStyles.container, {backgroundColor}]}>
        <Animated.View
          style={[
            styles.button,
            sizeStyles.button,
            {
              transform: [{translateX}]
            }
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start"
  },
  button: {
    backgroundColor: "#FFFFFF",
    shadowColor: "rgba(16, 24, 40, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 2
  },
  disabledButton: {
    backgroundColor: "#EAECF0"
  }
});

export default AnimatedToggle;
