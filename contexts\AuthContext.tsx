/**
 * Contexto de autenticação para gerenciar estado global
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useMemo
} from "react";
import {useRouter, useSegments} from "expo-router";
import {useQueryClient} from "@tanstack/react-query";
import {tokenManager} from "@/services/api/auth/token-manager";
import {apiClient} from "@/services/api/base/api-client";
import {UserInfo} from "@/models/api/auth.models";
import {ApiLogger} from "@/services/api/base/api-logger";
import {usersKeys} from "@/hooks/api/use-users";
import * as SecureStore from "expo-secure-store";
import * as LocalAuthentication from "expo-local-authentication";

// Chave para armazenar dados de sessão para biometria
const BIOMETRIC_SESSION_KEY = "session_data";

interface AuthContextType {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresBiometricAuth: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  authenticateWithBiometrics: () => Promise<boolean>;
  skipBiometricAuth: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({children}: AuthProviderProps) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [requiresBiometricAuth, setRequiresBiometricAuth] = useState(false);
  const [pendingTokenData, setPendingTokenData] = useState<any>(null);
  const router = useRouter();
  const segments = useSegments();
  const queryClient = useQueryClient();

  const isAuthenticated = !!user;

  // Verificar se está em rota protegida
  const inAuthGroup = segments[0] === "(auth)";
  const inRegistrationGroup = segments[0] === "(registration)";

  useEffect(() => {
    // Configurar callback para token expirado
    apiClient.setOnTokenExpired(() => {
      ApiLogger.warn("Token expirado, redirecionando para login");
      handleLogout();
    });

    // Configurar callback para acesso negado (403)
    apiClient.setOnForbiddenAccess(() => {
      ApiLogger.warn(
        "Acesso negado - usuário não tem permissão para este recurso"
      );
      // Para 403, não fazemos logout automático, apenas logamos o erro
      // O usuário pode continuar usando outras partes do app
    });

    // Verificar sessão existente
    checkExistingSession();
  }, []);

  useEffect(() => {
    // Gerenciar navegação baseada no estado de autenticação
    // Mas não interferir na tela de introdução (index)
    if (!isLoading) {
      const isOnIntroScreen = !segments.length || segments[0] === "index";

      if (isAuthenticated && (inAuthGroup || inRegistrationGroup)) {
        // Usuário autenticado em tela de auth ou registration -> redirecionar para home
        router.replace("/(tabs)/home");
      } else if (
        !isAuthenticated &&
        !inAuthGroup &&
        !inRegistrationGroup &&
        !isOnIntroScreen
      ) {
        // Usuário não autenticado em tela protegida -> redirecionar para login
        // Mas não se estiver na tela de introdução ou registration
        router.replace("/(auth)/login");
      }
    }
  }, [isAuthenticated, inAuthGroup, inRegistrationGroup, isLoading, segments]);

  const checkExistingSession = async () => {
    try {
      setIsLoading(true);

      // Carregar token do storage
      const tokenData = await tokenManager.loadTokenFromStorage();

      if (!tokenData) {
        setIsLoading(false);
        return;
      }

      // Verificar se token está válido
      if (tokenManager.isTokenExpired()) {
        // Tentar renovar token
        if (tokenData.refreshToken) {
          try {
            await tokenManager.refreshToken();
            const updatedTokenData = tokenManager.getTokenData();
            if (updatedTokenData) {
              // Token renovado com sucesso - exigir biometria para login automático
              await interceptAutomaticLogin(updatedTokenData);
            } else {
              await handleLogout();
            }
          } catch (error) {
            ApiLogger.error("Erro ao renovar token", error as Error);
            await handleLogout();
          }
        } else {
          await handleLogout();
        }
      } else {
        // Token válido - exigir biometria para login automático
        await interceptAutomaticLogin(tokenData);
      }
    } catch (error) {
      ApiLogger.error("Erro ao verificar sessão existente", error as Error);
      await handleLogout();
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Interceptar login automático e exigir biometria
   */
  const interceptAutomaticLogin = async (tokenData: any) => {
    try {
      // Verificar se dispositivo suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (hasHardware && isEnrolled) {
        // Dispositivo suporta biometria - exigir autenticação
        ApiLogger.info("Interceptando login automático - exigindo biometria");

        // Configurar token no API client temporariamente para verificar validade
        apiClient.setAuthToken(tokenData);

        // Armazenar dados do token para uso após autenticação biométrica
        setPendingTokenData(tokenData);

        // Ativar flag para mostrar prompt de biometria
        setRequiresBiometricAuth(true);

        return;
      } else {
        // Dispositivo não suporta biometria - prosseguir com login automático normal
        ApiLogger.info(
          "Dispositivo não suporta biometria - prosseguindo com login automático"
        );
        await proceedWithAutomaticLogin(tokenData);
      }
    } catch (error) {
      ApiLogger.error("Erro ao interceptar login automático", error as Error);
      // Em caso de erro, prosseguir com login automático normal
      await proceedWithAutomaticLogin(tokenData);
    }
  };

  /**
   * Prosseguir com login automático normal
   */
  const proceedWithAutomaticLogin = async (tokenData: any) => {
    try {
      // Configurar token no API client
      apiClient.setAuthToken(tokenData);

      // Carregar informações do usuário
      await loadUserInfo();

      ApiLogger.info("Login automático realizado com sucesso");
    } catch (error) {
      ApiLogger.error("Erro no login automático", error as Error);
      await handleLogout();
    }
  };

  const loadUserInfo = async () => {
    try {
      // Fazer requisição real para obter dados do usuário
      const tokenData = tokenManager.getTokenData();
      if (tokenData) {
        try {
          // Importar UsersService dinamicamente para evitar dependência circular
          const {default: UsersService} = await import(
            "@/services/api/users/users.service"
          );

          // Buscar dados reais do usuário
          const userInfo = await UsersService.getCurrentUser();
          setUser(userInfo);

          // Invalidar cache do React Query para garantir dados atualizados
          queryClient.invalidateQueries({queryKey: usersKeys.current()});

          ApiLogger.info("Dados do usuário carregados com sucesso", {
            userId: userInfo.id,
            name: userInfo.name
          });
        } catch (apiError: any) {
          // Se for erro 401, fazer logout
          if (apiError?.status === 401) {
            ApiLogger.warn("Token inválido, fazendo logout");
            await handleLogout();
            return;
          }

          // Se a API falhar, usar dados mock como fallback
          ApiLogger.warn(
            "API falhou, usando dados mock como fallback",
            apiError
          );

          const mockUser: UserInfo = {
            id: "1",
            name: "Mozart",
            email: "<EMAIL>",
            document: "12345678901",
            phone: "+55 11 99999-9999",
            avatar:
              "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg",
            isActive: true,
            roles: ["user"],
            permissions: ["read"]
          };
          setUser(mockUser);
        }
      }
    } catch (error) {
      ApiLogger.error(
        "Erro ao carregar informações do usuário",
        error as Error
      );
      throw error;
    }
  };

  /**
   * Salvar dados de sessão para login biométrico
   */
  const saveBiometricSessionData = async () => {
    try {
      // Verificar se o dispositivo suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (hasHardware && isEnrolled && user) {
        const tokenData = tokenManager.getTokenData();
        if (tokenData) {
          const sessionData = {
            token: tokenData.accessToken,
            refreshToken: tokenData.refreshToken,
            user: user,
            expiresAt:
              tokenData.expiresAt ||
              new Date(Date.now() + tokenData.expiresIn * 1000),
            issuedAt: new Date()
          };

          await SecureStore.setItemAsync(
            BIOMETRIC_SESSION_KEY,
            JSON.stringify(sessionData)
          );

          ApiLogger.info("Dados de sessão salvos para login biométrico");
        }
      }
    } catch (error) {
      ApiLogger.warn("Erro ao salvar dados para biometria", error as Error);
      // Não falhar o login se não conseguir salvar dados para biometria
    }
  };

  const handleLogin = async (emailOrDocument: string, password: string) => {
    try {
      setIsLoading(true);

      // Importar AuthService dinamicamente para evitar dependência circular
      const {AuthService} = await import("@/services/api/auth/auth.service");

      // Verificar se é email ou documento (CPF)
      const isEmail = emailOrDocument.includes("@");

      const response = isEmail
        ? await AuthService.login({email: emailOrDocument, password})
        : await AuthService.appLogin({document: emailOrDocument, password});

      if (response.accessToken) {
        await loadUserInfo();

        // Salvar dados para login biométrico após carregar informações do usuário
        await saveBiometricSessionData();

        // Invalidar cache para garantir dados atualizados após login
        queryClient.invalidateQueries({queryKey: usersKeys.current()});
        ApiLogger.info("Login realizado com sucesso");
      } else {
        throw new Error("Token de acesso não recebido");
      }
    } catch (error) {
      ApiLogger.error("Erro no login", error as Error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);

      // Importar AuthService dinamicamente
      const {default: AuthService} = await import(
        "@/services/api/auth/auth.service"
      );

      try {
        await AuthService.logout();
      } catch (error) {
        // Continuar com logout local mesmo se falhar no servidor
        ApiLogger.warn(
          "Erro no logout do servidor, continuando com logout local",
          error
        );
      }

      // Limpar estado local
      setUser(null);
      apiClient.clearAuthToken();

      // Limpar dados de sessão biométrica
      try {
        await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
        ApiLogger.info("Dados de sessão biométrica removidos");
      } catch (error) {
        ApiLogger.warn(
          "Erro ao remover dados de sessão biométrica",
          error as Error
        );
      }

      // Limpar cache do React Query
      queryClient.clear();

      ApiLogger.info("Logout realizado com sucesso");
    } catch (error) {
      ApiLogger.error("Erro no logout", error as Error);
      // Mesmo com erro, limpar estado local
      setUser(null);
      apiClient.clearAuthToken();

      // Tentar limpar dados de sessão biométrica mesmo em caso de erro
      try {
        await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
      } catch (biometricError) {
        ApiLogger.warn(
          "Erro ao remover dados de sessão biométrica no fallback",
          biometricError as Error
        );
      }

      // Limpar cache do React Query mesmo em caso de erro
      queryClient.clear();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      await loadUserInfo();
      // Invalidar cache para garantir dados atualizados
      queryClient.invalidateQueries({queryKey: usersKeys.current()});
    } catch (error) {
      ApiLogger.error("Erro ao atualizar dados do usuário", error as Error);
      throw error;
    }
  };

  /**
   * Autenticar com biometria para login automático
   */
  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      if (!pendingTokenData) {
        ApiLogger.error("Nenhum token pendente para autenticação biométrica");
        return false;
      }

      // Verificar se dispositivo ainda suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware || !isEnrolled) {
        ApiLogger.warn("Biometria não disponível durante autenticação");
        return false;
      }

      // Configurar opções de autenticação biométrica
      const biometricConfig = {
        promptMessage: "Confirme que é você",
        fallbackLabel: "Usar senha",
        disableDeviceFallback: false
      };

      // Realizar autenticação biométrica
      const authResult = await LocalAuthentication.authenticateAsync(
        biometricConfig
      );

      if (authResult.success) {
        // Autenticação bem-sucedida - prosseguir com login
        ApiLogger.info("Autenticação biométrica bem-sucedida");

        // Limpar estados de biometria
        setRequiresBiometricAuth(false);
        setPendingTokenData(null);

        // Prosseguir com login automático
        await proceedWithAutomaticLogin(pendingTokenData);

        return true;
      } else {
        ApiLogger.warn("Autenticação biométrica falhou", authResult.error);
        return false;
      }
    } catch (error) {
      ApiLogger.error("Erro na autenticação biométrica", error as Error);
      return false;
    }
  };

  /**
   * Pular autenticação biométrica e ir para login
   */
  const skipBiometricAuth = () => {
    ApiLogger.info("Usuário optou por pular autenticação biométrica");

    // Limpar estados
    setRequiresBiometricAuth(false);
    setPendingTokenData(null);

    // Limpar token e redirecionar para login
    apiClient.clearAuthToken();
    router.replace("/(auth)/login");
  };

  const value: AuthContextType = useMemo(
    () => ({
      user,
      isAuthenticated,
      isLoading,
      requiresBiometricAuth,
      login: handleLogin,
      logout: handleLogout,
      refreshUser,
      authenticateWithBiometrics,
      skipBiometricAuth
    }),
    [
      user,
      isAuthenticated,
      isLoading,
      requiresBiometricAuth,
      handleLogin,
      handleLogout,
      refreshUser,
      authenticateWithBiometrics,
      skipBiometricAuth
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }
  return context;
}

export default AuthContext;
