/**
 * Hooks para gerenciamento de pagamentos
 * Seguindo os padrões estabelecidos no projeto com TanStack Query
 */

import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import Toast from "react-native-toast-message";
import {PaymentsService} from "../../services/api/payments/payments.service";
import {
  CreatePaymentRequest,
  CreateCreditCardRequest,
  PaymentStatus,
  BoletoDetails,
  PIXDetails
} from "../../models/api/payments.models";
import {useLoading} from "../../contexts/loading-context";

// Query Keys
export const PAYMENTS_QUERY_KEYS = {
  all: ["payments"] as const,
  lists: () => [...PAYMENTS_QUERY_KEYS.all, "list"] as const,
  list: (filters: Record<string, any>) =>
    [...PAYMENTS_QUERY_KEYS.lists(), {filters}] as const,
  details: () => [...PAYMENTS_QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...PAYMENTS_QUERY_KEYS.details(), id] as const,
  creditCards: () => [...PAYMENTS_QUERY_KEYS.all, "credit-cards"] as const,
  creditCard: (id: string) =>
    [...PAYMENTS_QUERY_KEYS.creditCards(), id] as const,
  qrCode: (id: string) => [...PAYMENTS_QUERY_KEYS.all, "qr-code", id] as const,
  barCode: (id: string) => [...PAYMENTS_QUERY_KEYS.all, "bar-code", id] as const
};

/**
 * Hook para buscar pagamentos do usuário
 */
export const useUserPayments = (
  page: number = 1,
  pageSize: number = 10,
  status?: PaymentStatus
) => {
  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.list({page, pageSize, status}),
    queryFn: () => PaymentsService.getUserPayments(page, pageSize, status),
    select: (data) => data.data
  });
};

/**
 * Hook para buscar um pagamento específico
 */
export const usePayment = (id: string, options?: {enabled?: boolean}) => {
  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.detail(id),
    queryFn: () => PaymentsService.getPaymentById(id),
    select: (data) => data.data,
    enabled: options?.enabled !== undefined ? options.enabled : !!id
  });
};

/**
 * Hook para buscar detalhes do boleto
 */
export const useBoletoDetails = (
  paymentId: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: [...PAYMENTS_QUERY_KEYS.detail(paymentId), "boleto"],
    queryFn: () => PaymentsService.getBoletoDetails(paymentId),
    enabled: !!paymentId && enabled,
    select: (data) => data.data,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 3
  });
};

/**
 * Hook para buscar detalhes do PIX
 */
export const usePIXDetails = (paymentId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: [...PAYMENTS_QUERY_KEYS.detail(paymentId), "pix"],
    queryFn: () => PaymentsService.getPIXDetails(paymentId),
    enabled: !!paymentId && enabled,
    select: (data) => data.data,
    staleTime: 2 * 60 * 1000, // 2 minutos (menor que boleto devido à expiração)
    retry: 3
  });
};

/**
 * Hook para criar um novo pagamento
 */
export const useCreatePayment = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: (paymentData: CreatePaymentRequest) => {
      console.log("🚀 [USE-PAYMENTS] Criando pagamento via API");
      console.log("📤 [USE-PAYMENTS] Dados enviados:", paymentData);
      return PaymentsService.createPayment(paymentData);
    },
    onMutate: () => {
      console.log("⏳ [USE-PAYMENTS] Iniciando loading");
      setCurrentLoading?.(true);
    },
    onSuccess: (data) => {
      console.log("✅ [USE-PAYMENTS] Pagamento criado com sucesso:", data);
      queryClient.invalidateQueries({queryKey: PAYMENTS_QUERY_KEYS.lists()});
      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Pagamento criado com sucesso!",
        position: "top",
        topOffset: 60
      });
      return data.data;
    },
    onError: (error: any) => {
      console.error("❌ [USE-PAYMENTS] Erro ao criar pagamento:", error);
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao criar pagamento",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      console.log("🏁 [USE-PAYMENTS] Finalizando loading");
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para cancelar um pagamento
 */
export const useCancelPayment = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: (id: string) => PaymentsService.cancelPayment(id),
    onMutate: () => {
      setCurrentLoading?.(true);
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({queryKey: PAYMENTS_QUERY_KEYS.lists()});
      queryClient.invalidateQueries({queryKey: PAYMENTS_QUERY_KEYS.detail(id)});
      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Pagamento cancelado com sucesso!",
        position: "top",
        topOffset: 60
      });
      return data.data;
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao cancelar pagamento",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para confirmar pagamento PIX
 */
export const useConfirmPixPayment = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: (id: string) => PaymentsService.confirmPixPayment(id),
    onMutate: () => {
      setCurrentLoading?.(true);
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({queryKey: PAYMENTS_QUERY_KEYS.lists()});
      queryClient.invalidateQueries({queryKey: PAYMENTS_QUERY_KEYS.detail(id)});
      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Pagamento PIX confirmado!",
        position: "top",
        topOffset: 60
      });
      return data.data;
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao confirmar pagamento PIX",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para buscar QR Code do pagamento
 */
export const usePaymentQRCode = (paymentId: string) => {
  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.qrCode(paymentId),
    queryFn: () => PaymentsService.getPaymentQRCode(paymentId),
    select: (data) => data.data,
    enabled: !!paymentId
  });
};

/**
 * Hook para buscar código de barras do pagamento
 */
export const usePaymentBarCode = (paymentId: string) => {
  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.barCode(paymentId),
    queryFn: () => PaymentsService.getPaymentBarCode(paymentId),
    select: (data) => data.data,
    enabled: !!paymentId
  });
};

/**
 * Hook para buscar cartões de crédito salvos do usuário
 */
export const useCreditCards = () => {
  console.log(
    "🔄 [USE-PAYMENTS] 🔧 TESTE FLUXO CARTÕES - Buscando cartões de crédito do usuário"
  );

  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.creditCards(),
    queryFn: () => {
      console.log("📡 [USE-PAYMENTS] Fazendo chamada para API de cartões");
      return PaymentsService.getUserCreditCards();
    },
    select: (data) => {
      console.log(
        "✅ [USE-PAYMENTS] 🔧 CORREÇÃO CRÍTICA - Dados recebidos do serviço:",
        {
          success: data.success,
          hasData: !!data.data,
          cardsCount: data.data?.length || 0
        }
      );

      // O serviço já processa a resposta paginada e retorna data.data
      const cards = data.data || [];

      console.log("✅ [USE-PAYMENTS] 🔧 CORREÇÃO CRÍTICA - Cartões finais:", {
        cardsCount: cards.length,
        cards: cards.map((card) => ({
          id: card.id,
          lastFourDigits: card.lastFourDigits || card.number?.slice(-4),
          brand: card.brand,
          isDefault: card.isDefault
        }))
      });

      return cards;
    },
    retry: (failureCount, error: any) => {
      // Se for 404, não tentar novamente (endpoint pode não existir)
      if (error?.response?.status === 404) {
        console.log(
          "⚠️ [USE-PAYMENTS] Endpoint de cartões não encontrado, retornando array vazio"
        );
        return false;
      }
      console.error(
        "❌ [USE-PAYMENTS] 🔧 TESTE FLUXO CARTÕES - Erro ao carregar cartões:",
        {
          attempt: failureCount + 1,
          message: error.message,
          status: error.response?.status,
          data: error.response?.data
        }
      );
      return failureCount < 3;
    },
    // Retorna array vazio se falhar
    placeholderData: {
      success: true,
      data: []
    }
  });
};

/**
 * Hook para buscar um cartão de crédito específico
 */
export const useCreditCard = (id: string) => {
  return useQuery({
    queryKey: PAYMENTS_QUERY_KEYS.creditCard(id),
    queryFn: () => PaymentsService.getCreditCard(id),
    select: (data) => data.data,
    enabled: !!id
  });
};

/**
 * Hook para criar cartão de crédito
 */
export const useCreateCreditCard = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: (creditCardData: CreateCreditCardRequest) => {
      console.log("🚀 [USE-PAYMENTS] Criando cartão de crédito via API");
      console.log("📤 [USE-PAYMENTS] Dados enviados:", {
        holderName: creditCardData.holderName,
        lastFourDigits: creditCardData.number.slice(-4),
        expiryMonth: creditCardData.expiryMonth,
        expiryYear: creditCardData.expiryYear
      });
      return PaymentsService.createCreditCard(creditCardData);
    },
    onMutate: () => {
      console.log("⏳ [USE-PAYMENTS] Iniciando loading");
      setCurrentLoading?.(true);
    },
    onSuccess: (data) => {
      console.log("✅ [USE-PAYMENTS] Cartão criado com sucesso:", data);
      queryClient.invalidateQueries({
        queryKey: PAYMENTS_QUERY_KEYS.creditCards()
      });
      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Cartão de crédito adicionado com sucesso!",
        position: "top",
        topOffset: 60
      });
      return data.data;
    },
    onError: (error: any) => {
      console.error("❌ [USE-PAYMENTS] Erro ao criar cartão:", error);
      console.error("❌ [USE-PAYMENTS] Detalhes do erro:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          data: error.config?.data
        }
      });

      let errorMessage = "Erro ao adicionar cartão de crédito";

      if (error.response?.status === 400) {
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (Array.isArray(error.response?.data)) {
          // Se for um array de erros de validação
          const validationErrors = error.response.data
            .map((err: any) => err.errorMessage || err.message)
            .filter(Boolean)
            .join(", ");
          if (validationErrors) {
            errorMessage = validationErrors;
          }
        } else {
          errorMessage = "Dados do cartão inválidos. Verifique as informações.";
        }
      }

      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: errorMessage,
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      console.log("🏁 [USE-PAYMENTS] Finalizando loading");
      setCurrentLoading?.(false);
    }
  });
};

/**
 * Hook para remover cartão de crédito
 */
export const useDeleteCreditCard = () => {
  const queryClient = useQueryClient();
  const {setCurrentLoading} = useLoading();

  return useMutation({
    mutationFn: (id: string) => {
      console.log("🗑️ [USE-PAYMENTS] Removendo cartão de crédito:", id);
      return PaymentsService.deleteCreditCard(id);
    },
    onMutate: () => {
      console.log("⏳ [USE-PAYMENTS] Iniciando loading");
      setCurrentLoading?.(true);
    },
    onSuccess: () => {
      console.log("✅ [USE-PAYMENTS] Cartão removido com sucesso");
      queryClient.invalidateQueries({
        queryKey: PAYMENTS_QUERY_KEYS.creditCards()
      });
      Toast.show({
        type: "success",
        text1: "Sucesso!",
        text2: "Cartão de crédito removido com sucesso!",
        position: "top",
        topOffset: 60
      });
    },
    onError: (error: any) => {
      console.error("❌ [USE-PAYMENTS] Erro ao remover cartão:", error);
      Toast.show({
        type: "error",
        text1: "Erro!",
        text2: error.message || "Erro ao remover cartão de crédito",
        position: "top",
        topOffset: 60
      });
    },
    onSettled: () => {
      console.log("🏁 [USE-PAYMENTS] Finalizando loading");
      setCurrentLoading?.(false);
    }
  });
};
