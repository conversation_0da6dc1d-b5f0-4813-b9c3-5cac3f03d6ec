/**
 * Serviço de Benefícios para ClubM
 * Implementa operações completas para benefícios de parceiros
 */

import {apiClient} from "../base/api-client";
import {PaginationResponse} from "@/models/api/common.models";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {
  Benefit,
  BenefitListParams,
  BenefitListResponse,
  BenefitDetailsResponse,
  CreateBenefitRequest,
  UpdateBenefitRequest,
  BenefitInteractionRequest,
  BenefitInteractionResponse
} from "@/models/api/benefits.models";

export class BenefitsService {
  private static readonly BASE_PATH = "/api/app/benefits";
  private static readonly PARTNERS_PATH = "/api/app/partners";

  /**
   * Buscar benefícios de um parceiro específico
   */
  static async getPartnerBenefits(
    partnerId: string | number,
    params?: BenefitListParams
  ): Promise<BenefitListResponse> {
    try {
      ApiLogger.info("Buscando benefícios do parceiro", {partnerId, ...params});

      const response = await firstValueFrom(
        apiClient.get<BenefitListResponse>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits`,
          {
            Page: params?.page || 1,
            PageSize: params?.pageSize || 10,
            Search: params?.search || "",
            Status: params?.status || 1
          }
        )
      );

      ApiLogger.info(
        `Encontrados ${response.data.length} benefícios para o parceiro ${partnerId}`
      );
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar benefícios do parceiro", error as Error, {
        partnerId
      });
      throw error;
    }
  }

  /**
   * Buscar benefício específico por ID
   */
  static async getBenefit(
    partnerId: string | number,
    benefitId: number
  ): Promise<Benefit> {
    try {
      ApiLogger.info("Buscando benefício por ID", {partnerId, benefitId});

      const response = await firstValueFrom(
        apiClient.get<BenefitDetailsResponse>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits/${benefitId}`
        )
      );

      ApiLogger.info("Benefício encontrado", {
        partnerId,
        benefitId,
        title: response.title
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar benefício", error as Error, {
        partnerId,
        benefitId
      });
      throw error;
    }
  }

  /**
   * Buscar todos os benefícios disponíveis
   */
  static async getAllBenefits(
    params?: BenefitListParams
  ): Promise<BenefitListResponse> {
    try {
      ApiLogger.info("Buscando todos os benefícios", params);

      const response = await firstValueFrom(
        apiClient.get<BenefitListResponse>(this.BASE_PATH, {
          Page: params?.page || 1,
          PageSize: params?.pageSize || 10,
          Search: params?.search || "",
          Status: params?.status || 1
        })
      );

      ApiLogger.info(`Encontrados ${response.data.length} benefícios`);
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar benefícios", error as Error);
      throw error;
    }
  }

  /**
   * Interagir com um benefício (visualizar, clicar, resgatar)
   */
  static async interactWithBenefit(
    partnerId: string | number,
    benefitId: number,
    interactionType: "view" | "click" | "redeem"
  ): Promise<BenefitInteractionResponse> {
    try {
      ApiLogger.info("Registrando interação com benefício", {
        partnerId,
        benefitId,
        interactionType
      });

      const response = await firstValueFrom(
        apiClient.post<BenefitInteractionResponse>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits/${benefitId}/interact`,
          {
            interactionType
          }
        )
      );

      ApiLogger.info("Interação registrada com sucesso", {
        partnerId,
        benefitId,
        interactionType
      });

      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro ao registrar interação com benefício",
        error as Error,
        {
          partnerId,
          benefitId,
          interactionType
        }
      );
      throw error;
    }
  }

  /**
   * Criar novo benefício (admin)
   */
  static async createBenefit(
    partnerId: string | number,
    benefit: CreateBenefitRequest
  ): Promise<Benefit> {
    try {
      ApiLogger.info("Criando novo benefício", {
        partnerId,
        title: benefit.title
      });

      const response = await firstValueFrom(
        apiClient.post<Benefit>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits`,
          benefit
        )
      );

      ApiLogger.info("Benefício criado com sucesso", {
        partnerId,
        benefitId: response.id,
        title: response.title
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao criar benefício", error as Error, {partnerId});
      throw error;
    }
  }

  /**
   * Atualizar benefício existente (admin)
   */
  static async updateBenefit(
    partnerId: string | number,
    benefitId: number,
    benefit: UpdateBenefitRequest
  ): Promise<Benefit> {
    try {
      ApiLogger.info("Atualizando benefício", {partnerId, benefitId});

      const response = await firstValueFrom(
        apiClient.put<Benefit>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits/${benefitId}`,
          benefit
        )
      );

      ApiLogger.info("Benefício atualizado com sucesso", {
        partnerId,
        benefitId,
        title: response.title
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao atualizar benefício", error as Error, {
        partnerId,
        benefitId
      });
      throw error;
    }
  }

  /**
   * Deletar benefício (admin)
   */
  static async deleteBenefit(
    partnerId: string | number,
    benefitId: number
  ): Promise<void> {
    try {
      ApiLogger.info("Deletando benefício", {partnerId, benefitId});

      await firstValueFrom(
        apiClient.delete<void>(
          `${this.PARTNERS_PATH}/${partnerId}/benefits/${benefitId}`
        )
      );

      ApiLogger.info("Benefício deletado com sucesso", {partnerId, benefitId});
    } catch (error) {
      ApiLogger.error("Erro ao deletar benefício", error as Error, {
        partnerId,
        benefitId
      });
      throw error;
    }
  }

  /**
   * Buscar benefícios recomendados
   */
  static async getRecommendedBenefits(
    params?: Omit<BenefitListParams, "search">
  ): Promise<BenefitListResponse> {
    try {
      ApiLogger.info("Buscando benefícios recomendados", params);

      const response = await firstValueFrom(
        apiClient.get<BenefitListResponse>(`${this.BASE_PATH}/recommended`, {
          Page: params?.page || 1,
          PageSize: params?.pageSize || 10,
          Status: params?.status || 1
        })
      );

      ApiLogger.info(
        `Encontrados ${response.data.length} benefícios recomendados`
      );
      return response;
    } catch (error) {
      ApiLogger.error("Erro ao buscar benefícios recomendados", error as Error);
      throw error;
    }
  }

  /**
   * Buscar estatísticas de benefícios (admin)
   * NOTA: Endpoint /stats não existe na API, retornando dados mock
   */
  static async getBenefitsStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byPartner: Array<{partnerId: number; partnerName: string; count: number}>;
  }> {
    try {
      ApiLogger.info("Calculando estatísticas de benefícios (dados mock)");

      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Retornar dados mock já que o endpoint não existe
      const mockStats = {
        total: 0,
        active: 0,
        inactive: 0,
        byPartner: []
      };

      ApiLogger.warn(
        "Endpoint /api/benefits/stats não existe na API, retornando dados mock"
      );
      console.log(
        "⚠️ API Status: GET /api/benefits/stats - 404 Not Found (Endpoint não implementado)"
      );

      return mockStats;
    } catch (error) {
      ApiLogger.error(
        "Erro ao buscar estatísticas de benefícios",
        error as Error
      );
      throw error;
    }
  }
}
