import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface BiometricIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const BiometricIcon: React.FC<BiometricIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#F2F4F7",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M10 12.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M10 1.667c4.583 0 8.333 3.75 8.333 8.333 0 4.583-3.75 8.333-8.333 8.333S1.667 14.583 1.667 10c0-4.583 3.75-8.333 8.333-8.333Z"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M6.25 6.25c.833-.833 2.083-1.25 3.75-1.25s2.917.417 3.75 1.25M6.25 13.75c.833.833 2.083 1.25 3.75 1.25s2.917-.417 3.75-1.25"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M13.75 6.25c.833.833 1.25 2.083 1.25 3.75s-.417 2.917-1.25 3.75M6.25 6.25c-.833.833-1.25 2.083-1.25 3.75s.417 2.917 1.25 3.75"
      />
    </Svg>
  );
};

export default BiometricIcon;
