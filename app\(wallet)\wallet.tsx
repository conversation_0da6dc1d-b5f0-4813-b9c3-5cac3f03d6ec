import React, {useState, useRef, useCallback, useEffect} from "react";
import {
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator
} from "react-native";
import {Image} from "expo-image";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import PlusIcon from "../../components/icons/plus-icon";
import EyeIcon from "../../components/icons/eye-icon";
import DisableEyeIcon from "../../components/icons/disable-eye-icon";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";
import CreditCard from "../../components/credit-card";
import styles from "@/styles/wallet/wallet.style";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import WalletCardRegistration from "./wallet-card-registration";
import WalletCardEdit from "./wallet-card-edit";
import BottomModal from "../../components/bottom-modal";
import {
  useCreditCards,
  useInvalidateCreditCards
} from "../../hooks/api/use-credit-cards";
import {transformCreditCardsToMemberCards} from "../../utils/credit-card-transform";
import {MemberCard} from "../../models/api/credit-cards.models";
import {CreditCardErrorBoundary} from "../../components/credit-card-error-boundary";
import {useCreditCardErrorHandler} from "../../hooks/api/use-credit-card-error-handler";
import CreditCardEmptyState from "../../components/wallet/credit-card-empty-state";
import UserProfileCard, {
  getUserCardData
} from "../../components/wallet/user-profile-card";
import {useCurrentUser} from "../../hooks/api/use-users";

const Wallet: React.FC = () => {
  const {t} = useTranslation();
  const modal = useBottomModal();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [displayedCardIndex, setDisplayedCardIndex] = useState(0); // For delayed information display
  const [showCardInfo, setShowCardInfo] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false); // To prevent rapid clicks
  const scrollViewRef = useRef<ScrollView>(null);
  const transitionTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );

  // Error handling
  const {handleCreditCardError} = useCreditCardErrorHandler();

  // API hooks
  const {
    data: creditCardsResponse,
    isLoading: isLoadingCards,
    error: cardsError,
    refetch: refetchCards
  } = useCreditCards({page: 1, pageSize: 50});

  // Hook para buscar dados do usuário atual
  const {
    data: currentUser,
    isLoading: isLoadingUser,
    error: userError
  } = useCurrentUser();

  const invalidateCreditCards = useInvalidateCreditCards();

  // Constants - updated for new card dimensions and better centering
  const cardWidth = 212; // Width of card + margin (196 + 16)
  const TRANSITION_DELAY = 250; // 250ms delay for smooth transitions

  // Handle API errors
  React.useEffect(() => {
    if (cardsError) {
      handleCreditCardError(cardsError, {
        context: {operation: "list"},
        onRetry: () => refetchCards(),
        showAlert: false // We handle this in the UI
      });
    }
  }, [cardsError, handleCreditCardError, refetchCards]);

  // Transform API data to UI format
  const memberCards: MemberCard[] = React.useMemo(() => {
    if (!creditCardsResponse?.data) {
      return [];
    }

    console.log("🔍 Raw API data:", creditCardsResponse.data);
    const transformed = transformCreditCardsToMemberCards(
      creditCardsResponse.data,
      {
        defaultPhoto:
          "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
        defaultMembershipType: "Cartão de Crédito",
        defaultFederationUnit: "ClubM",
        defaultAssociationUnit: "Cartão Registrado"
      }
    );

    console.log("🔍 Transformed member cards:", transformed);
    return transformed;
  }, [creditCardsResponse?.data]);

  // Filter cards based on active tab
  const memberOnlyCards = memberCards.filter(
    (card) => card.cardType === "member"
  );
  const creditCards = memberCards.filter((card) => card.cardType !== "member");

  // Create a user profile card for the member tab using real user data
  const userCardData = currentUser ? getUserCardData(currentUser) : null;
  const userProfileCard: MemberCard = {
    id: "user-profile",
    name:
      currentUser?.name ||
      t("wallet.userCard.defaultName", "Nome do associado"),
    photo: currentUser?.avatar || "",
    membershipType:
      currentUser?.roles?.[0] === "admin"
        ? "Administrador"
        : currentUser?.companyName
        ? "Membro Empresarial"
        : "Membro",
    cardNumber: "",
    federationUnit: userCardData?.federationUnit || "Brasil",
    associationUnit: userCardData?.associationUnit || "ClubM Digital",
    activeSince:
      userCardData?.activeSince ||
      new Date().toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      }),
    isDefault: true,
    cardType: "member",
    expiryDate: ""
  };

  // Get cards to display based on active tab
  const cardsToDisplay = activeTab === 0 ? [userProfileCard] : creditCards;

  console.log("🔍 Cards to display:", {
    activeTab,
    memberOnlyCards: activeTab === 0 ? 1 : memberOnlyCards.length, // Always 1 for user profile card
    creditCards: creditCards.length,
    cardsToDisplay: cardsToDisplay.length
  });

  const hasCreditCards = creditCards.length > 0;
  const hasMemberCards = true; // Always show user profile card in member tab

  const currentCard = cardsToDisplay[displayedCardIndex]; // Use displayedCardIndex for information display

  // Function to mask sensitive information with dots
  const maskInformation = (text: string, showInfo: boolean) => {
    if (showInfo || !text) return text;

    // Always mask completely with dots when showInfo is false
    // Create dots based on text length, with minimum 4 and maximum 8 dots for consistency
    const dotCount = Math.min(Math.max(4, Math.ceil(text.length / 2)), 8);
    return "•".repeat(dotCount);
  };

  // Reset card index when cards change or tab changes
  useEffect(() => {
    if (
      cardsToDisplay.length > 0 &&
      currentCardIndex >= cardsToDisplay.length
    ) {
      setCurrentCardIndex(0);
      setDisplayedCardIndex(0);
    }
  }, [cardsToDisplay.length, currentCardIndex]);

  // Reset card index when switching tabs
  useEffect(() => {
    setCurrentCardIndex(0);
    setDisplayedCardIndex(0);
  }, [activeTab]);

  // Cleanup timeout on unmount and reset transitioning state
  useEffect(() => {
    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
      setIsTransitioning(false);
    };
  }, []);

  // Safety mechanism to reset transitioning state after a maximum timeout
  useEffect(() => {
    if (isTransitioning) {
      const safetyTimeout = setTimeout(() => {
        setIsTransitioning(false);
      }, TRANSITION_DELAY * 2); // Double the normal transition delay as safety

      return () => clearTimeout(safetyTimeout);
    }
  }, [isTransitioning, TRANSITION_DELAY]);

  // Handler functions
  const handleAddCard = useCallback(() => {
    modal.openModal({
      title: t("walletCardRegistration.title", "Adicionar novo cartão"),
      children: (
        <BottomModal.Container>
          <WalletCardRegistration
            onSave={() => {
              // Invalidate cache to refresh the card list
              invalidateCreditCards();
              console.log("Card added successfully");
            }}
            onCancel={() => {
              console.log("Card addition cancelled");
            }}
          />
        </BottomModal.Container>
      )
    });
  }, [modal, t, invalidateCreditCards]);

  const handleEditCard = useCallback(() => {
    const currentCard = cardsToDisplay[displayedCardIndex];
    if (!currentCard) return;

    modal.openModal({
      title: t("walletCardEdit.title", "Editar cartão"),
      children: (
        <BottomModal.Container>
          <WalletCardEdit
            card={currentCard}
            onSave={() => {
              // Invalidate cache to refresh the card list
              invalidateCreditCards();
              console.log("Card edited successfully");
            }}
            onCancel={() => {
              console.log("Card edit cancelled");
            }}
          />
        </BottomModal.Container>
      )
    });
  }, [modal, t, cardsToDisplay, displayedCardIndex, invalidateCreditCards]);

  const handleDeleteCard = useCallback(() => {
    const currentCard = cardsToDisplay[displayedCardIndex];
    if (!currentCard) return;

    Alert.alert(
      t("wallet.deleteCard.title", "Excluir cartão"),
      t(
        "wallet.deleteCard.message",
        "Tem certeza que deseja excluir este cartão? Esta ação não pode ser desfeita."
      ),
      [
        {
          text: t("common.cancel", "Cancelar"),
          style: "cancel"
        },
        {
          text: t("common.delete", "Excluir"),
          style: "destructive",
          onPress: () => {
            // Note: API doesn't support DELETE operation
            // This is a limitation of the current API
            Alert.alert(
              t("wallet.deleteCard.notSupported", "Operação não suportada"),
              t(
                "wallet.deleteCard.notSupportedMessage",
                "A exclusão de cartões não está disponível no momento. Entre em contato com o suporte."
              )
            );
          }
        }
      ]
    );
  }, [cardsToDisplay, displayedCardIndex, t]);

  // Loading state - show loading if either cards or user data is loading
  if (isLoadingCards || isLoadingUser) {
    return (
      <ScreenWithHeader
        screenTitle={t("wallet.title", "Sua carteira")}
        backButton
      >
        <View
          style={[
            styles.container,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={{marginTop: 16, fontSize: 16, color: "#666"}}>
            {isLoadingUser
              ? t("wallet.loadingUser", "Carregando dados do usuário...")
              : t("wallet.loading", "Carregando cartões...")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  // Error state
  if (cardsError) {
    return (
      <ScreenWithHeader
        screenTitle={t("wallet.title", "Sua carteira")}
        backButton
      >
        <View
          style={[
            styles.container,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text
            style={{
              fontSize: 16,
              color: "#FF3B30",
              textAlign: "center",
              marginBottom: 16
            }}
          >
            {t("wallet.error", "Erro ao carregar cartões")}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: "#007AFF",
              paddingHorizontal: 20,
              paddingVertical: 10,
              borderRadius: 8
            }}
            onPress={() => refetchCards()}
          >
            <Text style={{color: "white", fontSize: 16}}>
              {t("common.retry", "Tentar novamente")}
            </Text>
          </TouchableOpacity>
        </View>
      </ScreenWithHeader>
    );
  }

  // Empty state - show when no cards at all
  if (memberCards.length === 0) {
    return (
      <ScreenWithHeader
        screenTitle={t("wallet.title", "Sua carteira")}
        backButton
        rightHeaderChild={
          <TouchableOpacity
            style={[
              styles.addCardButton,
              {
                zIndex: 10,
                position: "relative"
              }
            ]}
            onPress={handleAddCard}
            activeOpacity={0.7}
          >
            <Text style={styles.addCardButtonText}>
              {t("wallet.addCard", "Ad. cartão")}
            </Text>
            <PlusIcon width={20} height={20} />
          </TouchableOpacity>
        }
      >
        <View
          style={[
            styles.container,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text
            style={{
              fontSize: 18,
              color: "#333",
              textAlign: "center",
              marginBottom: 16
            }}
          >
            {t("wallet.noCards", "Nenhum cartão encontrado")}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: "#666",
              textAlign: "center",
              marginBottom: 24
            }}
          >
            {t(
              "wallet.noCardsDescription",
              "Adicione seu primeiro cartão para começar"
            )}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: "#007AFF",
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 8
            }}
            onPress={handleAddCard}
          >
            <Text style={{color: "white", fontSize: 16}}>
              {t("wallet.addFirstCard", "Adicionar cartão")}
            </Text>
          </TouchableOpacity>
        </View>
      </ScreenWithHeader>
    );
  }

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / cardWidth);

    // Ensure index is within bounds
    if (
      index >= 0 &&
      index < cardsToDisplay.length &&
      index !== currentCardIndex
    ) {
      setCurrentCardIndex(index);

      // Clear any existing timeout
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }

      // Delay the information update to sync with scroll animation
      transitionTimeoutRef.current = setTimeout(() => {
        setDisplayedCardIndex(index);
        setIsTransitioning(false); // Reset transitioning state
      }, TRANSITION_DELAY);
    }
  };

  const handlePrevCard = () => {
    // Prevent rapid successive clicks
    if (isTransitioning) return;

    const newIndex =
      currentCardIndex === 0 ? cardsToDisplay.length - 1 : currentCardIndex - 1;

    setIsTransitioning(true);
    setCurrentCardIndex(newIndex);

    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: newIndex * cardWidth,
        animated: true
      });
    }

    // Clear any existing timeout
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Delay the information update to sync with scroll animation
    transitionTimeoutRef.current = setTimeout(() => {
      setDisplayedCardIndex(newIndex);
      setIsTransitioning(false);
    }, TRANSITION_DELAY);
  };

  const handleNextCard = () => {
    // Prevent rapid successive clicks
    if (isTransitioning) return;

    const newIndex =
      currentCardIndex === cardsToDisplay.length - 1 ? 0 : currentCardIndex + 1;

    setIsTransitioning(true);
    setCurrentCardIndex(newIndex);

    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: newIndex * cardWidth,
        animated: true
      });
    }

    // Clear any existing timeout
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    // Delay the information update to sync with scroll animation
    transitionTimeoutRef.current = setTimeout(() => {
      setDisplayedCardIndex(newIndex);
      setIsTransitioning(false);
    }, TRANSITION_DELAY);
  };

  return (
    <CreditCardErrorBoundary
      onError={(error) => {
        handleCreditCardError(error, {
          context: {operation: "read"},
          logError: true
        });
      }}
    >
      <ScreenWithHeader
        screenTitle={t("wallet.title", "Sua carteira")}
        backButton
        rightHeaderChild={
          <TouchableOpacity
            style={[
              styles.addCardButton,
              {
                zIndex: 10,
                position: "relative"
              }
            ]}
            onPress={handleAddCard}
            activeOpacity={0.7}
          >
            <Text style={styles.addCardButtonText}>
              {t("wallet.addCard", "Ad. cartão")}
            </Text>
            <PlusIcon width={20} height={20} />
          </TouchableOpacity>
        }
      >
        <View style={styles.container}>
          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 0 && styles.tabButtonActive
              ]}
              onPress={() => setActiveTab(0)}
            >
              <Text
                style={[
                  styles.tabButtonText,
                  activeTab === 0 && styles.tabButtonTextActive
                ]}
              >
                {t("wallet.memberCard", "Cartão de associado")}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 1 && styles.tabButtonActive
              ]}
              onPress={() => setActiveTab(1)}
            >
              <Text
                style={[
                  styles.tabButtonText,
                  activeTab === 1 && styles.tabButtonTextActive
                ]}
              >
                {t("wallet.yourCards", "Seus cartões")}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content Container */}
          <View style={{flex: 1}}>
            {/* Card Carousel or Empty State */}
            {(activeTab === 1 && !hasCreditCards) ||
            (activeTab === 0 && !hasMemberCards) ? (
              // Show empty state when active tab has no cards
              <CreditCardEmptyState onAddCard={handleAddCard} />
            ) : (
              <View style={styles.carouselContainer}>
                <ScrollView
                  ref={scrollViewRef}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.scrollViewContent}
                  style={styles.scrollView}
                  snapToInterval={cardWidth}
                  decelerationRate="fast"
                  snapToAlignment="center"
                  onScroll={handleScroll}
                  scrollEventThrottle={16}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                  contentOffset={{
                    x: currentCardIndex * cardWidth,
                    y: 0
                  }}
                >
                  {cardsToDisplay.map((card, index) => (
                    <TouchableOpacity
                      key={`${card.id}-${index}`}
                      style={[
                        styles.cardContainer,
                        index === 0 && styles.firstCard,
                        index === cardsToDisplay.length - 1 && styles.lastCard
                      ]}
                      onPress={() => {
                        // Prevent action if already transitioning
                        if (isTransitioning) return;

                        setIsTransitioning(true);
                        setCurrentCardIndex(index);
                        scrollViewRef.current?.scrollTo({
                          x: index * cardWidth,
                          animated: true
                        });

                        // Clear any existing timeout
                        if (transitionTimeoutRef.current) {
                          clearTimeout(transitionTimeoutRef.current);
                        }

                        // Delay the information update to sync with scroll animation
                        transitionTimeoutRef.current = setTimeout(() => {
                          setDisplayedCardIndex(index);
                          setIsTransitioning(false);
                        }, TRANSITION_DELAY);
                      }}
                    >
                      {card.cardType === "member" ? (
                        <UserProfileCard />
                      ) : (
                        <CreditCard
                          cardType={card.cardType}
                          cardNumber={card.cardNumber}
                          holderName={card.name}
                          expiryDate={card.expiryDate ?? ""}
                        />
                      )}
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                {/* Navigation */}
                <View style={styles.navigationContainer}>
                  <TouchableOpacity
                    style={[
                      styles.navigationButton,
                      isTransitioning && styles.navigationButtonDisabled
                    ]}
                    onPress={handlePrevCard}
                    disabled={isTransitioning}
                  >
                    <ChevronLeftIcon width={24} height={24} />
                  </TouchableOpacity>

                  <View style={styles.paginationWrapper}>
                    {cardsToDisplay.map((card, index) => (
                      <View
                        key={`dot-${card.id}`}
                        style={[
                          styles.paginationDot,
                          index === displayedCardIndex &&
                            styles.paginationIndicator
                        ]}
                      />
                    ))}
                  </View>

                  <TouchableOpacity
                    style={[
                      styles.navigationButton,
                      isTransitioning && styles.navigationButtonDisabled
                    ]}
                    onPress={handleNextCard}
                    disabled={isTransitioning}
                  >
                    <ChevronRightIcon width={24} height={24} />
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Card Information Section - Only show when we have cards and not in empty state */}
            {!(
              (activeTab === 1 && !hasCreditCards) ||
              (activeTab === 0 && !hasMemberCards)
            ) &&
              currentCard && (
                <View style={styles.cardInfoContainer}>
                  <View style={styles.cardInfoHeader}>
                    <Text style={styles.cardInfoTitle}>
                      {t("wallet.cardInfo", "Informações do cartão")}
                    </Text>
                    <TouchableOpacity
                      style={styles.toggleButton}
                      onPress={() => setShowCardInfo(!showCardInfo)}
                    >
                      {showCardInfo ? (
                        <DisableEyeIcon width={20} height={20} />
                      ) : (
                        <EyeIcon width={20} height={20} />
                      )}
                      <Text style={styles.toggleButtonText}>
                        {showCardInfo
                          ? t("wallet.hide", "Esconder")
                          : t("wallet.show", "Mostrar")}
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <View style={styles.cardInfoContent}>
                    <View style={styles.infoRowAlternate}>
                      <Text style={[styles.infoLabel, styles.infoLabelName]}>
                        {activeTab === 1
                          ? t("wallet.cardName", "Nome do cartão")
                          : t("wallet.memberName", "Nome do associado")}
                      </Text>
                      <Text style={[styles.infoValue, styles.infoValueName]}>
                        {maskInformation(currentCard.name, showCardInfo)}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text
                        style={[styles.infoLabel, styles.infoLabelFederation]}
                      >
                        {activeTab === 1
                          ? t("wallet.cardNumber", "Número do cartão")
                          : t("wallet.federationUnit", "Unidade Federativa")}
                      </Text>
                      <Text
                        style={[styles.infoValue, styles.infoValueFederation]}
                      >
                        {maskInformation(
                          activeTab === 1
                            ? currentCard.cardNumber || "••••••••••••••••"
                            : currentCard.federationUnit,
                          showCardInfo
                        )}
                      </Text>
                    </View>

                    <View style={styles.infoRowAlternate}>
                      <Text
                        style={[styles.infoLabel, styles.infoLabelAssociation]}
                      >
                        {activeTab === 1
                          ? t("wallet.expiryDate", "Expira em")
                          : t(
                              "wallet.associationUnit",
                              "Unidade de Associação"
                            )}
                      </Text>
                      <Text
                        style={[styles.infoValue, styles.infoValueAssociation]}
                      >
                        {maskInformation(
                          activeTab === 1
                            ? currentCard.expiryDate || "••/••••"
                            : currentCard.associationUnit,
                          showCardInfo
                        )}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text style={[styles.infoLabel, styles.infoLabelActive]}>
                        {activeTab === 1
                          ? t("wallet.cvv", "CVV (Código de segurança)")
                          : t("wallet.activeSince", "Membro ativo desde")}
                      </Text>
                      <Text style={[styles.infoValue, styles.infoValueActive]}>
                        {maskInformation(
                          activeTab === 1 ? "•••" : currentCard.activeSince,
                          showCardInfo
                        )}
                      </Text>
                    </View>

                    {/* Action Buttons - Only show when "Seus cartões" tab is active */}
                    {activeTab === 1 && (
                      <View style={styles.actionButtonsContainer}>
                        <TouchableOpacity
                          style={styles.editButton}
                          onPress={handleEditCard}
                        >
                          <Text style={styles.editButtonText}>
                            {t("wallet.editCard", "Editar cartão")}
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={styles.deleteButton}
                          onPress={handleDeleteCard}
                        >
                          <Text style={styles.deleteButtonText}>
                            {t("wallet.deleteCard", "Excluir cartão")}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              )}
          </View>
        </View>
      </ScreenWithHeader>
    </CreditCardErrorBoundary>
  );
};

export default Wallet;
