import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image
} from "react-native";
import ScreenWithHeader from "../../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import SearchIcon from "../../../components/icons/search-icon";
import DiscountIcon from "../../../components/icons/discount-icon";
import GiftIcon from "../../../components/icons/gift-icon";
import VipAccessIcon from "../../../components/icons/vip-access-icon";
import GastronomyIcon from "../../../components/icons/gastronomy-icon";
import HealthIcon from "../../../components/icons/health-icon";
import styles from "@/styles/partners/index.style";

interface Partner {
  id: string;
  name: string;
  logo: string | number | null;
  category: string;
}

const PartnersScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = [
    {
      id: "descontos",
      name: "Descontos",
      icon: <DiscountIcon width={25} height={25} />
    },
    {
      id: "brindes",
      name: "Brindes",
      icon: <GiftIcon width={25} height={25} />
    },
    {
      id: "acessos-vip",
      name: "Acessos VIP",
      icon: <VipAccessIcon width={25} height={25} />
    },
    {
      id: "gastronomia",
      name: "Gastronomia",
      icon: <GastronomyIcon width={25} height={25} />
    },
    {
      id: "saude",
      name: "Saúde",
      icon: <HealthIcon width={25} height={25} />
    }
  ];

  const partners: Partner[] = [
    {
      id: "1",
      name: "7Club",
      logo: require("../../../assets/images/7club.png"),
      category: "descontos"
    },
    {
      id: "2",
      name: "Sheep",
      logo: require("../../../assets/images/sheep.png"),
      category: "brindes"
    },
    {
      id: "3",
      name: "Grupo Belga",
      logo: require("../../../assets/images/grupAllita.png"),
      category: "acessos-vip"
    },
    {
      id: "4",
      name: "Altimas",
      logo: require("../../../assets/images/cafeteriaGaribaldi.png"),
      category: "gastronomia"
    },
    {
      id: "5",
      name: "Aquarius",
      logo: require("../../../assets/images/aquarius.png"),
      category: "saude"
    },
    {
      id: "8",
      name: "MCMII",
      logo: require("../../../assets/images/arbitralis.png"),
      category: "brindes"
    },
    {
      id: "7",
      name: "Anamnese",
      logo: require("../../../assets/images/atma.png"),
      category: "saude"
    },
    {
      id: "6",
      name: "Alfar",
      logo: require("../../../assets/images/arqcit.png"),
      category: "descontos"
    },
    {
      id: "9",
      name: "ATAAA",
      logo: require("../../../assets/images/aspas.png"),
      category: "acessos-vip"
    },
    {
      id: "10",
      name: "TG",
      logo: require("../../../assets/images/buenoGontijo.png"),
      category: "gastronomia"
    },
    {
      id: "11",
      name: "Altimas",
      logo: ,
      category: "gastronomia"
    }
  ];

  const filteredPartners = partners.filter((partner) => {
    const matchesSearch = partner.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === null || partner.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePartnerPress = (partnerId: string) => {
    router.push(`/(main)/partners/${partnerId}`);
  };

  const renderPartnerCard = (partner: Partner) => (
    <TouchableOpacity
      key={partner.id}
      style={styles.partnerCard}
      onPress={() => handlePartnerPress(partner.id)}
    >
      <View style={styles.partnerLogoContainer}>
        {partner.logo ? (
          <Image
            source={
              typeof partner.logo === "string"
                ? {uri: partner.logo}
                : partner.logo
            }
            style={styles.partnerLogo}
            resizeMode="contain"
          />
        ) : (
          <Text style={styles.partnerName}>{partner.name}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <ScreenWithHeader
      screenTitle={t("partners.title", "Parceiros Club M")}
      backButton
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder={t(
                "partners.searchPlaceholder",
                "Buscar parceiro, benefício, etc..."
              )}
              placeholderTextColor="#F2F4F7"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <Text style={styles.categoriesTitle}>
            {t("partners.categories", "Categorias")}
          </Text>
        </View>

        {/* Category Icons */}
        <View style={styles.categoryIconsContainer}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={styles.categoryIconItem}
              onPress={() =>
                setSelectedCategory(
                  selectedCategory === category.id ? null : category.id
                )
              }
            >
              <View style={styles.categoryIconWrapper}>{category.icon}</View>
              <Text style={styles.categoryIconText}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Partners Grid */}
        <View style={styles.partnersGrid}>
          {Array.from(
            {length: Math.ceil(filteredPartners.length / 2)},
            (_, rowIndex) => (
              <View key={rowIndex} style={styles.partnersRow}>
                {filteredPartners
                  .slice(rowIndex * 2, rowIndex * 2 + 2)
                  .map(renderPartnerCard)}
              </View>
            )
          )}
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PartnersScreen;
