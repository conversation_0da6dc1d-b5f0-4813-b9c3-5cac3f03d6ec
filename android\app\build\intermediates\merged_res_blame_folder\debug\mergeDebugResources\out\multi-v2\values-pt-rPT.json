{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,16017", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,16098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3396,3477,3557,3639,3738,4566,4669,4789,7617,7677,7741,10230,10462,10597,10687,10751,10819,10881,10954,11018,11072,11198,11256,11318,11372,11448,11749,11836,11916,12015,12101,12183,12322,12404,12486,12622,12709,12789,12845,12896,12962,13037,13117,13188,13267,13340,13417,13486,13560,13667,13760,13837,13930,14028,14102,14183,14282,14335,14419,14485,14574,14662,14724,14788,14851,14919,15035,15143,15250,15352,15412,15467,16103,16186,16265", "endLines": "5,36,37,38,39,40,48,49,50,73,74,75,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3472,3552,3634,3733,3829,4664,4784,4865,7672,7736,7828,10304,10522,10682,10746,10814,10876,10949,11013,11067,11193,11251,11313,11367,11443,11586,11831,11911,12010,12096,12178,12317,12399,12481,12617,12704,12784,12840,12891,12957,13032,13112,13183,13262,13335,13412,13481,13555,13662,13755,13832,13925,14023,14097,14178,14277,14330,14414,14480,14569,14657,14719,14783,14846,14914,15030,15138,15245,15347,15407,15462,15548,16181,16260,16339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4956,5061,5224,5352,5460,5628,5756,5878,6132,6320,6428,6598,6729,6888,7066,7134,7203", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "5056,5219,5347,5455,5623,5751,5873,5982,6315,6423,6593,6724,6883,7061,7129,7198,7285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,328,444,539,685,794,915,1044,1183,1330,1465,1594,1741,1843,2008,2136,2276,2425,2551,2680,2779,2915,3005,3132,3241,3387", "endColumns": "154,117,115,94,145,108,120,128,138,146,134,128,146,101,164,127,139,148,125,128,98,135,89,126,108,145,107", "endOffsets": "205,323,439,534,680,789,910,1039,1178,1325,1460,1589,1736,1838,2003,2131,2271,2420,2546,2675,2774,2910,3000,3127,3236,3382,3490"}, "to": {"startLines": "34,35,70,72,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,164,183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3123,3278,7290,7522,7833,7979,8402,8523,8652,8791,8938,9073,9202,9349,9451,9616,9744,9884,10033,15799,17405,17504,17640,17730,17857,17966,18112", "endColumns": "154,117,115,94,145,108,120,128,138,146,134,128,146,101,164,127,139,148,125,128,98,135,89,126,108,145,107", "endOffsets": "3273,3391,7401,7612,7974,8083,8518,8647,8786,8933,9068,9197,9344,9446,9611,9739,9879,10028,10154,15923,17499,17635,17725,17852,17961,18107,18215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "41,42,43,44,45,46,47,178", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3834,3931,4033,4132,4232,4339,4445,16984", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3926,4028,4127,4227,4334,4440,4561,17080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5987", "endColumns": "144", "endOffsets": "6127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "71,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7406,8088,8187,8299", "endColumns": "115,98,111,102", "endOffsets": "7517,8182,8294,8397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,351,434,504,583,662,750,834,908,997,1081,1157,1238,1320,1395,1473,1547,1637,1709,1795,1871", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "119,205,276,346,429,499,578,657,745,829,903,992,1076,1152,1233,1315,1390,1468,1542,1632,1704,1790,1866,1952"}, "to": {"startLines": "33,51,94,96,97,99,113,114,161,162,163,165,170,171,172,173,174,175,176,177,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,4870,10159,10309,10379,10527,11591,11670,15553,15641,15725,15928,16344,16428,16504,16585,16667,16742,16820,16894,17085,17157,17243,17319", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "3118,4951,10225,10374,10457,10592,11665,11744,15636,15720,15794,16012,16423,16499,16580,16662,16737,16815,16889,16979,17152,17238,17314,17400"}}]}]}