import React, {useState, useMemo, useCallback, memo} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Alert
} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";
import EditIcon from "../../components/icons/edit-icon";
import LoadingSpinner from "../../components/loading-spinner";
import OpportunitiesEmptyState from "../../components/opportunities/opportunities-empty-state";
import AnimatedToggle from "../../components/animated-toggle";
import {
  useUserOpportunities,
  useUpdateOpportunityStatus
} from "../../hooks/api/use-opportunities";
import {
  SafeOpportunityViewModel,
  OpportunityStatus as ApiOpportunityStatus
} from "../../services/api/opportunities/opportunities.service";
import {useAuth} from "../../contexts/AuthContext";
import styles from "@/styles/settings/my-opportunities.style";

interface OpportunityStatus {
  type: "active" | "inactive" | "closed" | "pending" | "rejected" | "unknown";
  label: string;
  color: string;
}

interface ProcessedOpportunity {
  id: number;
  title: string;
  description: string;
  status: OpportunityStatus;
  isToggleEnabled: boolean;
  isActive: boolean; // true = active (Approved), false = inactive (Inactive)
  originalData: SafeOpportunityViewModel;
}

// Separate toggle component to prevent unnecessary re-renders
interface OpportunityToggleProps {
  opportunityId: number;
  isActive: boolean;
  isToggleEnabled: boolean;
  isPending: boolean;
  onToggle: (id: number) => void;
}

const OpportunityToggle = memo(
  ({
    opportunityId,
    isActive,
    isToggleEnabled,
    isPending,
    onToggle
  }: OpportunityToggleProps) => {
    return (
      <AnimatedToggle
        isEnabled={isActive}
        onToggle={() => onToggle(opportunityId)}
        disabled={!isToggleEnabled || isPending}
        size="medium"
        activeColor="#1F9464"
        inactiveColor="#CFD4DC"
        disabledColor="#475467"
      />
    );
  }
);

const MyOpportunitiesScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"all" | "active" | "inactive">(
    "all"
  );

  // Authentication context
  const {user} = useAuth();

  // Fetch all user opportunities (no status filter to get all)
  const {
    data: opportunitiesData,
    isLoading,
    error,
    refetch,
    isRefetching
  } = useUserOpportunities({
    page: 1,
    pageSize: 50
  });

  // Hook for updating opportunity status
  const updateOpportunityStatusMutation = useUpdateOpportunityStatus();

  // Track which specific opportunity is being toggled
  const [pendingToggleId, setPendingToggleId] = useState<number | null>(null);

  // Process opportunities data to match UI requirements
  const processedOpportunities = useMemo((): ProcessedOpportunity[] => {
    if (!opportunitiesData?.data) return [];

    console.log("🔄 [UI-PROCESSING] Processing opportunities data...");
    console.log(
      "📊 [UI-PROCESSING] Raw data count:",
      opportunitiesData.data.length
    );

    const processed = opportunitiesData.data.map(
      (opportunity: SafeOpportunityViewModel) => {
        // Use the status field directly from the backend
        const isActive = opportunity.status === ApiOpportunityStatus.Approved;

        // Toggle is only enabled for Approved or Inactive status
        const isToggleEnabled =
          opportunity.status === ApiOpportunityStatus.Approved ||
          opportunity.status === ApiOpportunityStatus.Inactive;

        // Log each opportunity processing for debugging
        console.log(`🔍 [UI-PROCESSING] Opportunity ${opportunity.id}:`, {
          title: opportunity.title.substring(0, 30) + "...",
          backendStatus: opportunity.status,
          isApproved: opportunity.status === ApiOpportunityStatus.Approved,
          isInactive: opportunity.status === ApiOpportunityStatus.Inactive,
          isPendingPayment:
            opportunity.status === ApiOpportunityStatus.PendingPayment,
          isPendingModeration:
            opportunity.status === ApiOpportunityStatus.PendingModeration,
          isRejected: opportunity.status === ApiOpportunityStatus.Rejected,
          calculatedIsActive: isActive,
          toggleEnabled: isToggleEnabled
        });

        // Determine status display based on the backend status field
        let status: OpportunityStatus;

        switch (opportunity.status) {
          case ApiOpportunityStatus.Approved:
            status = {
              type: "active",
              label: "Oportunidade Ativa",
              color: "#47CD89"
            };
            break;
          case ApiOpportunityStatus.Inactive:
            status = {
              type: "inactive",
              label: "Oportunidade Inativa",
              color: "#FEC84B"
            };
            break;
          case ApiOpportunityStatus.PendingPayment:
            status = {
              type: "pending",
              label: "Aguardando Pagamento",
              color: "#F59E0B"
            };
            break;
          case ApiOpportunityStatus.PendingModeration:
            status = {
              type: "pending",
              label: "Aguardando Moderação",
              color: "#3B82F6"
            };
            break;
          case ApiOpportunityStatus.Rejected:
            status = {
              type: "rejected",
              label: "Rejeitada",
              color: "#EF4444"
            };
            break;
          default:
            status = {
              type: "unknown",
              label: "Status Desconhecido",
              color: "#6B7280"
            };
        }

        return {
          id: opportunity.id,
          title: opportunity.title,
          description: opportunity.description,
          status,
          isToggleEnabled,
          isActive,
          originalData: opportunity
        };
      }
    );

    // Log final processing results
    const activeCount = processed.filter((p) => p.isActive).length;
    const inactiveCount = processed.filter((p) => !p.isActive).length;

    console.log("📈 [UI-PROCESSING] Final results:", {
      total: processed.length,
      active: activeCount,
      inactive: inactiveCount,
      Approved: ApiOpportunityStatus.Approved,
      Inactive: ApiOpportunityStatus.Inactive,
      PendingPayment: ApiOpportunityStatus.PendingPayment,
      PendingModeration: ApiOpportunityStatus.PendingModeration,
      Rejected: ApiOpportunityStatus.Rejected
    });

    return processed;
  }, [opportunitiesData]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  const handleEditOpportunity = useCallback(
    (opportunityId: number) => {
      // Find the opportunity to check ownership
      const opportunity = processedOpportunities.find(
        (opp) => opp.id === opportunityId
      );

      if (!opportunity) {
        console.error("Opportunity not found:", opportunityId);
        return;
      }

      // Check if current user owns this opportunity
      if (user && opportunity.originalData.user.id !== parseInt(user.id)) {
        console.warn(
          "🚫 [PERMISSION-CHECK] User does not own this opportunity",
          {
            currentUserId: user.id,
            opportunityOwnerId: opportunity.originalData.user.id,
            opportunityId: opportunityId
          }
        );

        // Show error message
        Alert.alert(
          "Permissão negada",
          "Você não tem permissão para editar esta oportunidade.",
          [{text: "OK"}]
        );
        return;
      }

      // Navigate to create-opportunity screen in edit mode
      router.push(
        `/(business)/create-opportunity?opportunityId=${opportunityId}`
      );
    },
    [router, user, processedOpportunities]
  );

  const handleToggleOpportunity = useCallback(
    async (opportunityId: number) => {
      try {
        // Set this specific opportunity as pending
        setPendingToggleId(opportunityId);

        // Find the opportunity to get current state
        const opportunity = processedOpportunities.find(
          (opp) => opp.id === opportunityId
        );
        if (!opportunity) {
          console.error(`❌ [TOGGLE-${opportunityId}] Opportunity not found`);
          setPendingToggleId(null);
          return;
        }

        const currentIsActive = opportunity.isActive;
        const newIsActive = !currentIsActive;

        console.log(`🔄 [TOGGLE-${opportunityId}] Toggling:`, {
          from: currentIsActive ? "active" : "inactive",
          to: newIsActive ? "active" : "inactive",
          currentBackendStatus: opportunity.originalData.status,
          currentIsActive,
          newIsActive
        });

        // Determine API status based on new state
        const newStatus: ApiOpportunityStatus = newIsActive
          ? ApiOpportunityStatus.Approved // Active
          : ApiOpportunityStatus.Inactive; // Inactive

        console.log(`📤 [TOGGLE-${opportunityId}] Sending to API:`, {
          newStatus,
          statusName:
            newStatus === ApiOpportunityStatus.Approved
              ? "Approved"
              : "Inactive",
          statusValue: newStatus,
          expectedEndpoint:
            newStatus === ApiOpportunityStatus.Approved
              ? "/active"
              : "/inactive"
        });

        // Update opportunity status via API
        await updateOpportunityStatusMutation.mutateAsync({
          id: opportunityId,
          data: {status: newStatus}
        });

        console.log(
          `✅ [TOGGLE-${opportunityId}] Successfully updated to:`,
          newIsActive ? "Active" : "Inactive"
        );

        // Note: useUpdateOpportunityStatus hook automatically invalidates queries
        // No need to manually refetch - React Query will update the cache automatically
      } catch (error: any) {
        console.error(`❌ [TOGGLE-${opportunityId}] Error:`, error);

        // Show error message to user
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Erro desconhecido";

        if (error?.status === 400) {
          if (
            errorMessage.includes("já ativa") ||
            errorMessage.includes("already active")
          ) {
            Alert.alert("Aviso", "Esta oportunidade já está ativa.");
          } else if (
            errorMessage.includes("já inativa") ||
            errorMessage.includes("already inactive")
          ) {
            Alert.alert("Aviso", "Esta oportunidade já está inativa.");
          } else {
            Alert.alert("Erro", `Erro ao alterar status: ${errorMessage}`);
          }
        } else if (error?.status === 403) {
          Alert.alert(
            "Erro",
            "Você não tem permissão para alterar o status desta oportunidade."
          );
        } else if (error?.status === 404) {
          Alert.alert("Erro", "Oportunidade não encontrada.");
        } else {
          Alert.alert("Erro", `Erro ao alterar status: ${errorMessage}`);
        }
      } finally {
        // Always clear the pending state
        setPendingToggleId(null);
      }
    },
    [
      processedOpportunities,
      updateOpportunityStatusMutation,
      setPendingToggleId
    ]
  );

  const handleOpportunityPress = useCallback(
    (opportunityId: number) => {
      // Navigate to opportunity details screen
      router.push(`/(business)/opportunities/${opportunityId}`);
    },
    [router]
  );

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleCreateOpportunity = useCallback(() => {
    // Navigate to create opportunity screen
    router.push("/(business)/create-opportunity");
  }, [router]);

  const getTabCounts = useCallback(() => {
    const activeCount = processedOpportunities.filter(
      (op) => op.isActive === true
    ).length;
    const inactiveCount = processedOpportunities.filter(
      (op) => op.isActive === false
    ).length;
    return {
      all: processedOpportunities.length,
      active: activeCount,
      inactive: inactiveCount
    };
  }, [processedOpportunities]);

  const filteredOpportunities = useMemo(() => {
    return processedOpportunities.filter((opportunity) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return opportunity.isActive === true;
      if (activeTab === "inactive") return opportunity.isActive === false;
      return true;
    });
  }, [processedOpportunities, activeTab]);

  const tabCounts = useMemo(() => getTabCounts(), [getTabCounts]);

  const renderToggle = useCallback(
    (opportunity: ProcessedOpportunity) => {
      return (
        <OpportunityToggle
          opportunityId={opportunity.id}
          isActive={opportunity.isActive}
          isToggleEnabled={opportunity.isToggleEnabled}
          isPending={pendingToggleId === opportunity.id}
          onToggle={handleToggleOpportunity}
        />
      );
    },
    [handleToggleOpportunity, pendingToggleId]
  );

  const renderOpportunityCard = useCallback(
    (opportunity: ProcessedOpportunity) => (
      <>
        <TouchableOpacity
          style={styles.opportunityHeader}
          onPress={() => handleOpportunityPress(opportunity.id)}
        >
          <View style={styles.opportunityIcon} />
          <Text style={styles.opportunityTitle} numberOfLines={2}>
            {opportunity.title}
          </Text>
          <ChevronRightIcon width={24} height={24} />
        </TouchableOpacity>

        <View style={styles.opportunityDescription}>
          <Text style={styles.opportunityDescriptionText} numberOfLines={3}>
            {opportunity.description}
          </Text>
        </View>

        <View style={styles.opportunityActions}>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleEditOpportunity(opportunity.id)}
          >
            <EditIcon width={20} height={20} />
            <Text style={styles.editButtonText}>Editar</Text>
          </TouchableOpacity>

          <Text style={[styles.statusText, {color: opportunity.status.color}]}>
            {opportunity.status.label}
          </Text>

          {renderToggle(opportunity)}
        </View>
      </>
    ),
    [handleOpportunityPress, handleEditOpportunity, renderToggle]
  );

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#111828" />

        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Minhas oportunidades</Text>
        </View>

        <View
          style={[
            styles.scrollView,
            {justifyContent: "center", alignItems: "center", flex: 1}
          ]}
        >
          <LoadingSpinner size="large" color="#DFE9F0" />
          <Text
            style={[
              styles.opportunityDescriptionText,
              {textAlign: "center", marginTop: 16}
            ]}
          >
            {t("myOpportunities.loading", "Carregando suas oportunidades...")}
          </Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#111828" />

        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Minhas oportunidades</Text>
        </View>

        <View
          style={[
            styles.scrollView,
            {
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
              padding: 24
            }
          ]}
        >
          <Text
            style={[
              styles.opportunityTitle,
              {textAlign: "center", marginBottom: 8}
            ]}
          >
            {t("myOpportunities.errorTitle", "Erro ao carregar oportunidades")}
          </Text>
          <Text
            style={[
              styles.opportunityDescriptionText,
              {textAlign: "center", marginBottom: 24}
            ]}
          >
            {t(
              "myOpportunities.errorDescription",
              "Não foi possível carregar suas oportunidades. Tente novamente."
            )}
          </Text>
          <TouchableOpacity
            style={[
              styles.editButton,
              {
                backgroundColor: "#47CD89",
                paddingHorizontal: 24,
                paddingVertical: 12,
                borderRadius: 8
              }
            ]}
            onPress={handleRefresh}
          >
            <Text style={styles.editButtonText}>
              {t("common.tryAgain", "Tentar novamente")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#111828" />

      {/* Custom Header */}
      <View style={styles.customHeader}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Minhas oportunidades</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "all" && styles.activeTab]}
          onPress={() => setActiveTab("all")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "all" && styles.activeTabText
            ]}
          >
            Todas ({tabCounts.all})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "active" && styles.activeTab]}
          onPress={() => setActiveTab("active")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "active" && styles.activeTabText
            ]}
          >
            Ativas ({tabCounts.active})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "inactive" && styles.activeTab]}
          onPress={() => setActiveTab("inactive")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "inactive" && styles.activeTabText
            ]}
          >
            Inativas ({tabCounts.inactive})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Opportunities List */}
      {filteredOpportunities.length === 0 ? (
        <OpportunitiesEmptyState
          activeTab={activeTab}
          onCreateOpportunity={handleCreateOpportunity}
        />
      ) : (
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
          refreshControl={
            <RefreshControl
              refreshing={isRefetching}
              onRefresh={handleRefresh}
              tintColor="#DFE9F0"
              colors={["#DFE9F0"]}
            />
          }
        >
          {filteredOpportunities.map((opportunity) => (
            <View key={opportunity.id} style={styles.opportunityCard}>
              {renderOpportunityCard(opportunity)}
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
};

export default MyOpportunitiesScreen;
