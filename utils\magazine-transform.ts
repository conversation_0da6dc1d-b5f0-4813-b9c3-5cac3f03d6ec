/**
 * Magazine Data Transformation Utilities
 * Transforms API magazine data to match UI component expectations
 */

import {Magazine, MagazineEdition} from "../models/api/magazines.models";

/**
 * Raw edition data structure from API
 */
export interface RawEditionData {
  id: number;
  title: string;
  description: string;
  releaseDate: string;
  fileId: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

/**
 * Raw magazine data structure from API
 */
export interface RawMagazineData {
  id: number;
  title: string;
  description: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  disabledAt?: string;
  disabledBy?: string;
  status: number;
  franchiseId: number;
  franchise: {
    id: number;
    name: string;
    addressCity: string;
    addressState: string;
    // ... other franchise properties
  };
  editions: RawEditionData[];
}

/**
 * Logs detailed edition information
 */
export function logEditionsDetails(rawMagazine: RawMagazineData): void {
  if (rawMagazine.editions && rawMagazine.editions.length > 0) {
    console.log(
      `📖 [MAGAZINE-TRANSFORM] Edi<PERSON><PERSON><PERSON> da revista "${rawMagazine.title}" (ID: ${rawMagazine.id}):`
    );
    rawMagazine.editions.forEach((edition, index) => {
      console.log(`  📄 Edição ${index + 1}:`, {
        id: edition.id,
        title: edition.title,
        description: edition.description?.substring(0, 50) + "...",
        releaseDate: edition.releaseDate,
        fileId: edition.fileId,
        createdAt: edition.createdAt,
        createdBy: edition.createdBy
      });
    });
  } else {
    console.log(
      `📖 [MAGAZINE-TRANSFORM] Revista "${rawMagazine.title}" não possui edições`
    );
  }
}

/**
 * Transforms API RawMagazineData to UI Magazine format
 */
export function transformRawMagazineToMagazine(
  rawMagazine: RawMagazineData,
  index: number = 0
): Magazine {
  // Log detailed edition information
  logEditionsDetails(rawMagazine);
  // Generate placeholder data for missing fields
  const placeholderImages = [
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop",
    "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=600&fit=crop",
    "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=600&fit=crop",
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop",
    "https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=400&h=600&fit=crop"
  ];

  const categories = [
    "Negócios",
    "Liderança",
    "Inovação",
    "Tecnologia",
    "Mercado"
  ];

  return {
    id: rawMagazine.id.toString(),
    title: rawMagazine.title,
    description: rawMagazine.description,
    shortDescription: rawMagazine.description.substring(0, 100) + "...",
    coverImage: placeholderImages[index % placeholderImages.length],
    publishDate: rawMagazine.createdAt,
    category: categories[index % categories.length],
    readTime: "5 min",
    isNew: index < 2, // First 2 magazines are "new"
    isPremium: false,
    author: rawMagazine.createdBy,
    authorTitle: "Editor",
    content: rawMagazine.description,
    tags: [categories[index % categories.length]],
    isActive: rawMagazine.status === 1,
    isFeatured: index < 3, // First 3 magazines are "featured"
    createdAt: rawMagazine.createdAt,
    updatedAt: rawMagazine.updatedAt,
    fileId: `magazine-file-${rawMagazine.id}`, // ID fictício para teste (deprecated)
    editions:
      rawMagazine.editions?.map(
        (edition): MagazineEdition => ({
          id: edition.id.toString(),
          title: edition.title,
          description: edition.description,
          releaseDate: edition.releaseDate,
          fileId: edition.fileId,
          createdAt: edition.createdAt,
          createdBy: edition.createdBy,
          updatedAt: edition.updatedAt,
          updatedBy: edition.updatedBy
        })
      ) || [],
    metadata: {
      franchiseId: rawMagazine.franchiseId,
      franchiseName: rawMagazine.franchise?.name,
      originalStatus: rawMagazine.status
    }
  };
}

/**
 * Transforms array of API RawMagazineData to UI Magazines
 */
export function transformRawMagazinesToMagazines(
  rawMagazines: RawMagazineData[]
): Magazine[] {
  return rawMagazines.map((rawMagazine, index) =>
    transformRawMagazineToMagazine(rawMagazine, index)
  );
}

/**
 * Formats date for display
 */
export function formatMagazineDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "short",
    year: "numeric"
  });
}
