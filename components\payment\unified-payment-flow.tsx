/**
 * Componente de fluxo de pagamento unificado
 * Gerencia o fluxo completo de pagamento para eventos, produtos e oportunidades
 */

import React, {useState, useCallback, useMemo} from "react";
import {View, ScrollView, Alert} from "react-native";
import {useTranslation} from "react-i18next";
import {router} from "expo-router";

import {
  PaymentEntity,
  PaymentType,
  CreatePaymentRequest,
  CreditCard
} from "@/models/api/payments.models";

import PaymentMethodSelector from "./payment-method-selector";
import PaymentSummary, {PaymentSummaryItem} from "./payment-summary";
import CreditCardForm from "./credit-card-form";
import FullSizeButton from "@/components/full-size-button";

import {
  useCreatePayment,
  useCreditCards,
  useCreateCreditCard
} from "@/hooks/api/use-payments";

import styles from "@/styles/components/payment/unified-payment-flow.style";

export interface UnifiedPaymentFlowProps {
  // Dados do item sendo comprado
  entityId: number;
  entityType: PaymentEntity;
  entityTitle: string;
  entityDescription?: string;
  entityImageUrl?: string;
  amount: number;

  // Configurações opcionais
  allowInstallments?: boolean;
  maxInstallments?: number;
  showSavedCards?: boolean;

  // Callbacks
  onPaymentSuccess?: (paymentId: string) => void;
  onPaymentError?: (error: string) => void;
  onCancel?: () => void;
}

type PaymentStep = "method" | "details" | "processing" | "success" | "error";

const UnifiedPaymentFlow: React.FC<UnifiedPaymentFlowProps> = ({
  entityId,
  entityType,
  entityTitle,
  entityDescription,
  entityImageUrl,
  amount,
  allowInstallments = true,
  maxInstallments = 12,
  showSavedCards = true,
  onPaymentSuccess,
  onPaymentError,
  onCancel
}) => {
  const {t} = useTranslation();

  // Estados do fluxo
  const [currentStep, setCurrentStep] = useState<PaymentStep>("method");
  const [selectedMethod, setSelectedMethod] = useState<PaymentType>();
  const [selectedSavedCard, setSelectedSavedCard] = useState<CreditCard>();
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const [installments, setInstallments] = useState(1);

  // Hooks de API
  const {data: creditCards = [], isLoading: isLoadingCreditCards} =
    useCreditCards();

  const createPaymentMutation = useCreatePayment();
  const createCreditCardMutation = useCreateCreditCard();

  // Dados do item para o resumo
  const summaryItem: PaymentSummaryItem = useMemo(
    () => ({
      id: entityId.toString(),
      title: entityTitle,
      description: entityDescription,
      imageUrl: entityImageUrl,
      price: amount,
      quantity: 1,
      entity: entityType
    }),
    [
      entityId,
      entityTitle,
      entityDescription,
      entityImageUrl,
      amount,
      entityType
    ]
  );

  // Handlers
  const handleMethodSelect = useCallback(
    (method: PaymentType, preserveSelectedCard?: boolean) => {
      console.log("🔄 [UNIFIED-PAYMENT] Método selecionado:", method);
      setSelectedMethod(method);

      if (!preserveSelectedCard && method !== PaymentType.CreditCard) {
        setSelectedSavedCard(undefined);
      }

      setShowNewCardForm(false);
    },
    []
  );

  const handleSavedCardSelect = useCallback((card: CreditCard) => {
    console.log("💳 [UNIFIED-PAYMENT] Cartão salvo selecionado:", card.id);
    setSelectedSavedCard(card);
    setSelectedMethod(PaymentType.CreditCard);
    setShowNewCardForm(false);
  }, []);

  const handleAddNewCard = useCallback(() => {
    console.log("➕ [UNIFIED-PAYMENT] Adicionando novo cartão");
    setShowNewCardForm(true);
    setSelectedMethod(PaymentType.CreditCard);
    setSelectedSavedCard(undefined);
  }, []);

  const handleNewCardSave = useCallback(
    async (cardData: any) => {
      try {
        console.log("💾 [UNIFIED-PAYMENT] Salvando novo cartão");
        const newCard = await createCreditCardMutation.mutateAsync(cardData);
        setSelectedSavedCard(newCard);
        setShowNewCardForm(false);
        console.log(
          "✅ [UNIFIED-PAYMENT] Cartão salvo com sucesso:",
          newCard.id
        );
      } catch (error) {
        console.error("❌ [UNIFIED-PAYMENT] Erro ao salvar cartão:", error);
        Alert.alert(t("payment.error.title"), t("payment.error.cardSave"));
      }
    },
    [createCreditCardMutation, t]
  );

  const handlePayment = useCallback(async () => {
    if (selectedMethod === undefined || selectedMethod === null) {
      Alert.alert(
        t("payment.validation.title"),
        t("payment.validation.selectMethod")
      );
      return;
    }

    setCurrentStep("processing");

    try {
      const paymentData: CreatePaymentRequest = {
        entity: entityType,
        entityId: entityId,
        type: selectedMethod,
        termAccepted: true // Aceita automaticamente os termos de uso
      };

      // Adiciona dados específicos do método
      if (selectedMethod === PaymentType.CreditCard && selectedSavedCard) {
        paymentData.creditCard = {
          creditCardId: selectedSavedCard.id,
          installmentCount: installments
        };
      } else if (selectedMethod === PaymentType.Boleto) {
        paymentData.boleto = {
          installmentCount: installments
        };
      }

      console.log("💰 [UNIFIED-PAYMENT] Criando pagamento:", paymentData);

      const payment = await createPaymentMutation.mutateAsync(paymentData);

      console.log("✅ [UNIFIED-PAYMENT] Pagamento criado:", payment.id);
      setCurrentStep("success");

      if (onPaymentSuccess) {
        onPaymentSuccess(payment.id);
      }

      // Navegar para tela de confirmação baseada no método
      // Passa os dados do pagamento para evitar nova chamada à API
      const paymentDataForConfirmation = JSON.stringify({
        ...payment,
        entity: entityType,
        entityId: entityId
      });

      if (selectedMethod === PaymentType.Pix) {
        router.push(
          `/(logged-stack)/payment-confirmation?paymentId=${
            payment.id
          }&method=pix&paymentData=${encodeURIComponent(
            paymentDataForConfirmation
          )}&skipApiCall=true`
        );
      } else if (selectedMethod === PaymentType.Boleto) {
        router.push(
          `/(logged-stack)/payment-confirmation?paymentId=${
            payment.id
          }&method=boleto&paymentData=${encodeURIComponent(
            paymentDataForConfirmation
          )}&skipApiCall=true`
        );
      } else {
        router.push(
          `/(logged-stack)/payment-confirmation?paymentId=${
            payment.id
          }&method=credit-card&paymentData=${encodeURIComponent(
            paymentDataForConfirmation
          )}&skipApiCall=true`
        );
      }
    } catch (error: any) {
      console.error("❌ [UNIFIED-PAYMENT] Erro no pagamento:", error);
      setCurrentStep("error");

      const errorMessage = error?.message || t("payment.error.generic");

      if (onPaymentError) {
        onPaymentError(errorMessage);
      }

      Alert.alert(t("payment.error.title"), errorMessage);
    }
  }, [
    selectedMethod,
    entityType,
    entityId,
    selectedSavedCard,
    installments,
    createPaymentMutation,
    onPaymentSuccess,
    onPaymentError,
    t
  ]);

  // Verifica se pode prosseguir
  const canProceed = useMemo(() => {
    if (!selectedMethod) return false;

    if (selectedMethod === PaymentType.CreditCard) {
      return selectedSavedCard || showNewCardForm;
    }

    return true;
  }, [selectedMethod, selectedSavedCard, showNewCardForm]);

  const isProcessing =
    currentStep === "processing" || createPaymentMutation.isPending;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Resumo do pagamento */}
      <PaymentSummary
        items={[summaryItem]}
        total={amount}
        showItemDetails={true}
      />

      {/* Seletor de método de pagamento */}
      <PaymentMethodSelector
        selectedMethod={selectedMethod}
        onMethodSelect={handleMethodSelect}
        savedCreditCards={creditCards}
        onSelectSavedCard={handleSavedCardSelect}
        selectedSavedCard={selectedSavedCard}
        showSavedCards={showSavedCards}
        onAddNewCard={handleAddNewCard}
        showNewCardForm={showNewCardForm}
      />

      {/* Formulário de novo cartão */}
      {showNewCardForm && (
        <CreditCardForm
          onSubmit={handleNewCardSave}
          onBack={() => setShowNewCardForm(false)}
          isLoading={createCreditCardMutation.isPending}
        />
      )}

      {/* Botões de ação */}
      <View style={styles.actionsContainer}>
        <FullSizeButton
          text={isProcessing ? t("payment.processing") : t("payment.confirm")}
          onPress={handlePayment}
          disabled={!canProceed || isProcessing}
          variant={canProceed ? "primary" : "secondary"}
          loading={isProcessing}
        />

        {onCancel && (
          <FullSizeButton
            text={t("common.cancel")}
            onPress={onCancel}
            variant="outline"
            style={styles.cancelButton}
          />
        )}
      </View>
    </ScrollView>
  );
};

export default UnifiedPaymentFlow;
