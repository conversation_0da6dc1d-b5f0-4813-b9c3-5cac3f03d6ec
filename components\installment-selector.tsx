import React, {useState, useCallback} from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import ChevronDownIcon from "./icons/chevron-down-icon";
import CreditCardIcon from "./icons/credit-card-icon";
import styles from "@/styles/components/installment-selector.style";

export interface InstallmentOption {
  id: string;
  installments: number;
  amount: string;
  label: string;
}

export interface InstallmentSelectorProps {
  selectedInstallments?: number;
  onInstallmentSelect: (installments: number) => void;
  planValue?: number;
  maxInstallments?: number;
  disabled?: boolean;
}

const InstallmentSelector: React.FC<InstallmentSelectorProps> = ({
  selectedInstallments = 1,
  onInstallmentSelect,
  planValue = 0,
  maxInstallments = 12,
  disabled = false
}) => {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // Generate installment options based on plan value
  const generateInstallmentOptions = useCallback((): InstallmentOption[] => {
    if (planValue <= 0) return [];

    const options: InstallmentOption[] = [];

    for (let i = 1; i <= maxInstallments; i++) {
      const baseInstallmentValue = planValue / i;
      const hasInterest = i > 6; // Assume interest after 6 installments
      const finalInstallmentValue = hasInterest
        ? baseInstallmentValue * 1.05
        : baseInstallmentValue; // 5% interest

      options.push({
        id: i.toString(),
        installments: i,
        amount: `R$ ${finalInstallmentValue.toFixed(2).replace(".", ",")}`,
        label: i === 1 ? "À vista" : hasInterest ? "Com juros" : "Sem juros"
      });
    }

    return options;
  }, [planValue, maxInstallments]);

  const installmentOptions = generateInstallmentOptions();

  const toggleExpanded = useCallback(() => {
    if (!disabled) {
      setIsExpanded(!isExpanded);
    }
  }, [isExpanded, disabled]);

  const handleInstallmentSelect = useCallback(
    (installments: number) => {
      onInstallmentSelect(installments);
      setIsExpanded(false);
    },
    [onInstallmentSelect]
  );

  const getSelectedOptionLabel = useCallback(() => {
    const selectedOption = installmentOptions.find(
      (option) => option.installments === selectedInstallments
    );

    if (!selectedOption) {
      return t(
        "installmentSelector.placeholder",
        "Selecionar quantidade de parcelas"
      );
    }

    if (selectedOption.installments === 1) {
      return `À vista - ${selectedOption.amount}`;
    }

    return `${selectedOption.installments}x de ${selectedOption.amount}`;
  }, [installmentOptions, selectedInstallments, t]);

  if (installmentOptions.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Selector Button */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          disabled && styles.selectorButtonDisabled,
          isExpanded && styles.selectorButtonExpanded
        ]}
        onPress={toggleExpanded}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <CreditCardIcon width={20} height={20} replaceColor="#F2F4F7" />
        <Text
          style={[styles.selectorText, disabled && styles.selectorTextDisabled]}
        >
          {getSelectedOptionLabel()}
        </Text>
        <ChevronDownIcon
          width={20}
          height={20}
          style={[styles.chevronIcon, isExpanded && styles.chevronIconRotated]}
        />
      </TouchableOpacity>

      {/* Installment Options */}
      {isExpanded && (
        <View style={styles.optionsContainer}>
          <Text style={styles.optionsTitle}>
            {t("installmentSelector.title", "Parcelamento")}
          </Text>
          <View style={styles.optionsGrid}>
            {installmentOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  selectedInstallments === option.installments &&
                    styles.optionButtonSelected
                ]}
                onPress={() => handleInstallmentSelect(option.installments)}
                activeOpacity={0.7}
              >
                <Text style={styles.optionText}>
                  <Text style={styles.optionInstallments}>
                    {option.installments}x
                  </Text>
                  <Text style={styles.optionOf}> de </Text>
                  <Text style={styles.optionAmount}>{option.amount}</Text>
                </Text>
                <Text style={styles.optionLabel}>{option.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

export default InstallmentSelector;
