#!/bin/bash

# Club M Unified Build Script
# Builds production apps for both Android and iOS platforms

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[BUILD]${NC} $1"
}

# Parse command line arguments
ANDROID_ONLY=false
IOS_ONLY=false
SKIP_ANDROID=false
SKIP_IOS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --android-only)
            ANDROID_ONLY=true
            shift
            ;;
        --ios-only)
            IOS_ONLY=true
            shift
            ;;
        --skip-android)
            SKIP_ANDROID=true
            shift
            ;;
        --skip-ios)
            SKIP_IOS=true
            shift
            ;;
        -h|--help)
            echo "Club M Build Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --android-only    Build only Android APK"
            echo "  --ios-only        Build only iOS IPA (macOS only)"
            echo "  --skip-android    Skip Android build"
            echo "  --skip-ios        Skip iOS build"
            echo "  -h, --help        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                Build both platforms"
            echo "  $0 --android-only Build only Android"
            echo "  $0 --ios-only     Build only iOS"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if we're in the project root
if [ ! -f "package.json" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Detect OS
OS_TYPE="unknown"
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS_TYPE="macOS"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS_TYPE="Linux"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS_TYPE="Windows"
fi

print_header "Club M Production Build"
print_status "Detected OS: $OS_TYPE"
print_status "Build started at: $(date)"

# Create builds directory
mkdir -p builds

# Determine what to build
BUILD_ANDROID=true
BUILD_IOS=true

if [ "$ANDROID_ONLY" = true ]; then
    BUILD_IOS=false
elif [ "$IOS_ONLY" = true ]; then
    BUILD_ANDROID=false
fi

if [ "$SKIP_ANDROID" = true ]; then
    BUILD_ANDROID=false
fi

if [ "$SKIP_IOS" = true ]; then
    BUILD_IOS=false
fi

# Check iOS availability
if [ "$BUILD_IOS" = true ] && [[ "$OS_TYPE" != "macOS" ]]; then
    print_warning "iOS builds are only available on macOS"
    print_warning "Skipping iOS build on $OS_TYPE"
    BUILD_IOS=false
fi

# Build summary
print_status "Build plan:"
if [ "$BUILD_ANDROID" = true ]; then
    print_status "  ✓ Android APK"
else
    print_status "  ✗ Android APK (skipped)"
fi

if [ "$BUILD_IOS" = true ]; then
    print_status "  ✓ iOS IPA"
else
    print_status "  ✗ iOS IPA (skipped)"
fi

echo ""

# Track build results
ANDROID_SUCCESS=false
IOS_SUCCESS=false

# Build Android
if [ "$BUILD_ANDROID" = true ]; then
    print_header "Building Android APK..."
    
    if [ -f "scripts/build-android.sh" ]; then
        if ./scripts/build-android.sh; then
            ANDROID_SUCCESS=true
            print_success "Android build completed successfully"
        else
            print_error "Android build failed"
        fi
    else
        print_error "Android build script not found: scripts/build-android.sh"
    fi
    
    echo ""
fi

# Build iOS
if [ "$BUILD_IOS" = true ]; then
    print_header "Building iOS IPA..."
    
    if [ -f "scripts/build-ios.sh" ]; then
        if ./scripts/build-ios.sh; then
            IOS_SUCCESS=true
            print_success "iOS build completed successfully"
        else
            print_error "iOS build failed"
        fi
    else
        print_error "iOS build script not found: scripts/build-ios.sh"
    fi
    
    echo ""
fi

# Build summary
print_header "Build Summary"
print_status "Build completed at: $(date)"

if [ "$BUILD_ANDROID" = true ]; then
    if [ "$ANDROID_SUCCESS" = true ]; then
        print_success "✓ Android APK: builds/android/club-m-production.apk"
    else
        print_error "✗ Android APK: Build failed"
    fi
fi

if [ "$BUILD_IOS" = true ]; then
    if [ "$IOS_SUCCESS" = true ]; then
        print_success "✓ iOS IPA: builds/ios/club-m-production.ipa"
    else
        print_error "✗ iOS IPA: Build failed"
    fi
fi

# Exit with error if any build failed
if [ "$BUILD_ANDROID" = true ] && [ "$ANDROID_SUCCESS" = false ]; then
    exit 1
fi

if [ "$BUILD_IOS" = true ] && [ "$IOS_SUCCESS" = false ]; then
    exit 1
fi

print_success "All builds completed successfully!"
