import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 40
  },
  contentContainer: {
    flex: 1,
    justifyContent: "flex-start"
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 24,
    fontWeight: 700,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 32,
    marginBottom: 12
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 24
  },
  formContainer: {
    gap: 24
  },
  inputContainer: {
    gap: 16
  },
  buttonContainer: {
    marginTop: "auto",
    paddingBottom: 20
  },
  backButton: {
    alignSelf: "flex-start",
    marginBottom: 24
  }
});

export default styles;
