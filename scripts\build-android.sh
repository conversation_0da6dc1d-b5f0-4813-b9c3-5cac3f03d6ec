#!/bin/bash

# Club M Android Build Script
# Builds production APK for Android

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
if [ ! -f "package.json" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check if Android directory exists
if [ ! -d "android" ]; then
    print_error "Android directory not found. Make sure this is a React Native project."
    exit 1
fi

print_status "Starting Android production build..."

# Setup production environment
print_status "Setting up production environment..."
node scripts/setup-production.js

# Clean previous builds
print_status "Cleaning previous builds..."
cd android
./gradlew clean

# Build the APK
print_status "Building production APK..."
./gradlew app:assembleRelease

# Check if build was successful
if [ $? -eq 0 ]; then
    print_success "Android APK build completed successfully!"

    # Find the APK file
    APK_PATH=$(find app/build/outputs/apk/release -name "*.apk" | head -1)

    if [ -n "$APK_PATH" ]; then
        print_success "APK location: android/$APK_PATH"

        # Get APK size
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        print_status "APK size: $APK_SIZE"

        # Copy APK to builds directory
        cd ..
        mkdir -p builds/android
        cp "android/$APK_PATH" "builds/android/club-m-production.apk"
        print_success "APK copied to: builds/android/club-m-production.apk"
    else
        print_warning "APK file not found in expected location"
    fi
else
    print_error "Android build failed!"
    exit 1
fi

print_success "Android build process completed!"
