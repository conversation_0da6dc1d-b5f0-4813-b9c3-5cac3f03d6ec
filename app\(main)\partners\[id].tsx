import React, {useMemo, useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import {useLocalSearchParams, useRouter} from "expo-router";
import {useTranslation} from "react-i18next";
import {Image} from "expo-image";
import Screen<PERSON>ithHeader from "@/components/screen-with-header";
import {usePartner, usePartnerBenefits} from "@/hooks/api/use-partners";
import LoadingSpinner from "@/components/loading-spinner";
import ErrorMessage from "@/components/error-message";
import BusinessIcon from "@/components/icons/business-icon";
import GiftIcon from "@/components/icons/gift-icon";
import ChevronRightIcon from "@/components/icons/chevron-right-icon";
import styles from "@/styles/partners/partner-detail.style";

// Define BenefitPreview type if not imported
interface BenefitPreview {
  id: number;
  title: string;
  description: string;
}

interface PartnerDetailParams {
  id: string;
}

/**
 * Partner detail page component that displays comprehensive information about a partner
 * including their benefits, contact information, and navigation to benefits.
 *
 * Features:
 * - Partner information display
 * - Benefits preview
 * - Navigation to partner benefits
 * - Error handling with retry
 * - Loading states
 * - Responsive design
 */
const PartnerDetailPage: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  // Ensure id is string or number
  const partnerId = Array.isArray(params.id) ? params.id[0] : params.id;

  // Fetch partner data
  const {
    data: partner,
    isLoading: isLoadingPartner,
    error: partnerError,
    refetch: refetchPartner
  } = usePartner(partnerId);

  // Fetch partner benefits preview (first 3)
  const {data: benefitsData, isLoading: isLoadingBenefits} = usePartnerBenefits(
    partnerId,
    {
      page: 1,
      pageSize: 3
    }
  );

  const benefits = benefitsData?.data || [];
  const isLoading = isLoadingPartner || isLoadingBenefits;

  // Truncate description for preview
  const truncatedDescription = useMemo(() => {
    if (!partner?.description) return "";
    const description = partner.description;
    return description.length > 150
      ? description.substring(0, 150) + "..."
      : description;
  }, [partner?.description]);

  const handleSeeMoreDescription = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded);
  };

  const handleViewAllBenefits = () => {
    router.push(`/(main)/partners/partner-benefits?id=${params.id}`);
  };

  const handleBenefitPress = (benefitId: number) => {
    router.push(
      `/(main)/partners/partner-benefits?id=${params.id}&benefitId=${benefitId}`
    );
  };

  const handleRetry = () => {
    refetchPartner();
  };

  if (isLoading) {
    return (
      <ScreenWithHeader screenTitle={t("partners.partnerDetails")} backButton>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>{t("common.loading")}</Text>
        </View>
      </ScreenWithHeader>
    );
  }

  if (partnerError || !partner) {
    return (
      <ScreenWithHeader screenTitle={t("partners.partnerDetails")} backButton>
        <View style={styles.errorContainer}>
          <ErrorMessage
            message={t("partners.errorLoadingPartner")}
            onRetry={handleRetry}
          />
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader screenTitle="Benefícios" backButton>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Partner Header */}
        <View style={styles.headerSection}>
          <View style={styles.logoContainer}>
            {partner.logoUrl ? (
              <Image
                source={{uri: partner.logoUrl}}
                style={styles.logo}
                contentFit="contain"
              />
            ) : (
              <View style={styles.logoPlaceholder}>
                <BusinessIcon width={48} height={48} color="#666" />
              </View>
            )}
          </View>

          <View style={styles.headerInfo}>
            <Text style={styles.partnerName}>{partner.name}</Text>
            {partner.category && (
              <Text style={styles.partnerCategory}>{partner.category}</Text>
            )}
            {partner.website && (
              <Text style={styles.partnerWebsite}>{partner.website}</Text>
            )}
          </View>
        </View>

        {/* Partner Description */}
        {partner.description && (
          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>{t("partners.about")}</Text>
            <Text style={styles.description}>
              {isDescriptionExpanded
                ? partner.description
                : truncatedDescription}
            </Text>
            {partner.description.length > 150 && (
              <TouchableOpacity onPress={handleSeeMoreDescription}>
                <Text style={styles.seeMoreText}>
                  {isDescriptionExpanded
                    ? t("common.seeLess")
                    : t("common.seeMore")}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Benefits Preview */}
        <View style={styles.benefitsSection}>
          <View style={styles.benefitsHeader}>
            <Text style={styles.sectionTitle}>{t("partners.benefits")}</Text>
            {benefits.length > 0 && (
              <TouchableOpacity onPress={handleViewAllBenefits}>
                <Text style={styles.viewAllText}>{t("common.viewAll")}</Text>
              </TouchableOpacity>
            )}
          </View>

          {benefits.length > 0 ? (
            <View style={styles.benefitsList}>
              {benefits.map((benefit: BenefitPreview) => (
                <TouchableOpacity
                  key={benefit.id}
                  style={styles.benefitCard}
                  onPress={() => handleBenefitPress(benefit.id)}
                >
                  <View style={styles.benefitIcon}>
                    <GiftIcon width={24} height={24} color="#4A90E2" />
                  </View>
                  <View style={styles.benefitInfo}>
                    <Text style={styles.benefitTitle}>{benefit.title}</Text>
                    <Text style={styles.benefitDescription} numberOfLines={2}>
                      {benefit.description}
                    </Text>
                  </View>
                  <ChevronRightIcon width={16} height={16} color="#666" />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.noBenefitsContainer}>
              <Text style={styles.noBenefitsText}>
                {t("partners.noBenefits")}
              </Text>
            </View>
          )}
        </View>

        {/* View All Benefits Button */}
        {benefits.length > 0 && (
          <View style={styles.actionSection}>
            <TouchableOpacity
              style={styles.viewAllBenefitsButton}
              onPress={handleViewAllBenefits}
            >
              <Text style={styles.viewAllBenefitsButtonText}>
                {t("partners.viewAllBenefits")}
              </Text>
              <ChevronRightIcon width={20} height={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PartnerDetailPage;
