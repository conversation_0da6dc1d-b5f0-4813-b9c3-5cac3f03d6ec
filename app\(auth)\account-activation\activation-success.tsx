import React, {useCallback} from "react";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {Text, View} from "react-native";
import FullSizeButton from "@/components/full-size-button";
import CheckCircleIcon from "@/components/icons/check-circle-icon";
import {useTranslation} from "react-i18next";
import styles from "@/styles/auth/account-activation/activation-success.style";
import {useRouter} from "expo-router";

const ActivationSuccess: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  const onButtonPress = useCallback(
    () => router.replace("/(auth)/login"),
    [router]
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View style={styles.icon}>
            <CheckCircleIcon />
          </View>
          <Text style={[styles.text, styles.title]}>
            {t("accountActivationSuccess.successTitle")}
          </Text>
          <Text style={styles.text}>
            {t("accountActivationSuccess.successDescription")}
          </Text>
        </View>
        <View style={styles.footerButton}>
          <FullSizeButton
            text="accountActivationSuccess.successButton"
            onPress={onButtonPress}
          />
        </View>
      </Screen>
    </>
  );
};

export default ActivationSuccess;
