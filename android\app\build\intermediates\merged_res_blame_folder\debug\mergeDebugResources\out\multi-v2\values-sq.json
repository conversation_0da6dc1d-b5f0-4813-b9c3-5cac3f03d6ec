{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,16285", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,16362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3625,3704,3782,3868,3968,4797,4898,5024,7850,7915,7980,10474,10695,10823,10921,10983,11047,11106,11178,11241,11295,11412,11469,11531,11585,11657,12025,12108,12187,12283,12366,12444,12585,12669,12751,12899,12989,13067,13120,13179,13245,13316,13395,13466,13549,13625,13703,13775,13848,13952,14041,14113,14207,14306,14380,14452,14553,14603,14688,14754,14844,14933,14995,15059,15122,15189,15305,15418,15527,15632,15689,15752,16367,16452,16526", "endLines": "5,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "312,3699,3777,3863,3963,4055,4893,5019,5102,7910,7975,8075,10539,10749,10916,10978,11042,11101,11173,11236,11290,11407,11464,11526,11580,11652,11787,12103,12182,12278,12361,12439,12580,12664,12746,12894,12984,13062,13115,13174,13240,13311,13390,13461,13544,13620,13698,13770,13843,13947,14036,14108,14202,14301,14375,14447,14548,14598,14683,14749,14839,14928,14990,15054,15117,15184,15300,15413,15522,15627,15684,15747,15830,16447,16521,16599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,210,281,350,432,501,568,650,734,823,906,976,1062,1151,1226,1307,1388,1465,1540,1613,1700,1777,1858,1932", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "124,205,276,345,427,496,563,645,729,818,901,971,1057,1146,1221,1302,1383,1460,1535,1608,1695,1772,1853,1927,2010"}, "to": {"startLines": "33,53,96,98,99,101,115,116,117,164,165,166,168,173,174,175,176,177,178,179,180,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3013,5107,10403,10544,10613,10754,11792,11859,11941,15835,15924,16007,16199,16604,16693,16768,16849,16930,17007,17082,17155,17343,17420,17501,17575", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "3082,5183,10469,10608,10690,10818,11854,11936,12020,15919,16002,16072,16280,16688,16763,16844,16925,17002,17077,17150,17237,17415,17496,17570,17653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6249", "endColumns": "128", "endOffsets": "6373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "73,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7642,8346,8447,8558", "endColumns": "114,100,110,100", "endOffsets": "7752,8442,8553,8654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5188,5295,5468,5605,5712,5873,6007,6133,6378,6548,6656,6831,6969,7131,7315,7380,7447", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "5290,5463,5600,5707,5868,6002,6128,6244,6543,6651,6826,6964,7126,7310,7375,7442,7524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "43,44,45,46,47,48,49,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4060,4159,4261,4359,4456,4564,4675,17242", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "4154,4256,4354,4451,4559,4670,4792,17338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,122", "endOffsets": "165,288"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3087,3202", "endColumns": "114,122", "endOffsets": "3197,3320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,355,468,561,712,827,948,1073,1212,1355,1489,1624,1768,1864,2033,2159,2302,2450,2571,2693,2798,2939,3027,3151,3256,3397", "endColumns": "167,131,112,92,150,114,120,124,138,142,133,134,143,95,168,125,142,147,120,121,104,140,87,123,104,140,103", "endOffsets": "218,350,463,556,707,822,943,1068,1207,1350,1484,1619,1763,1859,2028,2154,2297,2445,2566,2688,2793,2934,3022,3146,3251,3392,3496"}, "to": {"startLines": "36,37,72,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,167,186,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3325,3493,7529,7757,8080,8231,8659,8780,8905,9044,9187,9321,9456,9600,9696,9865,9991,10134,10282,16077,17658,17763,17904,17992,18116,18221,18362", "endColumns": "167,131,112,92,150,114,120,124,138,142,133,134,143,95,168,125,142,147,120,121,104,140,87,123,104,140,103", "endOffsets": "3488,3620,7637,7845,8226,8341,8775,8900,9039,9182,9316,9451,9595,9691,9860,9986,10129,10277,10398,16194,17758,17899,17987,18111,18216,18357,18461"}}]}]}