import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

export default StyleSheet.create({
  container: {
    flex: 1
  },
  scrollContent: {
    paddingVertical: 24,
    gap: 24
  },
  pageTitleContainer: {
    marginBottom: 8
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 32,
    textAlign: "center",
    marginBottom: 8
  },
  questionHeader: {
    gap: 8
  },
  questionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 28
  },
  requiredIndicator: {
    color: "#FF6B6B"
  },
  questionDescription: {
    fontSize: 14,
    fontWeight: "400",
    color: "rgba(223, 233, 240, 0.8)",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 20
  },
  questionContent: {
    gap: 16
  },
  inputContainer: {
    gap: 8
  },
  textInput: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    fontFamily: stylesConstants.fonts.openSans,
    color: "#DFE9F0",
    minHeight: 120,
    textAlignVertical: "top"
  },
  optionsContainer: {
    gap: 12
  },
  optionItem: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16
  },
  optionItemSelected: {
    backgroundColor: "rgba(15, 124, 77, 0.1)",
    borderColor: stylesConstants.colors.brand.primary
  },
  optionContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  optionText: {
    fontSize: 16,
    fontWeight: "400",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    flex: 1,
    lineHeight: 22
  },
  optionTextSelected: {
    fontWeight: "500",
    color: "#FFFFFF"
  },
  checkIconContainer: {
    width: 24,
    height: 24,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 12
  },
  otherOptionContainer: {
    gap: 8,
    marginTop: 8
  },
  otherOptionLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans
  },
  otherOptionInput: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: stylesConstants.fonts.openSans,
    color: "#DFE9F0",
    minHeight: 48
  },
  requiredNotice: {
    paddingTop: 8
  },
  requiredNoticeText: {
    fontSize: 12,
    fontWeight: "400",
    color: "rgba(255, 107, 107, 0.8)",
    fontFamily: stylesConstants.fonts.openSans,
    fontStyle: "italic"
  }
});
