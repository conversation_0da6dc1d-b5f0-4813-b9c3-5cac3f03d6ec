import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface DiscountIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const DiscountIcon: React.FC<DiscountIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg viewBox="0 0 21 20" fill="none" {...props}>
      <Path
        d="M7.23685 13.3335L13.6471 6.66683M2.08301 15.3335C2.08301 15.6867 2.21773 16.0254 2.4576 16.2754C2.69748 16.5253 3.02292 16.6661 3.3625 16.6668H17.4702C17.8098 16.6661 18.1352 16.5253 18.3751 16.2754C18.615 16.0254 18.7497 15.6867 18.7497 15.3335V12.6215C18.1961 12.4654 17.7074 12.1241 17.3589 11.6503C17.0104 11.1766 16.8216 10.5967 16.8216 10.0002C16.8216 9.40367 17.0104 8.82377 17.3589 8.35C17.7074 7.87623 18.1961 7.53494 18.7497 7.37883V4.66683C18.7497 4.31367 18.615 3.97493 18.3751 3.72496C18.1352 3.47499 17.8098 3.3342 17.4702 3.3335H3.3625C3.02292 3.3342 2.69748 3.47499 2.4576 3.72496C2.21773 3.97493 2.08301 4.31367 2.08301 4.66683V7.3735C2.64098 7.52612 3.13465 7.86661 3.48693 8.3418C3.83921 8.817 4.03026 9.40013 4.03026 10.0002C4.03026 10.6002 3.83921 11.1833 3.48693 11.6585C3.13465 12.1337 2.64098 12.4742 2.08301 12.6268V15.3335ZM7.87788 8.00016C8.04789 8.00016 8.21094 7.92992 8.33115 7.8049C8.45137 7.67988 8.51891 7.51031 8.51891 7.3335C8.51891 7.15668 8.45137 6.98712 8.33115 6.86209C8.21094 6.73707 8.04789 6.66683 7.87788 6.66683C7.70787 6.66683 7.54482 6.73707 7.42461 6.86209C7.30439 6.98712 7.23685 7.15668 7.23685 7.3335C7.23685 7.51031 7.30439 7.67988 7.42461 7.8049C7.54482 7.92992 7.70787 8.00016 7.87788 8.00016ZM13.0061 13.3335C13.1761 13.3335 13.3391 13.2633 13.4594 13.1382C13.5796 13.0132 13.6471 12.8436 13.6471 12.6668C13.6471 12.49 13.5796 12.3204 13.4594 12.1954C13.3391 12.0704 13.1761 12.0002 13.0061 12.0002C12.8361 12.0002 12.673 12.0704 12.5528 12.1954C12.4326 12.3204 12.3651 12.49 12.3651 12.6668C12.3651 12.8436 12.4326 13.0132 12.5528 13.1382C12.673 13.2633 12.8361 13.3335 13.0061 13.3335Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default DiscountIcon;
