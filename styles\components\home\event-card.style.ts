import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  eventCard: {
    padding: 20,
    marginBottom: 16
  },

  loadingCard: {
    justifyContent: "center",
    alignItems: "center",
    height: 200
  },

  eventHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    marginBottom: 12
  },

  eventContent: {
    flex: 1
  },

  eventText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },

  organizerName: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },

  eventTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 8
  },

  eventDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 12
  },

  eventImg: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginBottom: 12
  },

  eventDetails: {
    gap: 8,
    marginBottom: 12
  },

  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },

  detailText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },

  divider: {
    width: "100%",
    height: 1,
    backgroundColor: stylesConstants.colors.borderStrokesLines,
    marginVertical: 12
  },

  eventActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    gap: 16
  },

  priceContainer: {
    flex: 1
  },

  eventPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginTop: 4
  },

  viewEventButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: "transparent",
    borderColor: "#FCFCFD",
    borderWidth: 1.5,
    borderRadius: 8,
    width: 140,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    // Remove todas as sombras e efeitos
    shadowColor: "transparent",
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0
  },

  viewEventButtonText: {
    color: "#FCFCFD",
    fontFamily: "Ubuntu",
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18
  }
});

export default styles;
