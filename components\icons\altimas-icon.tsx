import React from "react";
import Svg, {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>F<PERSON>od,
  FeColorMatrix,
  FeOffset,
  FeGaussianBlur,
  FeBlend,
  Pattern,
  Use,
  Image,
  G,
  SvgProps
} from "react-native-svg";

const AltimasIcon: React.FC<SvgProps> = (props) => {
  return (
    <Svg {...props} width={163} height={70} viewBox="0 0 163 70" fill="none">
      <Defs>
        <Filter
          id="filter0_dd_390_13080"
          width="163"
          height="70"
          filterUnits="userSpaceOnUse"
        >
          <FeFlood floodOpacity="0" result="BackgroundImageFix" />
          <FeColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 127 0"
            result="hardAlpha"
          />
          <FeOffset dy="1" />
          <FeGaussianBlur stdDeviation="1" />
          <FeColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627  0 0 0 0 0.0941  0 0 0 0 0.1569  0 0 0 0.06 0"
          />
          <FeBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_390_13080"
          />
          <FeColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 127 0"
            result="hardAlpha"
          />
          <FeOffset dy="1" />
          <FeGaussianBlur stdDeviation="1.5" />
          <FeColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627  0 0 0 0 0.0941  0 0 0 0 0.1569  0 0 0 0.1 0"
          />
          <FeBlend
            mode="normal"
            in2="effect1_dropShadow_390_13080"
            result="effect2_dropShadow_390_13080"
          />
          <FeBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_390_13080"
            result="shape"
          />
        </Filter>

        <Pattern
          id="pattern0_390_13080"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <Use
            href="#image0_390_13080"
            transform="matrix(0.00444444 0 0 0.0109028 0 -0.726563)"
          />
        </Pattern>

        <Image
          id="image0_390_13080"
          width="225"
          height="225"
          preserveAspectRatio="none"
          href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAIAAACx0UUtAAAUE0lEQVR4Ae2b32tU19rH9x8QeiH2prl0AkOr9XDKyymSUk0tVXGQcxIK49u8HnrAi9CeQ6S0MiROZswwJ6bHIaRgSJOY6eBFSUIvckiCxmrA/BDNhUrGuw..."
        />
      </Defs>

      <G filter="url(#filter0_dd_390_13080)">
        <Rect
          x="3"
          y="2"
          width="157"
          height="64"
          rx="8"
          fill="url(#pattern0_390_13080)"
        />
      </G>
    </Svg>
  );
};

export default AltimasIcon;
