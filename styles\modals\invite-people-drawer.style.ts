import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

export default StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  container: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: "85%",
    minHeight: 500
  },
  header: {
    paddingTop: 8,
    paddingBottom: 16,
    alignItems: "center",
    gap: 24
  },
  headerIndicator: {
    width: 44,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginTop: 8
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center",
    lineHeight: 24
  },
  searchContainer: {
    paddingHorizontal: 24,
    marginTop: 12
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C2230",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    shadowColor: "#101828",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: "#F2F4F7",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 20,
    minHeight: 20
  },
  recentlyInvitedSection: {
    marginTop: 16,
    paddingHorizontal: 16
  },
  sectionTitle: {
    fontSize: 12,
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 18,
    marginBottom: 12,
    paddingHorizontal: 8
  },
  recentUsersContainer: {
    backgroundColor: "#202938",
    borderRadius: 8,
    paddingVertical: 16,
    minHeight: 180,
    maxHeight: 220,
    width: "100%"
  },
  recentUsersContent: {
    paddingHorizontal: 16,
    gap: 16,
    alignItems: "flex-start",
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start"
  },
  recentUserItem: {
    alignItems: "center",
    width: 70,
    gap: 6,
    marginBottom: 8
  },
  recentUserAvatarContainer: {
    position: "relative"
  },
  recentUserCheckbox: {
    position: "absolute",
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: stylesConstants.colors.brand.brand500,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#111828"
  },
  recentUserName: {
    fontSize: 10,
    color: "#F9FAFB",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 12,
    textAlign: "center",
    width: "100%"
  },
  emptyRecentUsersContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20
  },
  emptyRecentUsersText: {
    fontSize: 14,
    color: "#6B7280",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  searchResultsSection: {
    flex: 1,
    paddingHorizontal: 24,
    marginTop: 16
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  errorText: {
    fontSize: 16,
    color: stylesConstants.colors.error500,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  usersList: {
    flex: 1
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    backgroundColor: "#1C2230",
    borderWidth: 1,
    borderColor: "transparent"
  },
  userItemSelected: {
    backgroundColor: "#0F7C4D20",
    borderColor: "#0F7C4D"
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1
  },
  userDetails: {
    flex: 1,
    marginLeft: 12
  },
  userName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#F9FAFB",
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 2
  },
  userEmail: {
    fontSize: 14,
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#0F7C4D",
    justifyContent: "center",
    alignItems: "center"
  },
  messageSection: {
    borderTopWidth: 1,
    borderTopColor: "#44445A",
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
    gap: 12
  },
  messageInputContainer: {
    backgroundColor: "#1C2230",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    paddingHorizontal: 14,
    paddingVertical: 12,
    shadowColor: "#101828",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  messageInput: {
    fontSize: 14,
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 20,
    minHeight: 48,
    maxHeight: 80,
    textAlignVertical: "top"
  },
  buttonContainer: {
    gap: 12,
    marginTop: 12
  }
});
