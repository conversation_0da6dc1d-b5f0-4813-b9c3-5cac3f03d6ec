/**
 * React Query hooks para gerenciamento de enquetes
 */

import {
  useQuery,
  useInfiniteQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions
} from "@tanstack/react-query";
import {
  PollsService,
  PollPaginateViewModel,
  PollFilters,
  VoteResponse
} from "@/services/api/polls/polls.service";
import {Poll} from "@/models/api/polls.models";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys para polls
export const pollsKeys = {
  all: ["polls"] as const,
  lists: () => [...pollsKeys.all, "list"] as const,
  list: (filters: PollFilters) => [...pollsKeys.lists(), filters] as const,
  details: () => [...pollsKeys.all, "detail"] as const,
  detail: (id: number) => [...pollsKeys.details(), id] as const
};

/**
 * Hook para buscar enquetes com paginação
 */
export const usePolls = (
  filters?: PollFilters,
  options?: UseQueryOptions<PollPaginateViewModel, BaseApiError>
) => {
  return useQuery({
    queryKey: pollsKeys.list(filters || {}),
    queryFn: () => PollsService.getPolls(filters),
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden)
      if (error?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para buscar enquetes com scroll infinito
 */
export const useInfinitePolls = (
  filters?: Omit<PollFilters, "page">,
  options?: UseInfiniteQueryOptions<PollPaginateViewModel, BaseApiError>
): UseInfiniteQueryResult<PollPaginateViewModel, BaseApiError> => {
  return useInfiniteQuery({
    queryKey: pollsKeys.list(filters || {}),
    queryFn: ({pageParam = 1}) =>
      PollsService.getPolls({
        ...filters,
        page: pageParam as number
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.currentPage + 1 : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: (failureCount, error) => {
      if (error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para buscar enquete por ID
 */
export const usePoll = (
  id: number,
  options?: UseQueryOptions<Poll, BaseApiError>
) => {
  return useQuery({
    queryKey: pollsKeys.detail(id),
    queryFn: () => PollsService.getPollById(id),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    enabled: !!id && id > 0,
    retry: (failureCount, error) => {
      // Não tentar novamente se for 404 (Not Found) ou 403 (Forbidden)
      if (error?.status === 404 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook para votar em uma enquete
 */
export const useVoteOnPoll = (
  options?: UseMutationOptions<
    VoteResponse,
    BaseApiError,
    {pollId: number; optionId: number}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({pollId, optionId}) =>
      PollsService.voteOnPoll(pollId, optionId),
    onSuccess: (data, variables) => {
      // Invalidar cache da enquete específica
      queryClient.invalidateQueries({
        queryKey: pollsKeys.detail(variables.pollId)
      });

      // Invalidar listas de enquetes
      queryClient.invalidateQueries({
        queryKey: pollsKeys.lists()
      });

      // Se a resposta contém a enquete atualizada, atualizar o cache
      if (data.poll) {
        queryClient.setQueryData(pollsKeys.detail(variables.pollId), data.poll);
      }
    },
    onError: (error, variables) => {
      console.error("Erro ao votar em enquete:", error, variables);
    },
    ...options
  });
};

/**
 * Hook utilitário para invalidar cache de enquetes
 */
export const useInvalidatePolls = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => {
      queryClient.invalidateQueries({queryKey: pollsKeys.all});
    },
    invalidateLists: () => {
      queryClient.invalidateQueries({queryKey: pollsKeys.lists()});
    },
    invalidatePoll: (id: number) => {
      queryClient.invalidateQueries({queryKey: pollsKeys.detail(id)});
    }
  };
};

/**
 * Hook utilitário para prefetch de enquetes
 */
export const usePrefetchPolls = () => {
  const queryClient = useQueryClient();

  return {
    prefetchPolls: (filters?: PollFilters) => {
      queryClient.prefetchQuery({
        queryKey: pollsKeys.list(filters || {}),
        queryFn: () => PollsService.getPolls(filters),
        staleTime: 2 * 60 * 1000
      });
    },
    prefetchPoll: (id: number) => {
      queryClient.prefetchQuery({
        queryKey: pollsKeys.detail(id),
        queryFn: () => PollsService.getPollById(id),
        staleTime: 5 * 60 * 1000
      });
    }
  };
};
