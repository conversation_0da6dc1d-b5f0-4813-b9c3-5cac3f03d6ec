/**
 * Utilitários para manipulação de imagens de oportunidades
 */

/**
 * Constrói a URL completa da imagem usando o imageId
 * @param imageId - ID da imagem retornado pela API
 * @returns URL completa da imagem ou undefined se imageId for inválido
 */
export const buildOpportunityImageUrl = (
  imageId?: string | null
): string | undefined => {
  if (!imageId || imageId.trim() === "") {
    return undefined;
  }

  const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
  if (!baseUrl) {
    console.warn("EXPO_PUBLIC_API_BASE_URL não está configurado");
    return undefined;
  }

  return `${baseUrl}/api/storage/${imageId}`;
};

/**
 * Extrai a URL da imagem de uma oportunidade
 * Prioriza o campo 'imageUrl' se existir, senão usa 'imageId' para construir a URL
 * @param opportunity - Objeto da oportunidade que pode conter imageUrl ou imageId
 * @returns URL da imagem ou undefined
 */
export const getOpportunityImageUrl = (
  opportunity?: {
    imageUrl?: string;
    imageId?: string;
  } | null
): string | undefined => {
  if (!opportunity) {
    return undefined;
  }

  // Se já tem uma URL completa no campo imageUrl, usar ela
  if (opportunity.imageUrl && opportunity.imageUrl.trim() !== "") {
    return opportunity.imageUrl;
  }

  // Senão, tentar construir a URL usando imageId
  if (opportunity.imageId) {
    return buildOpportunityImageUrl(opportunity.imageId);
  }

  return undefined;
};

/**
 * Verifica se uma string é uma URL válida de imagem
 * @param url - URL para verificar
 * @returns true se for uma URL válida
 */
export const isValidImageUrl = (url?: string | null): boolean => {
  if (!url || url.trim() === "") {
    return false;
  }

  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};
