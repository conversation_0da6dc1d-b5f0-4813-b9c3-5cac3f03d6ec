/**
 * Testes para o componente PaymentButton
 * Verifica se o botão de pagamento unificado funciona corretamente
 */

import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import PaymentButton from "@/components/payment/payment-button";
import {PaymentEntity} from "@/models/api/payments.models";

// Mock dos estilos
jest.mock("@/styles/components/payment/payment-button.style", () => ({
  button: {},
  buttonPrimary: {},
  buttonSecondary: {},
  buttonOutline: {},
  buttonSmall: {},
  buttonMedium: {},
  buttonLarge: {},
  buttonDisabled: {},
  buttonLoading: {},
  buttonFullWidth: {width: "100%"},
  content: {},
  textContainer: {},
  text: {},
  textPrimary: {},
  textSecondary: {},
  textOutline: {},
  textSmall: {},
  textMedium: {},
  textLarge: {},
  textDisabled: {},
  textLoading: {},
  price: {},
  pricePrimary: {},
  priceSecondary: {},
  priceOutline: {},
  priceDisabled: {}
}));

// Mock dos ícones
jest.mock("@/components/icons/shopping-bag-icon", () => "ShoppingBagIcon");
jest.mock("@/components/icons/credit-card-icon", () => "CreditCardIcon");

// Mock do router
const mockPush = jest.fn();

// Precisa ser definido antes do import do componente
jest.doMock("expo-router", () => ({
  router: {
    push: mockPush
  }
}));

// Mock do i18n
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        "payment.button.buyProduct": "Comprar Produto",
        "payment.button.getProduct": "Obter Produto",
        "payment.button.buyEvent": "Comprar Ingresso",
        "payment.button.buyOpportunity": "Investir",
        "payment.button.buy": "Comprar",
        "payment.button.loading": "Carregando...",
        "payment.free": "Gratuito"
      };
      return translations[key] || key;
    }
  })
}));

describe("PaymentButton", () => {
  const defaultProps = {
    entityId: 123,
    entityType: PaymentEntity.Product,
    entityTitle: "Produto Teste",
    amount: 99.99
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve renderizar corretamente para produto pago", () => {
    const {getByText} = render(<PaymentButton {...defaultProps} />);

    expect(getByText("Comprar Produto")).toBeTruthy();
    expect(getByText("R$ 99,99")).toBeTruthy();
  });

  it("deve renderizar corretamente para produto gratuito", () => {
    const props = {
      ...defaultProps,
      amount: 0
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("Obter Produto")).toBeTruthy();
    expect(getByText("Gratuito")).toBeTruthy();
  });

  it("deve renderizar corretamente para evento", () => {
    const props = {
      ...defaultProps,
      entityType: PaymentEntity.Event,
      entityTitle: "Evento Teste"
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("Comprar Ingresso")).toBeTruthy();
  });

  it("deve renderizar corretamente para oportunidade", () => {
    const props = {
      ...defaultProps,
      entityType: PaymentEntity.Opportunity,
      entityTitle: "Oportunidade Teste"
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("Investir")).toBeTruthy();
  });

  it("deve usar texto customizado quando fornecido", () => {
    const props = {
      ...defaultProps,
      text: "Texto Customizado"
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("Texto Customizado")).toBeTruthy();
  });

  it("deve chamar callback customizado quando fornecido", () => {
    const onPress = jest.fn();
    const props = {
      ...defaultProps,
      onPress
    };

    const {getByText} = render(<PaymentButton {...props} />);

    const button = getByText("Comprar Produto");
    fireEvent.press(button);

    expect(onPress).toHaveBeenCalled();
  });

  it("deve mostrar loading quando loading=true", () => {
    const props = {
      ...defaultProps,
      loading: true
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("Carregando...")).toBeTruthy();
  });

  it("deve aplicar estilo fullWidth quando especificado", () => {
    const props = {
      ...defaultProps,
      fullWidth: true
    };

    const {getByText} = render(<PaymentButton {...props} />);

    // Verifica se o componente renderiza sem erro com fullWidth
    expect(getByText("Comprar Produto")).toBeTruthy();
  });

  it("deve renderizar ícone correto para cada tipo de entidade", () => {
    // Teste para produto
    const {rerender} = render(<PaymentButton {...defaultProps} />);
    expect(render(<PaymentButton {...defaultProps} />)).toBeTruthy();

    // Teste para evento
    rerender(
      <PaymentButton {...defaultProps} entityType={PaymentEntity.Event} />
    );
    expect(
      render(
        <PaymentButton {...defaultProps} entityType={PaymentEntity.Event} />
      )
    ).toBeTruthy();

    // Teste para oportunidade
    rerender(
      <PaymentButton {...defaultProps} entityType={PaymentEntity.Opportunity} />
    );
    expect(
      render(
        <PaymentButton
          {...defaultProps}
          entityType={PaymentEntity.Opportunity}
        />
      )
    ).toBeTruthy();
  });

  it("deve formatar valor corretamente", () => {
    const props = {
      ...defaultProps,
      amount: 1234.56
    };

    const {getByText} = render(<PaymentButton {...props} />);

    expect(getByText("R$ 1.234,56")).toBeTruthy();
  });

  it("deve renderizar com descrição e imageUrl", () => {
    const props = {
      ...defaultProps,
      entityDescription: "Descrição teste",
      entityImageUrl: "https://example.com/image.jpg"
    };

    const {getByText} = render(<PaymentButton {...props} />);

    // Verifica se renderiza corretamente
    expect(getByText("Comprar Produto")).toBeTruthy();
  });
});
