1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="studio.takasaki.clubm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:3-69
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:20-67
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:3-71
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:20-69
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:3-63
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:20-61
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:3-78
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:20-76
20
21    <queries>
21-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:3-15:13
22        <intent>
22-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:10:5-14:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
25-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
28        </intent>
29
30        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
30-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
30-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
31        <intent>
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
32-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
33        </intent>
34        <intent>
34-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
35
36            <!-- Required for picking images from the camera roll if targeting API 30 -->
37            <action android:name="android.media.action.IMAGE_CAPTURE" />
37-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
37-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
38        </intent>
39        <intent>
39-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
40
41            <!-- Required for picking images from the camera if targeting API 30 -->
42            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
42-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
42-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
43        </intent>
44        <intent>
44-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
45
46            <!-- Required for opening tabs if targeting API 30 -->
47            <action android:name="android.support.customtabs.action.CustomTabsService" />
47-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
47-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
48        </intent>
49        <intent>
49-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
50            <action android:name="android.intent.action.GET_CONTENT" />
50-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
50-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
51
52            <category android:name="android.intent.category.OPENABLE" />
52-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
52-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
53
54            <data android:mimeType="*/*" />
54-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
55        </intent>
56    </queries>
57
58    <uses-permission android:name="android.permission.WAKE_LOCK" />
58-->[:react-native-firebase_auth] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
58-->[:react-native-firebase_auth] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
59    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required for picking images from camera directly -->
59-->[:react-native-firebase_auth] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
59-->[:react-native-firebase_auth] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
60    <uses-permission android:name="android.permission.CAMERA" />
60-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
60-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:22-62
61    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
61-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e268b52b8d1d552999367c8146ab575\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
61-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e268b52b8d1d552999367c8146ab575\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:22-73
62    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
62-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
62-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
63    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
63-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
63-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
64    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
64-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
64-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
65    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
65-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\16ab3b70e5a7332a8cee3b220623bc38\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
65-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\16ab3b70e5a7332a8cee3b220623bc38\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
72-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
72-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
73    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
74    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
75    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
76    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
77    <!-- for Samsung -->
78    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
79    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
80    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
81    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
82    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
83    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
84    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
85    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
86    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
87    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
88    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
89    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
90    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
91    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
92    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
93    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
94
95    <application
95-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:3-32:17
96        android:name="studio.takasaki.clubm.MainApplication"
96-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:16-47
97        android:allowBackup="true"
97-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:162-188
98        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
99        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
99-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:307-376
100        android:debuggable="true"
101        android:extractNativeLibs="false"
102        android:fullBackupContent="@xml/secure_store_backup_rules"
102-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:248-306
103        android:icon="@mipmap/ic_launcher"
103-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:81-115
104        android:label="@string/app_name"
104-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:48-80
105        android:roundIcon="@mipmap/ic_launcher_round"
105-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:116-161
106        android:supportsRtl="true"
106-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:221-247
107        android:theme="@style/AppTheme"
107-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:189-220
108        android:usesCleartextTraffic="true" >
108-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\debug\AndroidManifest.xml:6:18-53
109        <meta-data
109-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:5-83
110            android:name="expo.modules.updates.ENABLED"
110-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:16-59
111            android:value="false" />
111-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:60-81
112        <meta-data
112-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:5-105
113            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
113-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:16-80
114            android:value="ALWAYS" />
114-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:81-103
115        <meta-data
115-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:5-99
116            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
116-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:16-79
117            android:value="0" />
117-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:80-97
118
119        <activity
119-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:5-31:16
120            android:name="studio.takasaki.clubm.MainActivity"
120-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:15-43
121            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
121-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:44-157
122            android:exported="true"
122-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:279-302
123            android:launchMode="singleTask"
123-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:158-189
124            android:screenOrientation="portrait"
124-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:303-339
125            android:theme="@style/Theme.App.SplashScreen"
125-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:233-278
126            android:windowSoftInputMode="adjustResize" >
126-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:190-232
127            <intent-filter>
127-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:7-24:23
128                <action android:name="android.intent.action.MAIN" />
128-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:9-60
128-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:17-58
129
130                <category android:name="android.intent.category.LAUNCHER" />
130-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:9-68
130-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:19-66
131            </intent-filter>
132            <intent-filter>
132-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:7-30:23
133                <action android:name="android.intent.action.VIEW" />
133-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
133-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
134
135                <category android:name="android.intent.category.DEFAULT" />
135-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
135-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
136                <category android:name="android.intent.category.BROWSABLE" />
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
137
138                <data android:scheme="clubm" />
138-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
138-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
139            </intent-filter>
140        </activity>
141
142        <meta-data
142-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
143            android:name="app_data_collection_default_enabled"
143-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
144            android:value="true" />
144-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
145
146        <service
146-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
147            android:name="com.google.firebase.components.ComponentDiscoveryService"
147-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
148            android:directBootAware="true"
148-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
149            android:exported="false" >
149-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
150            <meta-data
150-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
151                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
151-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
153            <meta-data
153-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
154                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
154-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
156            <meta-data
156-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
157                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
157-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
159            <meta-data
159-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
160                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
160-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
162            <meta-data
162-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
163                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
163-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
165            <meta-data
165-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
166                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
166-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4351aedd751e05211e46afe65e09759e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
168            <meta-data
168-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
169                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
169-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
171            <meta-data
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
172                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
174            <meta-data
174-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
175                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
175-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
177        </service>
178
179        <provider
179-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
180            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
180-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
181            android:authorities="studio.takasaki.clubm.reactnativefirebaseappinitprovider"
181-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
182            android:exported="false"
182-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
183            android:initOrder="99" />
183-->[:react-native-firebase_app] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
184
185        <activity
185-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
186            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
186-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
187            android:exported="true"
187-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
188            android:launchMode="singleTask"
188-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
189            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
189-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
190            <intent-filter>
190-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
191                <action android:name="android.intent.action.VIEW" />
191-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
191-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
192
193                <category android:name="android.intent.category.DEFAULT" />
193-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
193-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
194                <category android:name="android.intent.category.BROWSABLE" />
194-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
194-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
195
196                <data android:scheme="expo-dev-launcher" />
196-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
196-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
197            </intent-filter>
198        </activity>
199        <activity
199-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
200            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
200-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
201            android:screenOrientation="portrait"
201-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
202            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
202-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
203        <activity
203-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
204            android:name="expo.modules.devmenu.DevMenuActivity"
204-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
205            android:exported="true"
205-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
206            android:launchMode="singleTask"
206-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
207            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
207-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
208            <intent-filter>
208-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
209                <action android:name="android.intent.action.VIEW" />
209-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
209-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
210
211                <category android:name="android.intent.category.DEFAULT" />
211-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
211-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
212                <category android:name="android.intent.category.BROWSABLE" />
212-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
212-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
213
214                <data android:scheme="expo-dev-menu" />
214-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
214-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
215            </intent-filter>
216        </activity>
217
218        <meta-data
218-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
219            android:name="org.unimodules.core.AppLoader#react-native-headless"
219-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
220            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
220-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
221        <meta-data
221-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
222            android:name="com.facebook.soloader.enabled"
222-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
223            android:value="true" />
223-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
224
225        <activity
225-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
226            android:name="com.facebook.react.devsupport.DevSettingsActivity"
226-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
227            android:exported="false" />
227-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
228
229        <provider
229-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:8:9-15:20
230            android:name="expo.modules.clipboard.ClipboardFileProvider"
230-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:9:13-72
231            android:authorities="studio.takasaki.clubm.ClipboardFileProvider"
231-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:10:13-73
232            android:exported="true" >
232-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:11:13-36
233            <meta-data
233-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:12:13-14:68
234                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
234-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:13:17-84
235                android:resource="@xml/clipboard_provider_paths" />
235-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:14:17-65
236        </provider>
237        <provider
237-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
238            android:name="expo.modules.filesystem.FileSystemFileProvider"
238-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
239            android:authorities="studio.takasaki.clubm.FileSystemFileProvider"
239-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
240            android:exported="false"
240-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
241            android:grantUriPermissions="true" >
241-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
242            <meta-data
242-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
243                android:name="android.support.FILE_PROVIDER_PATHS"
243-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
244                android:resource="@xml/file_system_provider_paths" />
244-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
245        </provider>
246
247        <service
247-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
248            android:name="com.google.android.gms.metadata.ModuleDependencies"
248-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
249            android:enabled="false"
249-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
250            android:exported="false" >
250-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
251            <intent-filter>
251-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
252                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
252-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
252-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
253            </intent-filter>
254
255            <meta-data
255-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
256                android:name="photopicker_activity:0:required"
256-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
257                android:value="" />
257-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
258        </service>
259
260        <activity
260-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
261            android:name="com.canhub.cropper.CropImageActivity"
261-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
262            android:exported="true"
262-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
263            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
263-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
264        <provider
264-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
265            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
265-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
266            android:authorities="studio.takasaki.clubm.ImagePickerFileProvider"
266-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
267            android:exported="false"
267-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
268            android:grantUriPermissions="true" >
268-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
269            <meta-data
269-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
270                android:name="android.support.FILE_PROVIDER_PATHS"
270-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
271                android:resource="@xml/image_picker_provider_paths" />
271-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
272        </provider>
273
274        <service
274-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
275            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
275-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
276            android:exported="false" >
276-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
277            <intent-filter android:priority="-1" >
277-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
277-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
278                <action android:name="com.google.firebase.MESSAGING_EVENT" />
278-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
278-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
279            </intent-filter>
280        </service>
281
282        <receiver
282-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
283            android:name="expo.modules.notifications.service.NotificationsService"
283-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
284            android:enabled="true"
284-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
285            android:exported="false" >
285-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
286            <intent-filter android:priority="-1" >
286-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
286-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
287                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
287-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
287-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
288                <action android:name="android.intent.action.BOOT_COMPLETED" />
288-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
288-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
289                <action android:name="android.intent.action.REBOOT" />
289-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
289-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
290                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
290-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
290-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
291                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
291-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
291-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
292                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
292-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
292-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
293            </intent-filter>
294        </receiver>
295
296        <activity
296-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
297            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
297-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
298            android:excludeFromRecents="true"
298-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
299            android:exported="false"
299-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
300            android:launchMode="standard"
300-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
301            android:noHistory="true"
301-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
302            android:taskAffinity=""
302-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
303            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
303-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
304        <activity
304-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
305            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
305-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
306            android:excludeFromRecents="true"
306-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
307            android:exported="true"
307-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
308            android:launchMode="singleTask"
308-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
309            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
309-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
310            <intent-filter>
310-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
311                <action android:name="android.intent.action.VIEW" />
311-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
311-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
312
313                <category android:name="android.intent.category.DEFAULT" />
313-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
313-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
314                <category android:name="android.intent.category.BROWSABLE" />
314-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
314-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
315
316                <data
316-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
317                    android:host="firebase.auth"
318                    android:path="/"
319                    android:scheme="genericidp" />
319-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
320            </intent-filter>
321        </activity>
322        <activity
322-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
323            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
323-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
324            android:excludeFromRecents="true"
324-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
325            android:exported="true"
325-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
326            android:launchMode="singleTask"
326-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
327            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
327-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
328            <intent-filter>
328-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd73a81e8b2f543adfbdb5d56880de2\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
329                <action android:name="android.intent.action.VIEW" />
329-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
329-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
330
331                <category android:name="android.intent.category.DEFAULT" />
331-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
331-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
332                <category android:name="android.intent.category.BROWSABLE" />
332-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
332-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
333
334                <data
334-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
335                    android:host="firebase.auth"
336                    android:path="/"
337                    android:scheme="recaptcha" />
337-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
338            </intent-filter>
339        </activity>
340
341        <receiver
341-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
342            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
342-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
343            android:exported="true"
343-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
344            android:permission="com.google.android.c2dm.permission.SEND" >
344-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
345            <intent-filter>
345-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:33:13-35:29
346                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
346-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:34:17-81
346-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:34:25-78
347            </intent-filter>
348
349            <meta-data
349-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
350                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
350-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
351                android:value="true" />
351-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
352        </receiver>
353        <!--
354             FirebaseMessagingService performs security checks at runtime,
355             but set to not exported to explicitly avoid allowing another app to call it.
356        -->
357        <service
357-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
358            android:name="com.google.firebase.messaging.FirebaseMessagingService"
358-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
359            android:directBootAware="true"
359-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
360            android:exported="false" >
360-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2353fa252c12c4cb05269ac96f6ff9\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
361            <intent-filter android:priority="-500" >
361-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
361-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
362                <action android:name="com.google.firebase.MESSAGING_EVENT" />
362-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
362-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
363            </intent-filter>
364        </service>
365        <service
365-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
366            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
366-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
367            android:enabled="true"
367-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
368            android:exported="false" >
368-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
369            <meta-data
369-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
370                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
370-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
371                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
371-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
372        </service>
373
374        <activity
374-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
375            android:name="androidx.credentials.playservices.HiddenActivity"
375-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
376            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
376-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
377            android:enabled="true"
377-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
378            android:exported="false"
378-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
379            android:fitsSystemWindows="true"
379-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
380            android:theme="@style/Theme.Hidden" >
380-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7aaceb7f4a4484d601b1556a6b54c39\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
381        </activity>
382        <activity
382-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
383            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
383-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
384            android:excludeFromRecents="true"
384-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
385            android:exported="false"
385-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
386            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
386-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
387        <!--
388            Service handling Google Sign-In user revocation. For apps that do not integrate with
389            Google Sign-In, this service will never be started.
390        -->
391        <service
391-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
392            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
392-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
393            android:exported="true"
393-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
394            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
394-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
395            android:visibleToInstantApps="true" />
395-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61a50bf027637f53c9a37068fa2a2017\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
396
397        <provider
397-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
398            android:name="com.canhub.cropper.CropFileProvider"
398-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
399            android:authorities="studio.takasaki.clubm.cropper.fileprovider"
399-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
400            android:exported="false"
400-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
401            android:grantUriPermissions="true" >
401-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
402            <meta-data
402-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
403                android:name="android.support.FILE_PROVIDER_PATHS"
403-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
404                android:resource="@xml/library_file_paths" />
404-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
405        </provider>
406
407        <activity
407-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b3c844b3393ff9a6c840f537ca40ca\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
408            android:name="com.google.android.gms.common.api.GoogleApiActivity"
408-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b3c844b3393ff9a6c840f537ca40ca\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
409            android:exported="false"
409-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b3c844b3393ff9a6c840f537ca40ca\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
410            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
410-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b3c844b3393ff9a6c840f537ca40ca\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
411
412        <provider
412-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
413            android:name="com.google.firebase.provider.FirebaseInitProvider"
413-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
414            android:authorities="studio.takasaki.clubm.firebaseinitprovider"
414-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
415            android:directBootAware="true"
415-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
416            android:exported="false"
416-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
417            android:initOrder="100" />
417-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
418
419        <meta-data
419-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
420            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
420-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
421            android:value="GlideModule" />
421-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
422
423        <provider
423-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
424            android:name="androidx.startup.InitializationProvider"
424-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
425            android:authorities="studio.takasaki.clubm.androidx-startup"
425-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
426            android:exported="false" >
426-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
427            <meta-data
427-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
428                android:name="androidx.emoji2.text.EmojiCompatInitializer"
428-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
429                android:value="androidx.startup" />
429-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
430            <meta-data
430-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
431                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
431-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
432                android:value="androidx.startup" />
432-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
433            <meta-data
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
434                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
435                android:value="androidx.startup" />
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
436        </provider>
437
438        <meta-data
438-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e054245bee9bbc8098dc00b5c8db8d90\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
439            android:name="com.google.android.gms.version"
439-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e054245bee9bbc8098dc00b5c8db8d90\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
440            android:value="@integer/google_play_services_version" />
440-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e054245bee9bbc8098dc00b5c8db8d90\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
441
442        <receiver
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
443            android:name="androidx.profileinstaller.ProfileInstallReceiver"
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
444            android:directBootAware="false"
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
445            android:enabled="true"
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
446            android:exported="true"
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
447            android:permission="android.permission.DUMP" >
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
448            <intent-filter>
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
449                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
450            </intent-filter>
451            <intent-filter>
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
452                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
453            </intent-filter>
454            <intent-filter>
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
455                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
456            </intent-filter>
457            <intent-filter>
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
458                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
459            </intent-filter>
460        </receiver>
461
462        <service
462-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
463            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
463-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
464            android:exported="false" >
464-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
465            <meta-data
465-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
466                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
466-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
467                android:value="cct" />
467-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
468        </service>
469        <service
469-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
470            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
470-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
471            android:exported="false"
471-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
472            android:permission="android.permission.BIND_JOB_SERVICE" >
472-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
473        </service>
474
475        <receiver
475-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
476            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
476-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
477            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
477-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
478        <activity
478-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4616352eb8692c4865ed3a5386567a49\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
479            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
479-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4616352eb8692c4865ed3a5386567a49\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
480            android:exported="false"
480-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4616352eb8692c4865ed3a5386567a49\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
481            android:stateNotNeeded="true"
481-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4616352eb8692c4865ed3a5386567a49\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
482            android:theme="@style/Theme.PlayCore.Transparent" />
482-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4616352eb8692c4865ed3a5386567a49\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
483    </application>
484
485</manifest>
