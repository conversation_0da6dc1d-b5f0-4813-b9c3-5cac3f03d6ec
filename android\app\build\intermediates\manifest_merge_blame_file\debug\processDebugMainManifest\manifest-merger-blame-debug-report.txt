1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="studio.takasaki.clubm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:3-69
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:20-67
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:3-71
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:20-69
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:3-63
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:20-61
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:3-78
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:20-76
20
21    <queries>
21-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:3-15:13
22        <intent>
22-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:10:5-14:14
23            <action android:name="android.intent.action.VIEW" />
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
25-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
26
27            <data android:scheme="https" />
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
27-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
28        </intent>
29
30        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
30-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
30-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
31        <intent>
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
32-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
33        </intent>
34        <intent>
34-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
35
36            <!-- Required for opening tabs if targeting API 30 -->
37            <action android:name="android.support.customtabs.action.CustomTabsService" />
37-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
37-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
38        </intent>
39    </queries>
40    <!--
41  Allows Glide to monitor connectivity status and restart failed requests if users go from a
42  a disconnected to a connected network state.
43    -->
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
44-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:5-79
44-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:22-76
45    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
45-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e268b52b8d1d552999367c8146ab575\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
45-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e268b52b8d1d552999367c8146ab575\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:22-73
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
46-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
46-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
47    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
47-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
47-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
48    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
48-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
49    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
49-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
50
51    <permission
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
52        android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
56    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
56-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
56-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
57    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
58    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
59    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
60    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
61    <!-- for Samsung -->
62    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
63    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
64    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
65    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
66    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
67    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
68    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
69    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
70    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
71    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
72    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
73    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
74    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
75    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
76    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
77    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
78
79    <application
79-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:3-32:17
80        android:name="studio.takasaki.clubm.MainApplication"
80-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:16-47
81        android:allowBackup="true"
81-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:162-188
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
83        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
83-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:307-376
84        android:debuggable="true"
85        android:extractNativeLibs="false"
86        android:fullBackupContent="@xml/secure_store_backup_rules"
86-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:248-306
87        android:icon="@mipmap/ic_launcher"
87-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:81-115
88        android:label="@string/app_name"
88-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:48-80
89        android:roundIcon="@mipmap/ic_launcher_round"
89-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:116-161
90        android:supportsRtl="true"
90-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:221-247
91        android:theme="@style/AppTheme"
91-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:16:189-220
92        android:usesCleartextTraffic="true" >
92-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\debug\AndroidManifest.xml:6:18-53
93        <meta-data
93-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:5-83
94            android:name="expo.modules.updates.ENABLED"
94-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:16-59
95            android:value="false" />
95-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:60-81
96        <meta-data
96-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:5-105
97            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
97-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:16-80
98            android:value="ALWAYS" />
98-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:81-103
99        <meta-data
99-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:5-99
100            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
100-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:16-79
101            android:value="0" />
101-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:80-97
102
103        <activity
103-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:5-31:16
104            android:name="studio.takasaki.clubm.MainActivity"
104-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:15-43
105            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
105-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:44-157
106            android:exported="true"
106-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:279-302
107            android:launchMode="singleTask"
107-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:158-189
108            android:screenOrientation="portrait"
108-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:303-339
109            android:theme="@style/Theme.App.SplashScreen"
109-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:233-278
110            android:windowSoftInputMode="adjustResize" >
110-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:190-232
111            <intent-filter>
111-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:7-24:23
112                <action android:name="android.intent.action.MAIN" />
112-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:9-60
112-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:17-58
113
114                <category android:name="android.intent.category.LAUNCHER" />
114-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:9-68
114-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:19-66
115            </intent-filter>
116            <intent-filter>
116-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:25:7-30:23
117                <action android:name="android.intent.action.VIEW" />
117-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
117-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
119-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
120                <category android:name="android.intent.category.BROWSABLE" />
120-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
120-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
121
122                <data android:scheme="clubm" />
122-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
122-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
123            </intent-filter>
124        </activity>
125        <activity
125-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
126            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
126-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
127            android:exported="true"
127-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
128            android:launchMode="singleTask"
128-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
129            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
129-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
130            <intent-filter>
130-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
131                <action android:name="android.intent.action.VIEW" />
131-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
131-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
132
133                <category android:name="android.intent.category.DEFAULT" />
133-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
133-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
134                <category android:name="android.intent.category.BROWSABLE" />
134-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
134-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
135
136                <data android:scheme="expo-dev-launcher" />
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
137            </intent-filter>
138        </activity>
139        <activity
139-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
140            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
140-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
141            android:screenOrientation="portrait"
141-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
142            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
142-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
143        <activity
143-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
144            android:name="expo.modules.devmenu.DevMenuActivity"
144-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
145            android:exported="true"
145-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
146            android:launchMode="singleTask"
146-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
147            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
147-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
148            <intent-filter>
148-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
149                <action android:name="android.intent.action.VIEW" />
149-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:7-58
149-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:15-56
150
151                <category android:name="android.intent.category.DEFAULT" />
151-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:9-67
151-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:27:19-65
152                <category android:name="android.intent.category.BROWSABLE" />
152-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-67
152-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:17-65
153
154                <data android:scheme="expo-dev-menu" />
154-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-37
154-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:13-35
155            </intent-filter>
156        </activity>
157
158        <meta-data
158-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
159            android:name="org.unimodules.core.AppLoader#react-native-headless"
159-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
160            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
160-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
161        <meta-data
161-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
162            android:name="com.facebook.soloader.enabled"
162-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
163            android:value="true" />
163-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
164
165        <activity
165-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
166            android:name="com.facebook.react.devsupport.DevSettingsActivity"
166-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
167            android:exported="false" />
167-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
168
169        <provider
169-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:8:9-15:20
170            android:name="expo.modules.clipboard.ClipboardFileProvider"
170-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:9:13-72
171            android:authorities="studio.takasaki.clubm.ClipboardFileProvider"
171-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:10:13-73
172            android:exported="true" >
172-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:11:13-36
173            <meta-data
173-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:12:13-14:68
174                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
174-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:13:17-84
175                android:resource="@xml/clipboard_provider_paths" />
175-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:14:17-65
176        </provider>
177        <provider
177-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
178            android:name="expo.modules.filesystem.FileSystemFileProvider"
178-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
179            android:authorities="studio.takasaki.clubm.FileSystemFileProvider"
179-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
180            android:exported="false"
180-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
181            android:grantUriPermissions="true" >
181-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
182            <meta-data
182-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
183                android:name="android.support.FILE_PROVIDER_PATHS"
183-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
184                android:resource="@xml/file_system_provider_paths" />
184-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
185        </provider>
186
187        <service
187-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
188            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
188-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
189            android:exported="false" >
189-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
190            <intent-filter android:priority="-1" >
190-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
190-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
191                <action android:name="com.google.firebase.MESSAGING_EVENT" />
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
192            </intent-filter>
193        </service>
194
195        <receiver
195-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
196            android:name="expo.modules.notifications.service.NotificationsService"
196-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
197            android:enabled="true"
197-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
198            android:exported="false" >
198-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
199            <intent-filter android:priority="-1" >
199-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
199-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
200                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
201                <action android:name="android.intent.action.BOOT_COMPLETED" />
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
202                <action android:name="android.intent.action.REBOOT" />
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
203                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
204                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
205                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
206            </intent-filter>
207        </receiver>
208
209        <activity
209-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
210            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
210-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
211            android:excludeFromRecents="true"
211-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
212            android:exported="false"
212-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
213            android:launchMode="standard"
213-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
214            android:noHistory="true"
214-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
215            android:taskAffinity=""
215-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
216-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
217
218        <receiver
218-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
219            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
219-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
220            android:exported="true"
220-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
221            android:permission="com.google.android.c2dm.permission.SEND" >
221-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
222            <intent-filter>
222-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
223                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
223-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
223-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
224            </intent-filter>
225
226            <meta-data
226-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
227                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
227-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
228                android:value="true" />
228-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
229        </receiver>
230        <!--
231             FirebaseMessagingService performs security checks at runtime,
232             but set to not exported to explicitly avoid allowing another app to call it.
233        -->
234        <service
234-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
235            android:name="com.google.firebase.messaging.FirebaseMessagingService"
235-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
236            android:directBootAware="true"
236-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
237            android:exported="false" >
237-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
238            <intent-filter android:priority="-500" >
238-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
238-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
239                <action android:name="com.google.firebase.MESSAGING_EVENT" />
239-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
239-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
240            </intent-filter>
241        </service>
242        <service
242-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
243            android:name="com.google.firebase.components.ComponentDiscoveryService"
243-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
244            android:directBootAware="true"
244-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
245            android:exported="false" >
245-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
246            <meta-data
246-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
247                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
247-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
249            <meta-data
249-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
250                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
250-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
252            <meta-data
252-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
253                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
253-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
255            <meta-data
255-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
256                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
256-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
258            <meta-data
258-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
259                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
259-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
261            <meta-data
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
262                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
264            <meta-data
264-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
265                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
265-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
267        </service>
268
269        <provider
269-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
270            android:name="com.google.firebase.provider.FirebaseInitProvider"
270-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
271            android:authorities="studio.takasaki.clubm.firebaseinitprovider"
271-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
272            android:directBootAware="true"
272-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
273            android:exported="false"
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
274            android:initOrder="100" />
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
275
276        <meta-data
276-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
277            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
277-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
278            android:value="GlideModule" />
278-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
279
280        <activity
280-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
281            android:name="com.google.android.gms.common.api.GoogleApiActivity"
281-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
282            android:exported="false"
282-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
283            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
283-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
284
285        <meta-data
285-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
286            android:name="com.google.android.gms.version"
286-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
287            android:value="@integer/google_play_services_version" />
287-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
288
289        <provider
289-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
290            android:name="androidx.startup.InitializationProvider"
290-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
291            android:authorities="studio.takasaki.clubm.androidx-startup"
291-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
292            android:exported="false" >
292-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
293            <meta-data
293-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
294                android:name="androidx.emoji2.text.EmojiCompatInitializer"
294-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
295                android:value="androidx.startup" />
295-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
296            <meta-data
296-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
297                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
297-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
298                android:value="androidx.startup" />
298-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
299            <meta-data
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
300                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
301                android:value="androidx.startup" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
302        </provider>
303
304        <receiver
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
305            android:name="androidx.profileinstaller.ProfileInstallReceiver"
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
306            android:directBootAware="false"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
307            android:enabled="true"
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
308            android:exported="true"
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
309            android:permission="android.permission.DUMP" >
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
310            <intent-filter>
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
311                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
312            </intent-filter>
313            <intent-filter>
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
314                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
315            </intent-filter>
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
317                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
318            </intent-filter>
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
320                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97de1dda6024289621eb58c05244b40b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
321            </intent-filter>
322        </receiver>
323
324        <service
324-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
325            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
325-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
326            android:exported="false" >
326-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
327            <meta-data
327-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
328                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
328-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
329                android:value="cct" />
329-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
330        </service>
331        <service
331-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
332            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
332-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
333            android:exported="false"
333-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
334            android:permission="android.permission.BIND_JOB_SERVICE" >
334-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
335        </service>
336
337        <receiver
337-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
338            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
338-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
339            android:exported="false" />
339-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
340    </application>
341
342</manifest>
