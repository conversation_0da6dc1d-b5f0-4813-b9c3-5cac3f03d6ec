@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-*********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging3979576319562072869\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\6q56y3y4" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b58fa16b3b4fa0649400855afae7a3f\\transformed\\hermes-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6e46b19b602d3371982eebc7ed690ed\\transformed\\fbjni-0.7.0\\prefab"
