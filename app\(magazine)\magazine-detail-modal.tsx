import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform
} from "react-native";
import {Image} from "expo-image";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import {useTranslation} from "react-i18next";
import {Magazine, MagazineEdition} from "@/models/api/magazines.models";
import {useMagazine, useRelatedMagazines} from "@/hooks/api/use-magazines";
import {useFileUrl} from "@/hooks/api/use-storage";
import {apiClient} from "@/services/api/base/api-client";
import stylesConstants from "@/styles/styles-constants";
import {formatDistanceToNow} from "date-fns";
import {ptBR} from "date-fns/locale";

interface MagazineDetailModalProps {
  magazine: Magazine;
  onClose?: () => void;
  onMagazinePress?: (magazine: Magazine) => void;
}

const MagazineDetailModal: React.FC<MagazineDetailModalProps> = ({
  magazine,
  onClose,
  onMagazinePress
}) => {
  const {t} = useTranslation();
  const screenWidth = Dimensions.get("window").width;
  const [isReadingMagazine, setIsReadingMagazine] = useState(false);

  const {data: fullMagazine, isLoading: isLoadingMagazine} = useMagazine(
    magazine.id
  );

  const {data: relatedMagazines, isLoading: isLoadingRelated} =
    useRelatedMagazines(magazine.id, 3);

  const displayMagazine = fullMagazine || magazine;

  // Função para lidar com o download de uma edição específica
  const handleReadEdition = async (edition: MagazineEdition) => {
    setIsReadingMagazine(true);

    try {
      console.log("📖 [MAGAZINE] Baixando edição para visualização:", {
        magazineId: displayMagazine.id,
        editionId: edition.id,
        editionTitle: edition.title,
        fileId: edition.fileId
      });

      // Obter token de autenticação
      const tokenData = await apiClient.getValidToken();
      if (!tokenData?.accessToken) {
        throw new Error("Token de autenticação não disponível");
      }

      // Construir URL do arquivo
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl) {
        throw new Error("EXPO_PUBLIC_API_BASE_URL não está configurado");
      }
      const fileUrl = `${baseUrl}/api/storage/${edition.fileId}`;

      // Criar nome do arquivo temporário
      const fileName = `revista-${displayMagazine.id}-edicao-${
        edition.id
      }-${Date.now()}.pdf`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Baixar o arquivo PDF com headers de autenticação
      const downloadResult = await FileSystem.downloadAsync(fileUrl, fileUri, {
        headers: {
          Authorization: `${tokenData.tokenType} ${tokenData.accessToken}`
        }
      });

      if (downloadResult.status !== 200) {
        throw new Error(`Download falhou com status: ${downloadResult.status}`);
      }

      console.log("📖 [MAGAZINE] Download da edição concluído:", {
        uri: downloadResult.uri,
        status: downloadResult.status,
        editionTitle: edition.title
      });

      // Verificar se o sharing está disponível
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        throw new Error(
          "Compartilhamento não está disponível neste dispositivo"
        );
      }

      // Abrir o PDF no app nativo
      await Sharing.shareAsync(downloadResult.uri, {
        mimeType: "application/pdf",
        dialogTitle: `${displayMagazine.title} - ${edition.title}`,
        UTI: "com.adobe.pdf"
      });

      console.log(
        "📖 [MAGAZINE] Edição aberta com sucesso no visualizador nativo:",
        edition.title
      );
    } catch (error) {
      console.error("📖 [MAGAZINE] Erro ao baixar/abrir edição:", error);

      Alert.alert(
        t("magazine.readError", "Erro"),
        t(
          "magazine.readErrorMessage",
          "Não foi possível baixar ou abrir a edição. Verifique sua conexão e se você tem um aplicativo para visualizar PDFs instalado."
        ),
        [{text: t("common.ok", "OK")}]
      );
    } finally {
      setIsReadingMagazine(false);
    }
  };

  const formatPublishDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ptBR
      });
    } catch {
      return dateString;
    }
  };

  const handleRelatedMagazinePress = (relatedMagazine: Magazine) => {
    if (onMagazinePress) {
      onMagazinePress(relatedMagazine);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        nestedScrollEnabled={true}
        bounces={true}
        scrollEnabled={true}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.headerContainer}>
          <Image
            source={{uri: displayMagazine.coverImage}}
            style={[styles.coverImage, {width: screenWidth - 32}]}
            contentFit="cover"
            placeholder="https://via.placeholder.com/400x600/0F7C4D/FFFFFF?text=Loading"
            transition={200}
          />

          <View style={styles.badgesContainer}>
            {displayMagazine.isNew && (
              <View style={styles.newBadge}>
                <Text style={styles.badgeText}>
                  {t("magazine.new", "Novo")}
                </Text>
              </View>
            )}
            {displayMagazine.isPremium && (
              <View style={styles.premiumBadge}>
                <Text style={styles.badgeText}>
                  {t("magazine.premium", "Premium")}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.contentSection}>
          <Text style={styles.category}>{displayMagazine.category}</Text>
          <Text style={styles.title}>{displayMagazine.title}</Text>

          <View style={styles.metadataContainer}>
            <Text style={styles.metadata}>
              {formatPublishDate(displayMagazine.publishDate)}
            </Text>
            <Text style={styles.metadataSeparator}>•</Text>
            <Text style={styles.metadata}>{displayMagazine.readTime}</Text>
          </View>

          {displayMagazine.author && (
            <View style={styles.authorContainer}>
              <Text style={styles.authorLabel}>
                {t("magazine.author", "Por")}:{" "}
              </Text>
              <Text style={styles.authorName}>{displayMagazine.author}</Text>
              {displayMagazine.authorTitle && (
                <Text style={styles.authorTitle}>
                  {" "}
                  • {displayMagazine.authorTitle}
                </Text>
              )}
            </View>
          )}

          <Text style={styles.description}>{displayMagazine.description}</Text>

          {isLoadingMagazine ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size="small"
                color={stylesConstants.colors.primary500}
              />
              <Text style={styles.loadingText}>
                {t("magazine.loadingContent", "Carregando conteúdo...")}
              </Text>
            </View>
          ) : (
            displayMagazine.content && (
              <View style={styles.fullContentContainer}>
                <Text style={styles.fullContent}>
                  {displayMagazine.content}
                </Text>
              </View>
            )
          )}

          {displayMagazine.tags && displayMagazine.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsLabel}>
                {t("magazine.tags", "Tags")}:
              </Text>
              <View style={styles.tagsWrapper}>
                {displayMagazine.tags.map((tag) => (
                  <View key={tag} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Botões das Edições */}
          {displayMagazine.editions && displayMagazine.editions.length > 0 ? (
            <View style={styles.readButtonContainer}>
              <Text style={styles.editionsTitle}>
                {t("magazine.editions", "Edições disponíveis")}
              </Text>
              {displayMagazine.editions.map((edition, index) => (
                <TouchableOpacity
                  key={edition.id}
                  style={[
                    styles.readButton,
                    index > 0 && styles.readButtonSpacing,
                    isReadingMagazine && styles.readButtonDisabled
                  ]}
                  onPress={() => handleReadEdition(edition)}
                  disabled={isReadingMagazine}
                >
                  {isReadingMagazine ? (
                    <View style={styles.readButtonContent}>
                      <ActivityIndicator
                        size="small"
                        color={stylesConstants.colors.white}
                        style={styles.readButtonLoader}
                      />
                      <Text style={styles.readButtonText}>
                        {t("magazine.downloading", "Baixando...")}
                      </Text>
                    </View>
                  ) : (
                    <View style={styles.editionButtonContent}>
                      <Text style={styles.readButtonText}>{edition.title}</Text>
                      <Text style={styles.editionDate}>
                        {new Date(edition.releaseDate).toLocaleDateString(
                          "pt-BR"
                        )}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.noEditionsContainer}>
              <Text style={styles.noEditionsText}>
                {t(
                  "magazine.noEditions",
                  "Esta revista ainda não possui edições disponíveis"
                )}
              </Text>
            </View>
          )}
        </View>

        {relatedMagazines && relatedMagazines.length > 0 && (
          <View style={styles.relatedSection}>
            <Text style={styles.relatedTitle}>
              {t("magazine.related", "Revistas relacionadas")}
            </Text>

            {isLoadingRelated ? (
              <ActivityIndicator
                size="small"
                color={stylesConstants.colors.primary500}
                style={styles.relatedLoading}
              />
            ) : (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.relatedScrollContent}
              >
                {relatedMagazines.map((relatedMagazine) => (
                  <TouchableOpacity
                    key={relatedMagazine.id}
                    style={styles.relatedItem}
                    onPress={() => handleRelatedMagazinePress(relatedMagazine)}
                  >
                    <Image
                      source={{uri: relatedMagazine.coverImage}}
                      style={styles.relatedImage}
                      contentFit="cover"
                    />
                    <Text style={styles.relatedItemTitle} numberOfLines={2}>
                      {relatedMagazine.title}
                    </Text>
                    <Text style={styles.relatedItemCategory}>
                      {relatedMagazine.category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.background,
    minHeight: 0
  },

  scrollContainer: {
    flex: 1,
    minHeight: 0
  },
  contentContainer: {
    paddingBottom: 80,
    flexGrow: 1
  },
  headerContainer: {
    position: "relative" as const,
    alignItems: "center" as const,
    marginBottom: 20
  },
  coverImage: {
    height: 300,
    borderRadius: 12,
    marginHorizontal: 16
  },
  badgesContainer: {
    position: "absolute" as const,
    top: 12,
    right: 28,
    flexDirection: "row" as const,
    gap: 8
  },
  newBadge: {
    backgroundColor: stylesConstants.colors.green400,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  premiumBadge: {
    backgroundColor: stylesConstants.colors.yellow400,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  badgeText: {
    color: stylesConstants.colors.white,
    fontSize: 12,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans
  },
  contentSection: {
    paddingHorizontal: 16
  },
  category: {
    color: stylesConstants.colors.primary500,
    fontSize: 14,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 8,
    textTransform: "uppercase" as const
  },
  title: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 24,
    fontWeight: "700" as const,
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 32,
    marginBottom: 12
  },
  metadataContainer: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    marginBottom: 16
  },
  metadata: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans
  },
  metadataSeparator: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    marginHorizontal: 8
  },
  authorContainer: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    marginBottom: 16,
    flexWrap: "wrap" as const
  },
  authorLabel: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans
  },
  authorName: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 14,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans
  },
  authorTitle: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    lineHeight: 24,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 20
  },
  loadingContainer: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    paddingVertical: 20
  },
  loadingText: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans,
    marginLeft: 8
  },
  fullContentContainer: {
    marginBottom: 20
  },
  fullContent: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    lineHeight: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  tagsContainer: {
    marginBottom: 20
  },
  tagsLabel: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 14,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 8
  },
  tagsWrapper: {
    flexDirection: "row" as const,
    flexWrap: "wrap" as const,
    gap: 8
  },
  tag: {
    backgroundColor: stylesConstants.colors.surface,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16
  },
  tagText: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 12,
    fontFamily: stylesConstants.fonts.openSans
  },
  relatedSection: {
    marginTop: 20,
    paddingHorizontal: 16
  },
  relatedTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 18,
    fontWeight: "700" as const,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 16
  },
  relatedLoading: {
    paddingVertical: 20
  },
  relatedScrollContent: {
    paddingRight: 16
  },
  relatedItem: {
    width: 120,
    marginRight: 12
  },
  relatedImage: {
    width: 120,
    height: 160,
    borderRadius: 8,
    marginBottom: 8
  },
  relatedItemTitle: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 12,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 16,
    marginBottom: 4
  },
  relatedItemCategory: {
    color: stylesConstants.colors.textSecondary,
    fontSize: 10,
    fontFamily: stylesConstants.fonts.openSans,
    textTransform: "uppercase" as const
  },

  // Estilos do botão "Ler revista"
  readButtonContainer: {
    marginTop: 24,
    marginBottom: 8,
    paddingHorizontal: 16
  },
  readButton: {
    backgroundColor: stylesConstants.colors.primary500,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center" as const,
    justifyContent: "center" as const,
    minHeight: 56,
    shadowColor: stylesConstants.colors.primary500,
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4
  },
  readButtonDisabled: {
    backgroundColor: stylesConstants.colors.gray400,
    shadowOpacity: 0
  },
  readButtonContent: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "center" as const
  },
  readButtonLoader: {
    marginRight: 8
  },
  readButtonText: {
    color: stylesConstants.colors.white,
    fontSize: 16,
    fontWeight: "600" as const,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center" as const
  },
  readButtonSpacing: {
    marginTop: 12
  },
  editionsTitle: {
    color: stylesConstants.colors.white,
    fontSize: 18,
    fontWeight: "700" as const,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 16,
    textAlign: "center" as const,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2
  },
  editionButtonContent: {
    alignItems: "center" as const,
    paddingVertical: 4
  },
  editionDate: {
    color: stylesConstants.colors.white,
    fontSize: 12,
    fontWeight: "400" as const,
    fontFamily: stylesConstants.fonts.openSans,
    marginTop: 4,
    opacity: 0.9,
    backgroundColor: "rgba(0, 0, 0, 0.2)",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4
  },
  noEditionsContainer: {
    marginTop: 24,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    marginHorizontal: 16
  },
  noEditionsText: {
    color: stylesConstants.colors.white,
    fontSize: 14,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center" as const,
    opacity: 0.7
  }
};

export default MagazineDetailModal;
