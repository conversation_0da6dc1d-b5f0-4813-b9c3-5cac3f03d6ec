/**
 * Tela de pagamento unificada
 * Suporta pagamentos de eventos, produtos e oportunidades
 */

import React, {useMemo} from "react";
import {Alert} from "react-native";
import {useLocalSearchParams, router} from "expo-router";
import {useTranslation} from "react-i18next";

import ScreenWithHeader from "@/components/screen-with-header";
import UnifiedPaymentFlow from "@/components/payment/unified-payment-flow";
import {PaymentEntity} from "@/models/api/payments.models";

const UnifiedPaymentScreen: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams();

  // Extração e validação dos parâmetros
  const paymentData = useMemo(() => {
    const entityId = parseInt(params.entityId as string, 10);
    const entityType = parseInt(
      params.entityType as string,
      10
    ) as PaymentEntity;
    const entityTitle = params.entityTitle as string;
    const entityDescription = params.entityDescription as string;
    const entityImageUrl = params.entityImageUrl as string;
    const amount = parseFloat(params.amount as string);

    // Validações
    if (isNaN(entityId) || entityId <= 0) {
      console.error(
        "❌ [UNIFIED-PAYMENT] ID da entidade inválido:",
        params.entityId
      );
      return null;
    }

    if (
      ![
        PaymentEntity.Event,
        PaymentEntity.Product,
        PaymentEntity.Opportunity
      ].includes(entityType)
    ) {
      console.error(
        "❌ [UNIFIED-PAYMENT] Tipo de entidade inválido:",
        params.entityType
      );
      return null;
    }

    if (!entityTitle) {
      console.error("❌ [UNIFIED-PAYMENT] Título da entidade obrigatório");
      return null;
    }

    if (isNaN(amount) || amount < 0) {
      console.error("❌ [UNIFIED-PAYMENT] Valor inválido:", params.amount);
      return null;
    }

    return {
      entityId,
      entityType,
      entityTitle,
      entityDescription: entityDescription || "",
      entityImageUrl: entityImageUrl || "",
      amount
    };
  }, [params]);

  // Título da tela baseado no tipo de entidade
  const screenTitle = useMemo(() => {
    if (!paymentData) return t("payment.title.generic");

    switch (paymentData.entityType) {
      case PaymentEntity.Product:
        return t("payment.title.product");
      case PaymentEntity.Event:
        return t("payment.title.event");
      case PaymentEntity.Opportunity:
        return t("payment.title.opportunity");
      default:
        return t("payment.title.generic");
    }
  }, [paymentData, t]);

  // Handlers
  const handlePaymentSuccess = (paymentId: string) => {
    console.log(
      "✅ [UNIFIED-PAYMENT] Pagamento realizado com sucesso:",
      paymentId
    );

    // O fluxo de pagamento unificado já navega para a tela de confirmação
    // Não precisamos fazer nada aqui, pois a navegação é feita no componente UnifiedPaymentFlow
  };

  const handlePaymentError = (error: string) => {
    console.error("❌ [UNIFIED-PAYMENT] Erro no pagamento:", error);

    Alert.alert(t("payment.error.title"), error, [
      {
        text: t("common.tryAgain"),
        style: "default"
      },
      {
        text: t("common.cancel"),
        style: "cancel",
        onPress: () => router.back()
      }
    ]);
  };

  const handleCancel = () => {
    Alert.alert(t("payment.cancel.title"), t("payment.cancel.message"), [
      {
        text: t("common.continue"),
        style: "cancel"
      },
      {
        text: t("payment.cancel.confirm"),
        style: "destructive",
        onPress: () => router.back()
      }
    ]);
  };

  // Validação dos dados
  if (!paymentData) {
    Alert.alert(t("payment.error.title"), t("payment.error.invalidData"), [
      {
        text: t("common.ok"),
        onPress: () => router.back()
      }
    ]);
    return null;
  }

  console.log("💰 [UNIFIED-PAYMENT] Iniciando pagamento:", {
    entityId: paymentData.entityId,
    entityType: paymentData.entityType,
    entityTitle: paymentData.entityTitle,
    amount: paymentData.amount
  });

  return (
    <ScreenWithHeader
      screenTitle={screenTitle}
      backButton
      onBackPress={handleCancel}
    >
      <UnifiedPaymentFlow
        entityId={paymentData.entityId}
        entityType={paymentData.entityType}
        entityTitle={paymentData.entityTitle}
        entityDescription={paymentData.entityDescription}
        entityImageUrl={paymentData.entityImageUrl}
        amount={paymentData.amount}
        allowInstallments={true}
        maxInstallments={12}
        showSavedCards={true}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
        onCancel={handleCancel}
      />
    </ScreenWithHeader>
  );
};

export default UnifiedPaymentScreen;
