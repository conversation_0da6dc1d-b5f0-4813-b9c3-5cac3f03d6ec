/**
 * FormData Client for React Native
 * Handles FormData requests using native fetch to avoid axios serialization issues
 */

import {ApiLogger} from "./api-logger";
import {TokenData} from "./api-types";

export interface FormDataRequestConfig {
  method?: "POST" | "PUT" | "PATCH";
  headers?: Record<string, string>;
  timeout?: number;
}

export interface FormDataResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

// Type for auth provider function
export type AuthProvider = () => TokenData | null;

export class FormDataClient {
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private static authProvider: AuthProvider | null = null;

  /**
   * Set authentication provider function
   */
  static setAuthProvider(provider: AuthProvider): void {
    this.authProvider = provider;
  }

  /**
   * Get base URL from environment
   */
  private static getBaseURL(): string {
    const baseURL = process.env["EXPO_PUBLIC_API_BASE_URL"] ?? "";
    return baseURL.endsWith("/") ? baseURL.slice(0, -1) : baseURL;
  }

  /**
   * Get authentication header from auth provider
   */
  private static getAuthHeader(): string | undefined {
    if (!this.authProvider) {
      return undefined;
    }

    const tokenData = this.authProvider();
    return tokenData?.accessToken
      ? `${tokenData.tokenType} ${tokenData.accessToken}`
      : undefined;
  }

  /**
   * Build full URL
   */
  private static buildURL(endpoint: string): string {
    const baseURL = this.getBaseURL();
    const cleanEndpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
    return `${baseURL}${cleanEndpoint}`;
  }

  /**
   * Create timeout promise
   */
  private static createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Send FormData request using native fetch
   */
  static async request<T = any>(
    endpoint: string,
    formData: FormData,
    config: FormDataRequestConfig = {}
  ): Promise<FormDataResponse<T>> {
    const {
      method = "POST",
      headers = {},
      timeout = this.DEFAULT_TIMEOUT
    } = config;

    const fullURL = this.buildURL(endpoint);
    const authHeader = this.getAuthHeader();

    // Log request details
    console.log(`🚀 [FormDataClient] Sending ${method} request to: ${fullURL}`);
    console.log(
      `🔑 [FormDataClient] Auth header: ${authHeader ? "Present" : "Missing"}`
    );

    // Log FormData contents for debugging
    try {
      console.log("📦 [FormDataClient] FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(
          `  ${key}: ${typeof value === "string" ? value : "[File/Blob]"}`
        );
      }
    } catch (error) {
      console.log("❌ [FormDataClient] Error iterating FormData:", error);
      console.log(
        "📦 [FormDataClient] FormData._parts:",
        (formData as any)._parts
      );
    }

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      // Don't set Content-Type - let fetch handle it for FormData
      "Accept-Language": "pt-br",
      ...headers,
      // Include Authorization if available
      ...(authHeader && {Authorization: authHeader})
    };

    try {
      // Create fetch promise
      const fetchPromise = fetch(fullURL, {
        method,
        body: formData,
        headers: requestHeaders
      });

      // Race between fetch and timeout
      const response = await Promise.race([
        fetchPromise,
        this.createTimeoutPromise(timeout)
      ]);

      console.log(
        `✅ [FormDataClient] Request completed: ${response.status} ${response.statusText}`
      );

      // Check if response is ok
      if (!response.ok) {
        const errorText = await response.text().catch(() => "Unknown error");
        console.log(`❌ [FormDataClient] Response not ok:`, {
          status: response.status,
          statusText: response.statusText,
          errorText
        });

        const error = new Error(
          `HTTP ${response.status}: ${response.statusText}`
        );
        (error as any).status = response.status;
        (error as any).response = {status: response.status, data: errorText};
        throw error;
      }

      // Parse response
      const responseData = await response.json();

      // Log success
      ApiLogger.info(`FormData request successful: ${method} ${endpoint}`, {
        status: response.status,
        dataKeys: Object.keys(responseData || {})
      });

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      };
    } catch (error: any) {
      console.log(`❌ [FormDataClient] Request failed:`, {
        error: error.message,
        type: error.constructor.name,
        status: error.status
      });

      // Log error
      ApiLogger.error(`FormData request failed: ${method} ${endpoint}`, error);

      throw error;
    }
  }

  /**
   * POST request with FormData
   */
  static async post<T = any>(
    endpoint: string,
    formData: FormData,
    config?: Omit<FormDataRequestConfig, "method">
  ): Promise<FormDataResponse<T>> {
    return this.request<T>(endpoint, formData, {...config, method: "POST"});
  }

  /**
   * PUT request with FormData
   */
  static async put<T = any>(
    endpoint: string,
    formData: FormData,
    config?: Omit<FormDataRequestConfig, "method">
  ): Promise<FormDataResponse<T>> {
    return this.request<T>(endpoint, formData, {...config, method: "PUT"});
  }

  /**
   * PATCH request with FormData
   */
  static async patch<T = any>(
    endpoint: string,
    formData: FormData,
    config?: Omit<FormDataRequestConfig, "method">
  ): Promise<FormDataResponse<T>> {
    return this.request<T>(endpoint, formData, {...config, method: "PATCH"});
  }
}
