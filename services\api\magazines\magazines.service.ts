/**
 * Serviço de Magazines para ClubM
 * Implementa operações para buscar e gerenciar revistas/magazines
 */

import {apiClient} from "../base/api-client";
import {PaginationResponse} from "@/models/api/common.models";
import {Magazine, MagazinesListParams} from "@/models/api/magazines.models";
import {firstValueFrom} from "rxjs";
import {
  RawMagazineData,
  transformRawMagazinesToMagazines
} from "@/utils/magazine-transform";

/**
 * Serviço para operações com magazines
 */
export class MagazinesService {
  private static readonly BASE_PATH = "/api/app/magazines";

  /**
   * Busca lista de magazines com paginação e filtros
   */
  static async getMagazines(
    params?: MagazinesListParams
  ): Promise<PaginationResponse<Magazine>> {
    console.log("📚 [MAGAZINES SERVICE] ===== INICIANDO REQUEST =====");
    console.log("📚 [MAGAZINES SERVICE] Fazendo request para:", this.BASE_PATH);
    console.log(
      "📚 [MAGAZINES SERVICE] Parâmetros:",
      JSON.stringify(params, null, 2)
    );

    try {
      const response = await firstValueFrom(
        apiClient.request<PaginationResponse<RawMagazineData>>(
          "GET",
          this.BASE_PATH,
          params
        )
      );

      console.log("📚 [MAGAZINES SERVICE] ===== RESPOSTA RECEBIDA =====");
      console.log("📚 [MAGAZINES SERVICE] Status: 200");
      console.log(
        "📚 [MAGAZINES SERVICE] Total de magazines:",
        response.data?.length || 0
      );
      console.log("📚 [MAGAZINES SERVICE] Paginação:", response.pagination);

      // Log detalhado dos primeiros magazines
      if (response.data && response.data.length > 0) {
        console.log("📚 [MAGAZINES SERVICE] Primeiros magazines (detalhado):");
        response.data.slice(0, 2).forEach((magazine, index) => {
          // Log da estrutura RAW das edições para debug
          if (magazine.editions && magazine.editions.length > 0) {
            console.log(
              `🔍 [DEBUG] Estrutura RAW das edições da revista ${magazine.id}:`,
              JSON.stringify(magazine.editions, null, 2)
            );
          }
          console.log(`📖 Magazine ${index + 1}:`, {
            id: magazine.id,
            title: magazine.title,
            description: magazine.description?.substring(0, 100) + "...",
            status: magazine.status,
            franchiseId: magazine.franchiseId,
            franchiseName: magazine.franchise?.name,
            editionsCount: magazine.editions?.length || 0,
            editions:
              magazine.editions?.map((edition) => ({
                id: edition.id || "N/A",
                title: edition.title || "N/A",
                description: edition.description || "N/A",
                fileId: edition.fileId || "❌ SEM FILEID",
                releaseDate: edition.releaseDate || "N/A",
                createdAt: edition.createdAt || "N/A",
                createdBy: edition.createdBy || "N/A"
              })) || []
          });
        });
      }

      // Transform raw data to Magazine format
      const transformedData = transformRawMagazinesToMagazines(
        response.data || []
      );

      return {
        ...response,
        data: transformedData
      };
    } catch (error: any) {
      console.error("📚 [MAGAZINES SERVICE] ===== ERRO =====");
      console.error("📚 [MAGAZINES SERVICE] Erro completo:", error);
      console.error("📚 [MAGAZINES SERVICE] Message:", error?.message);
      console.error("📚 [MAGAZINES SERVICE] Status:", error?.status);
      console.error(
        "📚 [MAGAZINES SERVICE] Response data:",
        error?.response?.data
      );
      console.error("📚 [MAGAZINES SERVICE] Config:", error?.config);
      throw error;
    }
  }

  /**
   * Busca um magazine específico por ID
   */
  static async getMagazine(id: string): Promise<Magazine> {
    try {
      console.log("📚 [MAGAZINES SERVICE] Buscando magazine por ID:", id);

      const response = await firstValueFrom(
        apiClient.request<RawMagazineData>("GET", `${this.BASE_PATH}/${id}`)
      );

      console.log("📚 [MAGAZINES SERVICE] Magazine encontrado (detalhado):", {
        id,
        title: response.title,
        description: response.description?.substring(0, 100) + "...",
        status: response.status,
        franchiseId: response.franchiseId,
        franchiseName: response.franchise?.name,
        editionsCount: response.editions?.length || 0,
        editions:
          response.editions?.map((edition) => ({
            id: edition.id || "N/A",
            title: edition.title || "N/A",
            description: edition.description || "N/A",
            fileId: edition.fileId || "❌ SEM FILEID",
            releaseDate: edition.releaseDate || "N/A",
            createdAt: edition.createdAt || "N/A",
            createdBy: edition.createdBy || "N/A"
          })) || []
      });

      // Transform raw data to Magazine format
      const transformedMagazine = transformRawMagazinesToMagazines([
        response
      ])[0];

      return transformedMagazine;
    } catch (error) {
      console.error("📚 [MAGAZINES SERVICE] Erro ao buscar magazine:", {
        id,
        error
      });
      throw error;
    }
  }

  /**
   * Busca magazines em destaque
   */
  static async getFeaturedMagazines(
    params?: Omit<MagazinesListParams, "isFeatured">
  ): Promise<PaginationResponse<Magazine>> {
    try {
      console.log(
        "📚 [MAGAZINES SERVICE] Buscando magazines em destaque:",
        params
      );

      const response = await this.getMagazines({
        ...params,
        isFeatured: true
      });

      console.log("📚 [MAGAZINES SERVICE] Magazines em destaque encontrados:", {
        count: response.data.length
      });

      return response;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines em destaque:",
        error
      );
      throw error;
    }
  }

  /**
   * Busca magazines novos/recentes
   */
  static async getNewMagazines(
    params?: Omit<MagazinesListParams, "isNew">
  ): Promise<PaginationResponse<Magazine>> {
    try {
      console.log("📚 [MAGAZINES SERVICE] Buscando magazines novos:", params);

      const response = await this.getMagazines({
        ...params,
        isNew: true
      });

      console.log("📚 [MAGAZINES SERVICE] Magazines novos encontrados:", {
        count: response.data.length
      });

      return response;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines novos:",
        error
      );
      throw error;
    }
  }

  /**
   * Busca magazines com termo de pesquisa
   */
  static async searchMagazines(
    searchTerm: string,
    params?: Omit<MagazinesListParams, "search">
  ): Promise<PaginationResponse<Magazine>> {
    try {
      console.log(
        "📚 [MAGAZINES SERVICE] Buscando magazines por termo:",
        searchTerm
      );

      const response = await this.getMagazines({
        ...params,
        search: searchTerm
      });

      console.log("📚 [MAGAZINES SERVICE] Magazines encontrados na busca:", {
        searchTerm,
        count: response.data.length
      });

      return response;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines por termo:",
        {
          searchTerm,
          error
        }
      );
      throw error;
    }
  }

  /**
   * Busca magazines por categoria
   */
  static async getMagazinesByCategory(
    category: string,
    params?: Omit<MagazinesListParams, "category">
  ): Promise<PaginationResponse<Magazine>> {
    try {
      console.log(
        "📚 [MAGAZINES SERVICE] Buscando magazines por categoria:",
        category
      );

      const response = await this.getMagazines({
        ...params,
        category
      });

      console.log(
        "📚 [MAGAZINES SERVICE] Magazines encontrados por categoria:",
        {
          category,
          count: response.data.length
        }
      );

      return response;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines por categoria:",
        {
          category,
          error
        }
      );
      throw error;
    }
  }

  /**
   * Busca magazines por autor
   */
  static async getMagazinesByAuthor(
    author: string,
    params?: Omit<MagazinesListParams, "author">
  ): Promise<PaginationResponse<Magazine>> {
    try {
      console.log(
        "📚 [MAGAZINES SERVICE] Buscando magazines por autor:",
        author
      );

      const response = await this.getMagazines({
        ...params,
        author
      });

      console.log("📚 [MAGAZINES SERVICE] Magazines encontrados por autor:", {
        author,
        count: response.data.length
      });

      return response;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines por autor:",
        {
          author,
          error
        }
      );
      throw error;
    }
  }

  /**
   * Busca magazines relacionados a um magazine específico
   */
  static async getRelatedMagazines(
    magazineId: string,
    limit: number = 5
  ): Promise<Magazine[]> {
    try {
      console.log("📚 [MAGAZINES SERVICE] Buscando magazines relacionados:", {
        magazineId,
        limit
      });

      // Primeiro busca o magazine atual para pegar sua categoria
      const currentMagazine = await this.getMagazine(magazineId);

      // Busca magazines da mesma categoria, excluindo o atual
      const response = await this.getMagazinesByCategory(
        currentMagazine.category,
        {
          pageSize: limit + 1 // +1 para excluir o atual
        }
      );

      // Remove o magazine atual da lista e limita o resultado
      const relatedMagazines = response.data
        .filter((mag) => mag.id !== magazineId)
        .slice(0, limit);

      console.log(
        "📚 [MAGAZINES SERVICE] Magazines relacionados encontrados:",
        {
          magazineId,
          count: relatedMagazines.length
        }
      );

      return relatedMagazines;
    } catch (error) {
      console.error(
        "📚 [MAGAZINES SERVICE] Erro ao buscar magazines relacionados:",
        {
          magazineId,
          error
        }
      );
      throw error;
    }
  }
}

export default MagazinesService;
