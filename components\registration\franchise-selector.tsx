/**
 * Franchise Selector Component
 * Reusable component for selecting franchises based on a plan
 */

import React, {useState, useCallback} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert
} from "react-native";
import {useTranslation} from "react-i18next";
import {useFranchisesByPlan} from "@/hooks/api";
import {FranchiseViewModel} from "@/services/api/plans/plans.service";
import CheckIcon from "@/components/icons/check-icon";

interface FranchiseSelectorProps {
  planId: number | null;
  selectedFranchiseId?: number | null;
  onFranchiseSelect: (franchise: FranchiseViewModel) => void;
  style?: any;
}

const FranchiseSelector: React.FC<FranchiseSelectorProps> = ({
  planId,
  selectedFranchiseId,
  onFranchiseSelect,
  style
}) => {
  const {t} = useTranslation();
  const [page, setPage] = useState(1);

  // Fetch franchises for the selected plan
  const {
    data: franchisesResponse,
    isLoading,
    error,
    refetch
  } = useFranchisesByPlan(
    planId!,
    {page, pageSize: 20},
    {enabled: !!planId}
  );

  const franchises = franchisesResponse?.data || [];

  const handleFranchisePress = useCallback(
    (franchise: FranchiseViewModel) => {
      onFranchiseSelect(franchise);
    },
    [onFranchiseSelect]
  );

  const handleRetry = useCallback(() => {
    if (planId) {
      refetch();
    }
  }, [planId, refetch]);

  const renderFranchiseItem = useCallback(
    ({item}: {item: FranchiseViewModel}) => (
      <TouchableOpacity
        style={{
          backgroundColor: selectedFranchiseId === item.id ? 'rgba(0, 122, 255, 0.1)' : 'rgba(255, 255, 255, 0.1)',
          borderWidth: selectedFranchiseId === item.id ? 2 : 1,
          borderColor: selectedFranchiseId === item.id ? '#007AFF' : 'rgba(255, 255, 255, 0.3)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 12,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
        onPress={() => handleFranchisePress(item)}
        activeOpacity={0.7}
      >
        <View style={{flex: 1}}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#FFFFFF',
            marginBottom: 4
          }}>
            {item.name}
          </Text>
          {item.description && (
            <Text style={{
              fontSize: 14,
              color: 'rgba(255, 255, 255, 0.8)',
              marginBottom: 4
            }}>
              {item.description}
            </Text>
          )}
          {item.location && (
            <Text style={{
              fontSize: 12,
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              📍 {item.location}
            </Text>
          )}
        </View>
        {selectedFranchiseId === item.id && (
          <View style={{
            width: 24,
            height: 24,
            borderRadius: 12,
            backgroundColor: '#007AFF',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: 12
          }}>
            <CheckIcon width={16} height={16} color="#FFFFFF" />
          </View>
        )}
      </TouchableOpacity>
    ),
    [selectedFranchiseId, handleFranchisePress]
  );

  if (!planId) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <Text style={{
          fontSize: 16,
          color: 'rgba(255, 255, 255, 0.8)',
          textAlign: 'center'
        }}>
          {t("franchiseSelector.selectPlanFirst", "Selecione um plano primeiro para ver as franquias disponíveis")}
        </Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={{
          marginTop: 12,
          fontSize: 16,
          color: '#FFFFFF',
          textAlign: 'center'
        }}>
          {t("franchiseSelector.loading", "Carregando franquias...")}
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <Text style={{
          fontSize: 16,
          color: '#FF6B6B',
          textAlign: 'center',
          marginBottom: 16
        }}>
          {t("franchiseSelector.error", "Erro ao carregar franquias")}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: '#007AFF',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8
          }}
          onPress={handleRetry}
        >
          <Text style={{
            color: '#FFFFFF',
            fontSize: 16,
            fontWeight: '600'
          }}>
            {t("franchiseSelector.retry", "Tentar novamente")}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (franchises.length === 0) {
    return (
      <View style={[{
        padding: 20,
        alignItems: 'center'
      }, style]}>
        <Text style={{
          fontSize: 16,
          color: 'rgba(255, 255, 255, 0.8)',
          textAlign: 'center'
        }}>
          {t("franchiseSelector.noFranchises", "Nenhuma franquia disponível para este plano")}
        </Text>
      </View>
    );
  }

  return (
    <View style={style}>
      <Text style={{
        fontSize: 18,
        fontWeight: '600',
        color: '#FFFFFF',
        marginBottom: 16
      }}>
        {t("franchiseSelector.title", "Selecione uma franquia")}
      </Text>
      <FlatList
        data={franchises}
        renderItem={renderFranchiseItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
    </View>
  );
};

export default FranchiseSelector;
