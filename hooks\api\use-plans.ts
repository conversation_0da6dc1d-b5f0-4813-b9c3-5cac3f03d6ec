/**
 * React Query hooks for plans management
 */

import {useQuery, UseQueryOptions} from "@tanstack/react-query";
import {
  PlansService,
  PlanViewModel,
  PlanListResponse,
  FranchiseViewModel,
  FranchiseListResponse,
  PlansListParams,
  FranchisesListParams
} from "@/services/api/plans/plans.service";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys for cache management
export const plansKeys = {
  all: ["plans"] as const,
  lists: () => [...plansKeys.all, "list"] as const,
  list: (params: PlansListParams) => [...plansKeys.lists(), params] as const,
  details: () => [...plansKeys.all, "detail"] as const,
  detail: (id: string | number) => [...plansKeys.details(), id] as const,
  franchises: (planId: string | number) =>
    [...plansKeys.all, "franchises", planId] as const,
  franchisesList: (planId: string | number, params: FranchisesListParams) =>
    [...plansKeys.franchises(planId), params] as const,
  public: (params: PlansListParams) =>
    [...plansKeys.all, "public", params] as const
};

/**
 * Hook to fetch subscription plans
 */
export const usePlans = (
  params?: PlansListParams,
  options?: UseQueryOptions<PlanListResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: plansKeys.list(params || {}),
    queryFn: () => PlansService.getPlans(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch subscription plans without authentication (for registration)
 */
export const usePlansPublic = (
  params?: PlansListParams,
  options?: UseQueryOptions<PlanListResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: plansKeys.public(params || {}),
    queryFn: () => PlansService.getPlansPublic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch a specific plan by ID
 */
export const usePlan = (
  planId: string | number,
  options?: UseQueryOptions<PlanViewModel, BaseApiError>
) => {
  return useQuery({
    queryKey: plansKeys.detail(planId),
    queryFn: () => PlansService.getPlanById(planId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!planId,
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch franchises for a specific plan
 */
export const useFranchisesByPlan = (
  planId: string | number,
  params?: FranchisesListParams,
  options?: UseQueryOptions<FranchiseListResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: plansKeys.franchisesList(planId, params || {}),
    queryFn: () => PlansService.getFranchisesByPlan(planId, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!planId,
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};
