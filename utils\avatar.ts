/**
 * Utilitários para manipulação de avatares de usuário
 */

/**
 * Constrói a URL completa do avatar usando o avatarId
 * @param avatarId - ID do avatar retornado pela API
 * @returns URL completa do avatar ou undefined se avatarId for inválido
 */
export const buildAvatarUrl = (
  avatarId?: string | null
): string | undefined => {
  if (!avatarId || avatarId.trim() === "") {
    return undefined;
  }

  const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
  if (!baseUrl) {
    console.warn("EXPO_PUBLIC_API_BASE_URL não está configurado");
    return undefined;
  }

  return `${baseUrl}/api/storage/${avatarId}`;
};

/**
 * Extrai a URL do avatar de um objeto de usuário
 * Prioriza o campo 'avatar' se existir, senão usa 'avatarId' para construir a URL
 * @param user - Objeto do usuário que pode conter avatar ou avatarId
 * @returns URL do avatar ou undefined
 */
export const getUserAvatarUrl = (
  user?: {
    avatar?: string;
    avatarId?: string;
  } | null
): string | undefined => {
  if (!user) {
    return undefined;
  }

  // Se já tem uma URL completa no campo avatar, usar ela
  if (user.avatar && user.avatar.trim() !== "") {
    return user.avatar;
  }

  // Senão, tentar construir a URL usando avatarId
  if (user.avatarId) {
    return buildAvatarUrl(user.avatarId);
  }

  return undefined;
};

/**
 * Verifica se uma string é uma URL válida de avatar
 * @param url - URL para verificar
 * @returns true se for uma URL válida
 */
export const isValidAvatarUrl = (url?: string | null): boolean => {
  if (!url || url.trim() === "") {
    return false;
  }

  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};
