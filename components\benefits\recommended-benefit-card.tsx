import React, {useCallback} from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import GiftIcon from "@/components/icons/gift-icon";
import ChevronRightIcon from "@/components/icons/chevron-right-icon";
import styles from "@/styles/components/benefits/recommended-benefit-card.style";

export interface RecommendedBenefitCardProps {
  benefitId: number;
  title: string;
  description: string;
  partnerId: number;
  partnerName: string;
  imageUrl?: string;
  icon?: React.ReactNode;
  onPress?: () => void;
}

/**
 * Recommended benefit card component that displays benefit information
 * in a card format with interaction capabilities.
 * 
 * Features:
 * - Benefit title and description
 * - Partner information
 * - Optional image or icon
 * - Touch interaction
 * - Consistent styling
 */
const RecommendedBenefitCard: React.FC<RecommendedBenefitCardProps> = ({
  benefitId,
  title,
  description,
  partnerId,
  partnerName,
  imageUrl,
  icon,
  onPress
}) => {
  const {t} = useTranslation();

  const handlePress = useCallback(() => {
    onPress?.();
  }, [onPress]);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      {/* Benefit Icon/Image */}
      <View style={styles.iconContainer}>
        {imageUrl ? (
          <Image
            source={{uri: imageUrl}}
            style={styles.image}
            contentFit="cover"
          />
        ) : (
          icon || <GiftIcon width={24} height={24} color="#4A90E2" />
        )}
      </View>

      {/* Benefit Content */}
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={2}>
            {title}
          </Text>
          <Text style={styles.partnerName} numberOfLines={1}>
            {partnerName}
          </Text>
        </View>

        <Text style={styles.description} numberOfLines={3}>
          {description}
        </Text>

        <View style={styles.footer}>
          <Text style={styles.benefitLabel}>{t("partners.benefit")}</Text>
        </View>
      </View>

      {/* Arrow Icon */}
      <View style={styles.arrowContainer}>
        <ChevronRightIcon width={16} height={16} color="#666" />
      </View>
    </TouchableOpacity>
  );
};

export default RecommendedBenefitCard;
