import React, {useCallback, useMemo, useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
  RadialGradient,
  Rect,
  Circle
} from "react-native-svg";
import UserHeader from "@/components/user/user-header";
import UserTabs from "@/components/user/user-tabs";
import OpportunityCard from "@/components/user/opportunity-card";
import Seals from "@/components/user/seals";
import {useAuth} from "@/contexts/AuthContext";
import stylesConstants from "@/styles/styles-constants";

import styles from "@/styles/profile/about-me.style";
import InstagramIconColor from "@/components/icons/instagram-icon-color";

// SVG Icons extracted from Motiff design
const EmailIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
    <Path
      d="M20 14.9996C20 15.5889 19.7659 16.1542 19.3491 16.5709C18.9324 16.9877 18.3671 17.2218 17.7778 17.2218H2.22222C1.63285 17.2218 1.06762 16.9877 0.650874 16.5709C0.234126 16.1542 0 15.5889 0 14.9996V4.99957C0 4.4102 0.234126 3.84496 0.650874 3.42822C1.06762 3.01147 1.63285 2.77734 2.22222 2.77734H17.7778C18.3671 2.77734 18.9324 3.01147 19.3491 3.42822C19.7659 3.84496 20 4.4102 20 4.99957V14.9996Z"
      fill="#CCD6DD"
    />
    <Path
      d="M6.63889 9.79766L0.35389 16.0827C0.33889 16.0982 0.333334 16.1177 0.320557 16.1332C0.509446 16.4499 0.772779 16.7127 1.08945 16.9021C1.10556 16.8893 1.12445 16.8838 1.13945 16.8688L7.425 10.5832C7.52917 10.479 7.58767 10.3376 7.58761 10.1902C7.58756 10.0429 7.52897 9.90155 7.42472 9.79738C7.32048 9.69321 7.17912 9.63471 7.03175 9.63477C6.88438 9.63482 6.74306 9.69341 6.63889 9.79766ZM19.6794 16.1332C19.6678 16.1177 19.6611 16.0982 19.6461 16.0832L13.3617 9.79766C13.3101 9.74604 13.2488 9.70509 13.1814 9.67714C13.114 9.64919 13.0418 9.63479 12.9688 9.63477C12.8958 9.63474 12.8236 9.64909 12.7561 9.67699C12.6887 9.70489 12.6275 9.7458 12.5758 9.79738C12.5242 9.84896 12.4833 9.9102 12.4553 9.97761C12.4274 10.045 12.413 10.1173 12.4129 10.1902C12.4129 10.2632 12.4273 10.3355 12.4552 10.4029C12.4831 10.4703 12.524 10.5316 12.5756 10.5832L18.8606 16.8688C18.875 16.8832 18.895 16.8893 18.9106 16.9021C19.2266 16.7134 19.4908 16.4492 19.6794 16.1332Z"
      fill="#99AAB5"
    />
    <Path
      d="M17.7778 2.77734H2.22222C1.63285 2.77734 1.06762 3.01147 0.650874 3.42822C0.234126 3.84496 0 4.4102 0 4.99957L0 5.57179L8.07111 13.6251C8.57915 14.1306 9.26666 14.4144 9.98333 14.4144C10.7 14.4144 11.3875 14.1306 11.8956 13.6251L20 5.56012V4.99957C20 4.4102 19.7659 3.84496 19.3491 3.42822C18.9324 3.01147 18.3671 2.77734 17.7778 2.77734Z"
      fill="#99AAB5"
    />
    <Path
      d="M17.7778 2.77734H2.2222C1.80397 2.77814 1.39449 2.89725 1.04108 3.12089C0.687672 3.34454 0.404741 3.66361 0.224976 4.04123L8.42831 12.2451C8.63466 12.4515 8.87964 12.6152 9.14926 12.7269C9.41888 12.8386 9.70786 12.896 9.9997 12.896C10.2915 12.896 10.5805 12.8386 10.8501 12.7269C11.1198 12.6152 11.3647 12.4515 11.5711 12.2451L19.775 4.04123C19.5952 3.66361 19.3123 3.34454 18.9589 3.12089C18.6055 2.89725 18.196 2.77814 17.7778 2.77734Z"
      fill="#E1E8ED"
    />
  </Svg>
);

const PhoneIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
    <Path
      d="M6.11114 20C6.11114 20 3.88892 20 3.88892 17.7778V2.22222C3.88892 2.22222 3.88892 0 6.11114 0H13.8889C13.8889 0 16.1111 0 16.1111 2.22222V17.7778C16.1111 17.7778 16.1111 20 13.8889 20H6.11114Z"
      fill="#31373D"
    />
    <Path d="M5 2.77734H15V17.2218H5V2.77734Z" fill="#55ACEE" />
  </Svg>
);

const FacebookIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
    <Defs>
      <LinearGradient
        id="paint0_linear"
        x1="10"
        y1="1.25"
        x2="10"
        y2="18.6981"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#18ACFE" />
        <Stop offset="1" stopColor="#0163E0" />
      </LinearGradient>
    </Defs>

    <Circle cx="10" cy="10" r="8.75" fill="url(#paint0_linear)" />
    <Path
      d="M13.2586 12.676L13.6472 10.2063H11.2158V8.60437C11.2158 7.92856 11.5548 7.26942 12.6438 7.26942H13.75V5.16687C13.75 5.16687 12.7466 5 11.7877 5C9.78427 5 8.47604 6.18309 8.47604 8.32403V10.2063H6.25V12.676H8.47604V18.6466C8.92294 18.715 9.38015 18.75 9.8459 18.75C10.3117 18.75 10.7689 18.715 11.2158 18.6466V12.676H13.2586Z"
      fill="#DFE9F0"
    />
  </Svg>
);

const TwitterIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 16 16" fill="none">
    <Path
      d="M12.1624 1.5H14.3687L9.5499 7.00625L15.2187 14.5H10.7812L7.30303 9.95625L3.32803 14.5H1.11865L6.27178 8.60938L0.837402 1.5H5.3874L8.52803 5.65312L12.1624 1.5ZM11.3874 13.1812H12.6093L4.72178 2.75H3.40928L11.3874 13.1812Z"
      fill="black"
    />
  </Svg>
);

const InstagramIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 20 20" fill="none">
    <Defs>
      <RadialGradient
        id="paint0_radial"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(7.5 14.375) rotate(-55.3758) scale(15.9498)"
      >
        <Stop stopColor="#B13589" />
        <Stop offset="0.79309" stopColor="#C62F94" />
        <Stop offset="1" stopColor="#8A3AC8" />
      </RadialGradient>

      <RadialGradient
        id="paint1_radial"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(6.875 19.375) rotate(-65.1363) scale(14.1214)"
      >
        <Stop stopColor="#E0E8B7" />
        <Stop offset="0.444662" stopColor="#FB8A2E" />
        <Stop offset="0.71474" stopColor="#E2425C" />
        <Stop offset="1" stopColor="#E2425C" stopOpacity="0" />
      </RadialGradient>

      <RadialGradient
        id="paint2_radial"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(0.312501 1.875) rotate(-8.1301) scale(24.3068 5.19897)"
      >
        <Stop offset="0.156701" stopColor="#406ADC" />
        <Stop offset="0.467799" stopColor="#6A45BE" />
        <Stop offset="1" stopColor="#6A45BE" stopOpacity="0" />
      </RadialGradient>
    </Defs>

    <Rect
      x="1.25"
      y="1.25"
      width="17.5"
      height="17.5"
      rx="6"
      fill="url(#paint0_radial)"
    />
    <Rect
      x="1.25"
      y="1.25"
      width="17.5"
      height="17.5"
      rx="6"
      fill="url(#paint1_radial)"
    />
    <Rect
      x="1.25"
      y="1.25"
      width="17.5"
      height="17.5"
      rx="6"
      fill="url(#paint2_radial)"
    />
    <Path
      d="M14.375 6.5625C14.375 7.08027 13.9553 7.5 13.4375 7.5C12.9197 7.5 12.5 7.08027 12.5 6.5625C12.5 6.04473 12.9197 5.625 13.4375 5.625C13.9553 5.625 14.375 6.04473 14.375 6.5625Z"
      fill="#DFE9F0"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 13.125C11.7259 13.125 13.125 11.7259 13.125 10C13.125 8.27411 11.7259 6.875 10 6.875C8.27411 6.875 6.875 8.27411 6.875 10C6.875 11.7259 8.27411 13.125 10 13.125ZM10 11.875C11.0355 11.875 11.875 11.0355 11.875 10C11.875 8.96447 11.0355 8.125 10 8.125C8.96447 8.125 8.125 8.96447 8.125 10C8.125 11.0355 8.96447 11.875 10 11.875Z"
      fill="#DFE9F0"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.75 9.75C3.75 7.6498 3.75 6.5997 4.15873 5.79754C4.51825 5.09193 5.09193 4.51825 5.79754 4.15873C6.5997 3.75 7.6498 3.75 9.75 3.75H10.25C12.3502 3.75 13.4003 3.75 14.2025 4.15873C14.9081 4.51825 15.4817 5.09193 15.8413 5.79754C16.25 6.5997 16.25 7.6498 16.25 9.75V10.25C16.25 12.3502 16.25 13.4003 15.8413 14.2025C15.4817 14.9081 14.9081 15.4817 14.2025 15.8413C13.4003 16.25 12.3502 16.25 10.25 16.25H9.75C7.6498 16.25 6.5997 16.25 5.79754 15.8413C5.09193 15.4817 4.51825 14.9081 4.15873 14.2025C3.75 13.4003 3.75 12.3502 3.75 10.25V9.75ZM9.75 5H10.25C11.3207 5 12.0486 5.00097 12.6112 5.04694C13.1592 5.09171 13.4395 5.17287 13.635 5.27248C14.1054 5.51217 14.4878 5.89462 14.7275 6.36502C14.8271 6.56052 14.9083 6.84078 14.9531 7.3888C14.999 7.95141 15 8.67928 15 9.75V10.25C15 11.3207 14.999 12.0486 14.9531 12.6112C14.9083 13.1592 14.8271 13.4395 14.7275 13.635C14.4878 14.1054 14.1054 14.4878 13.635 14.7275C13.4395 14.8271 13.1592 14.9083 12.6112 14.9531C12.0486 14.999 11.3207 15 10.25 15H9.75C8.67928 15 7.95141 14.999 7.3888 14.9531C6.84078 14.9083 6.56052 14.8271 6.36502 14.7275C5.89462 14.4878 5.51217 14.1054 5.27248 13.635C5.17287 13.4395 5.09171 13.1592 5.04694 12.6112C5.00097 12.0486 5 11.3207 5 10.25V9.75C5 8.67928 5.00097 7.95141 5.04694 7.3888C5.09171 6.84078 5.17287 6.56052 5.27248 6.36502C5.51217 5.89462 5.89462 5.51217 6.36502 5.27248C6.56052 5.17287 6.84078 5.09171 7.3888 5.04694C7.95141 5.00097 8.67928 5 9.75 5Z"
      fill="#DFE9F0"
    />
  </Svg>
);

const LinkedinIcon: React.FC = () => (
  <Svg width="30" height="30" viewBox="0 0 18 18" fill="none">
    <Rect
      x="0.25"
      y="0.25"
      width="17.5"
      height="17.5"
      rx="8.75"
      fill="#1275B1"
    />
    <Path
      d="M6.8866 5.05759C6.8866 5.64169 6.38032 6.11519 5.7558 6.11519C5.13128 6.11519 4.625 5.64169 4.625 5.05759C4.625 4.4735 5.13128 4 5.7558 4C6.38032 4 6.8866 4.4735 6.8866 5.05759Z"
      fill="#DFE9F0"
    />
    <Path d="M4.77964 6.89256H6.71263V12.75H4.77964V6.89256Z" fill="#DFE9F0" />
    <Path
      d="M9.82474 6.89256H7.89175V12.75H9.82474C9.82474 12.75 9.82474 10.906 9.82474 9.75304C9.82474 9.06101 10.061 8.36596 11.0039 8.36596C12.0694 8.36596 12.063 9.27158 12.058 9.97318C12.0515 10.8903 12.067 11.8262 12.067 12.75H14V9.65857C13.9836 7.68461 13.4693 6.77505 11.7771 6.77505C10.7721 6.77505 10.1492 7.23129 9.82474 7.64406V6.89256Z"
      fill="#DFE9F0"
    />
  </Svg>
);

const ArrowIcon: React.FC = () => (
  <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
    <Path
      d="M7.5 15L12.5 10L7.5 5"
      stroke="#FCFCFD"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

enum UserTabsEnum {
  Opportunities = 1,
  ClaimedBadges = 2,
  AboutMe = 3
}

const AboutMe: React.FC = () => {
  const {t} = useTranslation();
  const {user, isLoading} = useAuth();
  const [activeTab, setActiveTab] = useState(UserTabsEnum.AboutMe);

  const tabs = [
    {
      title: t("user.opportunities", "Oportunidades"),
      id: UserTabsEnum.Opportunities
    },
    {
      title: t("user.claimedBadges", "Selos e conquistas"),
      id: UserTabsEnum.ClaimedBadges
    },
    {title: t("user.aboutMe", "Sobre mim"), id: UserTabsEnum.AboutMe}
  ];

  const onTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const opportunities = useMemo(() => {
    const list = [];
    for (let i = 0; i < 10; i++) {
      list.push(
        <OpportunityCard
          key={i + 1}
          id={i + 1}
          title={`Oportunidade ${i + 1}`}
          avatarUrl={
            "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg"
          }
          userName={"Mozart"}
          createdAt={new Date("2023-10-01T12:00:00Z")}
          description={
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus volutpat imperdiet facilisis. Donec rhoncus lorem lacus, sed ornare nisi imperdiet vel."
          }
          value={1000}
          imageUrl="https://upload.wikimedia.org/wikipedia/commons/0/01/Steinway_Vienna_002.JPG"
        />
      );
    }
    return list;
  }, []);

  const seals = useMemo(() => {
    const list = [];
    for (let i = 0; i < 8; i++) {
      list.push({
        id: i + 1,
        title: `Ouro`,
        earnedAt: new Date().toISOString()
      });
    }
    return list;
  }, []);

  const aboutMeContent = useMemo(
    () => (
      <View>
        {/* Professional Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Perfil profissional</Text>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Especializações</Text>
            <Text style={styles.value}>
              Startup Manager, Especialista em Venture Capital.
            </Text>
            <ArrowIcon />
          </View>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Áreas de Interesse</Text>
            <Text style={styles.value}>
              Head Growth (Crescimento de Negócios), Análise de Inteligência de
              Mercado.
            </Text>
            <ArrowIcon />
          </View>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Objetivos</Text>
            <Text style={styles.value}>Estudos de Product Manager.</Text>
            <ArrowIcon />
          </View>
        </View>

        {/* Contacts Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contatos</Text>

          {isLoading ? (
            <View style={{alignItems: "center", padding: 20}}>
              <ActivityIndicator
                size="large"
                color={stylesConstants.colors.primary}
              />
              <Text
                style={{
                  marginTop: 10,
                  color: stylesConstants.colors.textSecondary
                }}
              >
                Carregando dados do perfil...
              </Text>
            </View>
          ) : (
            <View style={styles.contactsGrid}>
              <View style={styles.contactItem}>
                <View style={styles.socialIconContainer}>
                  <EmailIcon />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactLabel}>E-mail</Text>
                  <Text style={styles.contactValue}>
                    {user?.email || "E-mail não informado"}
                  </Text>
                </View>
              </View>

              <View style={styles.contactItem}>
                <View style={styles.socialIconContainer}>
                  <PhoneIcon />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactLabel}>Telefone</Text>
                  <Text style={styles.contactValue}>
                    {user?.phone || "Telefone não informado"}
                  </Text>
                </View>
              </View>
            </View>
          )}
        </View>

        {/* Social Networks Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Redes sociais</Text>

          <View style={styles.socialGrid}>
            <View style={styles.socialRow}>
              <View style={styles.socialItem}>
                <View style={styles.socialIconContainer}>
                  <FacebookIcon />
                </View>
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Facebook</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Visualizar link</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.socialItem}>
                <View style={styles.socialIconContainer}>
                  <TwitterIcon />
                </View>
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>X</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Visualizar link</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View style={styles.socialRow}>
              <View style={styles.socialItem}>
                <View style={styles.socialIconContainer}>
                  <InstagramIcon />
                </View>
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Instagram</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Visualizar link</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.socialItem}>
                <View style={styles.socialIconContainer}>
                  <LinkedinIcon />
                </View>
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Linkedin</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Visualizar link</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    ),
    []
  );

  return <View>{aboutMeContent}</View>;
};

export default AboutMe;
