import { StyleSheet } from 'react-native';
import stylesConstants from '../styles-constants';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: stylesConstants.colors.gray900,
  },
  backdropTouchable: {
    flex: 1,
  },
  drawer: {
    backgroundColor: stylesConstants.colors.gray100,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '85%',
    minHeight: 500,
  },
  safeArea: {
    flex: 1,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: stylesConstants.colors.gray300,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: stylesConstants.colors.gray900,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  optionsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  optionItem: {
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOptionItem: {
    backgroundColor: stylesConstants.colors.brand.brand50,
    borderColor: stylesConstants.colors.brand.brand500,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: stylesConstants.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIcon: {
    fontSize: 24,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: stylesConstants.colors.gray900,
    marginBottom: 4,
  },
  selectedOptionLabel: {
    color: stylesConstants.colors.brand.brand700,
  },
  optionDescription: {
    fontSize: 14,
    color: stylesConstants.colors.gray600,
    lineHeight: 20,
  },
  selectedOptionDescription: {
    color: stylesConstants.colors.brand.brand600,
  },
  checkmarkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: stylesConstants.colors.brand.brand500,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  checkmark: {
    fontSize: 14,
    fontWeight: 'bold',
    color: stylesConstants.colors.gray100,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.gray200,
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: stylesConstants.colors.gray300,
  },
  confirmButton: {
    backgroundColor: stylesConstants.colors.brand.brand500,
  },
  disabledButton: {
    backgroundColor: stylesConstants.colors.gray300,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: stylesConstants.colors.gray700,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: stylesConstants.colors.gray100,
  },
});

export default styles;
