import React, {useMemo} from "react";
import {TouchableOpacity, Text, View, ActivityIndicator} from "react-native";
import {useTranslation} from "react-i18next";
import BiometricIcon from "@/components/icons/biometric-icon";
import useBiometricAvailability from "@/hooks/use-biometric-availability";
import useBiometricLogin from "@/hooks/use-biometric-login";
import useBiometricSessionCheck from "@/hooks/use-biometric-session-check";
import styles from "@/styles/components/biometric-login-button.style";

export interface BiometricLoginButtonProps {
  disabled?: boolean;
}

const BiometricLoginButton: React.FC<BiometricLoginButtonProps> = ({
  disabled = false
}) => {
  const {t} = useTranslation();
  const {
    isAvailable,
    isEnrolled,
    isLoading: availabilityLoading
  } = useBiometricAvailability();
  const {isLoading: authLoading, authenticateWithBiometrics} =
    useBiometricLogin();
  const {hasSessionData, isChecking: sessionChecking} =
    useBiometricSessionCheck();

  const isVisible = useMemo(() => {
    return (
      !availabilityLoading && !sessionChecking && isAvailable && isEnrolled
    );
  }, [availabilityLoading, sessionChecking, isAvailable, isEnrolled]);

  const isButtonDisabled = useMemo(() => {
    return (
      disabled ||
      authLoading ||
      availabilityLoading ||
      sessionChecking ||
      !hasSessionData
    );
  }, [
    disabled,
    authLoading,
    availabilityLoading,
    sessionChecking,
    hasSessionData
  ]);

  if (!isVisible) {
    return null;
  }

  return (
    <TouchableOpacity
      style={[styles.container, isButtonDisabled && styles.disabled]}
      onPress={authenticateWithBiometrics}
      disabled={isButtonDisabled}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {authLoading ? (
          <ActivityIndicator size="small" color="#F2F4F7" />
        ) : (
          <BiometricIcon width={20} height={20} replaceColor="#F2F4F7" />
        )}
        <Text style={styles.text}>{t("login.biometricLogin")}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default BiometricLoginButton;
