import React, {useEffect, useRef, useState, useCallback} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
  SafeAreaView,
  ScrollView
} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "@/styles/components/date-time-picker-drawer.style";
import ChevronLeftIcon from "@/components/icons/chevron-left-icon";
import ChevronRightIcon from "@/components/icons/chevron-right-icon";
import ClockIcon from "@/components/icons/clock-icon";

export interface DateTimePickerDrawerProps {
  visible: boolean;
  selectedDate?: Date;
  onDateTimeSelect: (date: Date) => void;
  onClose: () => void;
  title?: string;
  hideTimeSection?: boolean;
  allowPastDates?: boolean;
}

const DateTimePickerDrawer: React.FC<DateTimePickerDrawerProps> = ({
  visible,
  selectedDate,
  onDateTimeSelect,
  onClose,
  title = "Data/horário do evento",
  hideTimeSection = false,
  allowPastDates = false
}) => {
  const {t} = useTranslation();
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(
    selectedDate ? selectedDate.getMonth() : new Date().getMonth()
  );
  const [currentYear, setCurrentYear] = useState(
    selectedDate ? selectedDate.getFullYear() : new Date().getFullYear()
  );
  const [selectedDay, setSelectedDay] = useState<number | null>(
    selectedDate ? selectedDate.getDate() : null
  );

  // Time state
  const [selectedHour, setSelectedHour] = useState(
    selectedDate ? selectedDate.getHours() : new Date().getHours()
  );
  const [selectedMinute, setSelectedMinute] = useState(
    selectedDate ? selectedDate.getMinutes() : new Date().getMinutes()
  );

  // Time picker visibility
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // End time state (2 hours after start time by default)
  const [endHour, setEndHour] = useState(
    selectedDate ? selectedDate.getHours() + 2 : new Date().getHours() + 2
  );
  const [endMinute, setEndMinute] = useState(
    selectedDate ? selectedDate.getMinutes() : new Date().getMinutes()
  );

  const monthNames = [
    "Janeiro",
    "Fevereiro",
    "Março",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro"
  ];

  const weekDays = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true
      }).start();
    }
  }, [visible, slideAnim]);

  const navigateMonth = useCallback(
    (direction: "prev" | "next") => {
      if (direction === "prev") {
        if (currentMonth === 0) {
          setCurrentMonth(11);
          setCurrentYear(currentYear - 1);
        } else {
          setCurrentMonth(currentMonth - 1);
        }
      } else {
        if (currentMonth === 11) {
          setCurrentMonth(0);
          setCurrentYear(currentYear + 1);
        } else {
          setCurrentMonth(currentMonth + 1);
        }
      }
    },
    [currentMonth, currentYear]
  );

  const getDaysInMonth = useCallback(() => {
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    // Get previous month's last days
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();

    // Get next month's first days
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    const weeks: Array<
      Array<{day: number; isCurrentMonth: boolean; date: Date}>
    > = [];
    let currentWeek: Array<{day: number; isCurrentMonth: boolean; date: Date}> =
      [];

    // Add previous month's days
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const day = prevMonthLastDay - i;
      currentWeek.push({
        day,
        isCurrentMonth: false,
        date: new Date(prevYear, prevMonth, day)
      });
    }

    // Add current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      currentWeek.push({
        day,
        isCurrentMonth: true,
        date: new Date(currentYear, currentMonth, day)
      });

      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    // Add next month's days to complete the last week
    let nextMonthDay = 1;
    while (currentWeek.length < 7) {
      currentWeek.push({
        day: nextMonthDay,
        isCurrentMonth: false,
        date: new Date(nextYear, nextMonth, nextMonthDay)
      });
      nextMonthDay++;
    }

    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }

    return weeks;
  }, [currentMonth, currentYear]);

  const handleDayPress = useCallback(
    (day: number) => {
      const selectedDate = new Date(currentYear, currentMonth, day);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison

      // Allow selection based on allowPastDates prop
      if (allowPastDates || selectedDate >= today) {
        setSelectedDay(day);
      }
    },
    [currentMonth, currentYear, allowPastDates]
  );

  // Time picker handlers
  const handleStartTimePress = useCallback(() => {
    setShowStartTimePicker(true);
  }, []);

  const handleEndTimePress = useCallback(() => {
    setShowEndTimePicker(true);
  }, []);

  const handleStartTimeSelect = useCallback((hour: number, minute: number) => {
    setSelectedHour(hour);
    setSelectedMinute(minute);

    // Automatically update end time to be 2 hours later
    const endTime = new Date();
    endTime.setHours(hour, minute);
    endTime.setTime(endTime.getTime() + 2 * 60 * 60 * 1000);

    setEndHour(endTime.getHours());
    setEndMinute(endTime.getMinutes());
    setShowStartTimePicker(false);
  }, []);

  const handleEndTimeSelect = useCallback((hour: number, minute: number) => {
    setEndHour(hour);
    setEndMinute(minute);
    setShowEndTimePicker(false);
  }, []);

  const formatTime = useCallback((hour: number, minute: number) => {
    return `${hour.toString().padStart(2, "0")}:${minute
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const handleApply = useCallback(() => {
    if (selectedDay) {
      const newDate = new Date(
        currentYear,
        currentMonth,
        selectedDay,
        selectedHour,
        selectedMinute
      );
      onDateTimeSelect(newDate);
      onClose();
    }
  }, [
    selectedDay,
    currentMonth,
    currentYear,
    selectedHour,
    selectedMinute,
    onDateTimeSelect,
    onClose
  ]);

  const clearSelection = useCallback(() => {
    setSelectedDay(null);
  }, []);

  const isToday = useCallback((date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }, []);

  const isPastDate = useCallback((date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    return compareDate < today;
  }, []);

  const weeks = getDaysInMonth();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [Dimensions.get("window").height, 0]
                  })
                }
              ]
            }
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            <View style={styles.handleBar} />

            <Text style={styles.title}>{title}</Text>

            <ScrollView
              style={styles.content}
              showsVerticalScrollIndicator={false}
            >
              {/* Calendar Section */}
              <Text style={styles.sectionTitle}>Selecionar datas</Text>

              <View style={styles.calendarContainer}>
                {/* Calendar Header */}
                <View style={styles.calendarHeader}>
                  <TouchableOpacity
                    onPress={() => {
                      const newYear = currentYear - 1;
                      setCurrentYear(newYear);
                    }}
                    style={styles.navButton}
                  >
                    <ChevronLeftIcon width={20} height={20} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => navigateMonth("prev")}
                    style={styles.navButton}
                  >
                    <ChevronLeftIcon width={16} height={16} />
                  </TouchableOpacity>

                  <Text style={styles.monthYear}>
                    {monthNames[currentMonth]} {currentYear}
                  </Text>

                  <TouchableOpacity
                    onPress={() => navigateMonth("next")}
                    style={styles.navButton}
                  >
                    <ChevronRightIcon width={16} height={16} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      const newYear = currentYear + 1;
                      setCurrentYear(newYear);
                    }}
                    style={styles.navButton}
                  >
                    <ChevronRightIcon width={20} height={20} />
                  </TouchableOpacity>
                </View>

                {/* Week Days Header */}
                <View style={styles.weekDaysHeader}>
                  {weekDays.map((day, index) => (
                    <Text key={index} style={styles.weekDayText}>
                      {day}
                    </Text>
                  ))}
                </View>

                {/* Calendar Grid */}
                <View style={styles.calendarGrid}>
                  {weeks.map((week, weekIndex) => (
                    <View key={weekIndex} style={styles.calendarRow}>
                      {week.map((dayData, dayIndex) => (
                        <TouchableOpacity
                          key={dayIndex}
                          style={[
                            styles.dayCell,
                            dayData.isCurrentMonth
                              ? styles.activeDayCell
                              : styles.inactiveDayCell,
                            dayData.day === selectedDay &&
                              dayData.isCurrentMonth &&
                              styles.selectedDayCell,
                            isToday(dayData.date) &&
                              !(
                                dayData.day === selectedDay &&
                                dayData.isCurrentMonth
                              ) &&
                              styles.todayDayCell,
                            isPastDate(dayData.date) && styles.pastDayCell
                          ]}
                          onPress={() =>
                            dayData.isCurrentMonth &&
                            (allowPastDates || !isPastDate(dayData.date)) &&
                            handleDayPress(dayData.day)
                          }
                          disabled={
                            !dayData.isCurrentMonth ||
                            (!allowPastDates && isPastDate(dayData.date))
                          }
                        >
                          <Text
                            style={[
                              styles.dayText,
                              dayData.isCurrentMonth
                                ? styles.activeDayText
                                : styles.inactiveDayText,
                              dayData.day === selectedDay &&
                                dayData.isCurrentMonth &&
                                styles.selectedDayText,
                              isToday(dayData.date) &&
                                !(
                                  dayData.day === selectedDay &&
                                  dayData.isCurrentMonth
                                ) &&
                                styles.todayDayText,
                              isPastDate(dayData.date) && styles.pastDayText
                            ]}
                          >
                            {dayData.day}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  ))}
                </View>

                {/* Clear Selection Button */}
                <TouchableOpacity
                  style={styles.clearButton}
                  onPress={clearSelection}
                >
                  <Text style={styles.clearButtonText}>Limpar seleção</Text>
                </TouchableOpacity>
              </View>

              {/* Time Section - Only show if not hidden */}
              {!hideTimeSection && (
                <>
                  <Text style={styles.sectionTitle}>Selecionar horários</Text>

                  <View style={styles.timeContainer}>
                    <View style={styles.timePickerContainer}>
                      <TouchableOpacity
                        style={styles.timeInput}
                        onPress={handleStartTimePress}
                      >
                        <ClockIcon width={20} height={20} color="#F2F4F7" />
                        <Text style={styles.timeValue}>
                          De: {formatTime(selectedHour, selectedMinute)}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    <View style={styles.timePickerContainer}>
                      <TouchableOpacity
                        style={styles.timeInput}
                        onPress={handleEndTimePress}
                      >
                        <ClockIcon width={20} height={20} color="#F2F4F7" />
                        <Text style={styles.timeValue}>
                          Até: {formatTime(endHour, endMinute)}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              )}
            </ScrollView>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.applyButton,
                  !selectedDay && styles.disabledButton
                ]}
                onPress={handleApply}
                disabled={!selectedDay}
              >
                <Text
                  style={[
                    styles.applyButtonText,
                    !selectedDay && styles.disabledButtonText
                  ]}
                >
                  {hideTimeSection ? "Aplicar data" : "Aplicar data e hora"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                <Text style={styles.cancelButtonText}>
                  {hideTimeSection ? "Voltar" : "Voltar aos filtros"}
                </Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>

        {/* Time Picker Modals */}
        {showStartTimePicker && (
          <TimePickerModal
            visible={showStartTimePicker}
            initialHour={selectedHour}
            initialMinute={selectedMinute}
            onTimeSelect={handleStartTimeSelect}
            onClose={() => setShowStartTimePicker(false)}
            title="Horário de início"
          />
        )}

        {showEndTimePicker && (
          <TimePickerModal
            visible={showEndTimePicker}
            initialHour={endHour}
            initialMinute={endMinute}
            onTimeSelect={handleEndTimeSelect}
            onClose={() => setShowEndTimePicker(false)}
            title="Horário de fim"
          />
        )}
      </View>
    </Modal>
  );
};

// Simple Time Picker Modal Component
interface TimePickerModalProps {
  visible: boolean;
  initialHour: number;
  initialMinute: number;
  onTimeSelect: (hour: number, minute: number) => void;
  onClose: () => void;
  title: string;
}

const TimePickerModal: React.FC<TimePickerModalProps> = ({
  visible,
  initialHour,
  initialMinute,
  onTimeSelect,
  onClose,
  title
}) => {
  const [selectedHour, setSelectedHour] = useState(initialHour);
  const [selectedMinute, setSelectedMinute] = useState(initialMinute);

  const hours = Array.from({length: 24}, (_, i) => i);
  const minutes = Array.from({length: 60}, (_, i) => i);

  const handleConfirm = () => {
    onTimeSelect(selectedHour, selectedMinute);
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.timePickerOverlay}>
        <View style={styles.timePickerModal}>
          <Text style={styles.timePickerTitle}>{title}</Text>

          <View style={styles.timePickerContent}>
            <View style={styles.timePickerColumn}>
              <Text style={styles.timePickerLabel}>Hora</Text>
              <ScrollView
                style={styles.timePickerScroll}
                showsVerticalScrollIndicator={false}
              >
                {hours.map((hour) => (
                  <TouchableOpacity
                    key={hour}
                    style={[
                      styles.timePickerItem,
                      selectedHour === hour && styles.timePickerItemSelected
                    ]}
                    onPress={() => setSelectedHour(hour)}
                  >
                    <Text
                      style={[
                        styles.timePickerItemText,
                        selectedHour === hour &&
                          styles.timePickerItemTextSelected
                      ]}
                    >
                      {hour.toString().padStart(2, "0")}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <View style={styles.timePickerColumn}>
              <Text style={styles.timePickerLabel}>Minuto</Text>
              <ScrollView
                style={styles.timePickerScroll}
                showsVerticalScrollIndicator={false}
              >
                {minutes
                  .filter((m) => m % 5 === 0)
                  .map((minute) => (
                    <TouchableOpacity
                      key={minute}
                      style={[
                        styles.timePickerItem,
                        selectedMinute === minute &&
                          styles.timePickerItemSelected
                      ]}
                      onPress={() => setSelectedMinute(minute)}
                    >
                      <Text
                        style={[
                          styles.timePickerItemText,
                          selectedMinute === minute &&
                            styles.timePickerItemTextSelected
                        ]}
                      >
                        {minute.toString().padStart(2, "0")}
                      </Text>
                    </TouchableOpacity>
                  ))}
              </ScrollView>
            </View>
          </View>

          <View style={styles.timePickerActions}>
            <TouchableOpacity
              style={styles.timePickerCancelButton}
              onPress={onClose}
            >
              <Text style={styles.timePickerCancelText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.timePickerConfirmButton}
              onPress={handleConfirm}
            >
              <Text style={styles.timePickerConfirmText}>Confirmar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DateTimePickerDrawer;
