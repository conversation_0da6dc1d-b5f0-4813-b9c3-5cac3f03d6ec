{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,300,515,724,917,1153,1358,1572,1791,2023,2264,2494,2715,2948,3150,3401,3622,3851,4092,4314,4537,4733,4959,5144,5357,5556,5785", "endColumns": "244,214,208,192,235,204,213,218,231,240,229,220,232,201,250,220,228,240,221,222,195,225,184,212,198,228,198", "endOffsets": "295,510,719,912,1148,1353,1567,1786,2018,2259,2489,2710,2943,3145,3396,3617,3846,4087,4309,4532,4728,4954,5139,5352,5551,5780,5979"}, "to": {"startLines": "31,32,40,42,43,44,48,49,50,51,52,53,54,55,56,57,58,59,60,61,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5959,6204,7846,8255,8448,8684,9501,9715,9934,10166,10407,10637,10858,11091,11293,11544,11765,11994,12235,12457,13070,13266,13492,13677,13890,14089,14318", "endColumns": "244,214,208,192,235,204,213,218,231,240,229,220,232,201,250,220,228,240,221,222,195,225,184,212,198,228,198", "endOffsets": "6199,6414,8050,8443,8679,8884,9710,9929,10161,10402,10632,10853,11086,11288,11539,11760,11989,12230,12452,12675,13261,13487,13672,13885,14084,14313,14512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "5528,5743", "endColumns": "214,215", "endOffsets": "5738,5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,12680", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,12861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "33,34,35,36,37,38,39,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6419,6615,6820,7021,7222,7429,7634,12866", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6610,6815,7016,7217,7424,7629,7841,13065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "41,45,46,47", "startColumns": "4,4,4,4", "startOffsets": "8055,8889,9088,9299", "endColumns": "199,198,210,201", "endOffsets": "8250,9083,9294,9496"}}]}]}