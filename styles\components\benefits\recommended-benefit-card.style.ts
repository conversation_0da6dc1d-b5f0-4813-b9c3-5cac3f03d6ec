import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 24,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    overflow: "hidden"
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 8
  },
  content: {
    flex: 1,
    marginRight: 8
  },
  header: {
    marginBottom: 8
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 22,
    marginBottom: 4
  },
  partnerName: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16
  },
  description: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 8
  },
  footer: {
    flexDirection: "row",
    alignItems: "center"
  },
  benefitLabel: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "500",
    lineHeight: 16,
    textTransform: "uppercase"
  },
  arrowContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: 24,
    height: 24
  }
});

export default styles;
