import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: 4,
    minWidth: 40,
    minHeight: 50,
    justifyContent: "center"
  },
  iconContainer: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: 6,
    borderRadius: 4,
    width: 32,
    height: 32
  },
  title: {
    fontSize: 10,
    fontFamily: stylesConstants.fonts.openSans,
    fontStyle: "normal",
    fontWeight: "400",
    lineHeight: 18,
    color: stylesConstants.colors.fullWhite
  },
  selectedContainer: {
    opacity: 1,
    transform: [{scale: 1.05}]
  },
  selectedIconContainer: {
    backgroundColor: stylesConstants.colors.primary,
    borderWidth: 2,
    borderColor: stylesConstants.colors.fullWhite
  },
  selectedTitle: {
    fontWeight: "600",
    color: stylesConstants.colors.primary
  }
});

export default styles;
