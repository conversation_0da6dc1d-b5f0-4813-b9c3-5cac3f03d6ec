import React, {use<PERSON><PERSON>back, useMemo, useState} from "react";
import {useTranslation} from "react-i18next";
import {
  ScrollView,
  View,
  Text,
  RefreshControl,
  Alert,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity
} from "react-native";
import {useL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter} from "expo-router";
import OpportunityCard from "@/components/user/opportunity-card";
import UserTabs from "@/components/user/user-tabs";
import ExternalAboutMe from "@/components/user/external-about-me";
import {useUserOpportunitiesById} from "@/hooks/api/use-opportunities";
import {useUserById, useUserBadges} from "@/hooks/api/use-users";
import styles from "@/styles/tabs/user.style";
import Seals from "@/components/user/seals";
import {ImageBackground} from "expo-image";
import Avatar from "@/components/user/avatar";
import Button from "@/components/button";
import MarkerPinIcon from "@/components/icons/marker-pin-icon";
import ChevronLeftIcon from "@/components/icons/chevron-left-icon";
import SendIcon from "@/components/icons/send-icon";
import stylesConstants from "@/styles/styles-constants";

enum UserTabsEnum {
  Opportunities = 1,
  ClaimedBadges = 2,
  AboutMe = 3
}

// Inline styles for the custom header (moved outside to prevent re-creation)
const headerStyles = StyleSheet.create({
  container: {
    zIndex: 1
  },
  backgroundImage: {
    overflow: "hidden",
    backgroundColor: "#02343B"
  },
  backgroundImageStyle: {
    backgroundColor: "#02343B",
    objectFit: "none",
    opacity: 0.1,
    transform: [
      {
        scale: 1.6
      }
    ]
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)"
  },
  content: {
    marginTop: 25,
    padding: 24,
    paddingBottom: 40,
    justifyContent: "center",
    alignItems: "center",
    width: "100%"
  },
  innerContainer: {
    gap: 12,
    alignItems: "center",
    width: "100%"
  },
  backButton: {
    position: "absolute",
    top: 60,
    left: 24,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10
  },
  userInfo: {
    alignItems: "center",
    gap: 8
  },
  userName: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "600"
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  locationText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    opacity: 0.8
  },
  startConversationButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginTop: 8,
    width: 160,
    alignSelf: "center"
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  }
});

const UserProfileScreen = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {id} = useLocalSearchParams<{id: string}>();
  const [activeTab, setActiveTab] = useState(UserTabsEnum.Opportunities);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch user data from API
  const {
    data: user,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser
  } = useUserById(id as string);

  // Fetch user opportunities from API
  const {
    data: opportunitiesData,
    isLoading: opportunitiesLoading,
    error: opportunitiesError,
    refetch: refetchOpportunities
  } = useUserOpportunitiesById(id as string, {
    pageSize: 10,
    page: 1
  });

  // Fetch user badges from API with error handling
  const {
    data: badgesData,
    isLoading: badgesLoading,
    error: badgesError,
    refetch: refetchBadges
  } = useUserBadges({
    pageSize: 20,
    page: 1
  });

  const tabs = [
    {title: t("user.opportunities"), id: UserTabsEnum.Opportunities},
    {title: t("user.claimedBadges"), id: UserTabsEnum.ClaimedBadges},
    {title: t("user.aboutMe"), id: UserTabsEnum.AboutMe}
  ];

  const onTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refetchUser(),
        refetchOpportunities(),
        refetchBadges()
      ]);
    } finally {
      setRefreshing(false);
    }
  }, [refetchUser, refetchOpportunities, refetchBadges]);

  const handleSendMessage = useCallback(() => {
    Alert.alert(
      t("userProfile.sendMessage", "Enviar Mensagem"),
      t("userProfile.messageFeatureComingSoon", "Funcionalidade em breve!")
    );
  }, [t]);

  // Mock data for fallback when API fails
  const mockOpportunities = useMemo(() => {
    const list = [];
    for (let i = 0; i < 3; i++) {
      list.push({
        id: i + 1,
        title: `Oportunidade de Negócio ${i + 1}`,
        user: {
          id: 1,
          name: user?.name || "Usuário",
          avatar: user?.avatar || "",
          avatarId: user?.avatarId
        },
        description:
          "Oportunidade de negócio interessante com potencial de crescimento. Ideal para investidores que buscam diversificar seu portfólio.",
        value: (i + 1) * 1000,
        imageUrl:
          "https://upload.wikimedia.org/wikipedia/commons/0/01/Steinway_Vienna_002.JPG",
        imageId: undefined, // Mock data usa imageUrl diretamente
        createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    return list;
  }, [user]);

  const opportunities = useMemo(() => {
    if (opportunitiesLoading) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t("common.loading")}</Text>
        </View>
      );
    }

    // If there's an error and no data, show error with mock data as fallback
    if (
      opportunitiesError &&
      (!opportunitiesData?.data || opportunitiesData.data.length === 0)
    ) {
      return (
        <View>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {t("user.opportunitiesTab.loadError")}
            </Text>
            <Text style={styles.errorSubText}>Mostrando dados de exemplo</Text>
          </View>
          {mockOpportunities.map((opportunity) => (
            <OpportunityCard
              key={opportunity.id}
              id={opportunity.id}
              title={opportunity.title}
              userName={opportunity.user.name}
              user={opportunity.user}
              createdAt={opportunity.createdAt}
              description={opportunity.description}
              value={opportunity.value}
              imageUrl={opportunity.imageUrl}
              imageId={opportunity.imageId}
              onPress={() => {
                router.push(`/(business)/opportunities/${opportunity.id}`);
              }}
            />
          ))}
        </View>
      );
    }

    // If API returned successfully but with no opportunities, show empty message
    if (opportunitiesData?.data && opportunitiesData.data.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {t("user.opportunitiesTab.noOpportunities")}
          </Text>
        </View>
      );
    }

    // Use API data if available, otherwise use mock data
    const dataToUse =
      opportunitiesData?.data && opportunitiesData.data.length > 0
        ? opportunitiesData.data
        : mockOpportunities;

    return dataToUse.map((opportunity) => (
      <OpportunityCard
        key={opportunity.id}
        id={opportunity.id}
        title={opportunity.title}
        userName={opportunity.user.name}
        user={opportunity.user}
        createdAt={opportunity.createdAt}
        description={opportunity.description}
        value={opportunity.value}
        imageUrl={opportunity.imageUrl}
        imageId={opportunity.imageId}
        onPress={() => {
          router.push(`/(business)/opportunities/${opportunity.id}`);
        }}
      />
    ));
  }, [
    opportunitiesData,
    opportunitiesLoading,
    opportunitiesError,
    mockOpportunities,
    t,
    router
  ]);

  const content = useMemo(() => {
    switch (activeTab) {
      case UserTabsEnum.Opportunities:
        return <View style={styles.opportunitiesList}>{opportunities}</View>;
      case UserTabsEnum.ClaimedBadges:
        return (
          <View style={styles.objectivesContainer}>
            <Seals
              badges={badgesData?.data}
              loading={badgesLoading}
              error={badgesError?.message}
            />
          </View>
        );
      case UserTabsEnum.AboutMe:
        return user ? (
          <ExternalAboutMe user={user} loading={isUserLoading} />
        ) : (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>
              {t("common.loading", "Carregando...")}
            </Text>
          </View>
        );
    }
  }, [
    activeTab,
    opportunities,
    badgesData,
    badgesLoading,
    badgesError,
    user,
    t,
    isUserLoading
  ]);

  // Custom User Header Component for External Profile
  const CustomUserHeader = useMemo(
    () => (
      <View style={headerStyles.container}>
        <ImageBackground
          imageStyle={headerStyles.backgroundImageStyle}
          style={headerStyles.backgroundImage}
          source={require("@/assets/textures/rotated-pattern.png")}
        >
          <View style={headerStyles.overlay} />

          {/* Back Button */}
          <TouchableOpacity
            style={headerStyles.backButton}
            onPress={() => router.back()}
            activeOpacity={0.7}
          >
            <ChevronLeftIcon width={24} height={24} color="#FFFFFF" />
          </TouchableOpacity>

          <View style={headerStyles.content}>
            <View style={headerStyles.innerContainer}>
              <Avatar user={user} name={user?.name} />
              <View style={headerStyles.userInfo}>
                <Text style={headerStyles.userName}>
                  {user?.name || "Usuário"}
                </Text>
                <View style={headerStyles.locationContainer}>
                  <MarkerPinIcon width={16} height={16} color="#FFFFFF" />
                  <Text style={headerStyles.locationText}>
                    Balneário Camboriú - SC
                  </Text>
                </View>
              </View>

              {/* Start Conversation Button */}
              <TouchableOpacity
                style={headerStyles.startConversationButton}
                onPress={handleSendMessage}
                activeOpacity={0.8}
              >
                <Text style={headerStyles.buttonText}>Iniciar conversa</Text>
                <SendIcon width={16} height={16} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        </ImageBackground>
      </View>
    ),
    [user, router, handleSendMessage]
  );

  // Loading state
  if (isUserLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="large"
          color={stylesConstants.colors.textPrimary}
        />
        <Text style={styles.loadingText}>
          {t("userProfile.loadingUser", "Carregando perfil...")}
        </Text>
      </View>
    );
  }

  // Error state
  if (userError || !user) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          {t("userProfile.userNotFound", "Usuário não encontrado")}
        </Text>
        <Button
          text={t("common.goBack", "Voltar")}
          onPress={() => router.back()}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={{backgroundColor: stylesConstants.colors.mainBackground}}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor="#FFFFFF"
        />
      }
    >
      {CustomUserHeader}
      <View style={styles.mainContainer}>
        <UserTabs
          tabs={tabs}
          currentTab={activeTab}
          style={styles.tabsContainer}
          onTabChange={onTabChange}
        />
        <View style={styles.contentContainer}>{content}</View>
      </View>
    </ScrollView>
  );
};

export default UserProfileScreen;
