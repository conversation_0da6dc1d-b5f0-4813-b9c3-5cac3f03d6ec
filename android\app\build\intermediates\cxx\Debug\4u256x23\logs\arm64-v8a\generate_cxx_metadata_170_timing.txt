# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 111ms
  generate-prefab-packages
    [gap of 95ms]
    exec-prefab 4362ms
    [gap of 81ms]
  generate-prefab-packages completed in 4538ms
  execute-generate-process
    exec-configure 2681ms
    [gap of 95ms]
  execute-generate-process completed in 2779ms
  [gap of 153ms]
generate_cxx_metadata completed in 7596ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 40ms
  [gap of 35ms]
generate_cxx_metadata completed in 81ms

