import React, {useEffect, useState} from "react";
import {View, Text} from "react-native";
import FirebaseService from "@/services/firebase.service";

interface FirebaseProviderProps {
  children: React.ReactNode;
}

/**
 * Firebase Provider Component
 * Initializes Firebase and manages authentication state
 */
export const FirebaseProvider: React.FC<FirebaseProviderProps> = ({
  children
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        console.log("🔥 Initializing Firebase...");

        // First check if Firebase is properly configured
        try {
          const currentUser = FirebaseService.getCurrentUser();
          console.log("🔥 Firebase configuration check passed");
        } catch (configError) {
          console.error("🚨 Firebase configuration error:", configError);
          setInitError("Firebase configuration not found");
          setIsInitialized(true);
          return;
        }

        // Initialize Firebase authentication by getting a token
        // This will sign in anonymously if no user is signed in
        const token = await FirebaseService.getFirebaseAuthToken().toPromise();

        if (token) {
          console.log("🔥 Firebase initialized successfully with token");
          console.log("🔑 Token length:", token.length);
        } else {
          console.log("🔥 Firebase initialized but no token obtained");
          console.log("⚠️ This might be due to configuration issues");
        }

        setIsInitialized(true);
      } catch (error) {
        console.error("🚨 Firebase initialization error:", error);
        setInitError(error instanceof Error ? error.message : "Unknown error");
        setIsInitialized(true); // Still allow app to continue
      }
    };

    initializeFirebase();
  }, []);

  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <View style={{flex: 1, justifyContent: "center", alignItems: "center"}}>
        <Text>Initializing Firebase...</Text>
      </View>
    );
  }

  // Show error if initialization failed but still render children
  if (initError) {
    console.warn(
      "Firebase initialization failed, but app will continue:",
      initError
    );
  }

  return <>{children}</>;
};

export default FirebaseProvider;
