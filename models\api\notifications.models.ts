/**
 * Modelos de dados para Notificações
 * Baseado na API ClubM - Fase 3: Recursos de Comunicação
 */

import {z} from "zod";

// Enums para Notificações
export enum NotificationType {
  EVENT = "EVENT",
  PRODUCT = "PRODUCT",
  CHAT = "CHAT",
  SYSTEM = "SYSTEM",
  PROMOTION = "PROMOTION",
  REMINDER = "REMINDER",
  ANNOUNCEMENT = "ANNOUNCEMENT",
  MESSAGE = "MESSAGE",
  GENERAL = "GENERAL"
}

export enum NotificationPriority {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH",
  URGENT = "URGENT"
}

export enum NotificationStatus {
  UNREAD = "UNREAD",
  READ = "READ",
  ARCHIVED = "ARCHIVED"
}

export const NotificationChannel = {
  PUSH: "PUSH",
  EMAIL: "EMAIL",
  SMS: "SMS",
  IN_APP: "IN_APP"
} as const;

export type NotificationChannel =
  (typeof NotificationChannel)[keyof typeof NotificationChannel];

// Schemas Zod para validação
export const NotificationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: z.nativeEnum(NotificationType),
  priority: z.nativeEnum(NotificationPriority),
  status: z.nativeEnum(NotificationStatus),
  title: z.string(),
  message: z.string(),
  data: z.record(z.any()).optional(),
  imageUrl: z.string().optional(),
  actionUrl: z.string().optional(),
  actionText: z.string().optional(),
  channels: z.array(z.nativeEnum(NotificationChannel)),
  scheduledAt: z.string().optional(),
  sentAt: z.string().optional(),
  readAt: z.string().optional(),
  expiresAt: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const NotificationPreferencesSchema = z.object({
  userId: z.string(),
  pushEnabled: z.boolean(),
  pushNotifications: z.boolean(),
  emailEnabled: z.boolean(),
  smsEnabled: z.boolean(),
  eventNotifications: z.boolean(),
  productNotifications: z.boolean(),
  chatNotifications: z.boolean(),
  systemNotifications: z.boolean(),
  promotionNotifications: z.boolean(),
  reminderNotifications: z.boolean(),
  announcementNotifications: z.boolean(),
  quietHoursEnabled: z.boolean(),
  quietHoursStart: z.string().optional(),
  quietHoursEnd: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const NotificationStatsSchema = z.object({
  totalCount: z.number(),
  totalNotifications: z.number(),
  unreadCount: z.number(),
  readCount: z.number(),
  archivedCount: z.number(),
  byType: z.record(z.number()),
  byPriority: z.record(z.number()),
  lastNotificationAt: z.string().optional()
});

// Interfaces TypeScript
export interface Notification extends z.infer<typeof NotificationSchema> {}

export interface NotificationPreferences
  extends z.infer<typeof NotificationPreferencesSchema> {}

export interface NotificationStats
  extends z.infer<typeof NotificationStatsSchema> {}

// Requests e Responses
export interface NotificationsListParams {
  page?: number;
  pageSize?: number;
  type?: NotificationType;
  priority?: NotificationPriority;
  status?: NotificationStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface CreateNotificationRequest {
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  imageUrl?: string;
  actionUrl?: string;
  actionText?: string;
  channels: NotificationChannel[];
  scheduledAt?: string;
  expiresAt?: string;
  targetUsers?: string[];
  targetGroups?: string[];
}

export interface UpdateNotificationRequest {
  status?: NotificationStatus;
  readAt?: string;
}

export interface UpdateNotificationPreferencesRequest {
  pushEnabled?: boolean;
  emailEnabled?: boolean;
  smsEnabled?: boolean;
  eventNotifications?: boolean;
  productNotifications?: boolean;
  chatNotifications?: boolean;
  systemNotifications?: boolean;
  promotionNotifications?: boolean;
  reminderNotifications?: boolean;
  announcementNotifications?: boolean;
  quietHoursEnabled?: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
}

export interface NotificationResponse {
  success: boolean;
  notification: Notification;
  message?: string;
}

export interface NotificationsListResponse {
  notifications: Notification[];
  stats: NotificationStats;
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface NotificationPreferencesResponse {
  success: boolean;
  preferences: NotificationPreferences;
  message?: string;
}

// Push Notification Types
export interface PushNotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  badge?: number;
  sound?: string;
  category?: string;
  threadId?: string;
}

export interface PushNotificationRegistration {
  deviceToken: string;
  token: string;
  platform: "ios" | "android";
  appVersion: string;
  osVersion: string;
  deviceModel: string;
  timezone: string;
  language: string;
}

// Real-time Notification Events
export interface NotificationEvent {
  type: "notification_received" | "notification_read" | "notification_deleted";
  notification: Notification;
  timestamp: string;
}

// Notification Templates
export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  title: string;
  message: string;
  variables: string[];
  channels: NotificationChannel[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Notification Campaigns
export interface NotificationCampaign {
  id: string;
  name: string;
  description: string;
  templateId: string;
  targetAudience: {
    userIds?: string[];
    userGroups?: string[];
    criteria?: Record<string, any>;
  };
  scheduledAt: string;
  status: "DRAFT" | "SCHEDULED" | "SENDING" | "SENT" | "CANCELLED";
  stats: {
    totalTargets: number;
    sent: number;
    delivered: number;
    read: number;
    clicked: number;
    failed: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Notification Analytics
export interface NotificationAnalytics {
  period: string;
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  totalClicked: number;
  deliveryRate: number;
  readRate: number;
  clickRate: number;
  byType: Record<
    NotificationType,
    {
      sent: number;
      delivered: number;
      read: number;
      clicked: number;
    }
  >;
  byChannel: Record<
    NotificationChannel,
    {
      sent: number;
      delivered: number;
      read: number;
      clicked: number;
    }
  >;
  topPerformingNotifications: Array<{
    id: string;
    title: string;
    readRate: number;
    clickRate: number;
  }>;
}

// Error Types
export interface NotificationError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Utility Types
export type NotificationWithStats = Notification & {
  stats: {
    views: number;
    clicks: number;
    shares: number;
  };
};

export type NotificationSummary = Pick<
  Notification,
  "id" | "type" | "priority" | "title" | "status" | "createdAt"
>;

export type NotificationPreferencesSummary = Pick<
  NotificationPreferences,
  "pushEnabled" | "emailEnabled" | "eventNotifications" | "chatNotifications"
>;
