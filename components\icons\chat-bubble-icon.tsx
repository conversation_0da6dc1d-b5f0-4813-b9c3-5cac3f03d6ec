import React from "react";
import Svg, { Defs, Clip<PERSON><PERSON>, <PERSON>, G } from "react-native-svg";

interface ChatBubbleIconProps {
  width?: number;
  height?: number;
  replaceColor?: string;
}

const ChatBubbleIcon: React.FC<ChatBubbleIconProps> = ({
  width = 24,
  height = 24,
  replaceColor = "rgb(207, 212, 220)"
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24">
      <Defs>
        <ClipPath id="clipPath5178638763">
          <Path d="M0 0L24 0L24 24L0 24L0 0Z" fillRule="nonzero" transform="matrix(1 0 0 1 0 0)" />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath5178638763)">
        <Path
          d="M3.0179 8.57106Q2.99996 8.28662 2.99996 8Q2.99996 6.16839 3.71269 4.49432Q4.40051 2.87876 5.65374 1.63372Q6.90628 0.389367 8.53076 -0.293237Q10.2127 -1 12.0526 -1Q13.8925 -1 15.5744 -0.293237Q17.1989 0.389369 18.4514 1.63372Q19.7047 2.87876 20.3925 4.49432Q21.1052 6.1684 21.1052 8Q21.1052 9.65544 20.5195 11.1909L20.5195 11.191Q20.4625 11.3404 20.4469 11.3842Q20.4442 11.3917 20.4421 11.3979L20.4425 11.4023Q20.4459 11.4384 20.4617 11.5661L20.8643 14.8363Q20.9078 15.1901 20.9128 15.3391Q20.9261 15.7315 20.7875 16.0343Q20.5471 16.5599 20.0161 16.7882Q19.7102 16.9197 19.3182 16.8974Q19.1694 16.8889 18.8167 16.8372L15.4569 16.3448L15.2437 16.4246Q13.7075 17 12.0526 17Q11.7843 17 11.518 16.9846Q11.5103 17.0036 11.5024 17.0225Q11.0212 18.1881 10.1432 19.0876Q9.26278 19.9896 8.11882 20.4853Q6.93113 21 5.63158 21Q4.55703 21 3.54945 20.6444L3.30458 20.558L0.135315 20.9908Q-0.0416994 21.015 -0.216134 20.9764Q-0.312297 20.9551 -0.40246 20.9154Q-0.492623 20.8758 -0.57332 20.8193Q-0.654017 20.7629 -0.722147 20.6917Q-0.790278 20.6206 -0.843223 20.5376Q-0.896168 20.4545 -0.931894 20.3627Q-0.96762 20.2709 -0.984753 20.174Q-1.00189 20.077 -0.999769 19.9785Q-0.997651 19.88 -0.976364 19.7839L-0.322252 16.829L-0.411016 16.5573Q-0.736842 15.5598 -0.736842 14.5Q-0.736842 13.1827 -0.239291 11.9775Q0.241932 10.8119 1.11992 9.91242Q1.95141 9.06055 3.0179 8.57106ZM4.35985 10.193C4.37913 10.1877 4.39815 10.1819 4.41689 10.1755Q5.00475 10 5.63158 10Q6.51643 10 7.32361 10.3498Q8.10645 10.689 8.71201 11.3094Q9.32005 11.9324 9.6538 12.7408Q10 13.5793 10 14.5Q10 15.0787 9.86324 15.6249C9.85718 15.6456 9.85175 15.6666 9.84698 15.6879Q9.76984 15.9782 9.6538 16.2592Q9.32005 17.0676 8.71201 17.6906Q8.10645 18.311 7.3236 18.6502Q6.51643 19 5.63158 19Q4.89961 19 4.21509 18.7584Q3.83562 18.6245 3.69563 18.5966Q3.50873 18.5593 3.31813 18.558Q3.1754 18.5571 2.85949 18.6002L1.28652 18.815L1.58771 17.4544Q1.66515 17.1046 1.67441 16.9445Q1.68678 16.7306 1.65357 16.5189Q1.62869 16.3604 1.49011 15.9362Q1.26316 15.2415 1.26316 14.5Q1.26316 13.5793 1.60936 12.7408Q1.94311 11.9324 2.55115 11.3094Q3.15671 10.689 3.93955 10.3498Q4.14713 10.2598 4.35985 10.193ZM11.9818 14.9997Q12 14.7519 12 14.5Q12 13.1827 11.5024 11.9775Q11.0212 10.8119 10.1432 9.91242Q9.26278 9.01038 8.11882 8.51466Q6.93113 8 5.63158 8Q5.31243 8 5.00003 8.03104Q4.99996 8.01557 4.99996 8Q4.99996 6.57642 5.55286 5.27776Q6.0875 4.022 7.06331 3.05257Q8.0398 2.08246 9.30554 1.5506Q10.6159 0.999999 12.0526 0.999999Q13.4893 1 14.7997 1.5506Q16.0654 2.08246 17.0419 3.05257Q18.0177 4.022 18.5523 5.27776Q19.1052 6.57642 19.1052 8Q19.1052 9.28692 18.6508 10.4781L18.6508 10.4781Q18.52 10.8209 18.4883 10.9638Q18.446 11.1545 18.4414 11.3499Q18.438 11.4962 18.4767 11.8105L18.8472 14.8203L15.9215 14.3915Q15.5971 14.3439 15.4456 14.3448Q15.2434 14.3459 15.0454 14.3875Q14.8972 14.4187 14.5422 14.5517Q13.3452 15 12.0526 15Q12.0172 15 11.9818 14.9997Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 2 2)"
          fill={replaceColor}
        />
      </G>
    </Svg>
  );
};

export default ChatBubbleIcon;
