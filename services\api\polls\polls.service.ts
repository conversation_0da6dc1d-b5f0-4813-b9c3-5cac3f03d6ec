/**
 * Serviço de API para Enquetes (Polls)
 * Gerencia todas as operações relacionadas a enquetes
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {
  Poll,
  PollPaginateViewModel,
  PollFilters,
  VoteRequest,
  VoteResponse
} from "@/models/api/polls.models";

/**
 * Serviço para operações de enquetes
 */
export class PollsService {
  private static readonly BASE_PATH = "/api/app/poll";

  /**
   * Busca lista de enquetes com filtros e paginação
   */
  static async getPolls(filters?: PollFilters): Promise<PollPaginateViewModel> {
    try {
      // Usar filtros fornecidos ou objeto vazio
      const cleanFilters = filters || {};

      ApiLogger.info("Buscando enquetes", {filters: cleanFilters});

      const response = await firstValueFrom(
        apiClient.get<PollPaginateViewModel>(this.BASE_PATH, cleanFilters)
      );

      // Transformar dados das enquetes
      const transformedPolls = response.data.map((poll) => {
        return {
          ...poll,
          isExpired: this.isExpired(poll),
          totalVotes: this.calculateTotalVotes(poll),
          // Mapear endDate para expiresAt para compatibilidade
          expiresAt: poll.endDate,
          // Usar description como question se não houver question
          question: poll.description,
          // Garantir que user existe
          user: poll.user || {id: 0, name: "Usuário Anônimo"}
        };
      });

      const result = {
        ...response,
        data: transformedPolls
      };

      ApiLogger.info("Enquetes carregadas com sucesso", {
        count: result.data.length,
        totalCount: result.totalCount
      });

      return result;
    } catch (error) {
      ApiLogger.error("Erro ao buscar enquetes", error, {filters});
      throw error;
    }
  }

  /**
   * Busca enquete específica por ID
   */
  static async getPollById(id: number): Promise<Poll> {
    try {
      ApiLogger.info("Buscando enquete por ID", {id});

      const response = await firstValueFrom(
        apiClient.get<Poll>(`${this.BASE_PATH}/${id}`)
      );

      // Transformar dados da enquete
      const result = {
        ...response,
        isExpired: this.isExpired(response),
        totalVotes: this.calculateTotalVotes(response),
        // Mapear endDate para expiresAt para compatibilidade
        expiresAt: response.endDate,
        // Usar description como question se não houver question
        question: response.description,
        // Garantir que user existe
        user: response.user || {id: 0, name: "Usuário Anônimo"}
      };

      ApiLogger.info("Enquete carregada com sucesso", {
        id,
        title: result.title
      });

      return result;
    } catch (error) {
      ApiLogger.error("Erro ao buscar enquete por ID", error, {id});
      throw error;
    }
  }

  /**
   * Vota em uma opção da enquete
   */
  static async voteOnPoll(
    pollId: number,
    optionId: number
  ): Promise<VoteResponse> {
    try {
      // Preparar dados do voto
      const voteData: VoteRequest = {pollId, optionId};

      ApiLogger.info("Votando em enquete", voteData);

      const response = await firstValueFrom(
        apiClient.post<VoteResponse>(
          `${this.BASE_PATH}/${pollId}/option/${optionId}/vote`
        )
      );

      ApiLogger.info("Voto registrado com sucesso", {
        pollId,
        optionId,
        success: response.success
      });

      return response;
    } catch (error) {
      ApiLogger.error("Erro ao votar em enquete", error, {pollId, optionId});
      throw error;
    }
  }

  /**
   * Verifica se uma enquete está expirada
   */
  private static isExpired(poll: any): boolean {
    if (!poll.endDate) return false;
    return new Date(poll.endDate) < new Date();
  }

  /**
   * Calcula o total de votos de uma enquete
   */
  private static calculateTotalVotes(poll: any): number {
    if (!poll.options || !Array.isArray(poll.options)) return 0;
    return poll.options.reduce(
      (total, option) => total + (option.voteCount || 0),
      0
    );
  }
}

// Exportar tipos para uso em outros arquivos
export type {
  Poll,
  PollPaginateViewModel,
  PollFilters,
  VoteRequest,
  VoteResponse
} from "@/models/api/polls.models";

// Exportar utilitários
export {PollUtils} from "@/models/api/polls.models";
