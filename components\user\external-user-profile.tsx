import React from "react";
import {View, Text, ScrollView} from "react-native";
import {useTranslation} from "react-i18next";
import {UserInfo} from "@/models/api/auth.models";

import styles from "@/styles/components/user/user-profile.style";
import UserStats from "./user-stats";
import UserSocialMedia from "./user-social-media";
import <PERSON><PERSON>ield from "./profile-field";

// Icons
import EmailIcon from "@/components/icons/email-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import CompanyIcon from "@/components/icons/company-icon";
import CalendarIcon from "@/components/icons/calendar-icon";

interface ExternalUserProfileProps {
  user: UserInfo;
  userName?: string;
}

const ExternalUserProfile: React.FC<ExternalUserProfileProps> = ({
  user,
  userName
}) => {
  const {t} = useTranslation();

  const formatMemberSince = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      year: "numeric",
      month: "long"
    });
  };

  // Get the display name for the profile title
  const displayName = userName || user?.name || "Usuário";

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.profileHeaderContent}>
          <Text style={styles.profileTitle}>
            {t("user.profile.externalTitle", "Perfil de {{name}}", {
              name: displayName
            })}
          </Text>
        </View>
      </View>

      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.basicInfo")}</Text>

        <ProfileField
          label={t("user.profile.name")}
          value={user.name || t("user.profile.notInformed")}
          icon={<EmailIcon />}
        />

        <ProfileField
          label={t("user.profile.email")}
          value={user.email || t("user.profile.notInformed")}
          icon={<EmailIcon />}
        />

        <ProfileField
          label={t("user.profile.phone")}
          value={
            user.phone || user.phoneNumber || t("user.profile.notInformed")
          }
          icon={<PhoneIcon />}
        />

        {user.companyName && (
          <ProfileField
            label={t("user.profile.company")}
            value={user.companyName}
            icon={<CompanyIcon />}
          />
        )}

        <ProfileField
          label={t("user.profile.memberSince")}
          value={formatMemberSince(user.createdAt || new Date().toISOString())}
          icon={<CalendarIcon />}
        />
      </View>

      {/* Social Media */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.socialMedia")}</Text>
        <UserSocialMedia
          socialMedias={user.socialMedias || []}
          editable={false}
        />
      </View>

      {/* User Statistics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.statistics")}</Text>
        <UserStats
          stats={{
            eventsAttended: 0,
            eventsRegistered: 0,
            totalPurchases: 0,
            totalSpent: 0,
            memberSince: user.createdAt || new Date().toISOString(),
            lastActivity: new Date().toISOString()
          }}
        />
      </View>
    </ScrollView>
  );
};

export default ExternalUserProfile;
