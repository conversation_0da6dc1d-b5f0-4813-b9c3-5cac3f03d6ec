/**
 * Test for free event registration bug fix
 * Verifies that free events bypass payment and register directly
 */

import React from "react";
import {render, fireEvent, waitFor} from "@testing-library/react-native";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import EventSale from "../../app/(events)/event-sale.tsx";

// Mock router
const mockPush = jest.fn();
const mockBack = jest.fn();
const mockUseLocalSearchParams = jest.fn();

jest.mock("expo-router", () => ({
  router: {
    push: mockPush,
    back: mockBack
  },
  useRouter: () => ({
    push: mockPush,
    back: mockBack
  }),
  useLocalSearchParams: () => mockUseLocalSearchParams()
}));

// Mock Toast
jest.mock("react-native-toast-message", () => ({
  show: jest.fn()
}));

// Mock translation
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback || key
  })
}));

// Mock components
jest.mock("@/components/screen-with-header", () => {
  return function MockScreenWithHeader({children}: any) {
    return <div data-testid="screen-with-header">{children}</div>;
  };
});

jest.mock("@/components/event-page/event-acquire-button", () => {
  return function MockEventAcquireButton({onPress}: any) {
    const React = require("react");
    const {TouchableOpacity, Text} = require("react-native");
    return React.createElement(
      TouchableOpacity,
      {
        testID: "acquire-button",
        onPress: onPress
      },
      React.createElement(Text, {}, "Acquire Event")
    );
  };
});

// Mock other components
jest.mock("@/components/pill", () => () => <div data-testid="pill" />);
jest.mock("@/components/event-page/event-details-card", () => () => (
  <div data-testid="event-details-card" />
));
jest.mock("@/components/event-page/event-payment-methods", () => () => (
  <div data-testid="event-payment-methods" />
));
jest.mock("@/components/event-page/similar-events", () => () => (
  <div data-testid="similar-events" />
));
jest.mock("@/components/icons/announce-icon", () => () => (
  <div data-testid="announce-icon" />
));
jest.mock("expo-image", () => ({
  Image: () => <div data-testid="image" />
}));

// Mock styles
jest.mock("@/styles/events/event-sale.style", () => ({
  container: {},
  scrollContainer: {},
  headerSection: {},
  imageContainer: {},
  eventImage: {},
  overlayContainer: {},
  overlayContent: {},
  announceIcon: {},
  eventTitle: {},
  eventDescription: {},
  acquire: {},
  bottomSection: {}
}));

// Mock hooks
const mockRegisterForEvent = jest.fn();
const mockUseEvent = jest.fn();
const mockUseRegisterForEvent = jest.fn();

jest.mock("../../hooks/api/use-events", () => ({
  useEvent: () => mockUseEvent(),
  useRegisterForEvent: (options: any) => {
    mockUseRegisterForEvent(options);
    return {
      mutate: mockRegisterForEvent,
      isPending: false
    };
  }
}));

// Test data
const freeEventData = {
  id: "free-event-1",
  title: "Free Event Test",
  description: "This is a free event for testing",
  price: {
    isFree: true,
    amount: 0,
    currency: "BRL"
  },
  category: {
    id: "cat-1",
    name: "Test Category"
  },
  images: []
};

const paidEventData = {
  id: "paid-event-1",
  title: "Paid Event Test",
  description: "This is a paid event for testing",
  price: {
    isFree: false,
    amount: 5000, // R$ 50.00 in cents
    currency: "BRL"
  },
  category: {
    id: "cat-1",
    name: "Test Category"
  },
  images: []
};

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      },
      mutations: {
        retry: false
      }
    }
  });

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>{component}</QueryClientProvider>
  );
};

describe("EventSale - Free Events Bug Fix", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should register directly for free events without going to payment", async () => {
    // Setup mock for free event
    mockUseLocalSearchParams.mockReturnValue({id: "free-event-1"});
    mockUseEvent.mockReturnValue({
      data: freeEventData,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });

    const {getByTestId} = renderWithQueryClient(<EventSale />);

    // Simulate clicking the acquire button
    const acquireButton = getByTestId("acquire-button");
    fireEvent.press(acquireButton);

    // Verify that registerForEvent was called (for free events)
    await waitFor(() => {
      expect(mockRegisterForEvent).toHaveBeenCalledWith({
        eventId: "free-event-1",
        registration: {
          eventId: "free-event-1",
          attendeeInfo: {}
        }
      });
    });

    // Verify that router.push was NOT called (no payment screen)
    expect(mockPush).not.toHaveBeenCalled();
  });

  it("should navigate to payment screen for paid events", async () => {
    // Setup mock for paid event
    mockUseLocalSearchParams.mockReturnValue({id: "paid-event-1"});
    mockUseEvent.mockReturnValue({
      data: paidEventData,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });

    const {getByTestId} = renderWithQueryClient(<EventSale />);

    // Simulate clicking the acquire button
    const acquireButton = getByTestId("acquire-button");
    fireEvent.press(acquireButton);

    // Verify that router.push was called with payment screen
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith({
        pathname: "/(logged-stack)/payment",
        params: {
          eventId: "paid-event-1",
          entity: "1",
          entityTitle: "Paid Event Test",
          entityDescription: "This is a paid event for testing",
          entityImageUrl: "",
          amount: "50"
        }
      });
    });

    // Verify that registerForEvent was NOT called (paid events go through payment)
    expect(mockRegisterForEvent).not.toHaveBeenCalled();
  });

  it("should handle events with value field instead of price.amount", async () => {
    // Setup mock for free event using value field
    const eventWithValueField = {
      ...freeEventData,
      value: 0, // Alternative price field
      price: undefined
    };

    mockUseLocalSearchParams.mockReturnValue({id: "free-event-value-1"});
    mockUseEvent.mockReturnValue({
      data: eventWithValueField,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });

    const {getByTestId} = renderWithQueryClient(<EventSale />);

    // Simulate clicking the acquire button
    const acquireButton = getByTestId("acquire-button");
    fireEvent.press(acquireButton);

    // Verify that registerForEvent was called (free event with value=0)
    await waitFor(() => {
      expect(mockRegisterForEvent).toHaveBeenCalled();
    });

    // Verify that router.push was NOT called (no payment screen)
    expect(mockPush).not.toHaveBeenCalled();
  });
});
