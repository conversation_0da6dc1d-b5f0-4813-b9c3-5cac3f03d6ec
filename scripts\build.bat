@echo off
REM Club M Unified Build Script for Windows
REM Builds production apps for Android (iOS not supported on Windows)

setlocal enabledelayedexpansion

echo [BUILD] Club M Production Build
echo [INFO] Detected OS: Windows
echo [INFO] Build started at: %date% %time%

REM Check if we're in the project root
if not exist "package.json" (
    echo [ERROR] This script must be run from the project root directory
    exit /b 1
)

REM Parse command line arguments
set ANDROID_ONLY=false
set SKIP_ANDROID=false

:parse_args
if "%~1"=="" goto :done_parsing
if "%~1"=="--android-only" (
    set ANDROID_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--skip-android" (
    set SKIP_ANDROID=true
    shift
    goto :parse_args
)
if "%~1"=="--ios-only" (
    echo [WARNING] iOS builds are not supported on Windows
    echo [INFO] Use macOS for iOS builds
    exit /b 1
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help

echo [ERROR] Unknown option: %~1
echo Use --help for usage information
exit /b 1

:show_help
echo Club M Build Script for Windows
echo.
echo Usage: %0 [options]
echo.
echo Options:
echo   --android-only    Build only Android APK
echo   --skip-android    Skip Android build
echo   -h, --help        Show this help message
echo.
echo Note: iOS builds are not supported on Windows
echo.
echo Examples:
echo   %0                Build Android
echo   %0 --android-only Build Android
exit /b 0

:done_parsing

REM Create builds directory
if not exist "builds" mkdir builds

REM Determine what to build
set BUILD_ANDROID=true

if "%SKIP_ANDROID%"=="true" (
    set BUILD_ANDROID=false
)

REM Build summary
echo [INFO] Build plan:
if "%BUILD_ANDROID%"=="true" (
    echo [INFO]   ✓ Android APK
) else (
    echo [INFO]   ✗ Android APK (skipped)
)
echo [INFO]   ✗ iOS IPA (not supported on Windows)
echo.

REM Track build results
set ANDROID_SUCCESS=false

REM Build Android
if "%BUILD_ANDROID%"=="true" (
    echo [BUILD] Building Android APK...
    
    if exist "scripts\build-android.bat" (
        call scripts\build-android.bat
        if !errorlevel! equ 0 (
            set ANDROID_SUCCESS=true
            echo [SUCCESS] Android build completed successfully
        ) else (
            echo [ERROR] Android build failed
        )
    ) else (
        echo [ERROR] Android build script not found: scripts\build-android.bat
    )
    
    echo.
)

REM Build summary
echo [BUILD] Build Summary
echo [INFO] Build completed at: %date% %time%

if "%BUILD_ANDROID%"=="true" (
    if "%ANDROID_SUCCESS%"=="true" (
        echo [SUCCESS] ✓ Android APK: builds\android\club-m-production.apk
    ) else (
        echo [ERROR] ✗ Android APK: Build failed
    )
)

REM Exit with error if build failed
if "%BUILD_ANDROID%"=="true" if "%ANDROID_SUCCESS%"=="false" (
    exit /b 1
)

echo [SUCCESS] All builds completed successfully!

endlocal
