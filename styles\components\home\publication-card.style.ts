import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  publicationCard: {
    padding: 20,
    marginBottom: 16
  },

  loadingCard: {
    justifyContent: "center",
    alignItems: "center",
    height: 200
  },

  publicationHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    marginBottom: 12
  },

  publicationContent: {
    flex: 1
  },

  publicationText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },

  userName: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },

  publicationDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 12
  },

  publicationImg: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginBottom: 12
  },

  imagePlaceholder: {
    backgroundColor: stylesConstants.colors.gray10,
    justifyContent: "center",
    alignItems: "center"
  },

  placeholderText: {
    fontSize: 32,
    opacity: 0.5
  },

  divider: {
    width: "100%",
    height: 1,
    backgroundColor: stylesConstants.colors.borderStrokesLines,
    marginVertical: 12
  },

  publicationActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    gap: 16
  },

  valueContainer: {
    flex: 1
  },

  publicationTextClock: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginTop: 4
  },

  interestButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: "transparent",
    borderColor: "#FCFCFD",
    borderWidth: 1.5,
    borderRadius: 8,
    width: 140,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    // Remove todas as sombras e efeitos
    shadowColor: "transparent",
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0
  },

  interestButtonText: {
    color: "#FCFCFD",
    fontFamily: "Ubuntu",
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18
  },

  // Poll component styles
  pollContainer: {
    marginVertical: 12
  },

  pollQuestion: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    marginBottom: 12
  },

  pollOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderWidth: 1,
    borderColor: stylesConstants.colors.fullWhite,
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8
  },

  pollOptionSelected: {
    backgroundColor: stylesConstants.colors.green400,
    borderColor: stylesConstants.colors.green400
  },

  pollOptionText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    flex: 1
  },

  pollOptionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: stylesConstants.colors.fullWhite
  },

  pollOptionIndicatorSelected: {
    backgroundColor: stylesConstants.colors.secondaryBackground
  },

  pollTimer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 16
  },

  pollTimerText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400"
  },

  pollTimerValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700"
  }
});

export default styles;
