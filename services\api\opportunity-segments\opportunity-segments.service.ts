/**
 * Opportunity Segments Service for Club M
 * Handles operations for opportunity segments management
 */

import {apiClient} from "../base/api-client";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";

// Types based on swagger.json schemas
export interface OpportunitySegmentViewModel {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface OpportunitySegmentListResponse {
  data: OpportunitySegmentViewModel[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface OpportunitySegmentsListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export class OpportunitySegmentsService {
  private static readonly BASE_PATH = "/api/app/opportunity-segment";

  /**
   * Get all opportunity segments
   */
  static async getOpportunitySegments(
    params?: OpportunitySegmentsListParams
  ): Promise<OpportunitySegmentListResponse> {
    try {
      ApiLogger.info("Fetching opportunity segments", params);

      const response = await firstValueFrom(
        apiClient.get<OpportunitySegmentListResponse>(this.BASE_PATH, {
          Page: params?.page || 1,
          PageSize: params?.pageSize || 50,
          Search: params?.search || ""
        })
      );

      ApiLogger.info(`Found ${response.data.length} opportunity segments`);
      return response;
    } catch (error) {
      ApiLogger.error("Error fetching opportunity segments", error as Error);
      throw error;
    }
  }

  /**
   * Get a specific opportunity segment by ID
   */
  static async getOpportunitySegmentById(
    segmentId: string | number
  ): Promise<OpportunitySegmentViewModel> {
    try {
      ApiLogger.info("Fetching opportunity segment by ID", {segmentId});

      const response = await firstValueFrom(
        apiClient.get<OpportunitySegmentViewModel>(`${this.BASE_PATH}/${segmentId}`)
      );

      ApiLogger.info(`Found opportunity segment: ${response.name}`);
      return response;
    } catch (error) {
      ApiLogger.error(`Error fetching opportunity segment ${segmentId}`, error as Error);
      throw error;
    }
  }
}

export default OpportunitySegmentsService;
