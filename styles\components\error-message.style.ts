import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
    paddingHorizontal: 24
  },
  iconContainer: {
    marginBottom: 16
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 8
  },
  message: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    marginBottom: 24
  },
  retryButton: {
    backgroundColor: stylesConstants.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120
  },
  retryButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
