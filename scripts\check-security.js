#!/usr/bin/env node

/**
 * Security Check Script
 * Verifies that no sensitive files are being committed to the repository
 */

const fs = require("fs");
const path = require("path");
const {execSync} = require("child_process");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  purple: "\x1b[35m"
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`[INFO] ${message}`, "blue");
}

function logSuccess(message) {
  log(`[SUCCESS] ${message}`, "green");
}

function logWarning(message) {
  log(`[WARNING] ${message}`, "yellow");
}

function logError(message) {
  log(`[ERROR] ${message}`, "red");
}

function logHeader(message) {
  log(`[SECURITY] ${message}`, "purple");
}

// Sensitive files and patterns that should never be committed
const sensitivePatterns = [
  // Environment files with secrets
  ".env.production",
  ".env.staging",

  // Firebase production configs
  "keys/production/google-services.json",
  "keys/production/GoogleService-Info.plist",
  "keys/production/*.json",

  // Build outputs
  "builds/",
  "*.apk",
  "*.ipa",
  "*.aab",

  // Signing files
  "*.keystore",
  "*.p12",
  "*.mobileprovision",
  "android/keystores/",
  "ios/certs/",
  "ios/profiles/",

  // Logs and temporary files
  "*.log",
  "temp/",
  "tmp/"
];

// Check if file exists and would be committed
function checkSensitiveFiles() {
  logHeader("Checking for sensitive files...");

  let foundSensitive = false;

  sensitivePatterns.forEach((pattern) => {
    try {
      // Check if files matching pattern exist and are tracked by git
      const result = execSync(`git ls-files "${pattern}"`, {
        encoding: "utf8",
        stdio: "pipe"
      });
      if (result.trim()) {
        logError(`Sensitive file found in git: ${result.trim()}`);
        foundSensitive = true;
      }
    } catch (error) {
      // File not tracked by git, which is good
    }
  });

  return foundSensitive;
}

// Check git status for staged sensitive files
function checkStagedFiles() {
  logHeader("Checking staged files...");

  try {
    // Check for added/modified files (not deletions)
    const stagedFiles = execSync(
      "git diff --cached --name-only --diff-filter=AM",
      {encoding: "utf8"}
    );
    const files = stagedFiles
      .trim()
      .split("\n")
      .filter((f) => f);

    let foundSensitive = false;

    files.forEach((file) => {
      sensitivePatterns.forEach((pattern) => {
        // Simple pattern matching (could be improved with glob)
        if (
          file.includes(pattern.replace("*", "")) ||
          (pattern.includes("*") && file.match(pattern.replace("*", ".*")))
        ) {
          logError(`Sensitive file staged for commit: ${file}`);
          foundSensitive = true;
        }
      });
    });

    if (!foundSensitive && files.length > 0) {
      logSuccess("No sensitive files found in staged changes");
    } else if (files.length === 0) {
      logInfo("No files staged for commit");
    }

    return foundSensitive;
  } catch (error) {
    logWarning("Could not check staged files (not in a git repository?)");
    return false;
  }
}

// Check .gitignore for required patterns
function checkGitignore() {
  logHeader("Checking .gitignore configuration...");

  const gitignorePath = path.join(process.cwd(), ".gitignore");

  if (!fs.existsSync(gitignorePath)) {
    logError(".gitignore file not found");
    return false;
  }

  const gitignoreContent = fs.readFileSync(gitignorePath, "utf8");

  const requiredPatterns = [
    ".env.production",
    "keys/production/",
    "builds/",
    "*.apk",
    "*.ipa",
    "*.keystore"
  ];

  let allPatternsFound = true;

  requiredPatterns.forEach((pattern) => {
    if (!gitignoreContent.includes(pattern)) {
      logError(`Missing pattern in .gitignore: ${pattern}`);
      allPatternsFound = false;
    }
  });

  if (allPatternsFound) {
    logSuccess("All required patterns found in .gitignore");
  }

  return allPatternsFound;
}

// Check for production files in wrong locations
function checkProductionFiles() {
  logHeader("Checking production file locations...");

  const wrongLocations = [
    "keys/google-services.json", // Should be in keys/production/
    "keys/GoogleService-Info.plist", // Should be in keys/production/
    ".env.production" // Should not exist or be in .gitignore
  ];

  let foundWrongLocation = false;

  wrongLocations.forEach((file) => {
    const filePath = path.join(process.cwd(), file);

    // Check if file exists in working directory
    if (fs.existsSync(filePath)) {
      try {
        // Check if it's tracked by git
        const result = execSync(`git ls-files "${file}"`, {
          encoding: "utf8",
          stdio: "pipe"
        });
        if (result.trim()) {
          logWarning(
            `Production file in wrong location (and tracked): ${file}`
          );
          foundWrongLocation = true;
        } else {
          logInfo(`Production file exists but not tracked: ${file}`);
        }
      } catch (error) {
        // Not tracked by git
        logInfo(`Production file exists but not tracked: ${file}`);
      }
    } else {
      // Check if it's still tracked in git but deleted from working directory
      try {
        const result = execSync(`git ls-files "${file}"`, {
          encoding: "utf8",
          stdio: "pipe"
        });
        if (result.trim()) {
          logInfo(
            `Production file removed from working directory but still tracked: ${file}`
          );
          logInfo(`This will be resolved when changes are committed`);
        }
      } catch (error) {
        // File not tracked, which is good
      }
    }
  });

  if (!foundWrongLocation) {
    logSuccess("No production files found in wrong locations");
  }

  return foundWrongLocation;
}

// Main security check function
function main() {
  logHeader("Club M Security Check");
  logInfo(`Security check started at: ${new Date().toLocaleString()}`);

  let hasSecurityIssues = false;

  try {
    // Run all security checks
    if (checkSensitiveFiles()) hasSecurityIssues = true;
    if (checkStagedFiles()) hasSecurityIssues = true;
    if (!checkGitignore()) hasSecurityIssues = true;
    if (checkProductionFiles()) hasSecurityIssues = true;

    console.log("\n" + "=".repeat(60));
    logHeader("Security Check Summary");

    if (hasSecurityIssues) {
      logError("❌ Security issues found!");
      logError("Please fix the issues above before committing.");
      console.log("\nRecommended actions:");
      console.log("1. Remove sensitive files from git: git rm --cached <file>");
      console.log("2. Update .gitignore to include missing patterns");
      console.log("3. Move production files to correct locations");
      console.log("4. Run this check again: bun run check:security");
    } else {
      logSuccess("✅ No security issues found!");
      logSuccess("Repository is safe for commit.");
    }

    console.log("=".repeat(60));

    if (hasSecurityIssues) {
      process.exit(1);
    }
  } catch (error) {
    logError(`Security check failed: ${error.message}`);
    process.exit(1);
  }
}

// Run security check if called directly
if (require.main == module) {
  main();
}

module.exports = {
  checkSensitiveFiles,
  checkStagedFiles,
  checkGitignore,
  checkProductionFiles
};
