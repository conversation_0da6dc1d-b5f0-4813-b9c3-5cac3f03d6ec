import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface GastronomyIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const GastronomyIcon: React.FC<GastronomyIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg {...props} viewBox="0 0 21 20" fill="none">
      <Path
        d="M5.9165 3.3335V17.5002M3.4165 3.3335V5.8335C3.4165 6.1618 3.48117 6.48689 3.60681 6.7902C3.73244 7.09352 3.91659 7.36912 4.14874 7.60126C4.38088 7.83341 4.65648 8.01756 4.9598 8.1432C5.26311 8.26883 5.5882 8.3335 5.9165 8.3335C6.24481 8.3335 6.5699 8.26883 6.87321 8.1432C7.17653 8.01756 7.45212 7.83341 7.68427 7.60126C7.91642 7.36912 8.10057 7.09352 8.2262 6.7902C8.35184 6.48689 8.4165 6.1618 8.4165 5.8335V3.3335M14.2498 10.0002C13.5868 10.0002 12.9509 9.64897 12.4821 9.02385C12.0132 8.39873 11.7498 7.55088 11.7498 6.66683C11.7498 5.78277 12.0132 4.93493 12.4821 4.30981C12.9509 3.68469 13.5868 3.3335 14.2498 3.3335C14.9129 3.3335 15.5488 3.68469 16.0176 4.30981C16.4864 4.93493 16.7498 5.78277 16.7498 6.66683C16.7498 7.55088 16.4864 8.39873 16.0176 9.02385C15.5488 9.64897 14.9129 10.0002 14.2498 10.0002ZM14.2498 10.0002V17.5002"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default GastronomyIcon;
