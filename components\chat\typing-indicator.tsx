/**
 * Typing Indicator Component
 * Shows when other users are typing in the chat
 */

import React, {useEffect, useRef} from "react";
import {View, Text, Animated} from "react-native";
import stylesConstants from "@/styles/styles-constants";
import {TypingUser} from "@/hooks/api/use-real-time-chat";

export interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  style?: any;
}

interface TypingIndicatorStyles {
  container: any;
  text: any;
  dotsContainer: any;
  dot: any;
}

const createStyles = (): TypingIndicatorStyles => ({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 4
  },
  text: {
    fontSize: 12,
    color: stylesConstants.colors.gray700,
    marginRight: 8
  },
  dotsContainer: {
    flexDirection: "row",
    alignItems: "center"
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: stylesConstants.colors.gray500,
    marginHorizontal: 1
  }
});

const TypingDots: React.FC = () => {
  const styles = createStyles();
  const dot1Opacity = useRef(new Animated.Value(0.3)).current;
  const dot2Opacity = useRef(new Animated.Value(0.3)).current;
  const dot3Opacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const duration = 600;
    const delay = 200;

    const animateSequence = Animated.sequence([
      Animated.timing(dot1Opacity, {
        toValue: 1,
        duration: duration / 2,
        useNativeDriver: true
      }),
      Animated.timing(dot1Opacity, {
        toValue: 0.3,
        duration: duration / 2,
        useNativeDriver: true
      })
    ]);

    const animateSequence2 = Animated.sequence([
      Animated.delay(delay),
      Animated.timing(dot2Opacity, {
        toValue: 1,
        duration: duration / 2,
        useNativeDriver: true
      }),
      Animated.timing(dot2Opacity, {
        toValue: 0.3,
        duration: duration / 2,
        useNativeDriver: true
      })
    ]);

    const animateSequence3 = Animated.sequence([
      Animated.delay(delay * 2),
      Animated.timing(dot3Opacity, {
        toValue: 1,
        duration: duration / 2,
        useNativeDriver: true
      }),
      Animated.timing(dot3Opacity, {
        toValue: 0.3,
        duration: duration / 2,
        useNativeDriver: true
      })
    ]);

    const animation = Animated.loop(
      Animated.parallel([animateSequence, animateSequence2, animateSequence3])
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [dot1Opacity, dot2Opacity, dot3Opacity]);

  return (
    <View style={styles.dotsContainer}>
      <Animated.View style={[styles.dot, {opacity: dot1Opacity}]} />
      <Animated.View style={[styles.dot, {opacity: dot2Opacity}]} />
      <Animated.View style={[styles.dot, {opacity: dot3Opacity}]} />
    </View>
  );
};

const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  style
}) => {
  const styles = createStyles();

  if (typingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0].userName} está digitando`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0].userName} e ${typingUsers[1].userName} estão digitando`;
    } else {
      return `${typingUsers[0].userName} e mais ${
        typingUsers.length - 1
      } pessoas estão digitando`;
    }
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.text}>{getTypingText()}</Text>
      <TypingDots />
    </View>
  );
};

export default TypingIndicator;
