import React, {useMemo} from "react";
import Svg, {
  Re<PERSON>,
  Defs,
  RadialGradient,
  Stop,
  SvgProps
} from "react-native-svg";
import {ColorValue} from "react-native";

export interface InstagramIconColor extends SvgProps {
  /** Cor substituta para substituir o gradiente. */
  replaceColor?: ColorValue;
}

const InstagramIconColor: React.FC<InstagramIconColor> = (props) => {
  const {replaceColor, ...svgProps} = props;

  // Gerar um id único para o gradiente, evitando conflitos se o componente for usado várias vezes.
  const gradientId = useMemo(
    () => `grad${Math.random().toString(36).substring(2, 9)}`,
    []
  );

  const useGradient = !replaceColor;

  return (
    <Svg width={18} height={18} viewBox="0 0 18 18" fill="none" {...svgProps}>
      {useGradient && (
        <Defs>
          <RadialGradient
            id={gradientId}
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            // Transformação igual à do SVG original
            gradientTransform="translate(-0.687499 0.875) rotate(-8.1301) scale(24.3068 5.19897)"
          >
            <Stop offset="0.156701" stopColor="#406ADC" />
            <Stop offset="0.467799" stopColor="#6A45BE" />
            <Stop offset="1" stopColor="#6A45BE" stopOpacity="0" />
          </RadialGradient>
        </Defs>
      )}

      <Rect
        x={0.25}
        y={0.25}
        width={17.5}
        height={17.5}
        rx={6}
        fill={
          useGradient
            ? `url(#${gradientId})`
            : replaceColor ||
              "#000" /* cor padrão se replaceColor não for passada */
        }
      />
    </Svg>
  );
};

export default InstagramIconColor;
