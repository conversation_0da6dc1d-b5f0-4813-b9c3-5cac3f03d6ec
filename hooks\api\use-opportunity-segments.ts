/**
 * React Query hooks for opportunity segments management
 */

import {
  useQuery,
  UseQueryOptions
} from "@tanstack/react-query";
import {
  OpportunitySegmentsService,
  OpportunitySegmentViewModel,
  OpportunitySegmentListResponse,
  OpportunitySegmentsListParams
} from "@/services/api/opportunity-segments/opportunity-segments.service";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys for cache management
export const opportunitySegmentsKeys = {
  all: ["opportunity-segments"] as const,
  lists: () => [...opportunitySegmentsKeys.all, "list"] as const,
  list: (params: OpportunitySegmentsListParams) => [...opportunitySegmentsKeys.lists(), params] as const,
  details: () => [...opportunitySegmentsKeys.all, "detail"] as const,
  detail: (id: string | number) => [...opportunitySegmentsKeys.details(), id] as const
};

/**
 * Hook to fetch opportunity segments
 */
export const useOpportunitySegments = (
  params?: OpportunitySegmentsListParams,
  options?: UseQueryOptions<OpportunitySegmentListResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: opportunitySegmentsKeys.list(params || {}),
    queryFn: () => OpportunitySegmentsService.getOpportunitySegments(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (segments don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch a specific opportunity segment by ID
 */
export const useOpportunitySegment = (
  segmentId: string | number,
  options?: UseQueryOptions<OpportunitySegmentViewModel, BaseApiError>
) => {
  return useQuery({
    queryKey: opportunitySegmentsKeys.detail(segmentId),
    queryFn: () => OpportunitySegmentsService.getOpportunitySegmentById(segmentId),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    enabled: !!segmentId,
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};
