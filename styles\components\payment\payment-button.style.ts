/**
 * Estilos para o componente PaymentButton
 * Seguindo os padrões de design do projeto
 */

import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  // Base button styles
  button: {
    borderRadius: stylesConstants.borderRadius.medium,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row"
  },

  // Variants
  buttonPrimary: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderWidth: 0
  },

  buttonSecondary: {
    backgroundColor: stylesConstants.colors.secondary,
    borderWidth: 0
  },

  buttonOutline: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary
  },

  // Sizes
  buttonSmall: {
    paddingHorizontal: stylesConstants.spacing.medium,
    paddingVertical: stylesConstants.spacing.small,
    minHeight: 36
  },

  buttonMedium: {
    paddingHorizontal: stylesConstants.spacing.large,
    paddingVertical: stylesConstants.spacing.medium,
    minHeight: 48
  },

  buttonLarge: {
    paddingHorizontal: stylesConstants.spacing.extraLarge,
    paddingVertical: stylesConstants.spacing.large,
    minHeight: 56
  },

  // States
  buttonDisabled: {
    opacity: 0.5
  },

  buttonLoading: {
    opacity: 0.7
  },

  buttonFullWidth: {
    width: "100%"
  },

  // Content
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: stylesConstants.spacing.small
  },

  textContainer: {
    alignItems: "center",
    justifyContent: "center"
  },

  // Text styles
  text: {
    fontFamily: stylesConstants.fonts.medium,
    textAlign: "center"
  },

  textPrimary: {
    color: stylesConstants.colors.white,
    fontSize: stylesConstants.fontSize.medium
  },

  textSecondary: {
    color: stylesConstants.colors.primary,
    fontSize: stylesConstants.fontSize.medium
  },

  textOutline: {
    color: stylesConstants.colors.brand.primary,
    fontSize: stylesConstants.fontSize.medium
  },

  textSmall: {
    fontSize: stylesConstants.fontSize.small
  },

  textMedium: {
    fontSize: stylesConstants.fontSize.medium
  },

  textLarge: {
    fontSize: stylesConstants.fontSize.large
  },

  textDisabled: {
    opacity: 0.7
  },

  textLoading: {
    opacity: 0.8
  },

  // Price styles
  price: {
    fontFamily: stylesConstants.fonts.bold,
    marginTop: 2
  },

  pricePrimary: {
    color: stylesConstants.colors.white,
    fontSize: stylesConstants.fontSize.small
  },

  priceSecondary: {
    color: stylesConstants.colors.primary,
    fontSize: stylesConstants.fontSize.small
  },

  priceOutline: {
    color: stylesConstants.colors.brand.primary,
    fontSize: stylesConstants.fontSize.small
  },

  priceDisabled: {
    opacity: 0.7
  }
});

export default styles;
