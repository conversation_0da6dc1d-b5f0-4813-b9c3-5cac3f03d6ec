/**
 * Exemplos de uso do componente QRCode
 * Este arquivo demonstra diferentes formas de usar o componente QRCode
 */

import React from "react";
import {View, Text, StyleSheet, ScrollView} from "react-native";
import QRCode from "./qr-code.component";
import {useTicketQRCode, usePaymentQRCode} from "@/hooks/api/use-qr-codes";
import stylesConstants from "@/styles/styles-constants";

/**
 * Exemplo 1: QR Code básico com valor estático
 */
export const BasicQRCodeExample: React.FC = () => {
  return (
    <View style={styles.exampleContainer}>
      <Text style={styles.exampleTitle}>QR Code Básico</Text>
      <QRCode
        value="https://clubm.com/example"
        size={150}
        bgColor="#FFFFFF"
        fgColor="#000000"
      />
    </View>
  );
};

/**
 * Exemplo 2: QR Code de ticket com dados da API
 */
export const TicketQRCodeExample: React.FC<{ticketId: string}> = ({
  ticketId
}) => {
  const {data: qrCodeValue, isLoading, error} = useTicketQRCode(ticketId);

  return (
    <View style={styles.exampleContainer}>
      <Text style={styles.exampleTitle}>QR Code do Ticket</Text>
      {isLoading ? (
        <Text style={styles.statusText}>Carregando...</Text>
      ) : error ? (
        <Text style={styles.errorText}>Erro ao carregar QR Code</Text>
      ) : qrCodeValue ? (
        <QRCode
          value={qrCodeValue}
          size={200}
          bgColor="#FFFFFF"
          fgColor="#000000"
          includeMargin={true}
        />
      ) : (
        <Text style={styles.statusText}>QR Code não disponível</Text>
      )}
    </View>
  );
};

/**
 * Exemplo 3: QR Code de pagamento
 */
export const PaymentQRCodeExample: React.FC<{paymentId: string}> = ({
  paymentId
}) => {
  const {data: qrCodeValue, isLoading, error} = usePaymentQRCode(paymentId);

  return (
    <View style={styles.exampleContainer}>
      <Text style={styles.exampleTitle}>QR Code do Pagamento</Text>
      {isLoading ? (
        <Text style={styles.statusText}>Carregando...</Text>
      ) : error ? (
        <Text style={styles.errorText}>Erro ao carregar QR Code</Text>
      ) : qrCodeValue ? (
        <QRCode
          value={qrCodeValue}
          size={180}
          bgColor="#FFFFFF"
          fgColor="#000000"
          level="H" // Alta correção de erro para pagamentos
        />
      ) : (
        <Text style={styles.statusText}>QR Code não disponível</Text>
      )}
    </View>
  );
};

/**
 * Exemplo 4: QR Code customizado com cores do tema
 */
export const CustomStyledQRCodeExample: React.FC = () => {
  return (
    <View style={styles.exampleContainer}>
      <Text style={styles.exampleTitle}>QR Code Customizado</Text>
      <QRCode
        value="https://clubm.com/custom"
        size={160}
        bgColor={stylesConstants.colors.fullWhite}
        fgColor={stylesConstants.colors.primary500}
        style={styles.customQRCode}
        includeMargin={true}
      />
    </View>
  );
};

/**
 * Exemplo 5: QR Code pequeno para lista
 */
export const SmallQRCodeExample: React.FC<{value: string}> = ({value}) => {
  return (
    <View style={styles.smallContainer}>
      <QRCode
        value={value}
        size={60}
        bgColor="#FFFFFF"
        fgColor="#000000"
        includeMargin={false}
      />
    </View>
  );
};

/**
 * Exemplo 6: Demonstração de todos os exemplos
 */
export const QRCodeExamplesDemo: React.FC = () => {
  return (
    <ScrollView style={styles.demoContainer}>
      <Text style={styles.demoTitle}>Exemplos de QR Code</Text>

      <BasicQRCodeExample />

      <TicketQRCodeExample ticketId="example-ticket-123" />

      <PaymentQRCodeExample paymentId="example-payment-456" />

      <CustomStyledQRCodeExample />

      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>QR Code Pequeno</Text>
        <SmallQRCodeExample value="https://clubm.com/small" />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  demoContainer: {
    flex: 1,
    backgroundColor: stylesConstants.colors.background,
    padding: 16
  },
  demoTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: stylesConstants.colors.textPrimary,
    textAlign: "center",
    marginBottom: 24,
    fontFamily: stylesConstants.fonts.openSans
  },
  exampleContainer: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  exampleTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  statusText: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  errorText: {
    fontSize: 14,
    color: stylesConstants.colors.error,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  customQRCode: {
    borderRadius: 8,
    borderWidth: 2,
    borderColor: stylesConstants.colors.primary500
  },
  smallContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 8
  }
});
