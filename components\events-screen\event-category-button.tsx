import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import styles from "../../styles/components/events-screen/event-category-button.style";
import {EventCategory} from "../../models/api/events.models";

export interface EventCategoryButtonProps {
  title: string;
  icon: React.ReactNode;
  category: EventCategory;
  isSelected?: boolean;
  onPress?: (category: EventCategory) => void;
}

const EventCategoryButton: React.FC<EventCategoryButtonProps> = (props) => {
  const handlePress = () => {
    if (props.onPress) {
      props.onPress(props.category);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, props.isSelected && styles.selectedContainer]}
      onPress={handlePress}
      activeOpacity={0.7}
      hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
      delayPressIn={0}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`${props.title} category ${
        props.isSelected ? "selected" : "not selected"
      }`}
    >
      <View
        style={[
          styles.iconContainer,
          props.isSelected && styles.selectedIconContainer
        ]}
      >
        {props.icon}
      </View>
      <Text style={[styles.title, props.isSelected && styles.selectedTitle]}>
        {props.title}
      </Text>
    </TouchableOpacity>
  );
};

export default React.memo(EventCategoryButton);
