# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 196ms
  generate-prefab-packages
    [gap of 95ms]
    exec-prefab 1246ms
    [gap of 35ms]
  generate-prefab-packages completed in 1376ms
  execute-generate-process
    exec-configure 2067ms
    [gap of 351ms]
  execute-generate-process completed in 2420ms
  [gap of 23ms]
  remove-unexpected-so-files 25ms
  [gap of 70ms]
generate_cxx_metadata completed in 4140ms

