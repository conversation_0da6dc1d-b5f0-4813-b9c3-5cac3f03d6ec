{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1019,1084,1178,1248,1310,1397,1460,1525,1584,1649,1710,1767,1886,1944,2005,2062,2133,2263,2349,2425,2510,2592,2670,2808,2883,2954,3104,3201,3279,3334,3390,3456,3536,3626,3697,3782,3861,3938,4008,4083,4195,4283,4356,4456,4555,4629,4705,4812,4866,4956,5029,5120,5216,5278,5342,5405,5476,5575,5673,5765,5861,5919,5979,6062,6144,6222", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,656,756,868,948,1014,1079,1173,1243,1305,1392,1455,1520,1579,1644,1705,1762,1881,1939,2000,2057,2128,2258,2344,2420,2505,2587,2665,2803,2878,2949,3099,3196,3274,3329,3385,3451,3531,3621,3692,3777,3856,3933,4003,4078,4190,4278,4351,4451,4550,4624,4700,4807,4861,4951,5024,5115,5211,5273,5337,5400,5471,5570,5668,5760,5856,5914,5974,6057,6139,6217,6293"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3577,3655,3731,3811,3890,4690,4790,4902,7594,7660,7725,10196,10420,10548,10635,10698,10763,10822,10887,10948,11005,11124,11182,11243,11300,11371,11723,11809,11885,11970,12052,12130,12268,12343,12414,12564,12661,12739,12794,12850,12916,12996,13086,13157,13242,13321,13398,13468,13543,13655,13743,13816,13916,14015,14089,14165,14272,14326,14416,14489,14580,14676,14738,14802,14865,14936,15035,15133,15225,15321,15379,15439,16044,16126,16204", "endLines": "5,38,39,40,41,42,50,51,52,75,76,77,97,100,102,103,104,105,106,107,108,109,110,111,112,113,114,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,170,171,172", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "314,3650,3726,3806,3885,3964,4785,4897,4977,7655,7720,7814,10261,10477,10630,10693,10758,10817,10882,10943,11000,11119,11177,11238,11295,11366,11496,11804,11880,11965,12047,12125,12263,12338,12409,12559,12656,12734,12789,12845,12911,12991,13081,13152,13237,13316,13393,13463,13538,13650,13738,13811,13911,14010,14084,14160,14267,14321,14411,14484,14575,14671,14733,14797,14860,14931,15030,15128,15220,15316,15374,15434,15517,16121,16199,16275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "43,44,45,46,47,48,49,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3969,4064,4167,4265,4365,4466,4578,16914", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "4059,4162,4260,4360,4461,4573,4685,17010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,353,437,503,572,648,725,808,888,959,1036,1118,1195,1278,1360,1436,1507,1577,1670,1749,1823,1902", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,206,278,348,432,498,567,643,720,803,883,954,1031,1113,1190,1273,1355,1431,1502,1572,1665,1744,1818,1897,1974"}, "to": {"startLines": "33,53,96,98,99,101,115,116,117,164,165,166,168,173,174,175,176,177,178,179,180,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,4982,10124,10266,10336,10482,11501,11570,11646,15522,15605,15685,15883,16280,16362,16439,16522,16604,16680,16751,16821,17015,17094,17168,17247", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3075,5060,10191,10331,10415,10543,11565,11641,11718,15600,15680,15751,15955,16357,16434,16517,16599,16675,16746,16816,16909,17089,17163,17242,17319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5065,5167,5322,5443,5548,5710,5834,5955,6213,6371,6488,6659,6784,6929,7087,7151,7209", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "5162,5317,5438,5543,5705,5829,5950,6059,6366,6483,6654,6779,6924,7082,7146,7204,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "73,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7396,8062,8159,8292", "endColumns": "96,96,132,99", "endOffsets": "7488,8154,8287,8387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,116", "endOffsets": "166,283"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3080,3196", "endColumns": "115,116", "endOffsets": "3191,3308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,319,429,530,668,773,883,1002,1134,1268,1427,1557,1715,1819,1973,2096,2236,2375,2505,2632,2724,2849,2930,3045,3142,3273", "endColumns": "147,115,109,100,137,104,109,118,131,133,158,129,157,103,153,122,139,138,129,126,91,124,80,114,96,130,104", "endOffsets": "198,314,424,525,663,768,878,997,1129,1263,1422,1552,1710,1814,1968,2091,2231,2370,2500,2627,2719,2844,2925,3040,3137,3268,3373"}, "to": {"startLines": "36,37,72,74,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,167,186,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3313,3461,7286,7493,7819,7957,8392,8502,8621,8753,8887,9046,9176,9334,9438,9592,9715,9855,9994,15756,17324,17416,17541,17622,17737,17834,17965", "endColumns": "147,115,109,100,137,104,109,118,131,133,158,129,157,103,153,122,139,138,129,126,91,124,80,114,96,130,104", "endOffsets": "3456,3572,7391,7589,7952,8057,8497,8616,8748,8882,9041,9171,9329,9433,9587,9710,9850,9989,10119,15878,17411,17536,17617,17732,17829,17960,18065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,15960", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,16039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6064", "endColumns": "148", "endOffsets": "6208"}}]}]}