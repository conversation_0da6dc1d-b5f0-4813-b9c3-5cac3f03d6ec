import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
  useMemo
} from "react";

export interface GuestUserData {
  document: string;
  name: string;
  phone: string;
}

export interface GuestUserContextType {
  isGuest: boolean;
  guestData: GuestUserData | null;
  setGuestUser: (data: GuestUserData) => void;
  clearGuestUser: () => void;
}

const GuestUserContext = createContext<GuestUserContextType | undefined>(
  undefined
);

interface GuestUserProviderProps {
  readonly children: ReactNode;
}

export function GuestUserProvider({children}: GuestUserProviderProps) {
  const [guestData, setGuestData] = useState<GuestUserData | null>(null);

  const setGuestUser = useCallback((data: GuestUserData) => {
    setGuestData(data);
  }, []);

  const clearGuestUser = useCallback(() => {
    setGuestData(null);
  }, []);

  const isGuest = useMemo(() => !!guestData, [guestData]);

  const contextValue = useMemo(
    () => ({
      isGuest,
      guestData,
      setGuestUser,
      clearGuestUser
    }),
    [isGuest, guestData, setGuestUser, clearGuestUser]
  );

  return (
    <GuestUserContext.Provider value={contextValue}>
      {children}
    </GuestUserContext.Provider>
  );
}

export function useGuestUser(): GuestUserContextType {
  const context = useContext(GuestUserContext);

  if (context === undefined) {
    throw new Error("useGuestUser must be used within a GuestUserProvider");
  }

  return context;
}

export default GuestUserProvider;
