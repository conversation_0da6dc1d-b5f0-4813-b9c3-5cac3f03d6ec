import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginTop: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary,
    shadowColor: stylesConstants.colors.fullBlack,
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  disabled: {
    backgroundColor: stylesConstants.colors.gray700,
    borderColor: stylesConstants.colors.gray700,
    opacity: 0.6
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12
  },
  text: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 16,
    fontWeight: "600",
    fontFamily: stylesConstants.fonts.inter,
    textAlign: "center"
  }
});

export default styles;
