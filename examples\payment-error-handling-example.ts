/**
 * Exemplo de como aplicar o novo sistema de tratamento de erros
 * no arquivo hooks/api/use-payments.ts
 */

// ANTES - Sistema antigo (linhas 353-393 do use-payments.ts)
/*
onError: (error: any) => {
  console.error("❌ [USE-PAYMENTS] Erro ao criar cartão:", error);
  console.error("❌ [USE-PAYMENTS] Detalhes do erro:", {
    message: error.message,
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data,
    config: {
      url: error.config?.url,
      method: error.config?.method,
      data: error.config?.data
    }
  });

  let errorMessage = "Erro ao adicionar cartão de crédito";

  if (error.response?.status === 400) {
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (Array.isArray(error.response?.data)) {
      // Se for um array de erros de validação
      const validationErrors = error.response.data
        .map((err: any) => err.errorMessage || err.message)
        .filter(Boolean)
        .join(", ");
      if (validationErrors) {
        errorMessage = validationErrors;
      }
    } else {
      errorMessage = "Dados do cartão inválidos. Verifique as informações.";
    }
  }

  Toast.show({
    type: "error",
    text1: "Erro!",
    text2: errorMessage,
    position: "top",
    topOffset: 60
  });
}
*/

// DEPOIS - Sistema novo e melhorado
import { showErrorToast } from "@/utils/error-handler";

// 1. Adicionar import no topo do arquivo
// import { showErrorToast } from "@/utils/error-handler";

// 2. Substituir o onError por:
/*
onError: (error: any) => {
  console.error("❌ [USE-PAYMENTS] Erro ao criar cartão:", error);
  
  // Usar o sistema unificado de tratamento de erros
  showErrorToast(error, getCreditCardErrorMessage(error));
}
*/

// 3. Criar função específica para erros de cartão de crédito
export const getCreditCardErrorMessage = (error: any): string => {
  // Extrair status do erro
  const status = error?.response?.status || error?.status;
  const responseData = error?.response?.data;
  
  if (status === 400) {
    // Verificar se há mensagem específica da API
    if (responseData?.message) {
      return responseData.message;
    }
    
    // Verificar se é array de erros de validação
    if (Array.isArray(responseData)) {
      const validationErrors = responseData
        .map((err: any) => err.errorMessage || err.message)
        .filter(Boolean)
        .join(", ");
      
      if (validationErrors) {
        return validationErrors;
      }
    }
    
    // Mensagens específicas baseadas no conteúdo do erro
    const errorMessage = (responseData?.message || error?.message || "").toLowerCase();
    
    if (errorMessage.includes("cartão") || errorMessage.includes("card")) {
      return "Dados do cartão inválidos. Verifique o número, validade e CVV.";
    }
    if (errorMessage.includes("número") || errorMessage.includes("number")) {
      return "Número do cartão inválido. Verifique se digitou corretamente.";
    }
    if (errorMessage.includes("validade") || errorMessage.includes("expiry")) {
      return "Data de validade inválida. Use o formato MM/AA.";
    }
    if (errorMessage.includes("cvv") || errorMessage.includes("cvc")) {
      return "Código de segurança (CVV) inválido.";
    }
    if (errorMessage.includes("nome") || errorMessage.includes("name")) {
      return "Nome do portador inválido. Use o nome como está no cartão.";
    }
    
    return "Dados do cartão inválidos. Verifique as informações e tente novamente.";
  }
  
  if (status === 401) {
    return "Sua sessão expirou. Faça login novamente para adicionar o cartão.";
  }
  
  if (status === 403) {
    return "Você não tem permissão para adicionar cartões de crédito.";
  }
  
  if (status === 409) {
    return "Este cartão já está cadastrado em sua conta.";
  }
  
  if (status === 422) {
    return "Cartão rejeitado pela operadora. Verifique os dados ou use outro cartão.";
  }
  
  if (status >= 500) {
    return "Erro interno do servidor. Tente novamente em alguns minutos.";
  }
  
  // Verificar se é erro de rede
  if (!error?.response) {
    return "Erro de conexão. Verifique sua internet e tente novamente.";
  }
  
  return "Erro ao adicionar cartão de crédito. Tente novamente.";
};

// 4. Exemplo de hook especializado para pagamentos
export const usePaymentErrorHandler = () => {
  const handleCreditCardError = (error: any) => {
    const message = getCreditCardErrorMessage(error);
    showErrorToast(error, message);
  };
  
  const handlePaymentError = (error: any) => {
    const status = error?.response?.status || error?.status;
    let message = "Erro ao processar pagamento. Tente novamente.";
    
    if (status === 400) {
      message = "Dados de pagamento inválidos. Verifique as informações.";
    } else if (status === 402) {
      message = "Pagamento recusado. Verifique os dados do cartão ou use outro método.";
    } else if (status === 409) {
      message = "Este pagamento já foi processado.";
    }
    
    showErrorToast(error, message);
  };
  
  return {
    handleCreditCardError,
    handlePaymentError
  };
};

// 5. Exemplo de uso no componente
/*
import { usePaymentErrorHandler } from "@/hooks/use-payment-error-handler";

const MyPaymentComponent = () => {
  const { handleCreditCardError } = usePaymentErrorHandler();
  
  const createCreditCard = useMutation({
    mutationFn: (cardData) => PaymentsService.createCreditCard(cardData),
    onSuccess: (data) => {
      showSuccessToast(
        "Cartão Adicionado!",
        "Seu cartão de crédito foi adicionado com sucesso."
      );
    },
    onError: handleCreditCardError
  });
};
*/

// BENEFÍCIOS DO NOVO SISTEMA:

// 1. CÓDIGO MAIS LIMPO
// - Menos linhas de código repetitivo
// - Lógica de erro centralizada
// - Mais fácil de manter

// 2. MENSAGENS MAIS ESPECÍFICAS
// - Erros contextualizados por domínio
// - Melhor experiência do usuário
// - Mensagens em português

// 3. CONSISTÊNCIA
// - Mesmo padrão visual em todo o app
// - Comportamento uniforme
// - Fácil de testar

// 4. EXTENSIBILIDADE
// - Fácil adicionar novos tipos de erro
// - Hooks especializados por domínio
// - Reutilização de código

// 5. MANUTENIBILIDADE
// - Mudanças centralizadas
// - Menos duplicação de código
// - Melhor organização
