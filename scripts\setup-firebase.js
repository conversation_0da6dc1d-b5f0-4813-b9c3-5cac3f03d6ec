#!/usr/bin/env node

/**
 * Firebase Setup Script
 * Helps verify and setup Firebase configuration
 */

const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logWarning(message) {
  console.log(`⚠️ ${message}`);
}

function logInfo(message) {
  console.log(`ℹ️ ${message}`);
}

function checkFirebaseConfiguration() {
  console.log('🔥 Checking Firebase Configuration...\n');

  // Check configuration files
  const androidConfig = path.join(process.cwd(), 'keys', 'google-services.json');
  const iosConfig = path.join(process.cwd(), 'keys', 'GoogleService-Info.plist');
  const appConfig = path.join(process.cwd(), 'app.config.js');

  logInfo('Checking configuration files:');
  
  if (checkFileExists(androidConfig)) {
    logSuccess('Android config found: keys/google-services.json');
  } else {
    logError('Android config missing: keys/google-services.json');
  }

  if (checkFileExists(iosConfig)) {
    logSuccess('iOS config found: keys/GoogleService-Info.plist');
  } else {
    logError('iOS config missing: keys/GoogleService-Info.plist');
  }

  if (checkFileExists(appConfig)) {
    logSuccess('App config found: app.config.js');
    
    // Check if Firebase plugin is added
    const appConfigContent = fs.readFileSync(appConfig, 'utf8');
    if (appConfigContent.includes('@react-native-firebase/app')) {
      logSuccess('Firebase plugin found in app.config.js');
    } else {
      logError('Firebase plugin missing from app.config.js');
      logInfo('Add "@react-native-firebase/app" to the plugins array');
    }
  } else {
    logError('App config missing: app.config.js');
  }

  // Check package.json for Firebase dependencies
  const packageJson = path.join(process.cwd(), 'package.json');
  if (checkFileExists(packageJson)) {
    const packageContent = JSON.parse(fs.readFileSync(packageJson, 'utf8'));
    const dependencies = { ...packageContent.dependencies, ...packageContent.devDependencies };
    
    logInfo('\nChecking Firebase dependencies:');
    
    if (dependencies['@react-native-firebase/app']) {
      logSuccess(`@react-native-firebase/app: ${dependencies['@react-native-firebase/app']}`);
    } else {
      logError('@react-native-firebase/app not found');
    }
    
    if (dependencies['@react-native-firebase/auth']) {
      logSuccess(`@react-native-firebase/auth: ${dependencies['@react-native-firebase/auth']}`);
    } else {
      logError('@react-native-firebase/auth not found');
    }
  }

  console.log('\n🔥 Firebase Configuration Check Complete\n');
}

function showNextSteps() {
  console.log('📋 Next Steps:\n');
  
  console.log('1. If configuration files are missing:');
  console.log('   - Download from Firebase Console');
  console.log('   - Place in keys/ directory');
  console.log('');
  
  console.log('2. If Firebase plugin is missing from app.config.js:');
  console.log('   - Add "@react-native-firebase/app" to plugins array');
  console.log('');
  
  console.log('3. If dependencies are missing:');
  console.log('   - Run: bun add @react-native-firebase/app @react-native-firebase/auth');
  console.log('');
  
  console.log('4. After making changes:');
  console.log('   - Rebuild the app: bun run android (or bun run ios)');
  console.log('   - Test Firebase: npx ts-node scripts/test-firebase-auth.ts');
  console.log('');
  
  console.log('5. Enable Anonymous Authentication in Firebase Console:');
  console.log('   - Go to Authentication > Sign-in method');
  console.log('   - Enable Anonymous authentication');
  console.log('');
}

function main() {
  console.log('🔥 Firebase Setup Helper\n');
  
  checkFirebaseConfiguration();
  showNextSteps();
  
  console.log('For more help, see: docs/firebase-authentication.md');
}

if (require.main === module) {
  main();
}

module.exports = {
  checkFirebaseConfiguration,
  showNextSteps
};
