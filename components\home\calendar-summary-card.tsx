import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import CalendarIcon from "../icons/calendar-icon";
import styles from "../../styles/components/home/<USER>";

export interface CalendarSummaryCardProps {
  eventsCount: number;
  isLoading?: boolean;
  onPress?: () => void;
}

const CalendarSummaryCard: React.FC<CalendarSummaryCardProps> = ({
  eventsCount,
  isLoading = false,
  onPress
}) => {
  const {t} = useTranslation();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={isLoading}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Calendário</Text>
          <View style={styles.iconContainer}>
            <CalendarIcon width={24} height={24} replaceColor="#F2994A" />
          </View>
        </View>
        <Text style={styles.subtitle}>
          Você tem{" "}
          <Text style={styles.count}>
            {isLoading ? "..." : eventsCount}
          </Text>{" "}
          eventos agendados.
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default CalendarSummaryCard;
