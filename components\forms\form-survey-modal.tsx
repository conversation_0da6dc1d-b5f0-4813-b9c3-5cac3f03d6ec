import React, {useState, useCallback, useMemo} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import {
  FormSurveyModalProps,
  CreateFormAnswersData
} from "@/models/api/forms.models";
import FormQuestionStep from "./form-question-step";
import CloseIcon from "../icons/close-icon";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import styles from "@/styles/components/forms/form-survey-modal.style";
import stylesConstants from "@/styles/styles-constants";

const FormSurveyModal: React.FC<FormSurveyModalProps> = ({
  visible,
  onClose,
  form,
  onSubmit,
  isSubmitting = false
}) => {
  const {t} = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});

  // Flatten all questions from all pages
  const allQuestions = useMemo(() => {
    if (!form.pages) return [];

    const sortedPages = [...form.pages].sort((a, b) => a.number - b.number);
    return sortedPages.flatMap((page) => page.questions || []);
  }, [form.pages]);

  // Get current page info for better context
  const currentPageInfo = useMemo(() => {
    if (!form.pages || !currentQuestion) return null;

    for (const page of form.pages) {
      if (page.questions?.some((q) => q.id === currentQuestion.id)) {
        return page;
      }
    }
    return null;
  }, [form.pages, currentQuestion]);

  const totalSteps = allQuestions.length;
  const currentQuestion = allQuestions[currentStep];
  const isLastStep = currentStep === totalSteps - 1;
  const isFirstStep = currentStep === 0;

  // Check if current step is valid to proceed
  const canProceed = useMemo(() => {
    if (!currentQuestion) return false;

    const answer = answers[currentQuestion.id];

    // If question is required, answer must be provided
    if (currentQuestion.required) {
      return answer && answer.trim().length > 0;
    }

    // If not required, can always proceed
    return true;
  }, [currentQuestion, answers]);

  // Check if form can be submitted
  const canSubmit = useMemo(() => {
    // Check all required questions have answers
    const requiredQuestions = allQuestions.filter((q) => q.required);
    return requiredQuestions.every((q) => {
      const answer = answers[q.id];
      return answer && answer.trim().length > 0;
    });
  }, [allQuestions, answers]);

  const handleAnswerChange = useCallback(
    (questionId: number, answer: string) => {
      setAnswers((prev) => ({
        ...prev,
        [questionId]: answer
      }));
    },
    []
  );

  const handleNext = useCallback(() => {
    if (canProceed && currentStep < totalSteps - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [canProceed, currentStep, totalSteps]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  const handleSubmit = useCallback(() => {
    if (!canSubmit || isSubmitting) return;

    // Convert answers to API format
    const formAnswers: CreateFormAnswersData = {
      answers: Object.entries(answers)
        .filter(([_, answer]) => answer && answer.trim().length > 0)
        .map(([questionId, answer]) => ({
          questionId: parseInt(questionId),
          answer: answer.trim()
        }))
    };

    onSubmit(formAnswers);
  }, [answers, canSubmit, isSubmitting, onSubmit]);

  const handleClose = useCallback(() => {
    // Reset state when closing
    setCurrentStep(0);
    setAnswers({});
    onClose();
  }, [onClose]);

  if (!visible || !currentQuestion) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={isFirstStep ? handleClose : handlePrevious}
            disabled={isSubmitting}
          >
            {isFirstStep ? (
              <CloseIcon
                width={24}
                height={24}
                stroke={stylesConstants.colors.fullWhite}
              />
            ) : (
              <ChevronLeftIcon
                width={24}
                height={24}
                stroke={stylesConstants.colors.fullWhite}
              />
            )}
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>{form.title}</Text>
            <Text style={styles.stepIndicator}>
              {currentStep + 1} de {totalSteps}
              {currentPageInfo && ` • ${currentPageInfo.title}`}
            </Text>
          </View>

          <View style={styles.headerRight} />
        </View>

        {/* Question Content */}
        <View style={styles.content}>
          <FormQuestionStep
            question={currentQuestion}
            answer={answers[currentQuestion.id]}
            onAnswerChange={(answer) =>
              handleAnswerChange(currentQuestion.id, answer)
            }
            isRequired={currentQuestion.required}
            pageTitle={currentPageInfo?.title}
          />
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          {isLastStep ? (
            <TouchableOpacity
              style={[
                styles.submitButton,
                (!canSubmit || isSubmitting) && styles.submitButtonDisabled
              ]}
              onPress={handleSubmit}
              disabled={!canSubmit || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.submitButtonText}>
                  {t("formSurvey.submit", "Enviar")}
                </Text>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                styles.nextButton,
                !canProceed && styles.nextButtonDisabled
              ]}
              onPress={handleNext}
              disabled={!canProceed || isSubmitting}
            >
              <Text style={styles.nextButtonText}>
                {t("formSurvey.next", "Próximo")}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default FormSurveyModal;
