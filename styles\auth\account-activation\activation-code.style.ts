import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
    justifyContent: "flex-start"
  },
  contentContainer: {
    flex: 1
  },
  backButtonMargin: {
    marginBottom: 24
  },
  text: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontStyle: "normal",
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  titleText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 30,
    marginBottom: 8
  },
  subScreenContainer: {
    flex: 1
  },
  descriptionText: {
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 32
  },
  securityCodeText: {
    marginBottom: 12
  },
  inputNewPassword: {
    marginBottom: 16
  },
  repeatPasswordInput: {
    marginBottom: 6
  },
  squareInputContainer: {
    alignSelf: "center",
    width: "100%"
  },
  footerContainer: {
    alignItems: "center",
    paddingHorizontal: 0,
    marginHorizontal: -4,
    marginTop: "auto",
    paddingBottom: 20
  },
  retrySendCode: {
    display: "flex",
    flexDirection: "row",
    gap: 5,
    marginBottom: 24
  },
  retrySendText: {
    color: stylesConstants.colors.fullWhite
  },
  retrySendButton: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 700
  }
});

export default styles;
