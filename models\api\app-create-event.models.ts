/**
 * Modelos de dados para criação de eventos via API do ClubM
 * Baseado no schema AppCreateEventViewModel da API
 */

import {z} from "zod";

// Enums baseados na API
export enum AppEventType {
  PRESENTIAL = 0,
  ONLINE = 1,
  HYBRID = 2
}

export enum AppEventCategory {
  CONFERENCE = 0,
  WORKSHOP = 1,
  SEMINAR = 2,
  NETWORKING = 3,
  WEBINAR = 4,
  SOCIAL = 5,
  TRAINING = 6,
  MEETING = 7,
  OTHER = 8
}

// Interface principal para criação de eventos (baseado no schema real da API)
export interface AppCreateEventViewModel {
  name?: string;
  description?: string;
  startDate: string; // ISO 8601 format ($date-time)
  endDate: string; // ISO 8601 format ($date-time)
  type: number; // EventType: 0, 1, 2
  status?: number; // EventStatus: 0, 1
  category: number; // EventCategory: 0, 1, 2, 3, 4
  localLink?: string;
  addressStreet?: string;
  addressNumber?: string;
  addressCity?: string;
  addressNeighborhood?: string;
  addressState?: string;
  addressZip?: string;
  addressComplement?: string;
  userIds: number[];
  message?: string;
}

// Interface para dados do formulário (antes da conversão para API)
export interface CreateEventFormData {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  type: AppEventType;
  category: AppEventCategory;
  capacity?: number;
  price: number;
  isFree: boolean;
  isPublic: boolean;
  notifyParticipants: boolean;
  requireQrCode: boolean;
}

// Schemas de validação usando Zod
export const AppCreateEventViewModelSchema = z
  .object({
    name: z
      .string()
      .min(1, "Título é obrigatório")
      .max(200, "Título muito longo"),
    description: z
      .string()
      .min(1, "Descrição é obrigatória")
      .max(1000, "Descrição muito longa"),
    startDate: z.string().datetime("Data de início inválida"),
    endDate: z.string().datetime("Data de fim inválida"),
    type: z.nativeEnum(AppEventType),
    category: z.nativeEnum(AppEventCategory),
    limit: z.number().min(1, "Capacidade deve ser maior que 0").optional(),
    value: z.number().min(0, "Preço não pode ser negativo"),
    notify: z.boolean(),
    validateQrCode: z.boolean(),

    // Campos de endereço opcionais
    addressStreet: z.string().optional(),
    addressNumber: z.string().optional(),
    addressCity: z.string().optional(),
    addressNeighborhood: z.string().optional(),
    addressState: z.string().optional(),
    addressZip: z.string().optional(),
    addressComplement: z.string().optional(),

    // URL online opcional
    localLink: z.string().url("URL inválida").optional(),

    // ID da imagem opcional
    imageId: z.string().optional()
  })
  .refine(
    (data) => {
      // Validar que data de fim é posterior à data de início
      return new Date(data.endDate) > new Date(data.startDate);
    },
    {
      message: "Data de fim deve ser posterior à data de início",
      path: ["endDate"]
    }
  )
  .refine(
    (data) => {
      // Validar que eventos online têm URL
      if (data.type === AppEventType.ONLINE && !data.localLink) {
        return false;
      }
      return true;
    },
    {
      message: "URL é obrigatória para eventos online",
      path: ["localLink"]
    }
  )
  .refine(
    (data) => {
      // Validar que eventos presenciais têm endereço
      if (data.type === AppEventType.PRESENTIAL && !data.addressStreet) {
        return false;
      }
      return true;
    },
    {
      message: "Endereço é obrigatório para eventos presenciais",
      path: ["addressStreet"]
    }
  );

export const CreateEventFormDataSchema = z
  .object({
    title: z
      .string()
      .min(1, "Título é obrigatório")
      .max(200, "Título muito longo"),
    description: z
      .string()
      .min(1, "Descrição é obrigatória")
      .max(1000, "Descrição muito longa"),
    startDate: z.date(),
    endDate: z.date(),
    type: z.nativeEnum(AppEventType),
    category: z.nativeEnum(AppEventCategory),
    capacity: z.number().min(1, "Capacidade deve ser maior que 0").optional(),
    price: z.number().min(0, "Preço não pode ser negativo"),
    isFree: z.boolean(),
    isPublic: z.boolean(),
    notifyParticipants: z.boolean(),
    requireQrCode: z.boolean()
  })
  .refine(
    (data) => {
      return data.endDate > data.startDate;
    },
    {
      message: "Data de fim deve ser posterior à data de início",
      path: ["endDate"]
    }
  )
  .refine(
    (data) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day
      const startDate = new Date(data.startDate);
      startDate.setHours(0, 0, 0, 0); // Reset time to start of day
      return startDate >= today;
    },
    {
      message: "Data de início não pode ser no passado",
      path: ["startDate"]
    }
  );

// Tipos validados
export type ValidatedAppCreateEventViewModel = z.infer<
  typeof AppCreateEventViewModelSchema
>;
export type ValidatedCreateEventFormData = z.infer<
  typeof CreateEventFormDataSchema
>;

// Funções utilitárias
export const getEventTypeLabel = (type: AppEventType): string => {
  switch (type) {
    case AppEventType.PRESENTIAL:
      return "Presencial";
    case AppEventType.ONLINE:
      return "Online";
    case AppEventType.HYBRID:
      return "Híbrido";
    default:
      return "Desconhecido";
  }
};

export const getEventCategoryLabel = (category: AppEventCategory): string => {
  switch (category) {
    case AppEventCategory.CONFERENCE:
      return "Conferência";
    case AppEventCategory.WORKSHOP:
      return "Workshop";
    case AppEventCategory.SEMINAR:
      return "Seminário";
    case AppEventCategory.NETWORKING:
      return "Networking";
    case AppEventCategory.WEBINAR:
      return "Webinar";
    case AppEventCategory.SOCIAL:
      return "Social";
    case AppEventCategory.TRAINING:
      return "Treinamento";
    case AppEventCategory.MEETING:
      return "Reunião";
    case AppEventCategory.OTHER:
      return "Outro";
    default:
      return "Desconhecido";
  }
};

// Função para converter dados do formulário para o formato da API
export const convertFormDataToApiModel = (
  formData: CreateEventFormData
): AppCreateEventViewModel => {
  // Converter enums para números conforme a API
  const getTypeNumber = (type: AppEventType): number => {
    switch (type) {
      case AppEventType.PRESENTIAL:
        return 0;
      case AppEventType.ONLINE:
        return 1;
      case AppEventType.HYBRID:
        return 2;
      default:
        return 0;
    }
  };

  const getCategoryNumber = (category: AppEventCategory): number => {
    switch (category) {
      case AppEventCategory.CONFERENCE:
        return 0;
      case AppEventCategory.WORKSHOP:
        return 1;
      case AppEventCategory.SEMINAR:
        return 2;
      case AppEventCategory.NETWORKING:
        return 3;
      case AppEventCategory.WEBINAR:
        return 4;
      case AppEventCategory.SOCIAL:
        return 0; // Mapear para conference
      case AppEventCategory.TRAINING:
        return 1; // Mapear para workshop
      case AppEventCategory.MEETING:
        return 2; // Mapear para seminar
      case AppEventCategory.OTHER:
        return 0; // Mapear para conference
      default:
        return 0;
    }
  };

  const result: AppCreateEventViewModel = {
    name: formData.title,
    description: formData.description,
    startDate: formData.startDate.toISOString(),
    endDate: formData.endDate.toISOString(),
    type: getTypeNumber(formData.type),
    category: getCategoryNumber(formData.category),
    status: 0, // Ativo por padrão
    userIds: [] // Campo obrigatório - array vazio por padrão
  };

  // Adicionar campos de localização baseado no tipo
  if (formData.type === AppEventType.ONLINE && formData.localLink) {
    result.localLink = formData.localLink;
  } else if (formData.type === AppEventType.PRESENTIAL) {
    if (formData.addressStreet) result.addressStreet = formData.addressStreet;
    if (formData.addressNumber) result.addressNumber = formData.addressNumber;
    if (formData.addressCity) result.addressCity = formData.addressCity;
    if (formData.addressNeighborhood)
      result.addressNeighborhood = formData.addressNeighborhood;
    if (formData.addressState) result.addressState = formData.addressState;
    if (formData.addressZip) result.addressZip = formData.addressZip;
    if (formData.addressComplement)
      result.addressComplement = formData.addressComplement;
  } else if (formData.type === AppEventType.HYBRID) {
    if (formData.localLink) result.localLink = formData.localLink;
    if (formData.addressStreet) result.addressStreet = formData.addressStreet;
    if (formData.addressNumber) result.addressNumber = formData.addressNumber;
    if (formData.addressCity) result.addressCity = formData.addressCity;
    if (formData.addressNeighborhood)
      result.addressNeighborhood = formData.addressNeighborhood;
    if (formData.addressState) result.addressState = formData.addressState;
    if (formData.addressZip) result.addressZip = formData.addressZip;
    if (formData.addressComplement)
      result.addressComplement = formData.addressComplement;
  }

  // Debug log para verificar se userIds está presente
  console.log(
    "🔍 [DEBUG] Resultado da conversão:",
    JSON.stringify(result, null, 2)
  );

  return result;
};

// Função para converter para o formato do serviço de eventos existente
export const convertFormDataToCreateEventRequest = (
  formData: CreateEventFormData
): any => {
  // Mapear categoria enum para string
  const getCategoryId = (category: AppEventCategory): string => {
    switch (category) {
      case AppEventCategory.CONFERENCE:
        return "conference";
      case AppEventCategory.WORKSHOP:
        return "workshop";
      case AppEventCategory.SEMINAR:
        return "seminar";
      case AppEventCategory.NETWORKING:
        return "networking";
      case AppEventCategory.WEBINAR:
        return "webinar";
      case AppEventCategory.SOCIAL:
        return "social";
      case AppEventCategory.TRAINING:
        return "training";
      case AppEventCategory.MEETING:
        return "meeting";
      case AppEventCategory.OTHER:
        return "other";
      default:
        return "other";
    }
  };

  // Mapear tipo enum para string
  const getEventType = (type: AppEventType): string => {
    switch (type) {
      case AppEventType.PRESENTIAL:
        return "conference"; // Mapear para conference como padrão
      case AppEventType.ONLINE:
        return "webinar";
      case AppEventType.HYBRID:
        return "conference";
      default:
        return "conference";
    }
  };

  // Determinar tipo de localização
  let locationType = "physical";
  if (formData.type === AppEventType.ONLINE) {
    locationType = "online";
  } else if (formData.type === AppEventType.HYBRID) {
    locationType = "hybrid";
  }

  return {
    title: formData.title,
    description: formData.description,
    shortDescription: formData.description.substring(0, 200),
    categoryId: getCategoryId(formData.category),
    type: getEventType(formData.type),
    startDate: formData.startDate.toISOString(),
    endDate: formData.endDate.toISOString(),
    location: {
      type: locationType,
      name: "Local do evento",
      address: ""
    },
    capacity: formData.capacity,
    price: {
      isFree: formData.isFree,
      amount: formData.isFree ? 0 : formData.price,
      currency: "BRL"
    },
    tags: [],
    requirements: [],
    images: []
  };
};
