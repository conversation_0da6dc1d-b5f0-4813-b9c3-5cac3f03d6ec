import React, {useCallback, useState} from "react";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {ColorValue, Text, TouchableOpacity, View} from "react-native";
import styles from "@/styles/auth/account-activation/activation-code.style";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import RecoveryDigits from "@/components/recovery-password/recovery-digits";
import FullSizeButton from "@/components/full-size-button";
import InputField from "@/components/input-field";
import PasswordIcon from "@/components/icons/password-icon";
import {ActivateAccount as ActivateAccountModel} from "@/models/login";
import {useLocalSearchParams} from "expo-router";
import useAccountActivation from "@/hooks/use-account-activation";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";

export interface ActivationCodeParams {
  document?: string;
  email?: string;
}

const ActivationCode: React.FC = () => {
  const params = useLocalSearchParams() as ActivationCodeParams;
  const activationAction = useAccountActivation();
  const errorAction = useErrorMessage();
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [activationCode, setActivationCode] = useState<string>("");
  const [activationStep, setActivationStep] = useState<number>(1);
  const [passwordForm, setPasswordForm] = useState<ActivateAccountModel>({
    code: "",
    password: "",
    repeatPassword: ""
  });

  const {t} = useTranslation();

  const onActivationCodeChange = useCallback((value: string) => {
    setActivationCode(value);
  }, []);

  const onPasswordFormChange = useCallback(
    (field: keyof ActivateAccountModel) => (value: string) => {
      setPasswordForm((prev) => ({
        ...prev,
        [field]: value
      }));
    },
    []
  );

  const nextStep = useCallback(() => {
    if (activationStep == 1) {
      if (activationCode.length != 6) {
        errorAction.emitError({
          errorType: ErrorType.Error,
          title: t("errors.authenticationError"),
          description: t("errors.authenticationErrorDescription")
        });
        return;
      }
      setPasswordForm((prev) => ({...prev, code: activationCode}));
      setActivationStep((x) => x + 1);
    } else {
      if (passwordForm.password != passwordForm.repeatPassword) {
        setFormErrors({
          repeatPassword: t("errors.passwordNotMatch")
        });
        return;
      }

      setFormErrors({});
      activationAction.activateAccount({
        code: activationCode,
        password: passwordForm.password,
        document: params.document || ""
      });
    }
  }, [
    activationStep,
    activationAction.activateAccount,
    params.document,
    activationCode,
    passwordForm.password,
    passwordForm.repeatPassword,
    errorAction.emitError,
    t
  ]);

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon color={errorColor} />,
    []
  );

  const onRetrySendCode = useCallback(() => {
    if (params.document) {
      activationAction.resendActivationCode(params.document);
    } else {
      errorAction.emitError({
        errorType: ErrorType.Error,
        title: t("errors.invalidData"),
        description: t("errors.requiredField")
      });
    }
  }, [params.document, activationAction.resendActivationCode, errorAction, t]);

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <View style={styles.backButtonMargin}>
              <BackButton />
            </View>
            <Text style={[styles.text, styles.titleText]}>
              {t("accountActivation.title")}
            </Text>
            {activationStep === 1 ? (
              <View style={styles.subScreenContainer}>
                <Text style={[styles.text, styles.descriptionText]}>
                  {t("accountActivation.firstStepDescription", {
                    email: params.email || "seu e-mail"
                  })}
                </Text>
                <Text style={[styles.text, styles.securityCodeText]}>
                  {t("accountActivation.securityCode")}
                </Text>
                <RecoveryDigits
                  value={activationCode}
                  onChange={onActivationCodeChange}
                />
              </View>
            ) : (
              <View style={styles.subScreenContainer}>
                <Text style={[styles.text, styles.descriptionText]}>
                  {t("accountActivation.secondStepDescription")}
                </Text>
                <InputField
                  label={t("accountActivation.newPassword")}
                  isPassword={true}
                  style={[styles.inputNewPassword, styles.squareInputContainer]}
                  value={passwordForm.password}
                  icon={passwordIcon}
                  error={activationAction.errors["password"]}
                  placeholder={t("accountActivation.newPasswordPlaceholder")}
                  onChangeText={onPasswordFormChange("password")}
                  height={56}
                />
                <InputField
                  label={t("accountActivation.repeatNewPassword")}
                  isPassword={true}
                  style={[
                    styles.repeatPasswordInput,
                    styles.squareInputContainer
                  ]}
                  value={passwordForm.repeatPassword}
                  icon={passwordIcon}
                  error={
                    formErrors["repeatPassword"] ||
                    activationAction.errors["repeatPassword"]
                  }
                  placeholder={t(
                    "accountActivation.repeatNewPasswordPlaceholder"
                  )}
                  onChangeText={onPasswordFormChange("repeatPassword")}
                  height={56}
                />
                <Text style={styles.text}>
                  {t("accountActivation.passwordRequirement")}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.footerContainer}>
            {activationStep === 1 && (
              <View style={styles.retrySendCode}>
                <Text style={[styles.text, styles.retrySendText]}>
                  {t("accountActivation.didNotReceiveCode")}
                </Text>
                <TouchableOpacity onPress={onRetrySendCode}>
                  <Text style={[styles.text, styles.retrySendButton]}>
                    {t("accountActivation.resendCode")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            <FullSizeButton
              text={
                activationStep === 1
                  ? "accountActivation.nextButton"
                  : "accountActivation.activateButton"
              }
              onPress={nextStep}
            />
          </View>
        </View>
      </Screen>
    </>
  );
};

export default ActivationCode;
