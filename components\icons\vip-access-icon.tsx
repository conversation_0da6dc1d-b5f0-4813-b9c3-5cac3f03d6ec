import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface VipAccessIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const VipAccessIcon: React.FC<VipAccessIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg {...props} viewBox="0 0 21 20" fill="none">
      <Path
        d="M2.16595 7.5H17.9993M8.41595 2.5L6.74929 7.5L10.0826 17.0833L13.416 7.5L11.7493 2.5M10.5948 16.8854L18.0603 7.92679C18.1868 7.77496 18.2501 7.69905 18.2743 7.6143C18.2956 7.53959 18.2956 7.46041 18.2743 7.38569C18.2501 7.30095 18.1868 7.22504 18.0603 7.07321L14.4492 2.73988C14.3757 2.6517 14.339 2.60762 14.2939 2.57592C14.254 2.54784 14.2095 2.52698 14.1624 2.51431C14.1092 2.5 14.0518 2.5 13.937 2.5H6.2282C6.11343 2.5 6.05604 2.5 6.00286 2.51431C5.95574 2.52698 5.91121 2.54784 5.87131 2.57592C5.82627 2.60762 5.78954 2.6517 5.71606 2.73988L2.10495 7.07321C1.97842 7.22504 1.91516 7.30095 1.89099 7.38569C1.86967 7.46041 1.86967 7.53959 1.89099 7.6143C1.91516 7.69904 1.97842 7.77496 2.10495 7.92679L9.57047 16.8854C9.74635 17.0965 9.83429 17.202 9.93958 17.2405C10.032 17.2743 10.1333 17.2743 10.2257 17.2405C10.3309 17.202 10.4189 17.0965 10.5948 16.8854Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default VipAccessIcon;
