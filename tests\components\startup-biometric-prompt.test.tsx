import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import StartupBiometricPrompt from "@/components/startup-biometric-prompt";

// Mock dos componentes
jest.mock("@/components/icons/biometric-icon", () => {
  return ({children}: any) => <>{children}</>;
});

jest.mock("@/components/logos/small-logo", () => {
  return ({children}: any) => <>{children}</>;
});

describe("StartupBiometricPrompt", () => {
  const defaultProps = {
    isAuthenticating: false,
    onBiometricPress: jest.fn(),
    onSkipPress: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve renderizar corretamente", () => {
    const {getByText} = render(<StartupBiometricPrompt {...defaultProps} />);

    expect(getByText("Bem-vindo de volta!")).toBeTruthy();
    expect(getByText("Use sua biometria para acessar rapidamente sua conta.")).toBeTruthy();
    expect(getByText("Usar Biometria")).toBeTruthy();
    expect(getByText("Usar senha")).toBeTruthy();
  });

  it("deve chamar onBiometricPress quando botão de biometria é pressionado", () => {
    const {getByText} = render(<StartupBiometricPrompt {...defaultProps} />);
    
    fireEvent.press(getByText("Usar Biometria"));
    
    expect(defaultProps.onBiometricPress).toHaveBeenCalledTimes(1);
  });

  it("deve chamar onSkipPress quando botão de pular é pressionado", () => {
    const {getByText} = render(<StartupBiometricPrompt {...defaultProps} />);
    
    fireEvent.press(getByText("Usar senha"));
    
    expect(defaultProps.onSkipPress).toHaveBeenCalledTimes(1);
  });

  it("deve mostrar estado de carregamento quando isAuthenticating é true", () => {
    const {getByText, queryByText} = render(
      <StartupBiometricPrompt {...defaultProps} isAuthenticating={true} />
    );

    expect(getByText("Autenticando...")).toBeTruthy();
    expect(queryByText("Usar Biometria")).toBeFalsy();
  });

  it("deve desabilitar botões quando isAuthenticating é true", () => {
    const {getByText} = render(
      <StartupBiometricPrompt {...defaultProps} isAuthenticating={true} />
    );

    const biometricButton = getByText("Autenticando...").parent;
    const skipButton = getByText("Usar senha").parent;

    fireEvent.press(biometricButton!);
    fireEvent.press(skipButton!);

    // Não deve chamar as funções quando desabilitado
    expect(defaultProps.onBiometricPress).not.toHaveBeenCalled();
    expect(defaultProps.onSkipPress).not.toHaveBeenCalled();
  });
});
