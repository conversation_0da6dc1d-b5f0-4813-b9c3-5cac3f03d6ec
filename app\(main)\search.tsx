import React, {useState, useCallback, useEffect, useMemo} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  RefreshControl
} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import styles from "@/styles/main/search.style";
import SearchIcon from "@/components/icons/search-icon";
import CloseIcon from "@/components/icons/close-icon";
import SearchResultItem from "@/components/search-result-item";
import {useGlobalSearch} from "@/hooks/api/use-search";
import {SearchResult} from "@/services/api/search/search.service";
import {
  RecentSearchesManager,
  RecentSearch,
  RecentSearchResult
} from "@/utils/recent-searches";

// Hook useDebounce inline para evitar problemas de importação
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

const SearchScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  // State management with safe defaults to prevent undefined errors
  const [searchTerm, setSearchTerm] = useState("");
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [recentResults, setRecentResults] = useState<RecentSearchResult[]>([]);
  const [isLoadingRecent, setIsLoadingRecent] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [showResults, setShowResults] = useState(true);

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Global search query
  const {
    data: searchData,
    isLoading: isSearching,
    error: searchError,
    refetch: refetchSearch
  } = useGlobalSearch(debouncedSearchTerm, {pageSize: 20});

  // Control when to show results based on search term and data
  useEffect(() => {
    // Show results when we have a search term
    if (debouncedSearchTerm.trim().length > 0) {
      setShowResults(true);
    } else {
      // Hide results when search term is empty
      setShowResults(false);
    }
  }, [debouncedSearchTerm]);

  // Note: Removed infinite search for now to avoid conflicts

  const loadRecentData = useCallback(async () => {
    try {
      setIsLoadingRecent(true);
      const [searches, results] = await Promise.all([
        RecentSearchesManager.getRecentSearches(),
        RecentSearchesManager.getRecentResults()
      ]);
      // Ensure we always have arrays, even if the functions return null/undefined
      setRecentSearches(Array.isArray(searches) ? searches : []);
      setRecentResults(Array.isArray(results) ? results : []);
      console.log("TEXTO AQUI" + setRecentResults);
      console.log("TEXTO AQUI" + setRecentSearches);
      console.log("TEXTO AQUI" + searches);
    } catch (error) {
      console.error("Error loading recent data:", error);
    } finally {
      setIsLoadingRecent(false);
    }
  }, []);

  // Load recent searches and results on mount
  useEffect(() => {
    loadRecentData();
  }, [loadRecentData]);

  // Handle search term change
  const handleSearchChange = useCallback((text: string) => {
    setSearchTerm(text);
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchTerm("");
    setShowResults(false);
  }, []);

  // Handle search submission (when user presses search or enters)
  const handleSearchSubmit = useCallback(async () => {
    if (searchTerm.trim().length >= 2) {
      await RecentSearchesManager.addRecentSearch(searchTerm.trim());
      loadRecentData(); // Refresh recent searches
    }
  }, [searchTerm, loadRecentData]);

  // Handle result press
  const handleResultPress = useCallback(
    async (result: SearchResult) => {
      // Add to recent results if it's from a search
      if (debouncedSearchTerm.trim().length >= 2) {
        await RecentSearchesManager.addRecentResults(debouncedSearchTerm, [
          result
        ]);
      }

      // Navigate based on result type
      switch (result.type) {
        case "member":
          router.push(`/(main)/user-profile/${result.id}`);
          break;
        case "event":
          // Navigate to event sale page with the correct parameter name
          router.push({
            pathname: "/(events)/event-sale",
            params: {
              id: result.id
            }
          });
          break;
        case "product":
          // Navigate to product page with the correct parameter name
          router.push({
            pathname: "/(logged-stack)/product-page",
            params: {
              id: result.id
            }
          });
          break;
        case "magazine":
          router.push(`/(magazine)/magazine-details/${result.id}`);
          break;
        case "chat":
          router.push(`/(logged-stack)/chat?chatId=${result.id}`);
          break;
        case "partner":
          router.push(`/(main)/partners/${result.id}`);
          break;
        case "opportunity":
          router.push(`/(business)/opportunities/${result.id}`);
          break;
        default:
          console.warn(`No navigation defined for result type: ${result.type}`);
      }
    },
    [debouncedSearchTerm, router]
  );

  // Handle removing recent search
  const handleRemoveRecentSearch = useCallback(
    async (term: string) => {
      await RecentSearchesManager.removeRecentSearch(term);
      loadRecentData();
    },
    [loadRecentData]
  );

  // Handle removing recent result
  const handleRemoveRecentResult = useCallback(
    async (result: RecentSearchResult) => {
      await RecentSearchesManager.removeRecentResult(result.id);
      loadRecentData();
    },
    [loadRecentData]
  );

  // Handle clearing all recent data
  const handleClearAllRecent = useCallback(async () => {
    await RecentSearchesManager.clearAllRecentData();
    loadRecentData();
  }, [loadRecentData]);

  // Filter options
  const filterOptions = useMemo(
    () => [
      {key: "all", label: t("search.filters.all", "Todos")},
      {key: "member", label: t("search.filters.members", "Membros")},
      {key: "event", label: t("search.filters.events", "Eventos")},
      {key: "product", label: t("search.filters.products", "Produtos")},
      {key: "magazine", label: t("search.filters.magazines", "Revistas")},
      {key: "partner", label: t("search.filters.partners", "Parceiros")}
    ],
    [t]
  );

  // Filter results based on selected filter with safe defaults
  const filteredResults = useMemo(() => {
    // Ensure we always have a valid array
    const results = searchData?.data;
    if (!results || !Array.isArray(results)) {
      return [];
    }

    // The SearchService now handles transformation, so we just need to filter
    if (selectedFilter === "all") {
      return results;
    }

    return results.filter((result) => result && result.type === selectedFilter);
  }, [searchData, selectedFilter]);

  // Get recent results by type for display with safe defaults
  const recentResultsByType = useMemo(() => {
    const grouped: Record<string, RecentSearchResult[]> = {};

    // Ensure we have a valid array and each result is valid
    if (recentResults && Array.isArray(recentResults)) {
      recentResults.forEach((result) => {
        if (result?.type) {
          if (!grouped[result.type]) {
            grouped[result.type] = [];
          }
          grouped[result.type].push(result);
        }
      });
    }

    return grouped;
  }, [recentResults]);

  return (
    <ScreenWithHeader screenTitle={t("search.title", "Busca")} backButton>
      <View style={styles.contentContainer}>
        {/* Search Input Row */}
        <View style={styles.searchRow}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} replaceColor="#F2F4F7" />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={handleSearchChange}
              onSubmitEditing={handleSearchSubmit}
              placeholder={t(
                "search.placeholder",
                "Buscar membros, eventos, etc..."
              )}
              placeholderTextColor="#F2F4F7"
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="search"
            />
          </View>
          <TouchableOpacity
            onPress={handleClearSearch}
            style={styles.cancelButton}
          >
            <Text style={styles.cancelButtonText}>
              {t("search.cancel", "Cancelar")}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoadingRecent}
              onRefresh={loadRecentData}
              tintColor="#FFFFFF"
            />
          }
        >
          {/* Show search results if user has searched */}
          {showResults ? (
            <>
              {/* Filter chips */}
              <View style={styles.filtersContainer}>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.filtersScrollContent}
                >
                  {filterOptions.map((filter) => (
                    <TouchableOpacity
                      key={filter.key}
                      style={[
                        styles.filterChip,
                        selectedFilter === filter.key && styles.filterChipActive
                      ]}
                      onPress={() => setSelectedFilter(filter.key)}
                    >
                      <Text
                        style={[
                          styles.filterChipText,
                          selectedFilter === filter.key &&
                            styles.filterChipTextActive
                        ]}
                      >
                        {filter.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Search results */}
              {isSearching ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#FFFFFF" />
                  <Text style={styles.loadingText}>
                    {t("search.searching", "Buscando...")}
                  </Text>
                </View>
              ) : searchError ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>
                    {t("search.error", "Erro ao buscar. Tente novamente.")}
                  </Text>
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={() => refetchSearch()}
                  >
                    <Text style={styles.retryButtonText}>
                      {t("search.retry", "Tentar novamente")}
                    </Text>
                  </TouchableOpacity>
                </View>
              ) : Array.isArray(filteredResults) &&
                filteredResults.length > 0 ? (
                <>
                  <View style={styles.resultsHeader}>
                    <Text style={styles.resultsCount}>
                      {t("search.resultsCount", "{{count}} resultados", {
                        count: filteredResults.length
                      })}
                    </Text>
                  </View>
                  {filteredResults.map((result, index) => {
                    // Skip invalid results
                    if (!result?.id) {
                      console.warn(`Invalid result at index ${index}:`, result);
                      return null;
                    }

                    try {
                      return (
                        <SearchResultItem
                          key={`${result.type}-${result.id}`}
                          result={result}
                          onPress={handleResultPress}
                        />
                      );
                    } catch (error) {
                      console.error(
                        `Error rendering result at index ${index}:`,
                        error,
                        result
                      );
                      return null;
                    }
                  })}
                </>
              ) : (
                <View style={styles.noResultsContainer}>
                  <Text style={styles.noResultsText}>
                    {t("search.noResults", "Nenhum resultado encontrado")}
                  </Text>
                  <Text style={styles.noResultsSubtext}>
                    {t(
                      "search.noResultsSubtext",
                      "Tente usar palavras-chave diferentes"
                    )}
                  </Text>
                </View>
              )}
            </>
          ) : (
            <>
              {/* Recent Searches Header */}
              <View style={styles.recentSearchesHeader}>
                <Text style={styles.recentSearchesTitle}>
                  {t("search.recentSearches", "Pesquisas recentes")}
                </Text>
                <TouchableOpacity
                  style={styles.clearRecentButton}
                  onPress={handleClearAllRecent}
                >
                  <Text style={styles.clearRecentButtonText}>
                    {t("search.clearRecent", "Limpar recentes")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Recent search terms */}
              {Array.isArray(recentSearches) && recentSearches.length > 0 && (
                <View style={styles.recentSearchTerms}>
                  {recentSearches.slice(0, 5).map((search) => (
                    <TouchableOpacity
                      key={search?.term || Math.random().toString()}
                      style={styles.recentSearchTerm}
                      onPress={() => search?.term && setSearchTerm(search.term)}
                    >
                      <Text style={styles.recentSearchTermText}>
                        {search?.term || ""}
                      </Text>
                      <TouchableOpacity
                        onPress={() =>
                          search?.term && handleRemoveRecentSearch(search.term)
                        }
                        style={styles.recentSearchTermClose}
                        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
                      >
                        <CloseIcon width={16} height={16} />
                      </TouchableOpacity>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {/* Recent results by category */}
              {Object.entries(recentResultsByType || {}).map(
                ([type, results]) => (
                  <View key={type} style={styles.categorySection}>
                    <Text style={styles.categoryTitle}>
                      {type === "member" && t("search.members", "Membros")}
                      {type === "event" && t("search.events", "Eventos")}
                      {type === "product" && t("search.products", "Produtos")}
                      {type === "magazine" && t("search.magazines", "Revistas")}
                      {type === "partner" && t("search.partners", "Parceiros")}
                      {type === "opportunity" &&
                        t("search.opportunities", "Oportunidades")}
                      {type === "chat" && t("search.chats", "Conversas")}
                    </Text>
                    {Array.isArray(results) &&
                      results.slice(0, 3).map((result, index) => {
                        // Skip invalid results
                        if (!result?.id) {
                          console.warn(
                            `Invalid recent result at index ${index}:`,
                            result
                          );
                          return null;
                        }

                        try {
                          return (
                            <SearchResultItem
                              key={`recent-${result.type}-${result.id}`}
                              result={result}
                              onPress={handleResultPress}
                              onRemove={() => handleRemoveRecentResult(result)}
                              showRemoveButton={true}
                            />
                          );
                        } catch (error) {
                          console.error(
                            `Error rendering recent result at index ${index}:`,
                            error,
                            result
                          );
                          return null;
                        }
                      })}
                  </View>
                )
              )}

              {/* Show empty state if no recent data */}
              {(!Array.isArray(recentSearches) ||
                recentSearches.length === 0) &&
                Object.keys(recentResultsByType || {}).length === 0 &&
                !isLoadingRecent && (
                  <View style={styles.emptyStateContainer}>
                    <Text style={styles.emptyStateTitle}>
                      {t("search.emptyState.title", "Comece a buscar")}
                    </Text>
                    <Text style={styles.emptyStateText}>
                      {t(
                        "search.emptyState.subtitle",
                        "Digite algo na barra de busca para encontrar membros, eventos, produtos e muito mais."
                      )}
                    </Text>
                  </View>
                )}
            </>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default SearchScreen;
