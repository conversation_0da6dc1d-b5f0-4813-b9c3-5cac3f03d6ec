# Firebase Configuration Files

This directory contains Firebase configuration files for the Club M app.

## Directory Structure

```
keys/
├── README.md                           # This file
├── google-services.example.json        # Example Android config
├── GoogleService-Info.example.plist    # Example iOS config
├── google-services.json               # Development Android config (not in repo)
├── GoogleService-Info.plist           # Development iOS config (not in repo)
└── production/                        # Production configs (not in repo)
    ├── README.md
    ├── google-services.json           # Production Android config
    ├── GoogleService-Info.plist       # Production iOS config
    └── clubm-970aa-firebase-adminsdk-fbsvc-be4c5592a9.json
```

## Configuration Types

### Development Configuration
- **Location**: `keys/` directory
- **Usage**: Local development and testing
- **Git Status**: Not tracked (in .gitignore)
- **Firebase Project**: Development project

### Production Configuration
- **Location**: `keys/production/` directory
- **Usage**: Production builds only
- **Git Status**: Not tracked (in .gitignore)
- **Firebase Project**: `clubm-970aa`

## Setup Instructions

### For Development
1. Copy the example files:
   ```bash
   cp keys/google-services.example.json keys/google-services.json
   cp keys/GoogleService-Info.example.plist keys/GoogleService-Info.plist
   ```
2. Replace the placeholder values with your development Firebase project configuration
3. These files will be used for local development and testing

### For Production Builds
1. Obtain production Firebase configuration files from the project administrator
2. Place them in the `keys/production/` directory
3. Run `bun run validate:production` to verify the configuration
4. Production builds will automatically use these files

## Security Notes

⚠️ **IMPORTANT**: 
- Never commit actual Firebase configuration files to the repository
- All `.json` and `.plist` files in this directory are in `.gitignore`
- Only the example files should be committed
- Production configurations contain sensitive data and must be handled securely

## Environment Detection

The app automatically selects the correct Firebase configuration:
- **Development**: Uses files in `keys/` directory
- **Production**: Uses files in `keys/production/` directory (when `NODE_ENV=production`)

This is configured in `app.config.js` and happens automatically during builds.

## Troubleshooting

### Missing Configuration Files
If you get errors about missing Firebase configuration:
1. Check that the required files exist in the correct directories
2. Run `bun run validate:production` to check production configuration
3. Verify file names match exactly (case-sensitive)

### Wrong Project Configuration
If the app connects to the wrong Firebase project:
1. Verify the `project_id` in the configuration files
2. Ensure you're using the correct environment (development vs production)
3. Check that the build process is using the intended configuration

### Permission Issues
If you get Firebase permission errors:
1. Verify the configuration files match your Firebase project settings
2. Check that the package name (Android) and bundle ID (iOS) are correct
3. Ensure the Firebase project has the correct app configurations
