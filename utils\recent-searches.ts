/**
 * Recent Searches Utility
 * Manages local storage of recent search terms and results
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { SearchResult } from '@/services/api/search/search.service';

const RECENT_SEARCHES_KEY = '@clubm_recent_searches';
const RECENT_RESULTS_KEY = '@clubm_recent_results';
const MAX_RECENT_SEARCHES = 10;
const MAX_RECENT_RESULTS = 20;

export interface RecentSearch {
  term: string;
  timestamp: number;
}

export interface RecentSearchResult extends SearchResult {
  searchTerm: string;
  timestamp: number;
}

export class RecentSearchesManager {
  /**
   * Add a search term to recent searches
   */
  static async addRecentSearch(term: string): Promise<void> {
    try {
      if (!term || term.trim().length < 2) {
        return;
      }

      const normalizedTerm = term.trim().toLowerCase();
      const recentSearches = await this.getRecentSearches();
      
      // Remove existing entry if it exists
      const filteredSearches = recentSearches.filter(
        search => search.term.toLowerCase() !== normalizedTerm
      );

      // Add new search at the beginning
      const newSearch: RecentSearch = {
        term: term.trim(),
        timestamp: Date.now()
      };

      const updatedSearches = [newSearch, ...filteredSearches].slice(0, MAX_RECENT_SEARCHES);
      
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error adding recent search:', error);
    }
  }

  /**
   * Get recent search terms
   */
  static async getRecentSearches(): Promise<RecentSearch[]> {
    try {
      const stored = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
      if (!stored) {
        return [];
      }

      const searches: RecentSearch[] = JSON.parse(stored);
      
      // Filter out searches older than 30 days
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      const validSearches = searches.filter(search => search.timestamp > thirtyDaysAgo);
      
      // If we filtered out old searches, update storage
      if (validSearches.length !== searches.length) {
        await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(validSearches));
      }

      return validSearches;
    } catch (error) {
      console.error('Error getting recent searches:', error);
      return [];
    }
  }

  /**
   * Clear all recent searches
   */
  static async clearRecentSearches(): Promise<void> {
    try {
      await AsyncStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('Error clearing recent searches:', error);
    }
  }

  /**
   * Remove a specific recent search
   */
  static async removeRecentSearch(term: string): Promise<void> {
    try {
      const recentSearches = await this.getRecentSearches();
      const filteredSearches = recentSearches.filter(
        search => search.term.toLowerCase() !== term.toLowerCase()
      );
      
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(filteredSearches));
    } catch (error) {
      console.error('Error removing recent search:', error);
    }
  }

  /**
   * Add search results to recent results
   */
  static async addRecentResults(searchTerm: string, results: SearchResult[]): Promise<void> {
    try {
      if (!results || results.length === 0) {
        return;
      }

      const recentResults = await this.getRecentResults();
      const timestamp = Date.now();
      
      // Convert results to recent results format
      const newResults: RecentSearchResult[] = results.map(result => ({
        ...result,
        searchTerm,
        timestamp
      }));

      // Remove existing results for the same search term
      const filteredResults = recentResults.filter(
        result => result.searchTerm.toLowerCase() !== searchTerm.toLowerCase()
      );

      // Add new results and limit total count
      const updatedResults = [...newResults, ...filteredResults].slice(0, MAX_RECENT_RESULTS);
      
      await AsyncStorage.setItem(RECENT_RESULTS_KEY, JSON.stringify(updatedResults));
    } catch (error) {
      console.error('Error adding recent results:', error);
    }
  }

  /**
   * Get recent search results
   */
  static async getRecentResults(): Promise<RecentSearchResult[]> {
    try {
      const stored = await AsyncStorage.getItem(RECENT_RESULTS_KEY);
      if (!stored) {
        return [];
      }

      const results: RecentSearchResult[] = JSON.parse(stored);
      
      // Filter out results older than 7 days
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const validResults = results.filter(result => result.timestamp > sevenDaysAgo);
      
      // If we filtered out old results, update storage
      if (validResults.length !== results.length) {
        await AsyncStorage.setItem(RECENT_RESULTS_KEY, JSON.stringify(validResults));
      }

      return validResults;
    } catch (error) {
      console.error('Error getting recent results:', error);
      return [];
    }
  }

  /**
   * Clear all recent results
   */
  static async clearRecentResults(): Promise<void> {
    try {
      await AsyncStorage.removeItem(RECENT_RESULTS_KEY);
    } catch (error) {
      console.error('Error clearing recent results:', error);
    }
  }

  /**
   * Remove a specific recent result
   */
  static async removeRecentResult(resultId: string): Promise<void> {
    try {
      const recentResults = await this.getRecentResults();
      const filteredResults = recentResults.filter(result => result.id !== resultId);
      
      await AsyncStorage.setItem(RECENT_RESULTS_KEY, JSON.stringify(filteredResults));
    } catch (error) {
      console.error('Error removing recent result:', error);
    }
  }

  /**
   * Get recent results grouped by type
   */
  static async getRecentResultsByType(): Promise<Record<string, RecentSearchResult[]>> {
    try {
      const recentResults = await this.getRecentResults();
      
      return recentResults.reduce((acc, result) => {
        if (!acc[result.type]) {
          acc[result.type] = [];
        }
        acc[result.type].push(result);
        return acc;
      }, {} as Record<string, RecentSearchResult[]>);
    } catch (error) {
      console.error('Error getting recent results by type:', error);
      return {};
    }
  }

  /**
   * Clear all recent data (searches and results)
   */
  static async clearAllRecentData(): Promise<void> {
    try {
      await Promise.all([
        this.clearRecentSearches(),
        this.clearRecentResults()
      ]);
    } catch (error) {
      console.error('Error clearing all recent data:', error);
    }
  }
}
