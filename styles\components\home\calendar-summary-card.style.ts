import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)"
  },
  content: {
    flex: 1
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.white,
    fontFamily: stylesConstants.fontFamily.primary
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(242, 153, 74, 0.2)",
    justifyContent: "center",
    alignItems: "center"
  },
  subtitle: {
    fontSize: 14,
    color: stylesConstants.colors.gray25,
    fontFamily: stylesConstants.fontFamily.primary,
    lineHeight: 20
  },
  count: {
    fontWeight: "600",
    color: stylesConstants.colors.white
  }
});

export default styles;
