ninja: Entering directory `C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o
[3/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o
[5/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o
[6/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[7/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[8/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[10/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[11/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o
[12/62] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[13/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o
[14/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o
[15/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o
[16/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[17/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[18/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/EventEmitters.cpp.o
[19/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[20/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[21/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/States.cpp.o
[22/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/81652923ecfc9bdd5f0e3b73b09a03ba/components/safeareacontext/Props.cpp.o
[23/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[24/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[25/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[26/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewState.cpp.o
[27/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[28/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSScreenShadowNode.cpp.o
[29/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/028e3213fe8881349ad29c9b097e59d4/safeareacontext/ShadowNodes.cpp.o
[30/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/ComponentDescriptors.cpp.o
[31/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/16d00a0261cac9ff43ce46900d57e7db/jni/safeareacontext-generated.cpp.o
[32/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e44745060bc3d6dfe473b785e4d9ac/RNCSafeAreaViewShadowNode.cpp.o
[33/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[34/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b3e4510636d7fc7d80894c30ddaeab1d/safeareacontextJSI-generated.cpp.o
[35/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/beec13cd9beffcff163006cda4d1844b/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[36/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[37/62] Linking CXX shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
[38/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b1e29d414343da1e7d3f8ac8e36d3da3/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[39/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/31d41a69d6af3f8a771be753aad03dfb/RNSScreenStackHeaderConfigShadowNode.cpp.o
[40/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[41/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/bdf5ea98b8ef82e55b7165b42279078c/react/renderer/components/rnscreens/States.cpp.o
[42/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/rnscreensJSI-generated.cpp.o
[43/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/EventEmitters.cpp.o
[44/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ea49dc9aacd966da88f6745ee0bf23b3/renderer/components/rnscreens/RNSScreenState.cpp.o
[45/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[46/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[47/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[48/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e819f92720ac3814b602db3a20c332a5/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[49/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f6d8a8a947747c6af374051b5140e013/renderer/components/rnscreens/ShadowNodes.cpp.o
[50/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9d75e665e83f82cca7c094b1da6862b9/jni/react/renderer/components/rnscreens/Props.cpp.o
[51/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2a355fff8e3d0c5ecd97bae98b9b323b/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[52/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c3b00cb14507cd5ef58b02bd72d3c86f/components/rnscreens/ComponentDescriptors.cpp.o
[53/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[54/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/40587413c60aab511d08e5a093fd1bb2/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 | void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
      |                                            ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                          ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                           ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   source.setProperty(runtime, "width", $event.source.width);
      |                                        ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |   source.setProperty(runtime, "height", $event.source.height);
      |                                         ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |   source.setProperty(runtime, "uri", $event.source.uri);
      |                                      ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |   $payload.setProperty(runtime, "source", source);
      |   ^
C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   41 |     return $payload;
      |            ^
9 warnings generated.
[55/62] Linking CXX shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnscreens.so
[56/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1332f687fcd373fa10d1d54ed92a6c7a/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[57/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[58/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88e5b67d0f50da1584fc050fbef06bb5/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[59/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d8e3648d0d1b9e8d231233f37e48042e/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[60/62] Linking CXX shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libreact_codegen_rnsvg.so
[61/62] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[62/62] Linking CXX shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libappmodules.so
