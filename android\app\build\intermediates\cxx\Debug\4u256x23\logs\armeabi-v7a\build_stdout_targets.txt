ninja: Entering directory `C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\.cxx\Debug\4u256x23\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/.cxx/Debug/4u256x23/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[3/3] Linking CXX shared library C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\build\intermediates\cxx\Debug\4u256x23\obj\armeabi-v7a\libappmodules.so
