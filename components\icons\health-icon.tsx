import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface HealthIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const HealthIcon: React.FC<HealthIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg {...props} viewBox="0 0 21 20" fill="none">
      <Path
        d="M12.583 3.83333C12.583 3.36662 12.583 3.13327 12.4922 2.95501C12.4123 2.79821 12.2848 2.67072 12.128 2.59083C11.9497 2.5 11.7164 2.5 11.2497 2.5H8.91634C8.44963 2.5 8.21628 2.5 8.03802 2.59083C7.88121 2.67072 7.75373 2.79821 7.67384 2.95501C7.58301 3.13327 7.58301 3.36662 7.58301 3.83333V6.16667C7.58301 6.63338 7.58301 6.86673 7.49218 7.04499C7.41229 7.20179 7.2848 7.32928 7.128 7.40917C6.94974 7.5 6.71638 7.5 6.24967 7.5H3.91634C3.44963 7.5 3.21628 7.5 3.03802 7.59083C2.88121 7.67072 2.75373 7.79821 2.67384 7.95501C2.58301 8.13327 2.58301 8.36662 2.58301 8.83333V11.1667C2.58301 11.6334 2.58301 11.8667 2.67384 12.045C2.75373 12.2018 2.88121 12.3293 3.03802 12.4092C3.21628 12.5 3.44963 12.5 3.91634 12.5H6.24967C6.71638 12.5 6.94974 12.5 7.128 12.5908C7.2848 12.6707 7.41229 12.7982 7.49218 12.955C7.58301 13.1333 7.58301 13.3666 7.58301 13.8333V16.1667C7.58301 16.6334 7.58301 16.8667 7.67384 17.045C7.75373 17.2018 7.88121 17.3293 8.03802 17.4092C8.21628 17.5 8.44963 17.5 8.91634 17.5H11.2497C11.7164 17.5 11.9497 17.5 12.128 17.4092C12.2848 17.3293 12.4123 17.2018 12.4922 17.045C12.583 16.8667 12.583 16.6334 12.583 16.1667V13.8333C12.583 13.3666 12.583 13.1333 12.6738 12.955C12.7537 12.7982 12.8812 12.6707 13.038 12.5908C13.2163 12.5 13.4496 12.5 13.9163 12.5H16.2497C16.7164 12.5 16.9497 12.5 17.128 12.4092C17.2848 12.3293 17.4123 12.2018 17.4922 12.045C17.583 11.8667 17.583 11.6334 17.583 11.1667V8.83333C17.583 8.36662 17.583 8.13327 17.4922 7.95501C17.4123 7.79821 17.2848 7.67072 17.128 7.59083C16.9497 7.5 16.7164 7.5 16.2497 7.5L13.9163 7.5C13.4496 7.5 13.2163 7.5 13.038 7.40917C12.8812 7.32928 12.7537 7.20179 12.6738 7.04499C12.583 6.86673 12.583 6.63338 12.583 6.16667V3.83333Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default HealthIcon;
