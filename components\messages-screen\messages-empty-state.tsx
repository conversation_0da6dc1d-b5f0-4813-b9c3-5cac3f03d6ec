import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import MessagePlusIcon from "@/components/icons/message-plus-icon";
import styles from "@/styles/components/messages-screen/messages-empty-state.style";

export interface MessagesEmptyStateProps {
  onStartConversation?: () => void;
}

const MessagesEmptyState: React.FC<MessagesEmptyStateProps> = ({
  onStartConversation
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <MessagePlusIcon width={24} height={24} replaceColor="#344053" />
      </View>
      <Text style={styles.title}>
        Você ainda não possui mensagens
      </Text>
      <Text style={styles.description}>
        Que tal dar uma olhada nas novidades e interagir com os membros do aplicativo?
      </Text>
      <TouchableOpacity
        style={styles.button}
        onPress={onStartConversation}
        activeOpacity={0.8}
      >
        <Text style={styles.buttonText}>
          Iniciar uma conversa
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default MessagesEmptyState;
