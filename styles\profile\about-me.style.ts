import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  mainContainer: {
    backgroundColor: stylesConstants.colors.mainBackground,
    width: "100%",
    height: "100%",
    borderTopRightRadius: 24,
    borderTopLeftRadius: 24,
    marginTop: -22,
    zIndex: 2
  },
  section: {
    marginBottom: 12
  },
  sectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 16,  
    marginBottom: 8
  },
  profileRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
    gap: 8
  },

  profileCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 8
  },

  profileItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingVertical: 8,
    gap: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)"
  },

  label: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    minWidth: 120
  },

  value: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    flex: 1
  },
  contactsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    gap: 16
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    flex: 1
  },
  contactInfo: {
    gap: 4
  },
  contactLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  contactValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 20
  },
  socialGrid: {
    marginTop: 16,
    gap: 12
  },
  socialRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12
  },
  socialItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    flex: 1
  },
  socialInfo: {
    gap: 4,
    marginLeft: 4
  },
  socialIconContainer: {
    backgroundColor: "#999CA2",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#D5D5D5",
    width: 45,
    height: 45,
    justifyContent: "center",
    alignItems: "center"
  },
  socialLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    textAlign: "left"
  },
  socialLink: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "bold",
    lineHeight: 20,
    textAlign: "left"
  },
  opportunitiesList: {
    gap: 24
  },
  objectivesContainer: {
    gap: 24
  }
});

export default styles;
