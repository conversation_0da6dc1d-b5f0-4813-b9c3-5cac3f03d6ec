import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "@/styles/components/opportunities/opportunities-empty-state.style";

export interface OpportunitiesEmptyStateProps {
  activeTab: "all" | "active" | "inactive";
  onCreateOpportunity?: () => void;
}

const OpportunitiesEmptyState: React.FC<OpportunitiesEmptyStateProps> = ({
  activeTab,
  onCreateOpportunity
}) => {
  const {t} = useTranslation();

  const getEmptyStateContent = () => {
    switch (activeTab) {
      case "active":
        return {
          title: t("myOpportunities.emptyActive", "Você não possui oportunidades ativas"),
          description: t("myOpportunities.emptyActiveDescription", "Ative suas oportunidades para começar a receber propostas.")
        };
      case "inactive":
        return {
          title: t("myOpportunities.emptyInactive", "Você não possui oportunidades inativas"),
          description: t("myOpportunities.emptyInactiveDescription", "Oportunidades inativas não aparecem para outros usuários.")
        };
      default:
        return {
          title: t("myOpportunities.emptyAll", "Você ainda não criou nenhuma oportunidade"),
          description: t("myOpportunities.emptyDescription", "Crie sua primeira oportunidade e comece a expandir sua rede de negócios.")
        };
    }
  };

  const content = getEmptyStateContent();

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer} />
      <Text style={styles.title}>
        {content.title}
      </Text>
      <Text style={styles.description}>
        {content.description}
      </Text>
      {activeTab === "all" && onCreateOpportunity && (
        <TouchableOpacity
          style={styles.button}
          onPress={onCreateOpportunity}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>
            {t("myOpportunities.createFirst", "Criar minha primeira oportunidade")}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default OpportunitiesEmptyState;
