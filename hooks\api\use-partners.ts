import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery
} from "@tanstack/react-query";
import {
  Partner,
  PartnerListParams,
  PartnerListResponse
} from "@/models/api/partners.models";
import {
  Benefit,
  BenefitListParams,
  BenefitListResponse,
  BenefitInteractionRequest
} from "@/models/api/benefits.models";
import {useMemo} from "react";

// Mock data temporário enquanto a API não está disponível
const mockPartners: Partner[] = [
  {
    id: 1,
    name: "Club 7",
    description:
      "Parceiro premium com diversos benefícios exclusivos para membros do Club M.",
    category: "Tecnologia",
    logoUrl: "https://via.placeholder.com/100x100/4A90E2/FFFFFF?text=C7",
    status: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: "TechStore",
    description:
      "Loja especializada em produtos de tecnologia com descontos especiais.",
    category: "Varejo",
    logoUrl: "https://via.placeholder.com/100x100/E74C3C/FFFFFF?text=TS",
    status: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const mockBenefits: Benefit[] = [
  {
    id: 1,
    partnerId: 1,
    title: "20% OFF em desenvolvimento de sites",
    description:
      "Desconto especial para desenvolvimento de sites e aplicações web.",
    terms:
      "Válido apenas para novos projetos. Não cumulativo com outras promoções.",
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 dias
    imageUrl:
      "https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=20%25+OFF",
    status: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    partnerId: 1,
    title: "Consultoria gratuita",
    description:
      "Uma hora de consultoria gratuita para planejamento de projetos digitais.",
    terms: "Agendamento prévio necessário. Válido por 60 dias.",
    validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 dias
    imageUrl:
      "https://via.placeholder.com/300x200/2ECC71/FFFFFF?text=Consultoria",
    status: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

/**
 * Hook for fetching a list of partners with pagination and search
 */
export const usePartners = (params: PartnerListParams) => {
  return useQuery<PartnerListResponse>({
    queryKey: ["partners", params],
    queryFn: async () => {
      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Filtrar por busca se fornecida
      let filteredPartners = mockPartners;
      if (params.search) {
        filteredPartners = mockPartners.filter(
          (partner) =>
            partner.name.toLowerCase().includes(params.search!.toLowerCase()) ||
            partner.description
              .toLowerCase()
              .includes(params.search!.toLowerCase())
        );
      }

      return {
        data: filteredPartners,
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        totalItems: filteredPartners.length,
        hasNextPage: false,
        hasPreviousPage: false,
        pageCount: 1
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });
};

/**
 * Hook for fetching partners with infinite scroll
 */
export const usePartnersInfinite = (
  params: Omit<PartnerListParams, "page">
) => {
  return useInfiniteQuery({
    queryKey: ["partners", "infinite", params],
    queryFn: ({pageParam = 1}) =>
      PartnersService.getPartners({...params, page: pageParam}),
    getNextPageParam: (lastPage) => {
      return lastPage.hasNextPage ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching a single partner by ID
 */
export const usePartner = (partnerId: string | number) => {
  return useQuery<Partner>({
    queryKey: ["partner", partnerId],
    queryFn: async () => {
      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 300));

      const partner = mockPartners.find(
        (p) => p.id.toString() === partnerId.toString()
      );
      if (!partner) {
        throw new Error(`Parceiro com ID ${partnerId} não encontrado`);
      }
      return partner;
    },
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching benefits for a specific partner
 */
export const usePartnerBenefits = (
  partnerId: string | number,
  params: BenefitListParams
) => {
  return useQuery<BenefitListResponse>({
    queryKey: ["partner", partnerId, "benefits", params],
    queryFn: async () => {
      // Simular delay da API
      await new Promise((resolve) => setTimeout(resolve, 400));

      // Filtrar benefícios por parceiro
      const partnerBenefits = mockBenefits.filter(
        (benefit) => benefit.partnerId.toString() === partnerId.toString()
      );

      return {
        data: partnerBenefits,
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        totalItems: partnerBenefits.length,
        hasNextPage: false,
        hasPreviousPage: false,
        pageCount: 1
      };
    },
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching partner benefits with infinite scroll
 */
export const usePartnerBenefitsInfinite = (
  partnerId: string | number,
  params: Omit<BenefitListParams, "page">
) => {
  return useInfiniteQuery({
    queryKey: ["partner", partnerId, "benefits", "infinite", params],
    queryFn: ({pageParam = 1}) =>
      BenefitsService.getPartnerBenefits(partnerId, {
        ...params,
        page: pageParam
      }),
    getNextPageParam: (lastPage) => {
      return lastPage.hasNextPage ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
    enabled: !!partnerId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for fetching a single benefit by ID
 */
export const useBenefit = (partnerId: string | number, benefitId: number) => {
  return useQuery<Benefit>({
    queryKey: ["partner", partnerId, "benefit", benefitId],
    queryFn: () => BenefitsService.getBenefit(partnerId, benefitId),
    enabled: !!partnerId && !!benefitId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  });
};

/**
 * Hook for benefit interactions (view, click, redeem)
 */
export const useBenefitInteraction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: BenefitInteractionRequest) =>
      BenefitsService.interactWithBenefit(
        request.partnerId,
        request.benefitId,
        request.interactionType
      ),
    onSuccess: (_, variables) => {
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["partner", variables.partnerId, "benefits"]
      });
      queryClient.invalidateQueries({
        queryKey: [
          "partner",
          variables.partnerId,
          "benefit",
          variables.benefitId
        ]
      });
    }
  });
};

/**
 * Hook for creating a new partner
 */
export const useCreatePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (partner: Omit<Partner, "id" | "createdAt" | "updatedAt">) =>
      PartnersService.createPartner(partner),
    onSuccess: () => {
      // Invalidate partners list to refresh data
      queryClient.invalidateQueries({queryKey: ["partners"]});
    }
  });
};

/**
 * Hook for updating a partner
 */
export const useUpdatePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partnerId,
      partner
    }: {
      partnerId: string | number;
      partner: Partial<Partner>;
    }) => PartnersService.updatePartner(partnerId, partner),
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({queryKey: ["partners"]});
      queryClient.invalidateQueries({
        queryKey: ["partner", variables.partnerId]
      });
    }
  });
};

/**
 * Hook for deleting a partner
 */
export const useDeletePartner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (partnerId: string | number) =>
      PartnersService.deletePartner(partnerId),
    onSuccess: (_, partnerId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({queryKey: ["partner", partnerId]});
      queryClient.invalidateQueries({queryKey: ["partners"]});
    }
  });
};

/**
 * Utility hook to get flattened partners from infinite query
 */
export const useFlattenedPartners = (
  infiniteQuery: ReturnType<typeof usePartnersInfinite>
) => {
  return useMemo(() => {
    return infiniteQuery.data?.pages.flatMap((page) => page.data) ?? [];
  }, [infiniteQuery.data]);
};

/**
 * Utility hook to get flattened benefits from infinite query
 */
export const useFlattenedBenefits = (
  infiniteQuery: ReturnType<typeof usePartnerBenefitsInfinite>
) => {
  return useMemo(() => {
    return infiniteQuery.data?.pages.flatMap((page) => page.data) ?? [];
  }, [infiniteQuery.data]);
};
