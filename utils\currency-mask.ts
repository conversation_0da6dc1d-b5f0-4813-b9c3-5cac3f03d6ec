import * as React from "react";

/**
 * Utilitário para formatação de valores monetários em pt-BR
 */

/**
 * Formata um valor numérico para o formato monetário brasileiro
 * @param value - Valor em centavos (número inteiro)
 * @returns String formatada no padrão pt-BR (ex: "1.000,00")
 */
export const formatCurrency = (value: number): string => {
  const reais = value / 100;

  return new Intl.NumberFormat("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(reais);
};

/**
 * Remove a formatação de uma string monetária e retorna o valor em centavos
 * @param formattedValue - String formatada (ex: "1.000,00")
 * @returns Valor em centavos (número inteiro)
 */
export const parseCurrency = (formattedValue: string): number => {
  const cleanValue = formattedValue.replace(/[^\d,]/g, "");

  if (!cleanValue.includes(",")) {
    return parseInt(cleanValue || "0", 10);
  }

  const [reaisPart, centavosPart = "00"] = cleanValue.split(",");

  const reais = parseInt(reaisPart || "0", 10);
  const centavos = parseInt(centavosPart.padEnd(2, "0").slice(0, 2), 10);

  return reais * 100 + centavos;
};

/**
 * Aplica máscara monetária conforme o usuário digita
 * @param input - Texto digitado pelo usuário
 * @param previousValue - Valor anterior (para detectar se está apagando)
 * @returns String formatada para exibição
 */
export const applyCurrencyMask = (
  input: string,
  previousValue: string = ""
): string => {
  const numbersOnly = input.replace(/\D/g, "");

  if (!numbersOnly) {
    return "";
  }

  const valueInCents = parseInt(numbersOnly, 10);

  return formatCurrency(valueInCents);
};

/**
 * Hook personalizado para gerenciar estado de input monetário
 * @param initialValue - Valor inicial em centavos
 * @returns Objeto com valor formatado, função de mudança e valor em centavos
 */
export const useCurrencyInput = (initialValue: number = 0) => {
  const [displayValue, setDisplayValue] = React.useState(
    initialValue > 0 ? formatCurrency(initialValue) : ""
  );

  const handleChange = (newValue: string) => {
    const formatted = applyCurrencyMask(newValue, displayValue);
    setDisplayValue(formatted);
  };

  const valueInCents = parseCurrency(displayValue);

  return {
    displayValue,
    handleChange,
    valueInCents,
    setValue: setDisplayValue
  };
};
