import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "@/components/screen";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "@/components/full-size-button";
import InputField from "@/components/input-field";
import PasswordIcon from "@/components/icons/password-icon";
import styles from "@/styles/auth/account-activation/activation-new-password.style";
import useAccountActivation from "@/hooks/use-account-activation";
import {useLocalSearchParams} from "expo-router";

interface ActivationNewPasswordParams {
  document?: string;
  code?: string;
}

const ActivationNewPassword: React.FC = () => {
  const {t} = useTranslation();
  const params = useLocalSearchParams() as ActivationNewPasswordParams;
  const activationAction = useAccountActivation();

  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon color={errorColor} />,
    []
  );

  const handleInputChange = useCallback(
    (field: string) => (value: string) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value
      }));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({
          ...prev,
          [field]: ""
        }));
      }
    },
    [errors]
  );

  const handleSubmit = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.newPassword) {
      newErrors.newPassword = t(
        "accountActivationNewPassword.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = t(
        "accountActivationNewPassword.errors.minLength",
        "Mínimo 8 caracteres"
      );
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "accountActivationNewPassword.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "accountActivationNewPassword.errors.mismatch",
        "Senhas não coincidem"
      );
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      // Call activation API
      activationAction.activateAccount({
        code: params.code || "",
        password: formData.newPassword,
        document: params.document || ""
      });
    }
  }, [formData, t, params, activationAction]);

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <View style={styles.backButton}>
              <BackButton />
            </View>
            <View style={styles.headerContainer}>
              <Text style={styles.title}>
                {t("accountActivationNewPassword.title")}
              </Text>
              <Text style={styles.description}>
                {t("accountActivationNewPassword.description")}
              </Text>
            </View>
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <InputField
                  label={t("accountActivationNewPassword.newPassword")}
                  isPassword={true}
                  value={formData.newPassword}
                  icon={passwordIcon}
                  error={
                    errors.newPassword || activationAction.errors["password"]
                  }
                  placeholder={t(
                    "accountActivationNewPassword.newPasswordPlaceholder"
                  )}
                  onChangeText={handleInputChange("newPassword")}
                  height={56}
                />
                <InputField
                  label={t("accountActivationNewPassword.confirmPassword")}
                  isPassword={true}
                  value={formData.confirmPassword}
                  icon={passwordIcon}
                  error={errors.confirmPassword}
                  placeholder={t(
                    "accountActivationNewPassword.confirmPasswordPlaceholder"
                  )}
                  onChangeText={handleInputChange("confirmPassword")}
                  height={56}
                />
              </View>
              <Text style={styles.description}>
                {t("accountActivationNewPassword.passwordRequirement")}
              </Text>
            </View>
          </View>
          <View style={styles.buttonContainer}>
            <FullSizeButton
              text="accountActivationNewPassword.activateButton"
              onPress={handleSubmit}
            />
          </View>
        </View>
      </Screen>
    </>
  );
};

export default ActivationNewPassword;
