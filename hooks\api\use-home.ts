/**
 * React Query hooks for home feed management
 */

import {
  useQuery,
  useInfiniteQuery,
  UseQueryOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult
} from "@tanstack/react-query";
import {
  HomeService,
  HomeFeedResponse,
  HomeFeedParams,
  HomeSearchResponse,
  HomeSearchParams
} from "@/services/api/home/<USER>";
import {BaseApiError} from "@/services/api/base/api-errors";

// Query keys for home
export const homeKeys = {
  all: ["home"] as const,
  feed: () => [...homeKeys.all, "feed"] as const,
  feedList: (params: HomeFeedParams) => [...homeKeys.feed(), params] as const,
  search: () => [...homeKeys.all, "search"] as const,
  searchList: (params: HomeSearchParams) =>
    [...homeKeys.search(), params] as const
};

/**
 * Hook to fetch home feed with pagination
 */
export const useHomeFeed = (
  params?: HomeFeedParams,
  options?: UseQueryOptions<HomeFeedResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: homeKeys.feedList(params || {}),
    queryFn: () => HomeService.getHomeFeed(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden)
      if (error?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch home feed with infinite scroll
 */
export const useInfiniteHomeFeed = (
  params?: Omit<HomeFeedParams, "page">,
  options?: UseInfiniteQueryOptions<HomeFeedResponse, BaseApiError>
): UseInfiniteQueryResult<HomeFeedResponse, BaseApiError> => {
  return useInfiniteQuery({
    queryKey: homeKeys.feedList(params || {}),
    queryFn: ({pageParam = 1}) =>
      HomeService.getHomeFeed({
        ...params,
        page: pageParam as number
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.currentPage + 1 : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden)
      if (error?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to search home content
 */
export const useHomeSearch = (
  params?: HomeSearchParams,
  options?: UseQueryOptions<HomeSearchResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: homeKeys.searchList(params || {}),
    queryFn: () => HomeService.searchHome(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    enabled: !!params?.search && params.search.length > 0, // Only search if there's a search term
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden)
      if (error?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to search home content with infinite scroll
 */
export const useInfiniteHomeSearch = (
  params?: Omit<HomeSearchParams, "page">,
  options?: UseInfiniteQueryOptions<HomeSearchResponse, BaseApiError>
): UseInfiniteQueryResult<HomeSearchResponse, BaseApiError> => {
  return useInfiniteQuery({
    queryKey: homeKeys.searchList(params || {}),
    queryFn: ({pageParam = 1}) =>
      HomeService.searchHome({
        ...params,
        page: pageParam as number
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.currentPage + 1 : undefined,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    enabled: !!params?.search && params.search.length > 0, // Only search if there's a search term
    retry: (failureCount, error) => {
      // Não tentar novamente se for 403 (Forbidden)
      if (error?.status === 403) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook for home utility functions
 */
export const useHomeUtils = () => {
  return {
    formatFeedItemDate: HomeService.formatFeedItemDate,
    getFeedItemTypeName: HomeService.getFeedItemTypeName,
    getSearchResultTypeName: HomeService.getSearchResultTypeName
  };
};
