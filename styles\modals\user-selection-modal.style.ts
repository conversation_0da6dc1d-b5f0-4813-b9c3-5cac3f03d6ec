import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.primaryBackground
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.border,
    backgroundColor: stylesConstants.colors.primaryBackground
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    justifyContent: "center",
    alignItems: "center"
  },
  closeButtonText: {
    fontSize: 20,
    fontWeight: "600",
    color: stylesConstants.colors.textSecondary
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16
  },
  selectedCount: {
    backgroundColor: stylesConstants.colors.primary + "20",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16
  },
  selectedCountText: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  errorText: {
    fontSize: 16,
    color: stylesConstants.colors.error500,
    fontFamily: stylesConstants.fonts.openSans,
    textAlign: "center"
  },
  usersList: {
    flex: 1
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderWidth: 1,
    borderColor: "transparent"
  },
  userItemSelected: {
    backgroundColor: stylesConstants.colors.primary + "10",
    borderColor: stylesConstants.colors.primary
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.highlightBackground,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  userDetails: {
    flex: 1
  },
  userName: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 2
  },
  userEmail: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    marginBottom: 2
  },
  userCompany: {
    fontSize: 12,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.border,
    backgroundColor: stylesConstants.colors.primaryBackground
  }
});
