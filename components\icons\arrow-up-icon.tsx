import React from "react";
import Svg, {Path} from "react-native-svg";

interface ArrowUpIconProps {
  width?: number;
  height?: number;
  color?: string;
}

const ArrowUpIcon: React.FC<ArrowUpIconProps> = ({
  width = 20,
  height = 20,
  color = "#FCFCFD"
}) => {
  return (
    <Svg width={21} height={20} viewBox="0 0 21 20" fill="none">
      <Path
        d="M18 16.6668H15.1667C12.3664 16.6668 10.9663 16.6668 9.89671 16.1219C8.9559 15.6425 8.191 14.8776 7.71163 13.9368C7.16667 12.8672 7.16667 11.4671 7.16667 8.66683V3.3335M7.16667 3.3335L11.3333 7.50016M7.16667 3.3335L3 7.50016"
        stroke="#FCFCFD"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ArrowUpIcon;
