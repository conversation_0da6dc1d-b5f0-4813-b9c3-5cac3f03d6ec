import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface PieChartIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const PieChartIcon: React.FC<PieChartIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#F2F4F7",
    [props.replaceColor]
  );

  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M10 1.667v8.333l6.667 3.333A8.333 8.333 0 1 0 10 1.667Z"
      />
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="M18.333 10A8.333 8.333 0 0 0 10 1.667v8.333h8.333Z"
      />
    </Svg>
  );
};

export default PieChartIcon;
