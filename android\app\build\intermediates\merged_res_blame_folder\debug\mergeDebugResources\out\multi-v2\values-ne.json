{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,15488", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,15563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5973", "endColumns": "163", "endOffsets": "6132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "50,94,95,97,111,163,164", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4850,10219,10287,10427,11479,15806,15876", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4922,10282,10361,10490,11541,15871,15940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3388,3477,3565,3647,3742,4551,4653,4763,7567,7627,7693,10153,10366,10495,10600,10664,10736,10794,10868,10930,10984,11097,11157,11218,11277,11355,11546,11627,11709,11809,11894,11979,12115,12196,12279,12410,12493,12579,12641,12695,12761,12838,12917,12988,13071,13140,13216,13297,13365,13469,13560,13638,13731,13828,13902,13981,14079,14139,14227,14293,14381,14469,14531,14599,14662,14728,14833,14939,15034,15139,15205,15263,15568,15657,15733", "endLines": "5,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3472,3560,3642,3737,3826,4648,4758,4845,7622,7688,7784,10214,10422,10595,10659,10731,10789,10863,10925,10979,11092,11152,11213,11272,11350,11474,11622,11704,11804,11889,11974,12110,12191,12274,12405,12488,12574,12636,12690,12756,12833,12912,12983,13066,13135,13211,13292,13360,13464,13555,13633,13726,13823,13897,13976,14074,14134,14222,14288,14376,14464,14526,14594,14657,14723,14828,14934,15029,15134,15200,15258,15342,15652,15728,15801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,364,482,572,728,855,974,1096,1224,1356,1501,1633,1781,1877,2049,2192,2332,2471,2602,2743,2863,3012,3117,3251,3372,3522", "endColumns": "168,139,117,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "219,359,477,567,723,850,969,1091,1219,1351,1496,1628,1776,1872,2044,2187,2327,2466,2597,2738,2858,3007,3112,3246,3367,3517,3636"}, "to": {"startLines": "33,34,69,71,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,158,166,167,168,169,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3079,3248,7258,7477,7789,7945,8406,8525,8647,8775,8907,9052,9184,9332,9428,9600,9743,9883,10022,15347,16046,16166,16315,16420,16554,16675,16825", "endColumns": "168,139,117,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "3243,3383,7371,7562,7940,8067,8520,8642,8770,8902,9047,9179,9327,9423,9595,9738,9878,10017,10148,15483,16161,16310,16415,16549,16670,16820,16939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,304,458,588,701,868,1000,1106,1207,1383,1493,1653,1782,1926,2074,2136,2204", "endColumns": "110,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "303,457,587,700,867,999,1105,1206,1382,1492,1652,1781,1925,2073,2135,2203,2291"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4927,5042,5200,5334,5451,5622,5758,5868,6137,6317,6431,6595,6728,6876,7028,7094,7166", "endColumns": "114,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "5037,5195,5329,5446,5617,5753,5863,5968,6312,6426,6590,6723,6871,7023,7089,7161,7253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "70,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "7376,8072,8184,8298", "endColumns": "100,111,113,107", "endOffsets": "7472,8179,8293,8401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "40,41,42,43,44,45,46,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3831,3934,4037,4139,4245,4343,4443,15945", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3929,4032,4134,4240,4338,4438,4546,16041"}}]}]}