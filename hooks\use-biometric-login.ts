import {useCallback, useState} from "react";
import {useTranslation} from "react-i18next";
import * as LocalAuthentication from "expo-local-authentication";
import * as SecureStore from "expo-secure-store";
import {useRouter} from "expo-router";
import {useSession} from "@/contexts/session.context";
import {useLoading} from "@/contexts/loading-context";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";
import BiometricService from "@/services/biometric.service";

const STORAGE_KEY_NAME = "session_data";

export interface BiometricLoginState {
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook para login com biometria
 */
export const useBiometricLogin = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const session = useSession();
  const {setCurrentLoading} = useLoading();
  const errorActions = useErrorMessage();

  const [state, setState] = useState<BiometricLoginState>({
    isLoading: false,
    error: null
  });

  const authenticateWithBiometrics = useCallback(async () => {
    setState({isLoading: true, error: null});
    setCurrentLoading?.(true);

    try {
      // Verificar se há dados salvos para login biométrico
      const savedSessionData = await SecureStore.getItemAsync(STORAGE_KEY_NAME);

      if (!savedSessionData) {
        throw new Error(
          t(
            "login.biometricNoDataSaved",
            "Nenhum dado de login salvo para biometria"
          )
        );
      }

      // Configurar opções de autenticação biométrica
      const biometricConfig = BiometricService.createBiometricConfig(t);

      // Verificar se o dispositivo suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware) {
        throw new Error(
          t(
            "login.biometricNotSupported",
            "Dispositivo não suporta autenticação biométrica"
          )
        );
      }

      if (!isEnrolled) {
        throw new Error(
          t(
            "login.biometricNotEnrolled",
            "Nenhuma biometria cadastrada no dispositivo"
          )
        );
      }

      // Realizar autenticação biométrica
      const authResult = await LocalAuthentication.authenticateAsync(
        biometricConfig
      );

      if (!authResult.success) {
        if (authResult.error === "user_cancel") {
          throw new Error(
            t("login.biometricCancelled", "Autenticação cancelada pelo usuário")
          );
        } else if (authResult.error === "user_fallback") {
          throw new Error(
            t("login.biometricUserFallback", "Usuário optou por usar senha")
          );
        } else {
          throw new Error(
            t("login.biometricFailed", "Falha na autenticação biométrica")
          );
        }
      }

      // Se a autenticação foi bem-sucedida, restaurar sessão
      const sessionData = JSON.parse(savedSessionData);
      session.setLoginData?.(sessionData);
      router.replace("/(tabs)/home");

      setState({isLoading: false, error: null});
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t("login.biometricGenericError", "Erro na autenticação biométrica");

      setState({isLoading: false, error: errorMessage});

      errorActions.emitError({
        title: t("login.biometricErrorTitle", "Erro na Biometria"),
        description: errorMessage,
        errorType: ErrorType.Error
      });
    } finally {
      setCurrentLoading?.(false);
    }
  }, [t, router, session, setCurrentLoading, errorActions]);

  const clearError = useCallback(() => {
    setState((prev) => ({...prev, error: null}));
  }, []);

  return {
    ...state,
    authenticateWithBiometrics,
    clearError
  };
};

export default useBiometricLogin;
