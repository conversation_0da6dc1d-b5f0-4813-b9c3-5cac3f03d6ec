import React from "react";
import {View, Text, TouchableOpacity, ActivityIndicator} from "react-native";
import {useTranslation} from "react-i18next";
import BiometricIcon from "@/components/icons/biometric-icon";
import SmallLogo from "@/components/logos/small-logo";
import styles from "@/styles/components/startup-biometric-prompt.style";

export interface StartupBiometricPromptProps {
  isAuthenticating: boolean;
  onBiometricPress: () => void;
  onSkipPress: () => void;
}

const StartupBiometricPrompt: React.FC<StartupBiometricPromptProps> = ({
  isAuthenticating,
  onBiometricPress,
  onSkipPress
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <SmallLogo width={63.3} height={86.4} />
        </View>

        {/* T<PERSON><PERSON><PERSON> e descrição */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>
            {t("startup.biometric.title", "Bem-vindo de volta!")}
          </Text>
          <Text style={styles.description}>
            {t(
              "startup.biometric.mandatoryDescription",
              "Para sua segurança, confirme sua identidade com biometria para continuar."
            )}
          </Text>
        </View>

        {/* Botões */}
        <View style={styles.buttonsContainer}>
          {/* Botão de Biometria */}
          <TouchableOpacity
            style={[
              styles.biometricButton,
              isAuthenticating && styles.buttonDisabled
            ]}
            onPress={onBiometricPress}
            disabled={isAuthenticating}
            activeOpacity={0.7}
          >
            <View style={styles.buttonContent}>
              {isAuthenticating ? (
                <ActivityIndicator size="small" color="#F2F4F7" />
              ) : (
                <BiometricIcon width={20} height={20} replaceColor="#F2F4F7" />
              )}
              <Text style={styles.biometricButtonText}>
                {isAuthenticating
                  ? t("startup.biometric.authenticating", "Autenticando...")
                  : t("startup.biometric.useButton", "Usar Biometria")}
              </Text>
            </View>
          </TouchableOpacity>

          {/* Botão de Pular */}
          <TouchableOpacity
            style={[
              styles.skipButton,
              isAuthenticating && styles.buttonDisabled
            ]}
            onPress={onSkipPress}
            disabled={isAuthenticating}
            activeOpacity={0.7}
          >
            <Text style={styles.skipButtonText}>
              {t("startup.biometric.skipButton", "Usar senha")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default StartupBiometricPrompt;
