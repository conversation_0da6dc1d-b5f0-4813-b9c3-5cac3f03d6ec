/**
 * Componente QR Code reutilizável para React Native
 * Usa react-qr-code para gerar QR codes
 */

import React from "react";
import {View, StyleSheet, ViewStyle} from "react-native";
import QRCodeGenerator from "react-qr-code";

export interface QRCodeProps {
  /**
   * Valor a ser codificado no QR code
   */
  value: string;
  /**
   * Tamanho do QR code (padrão: 256)
   */
  size?: number;
  /**
   * Cor de fundo (padrão: branco)
   */
  bgColor?: string;
  /**
   * Cor do QR code (padrão: preto)
   */
  fgColor?: string;
  /**
   * Nível de correção de erro
   */
  level?: "L" | "M" | "Q" | "H";
  /**
   * Estilo personalizado para o container
   */
  style?: ViewStyle;
  /**
   * Se deve incluir uma zona silenciosa (padding) ao redor do QR code
   */
  includeMargin?: boolean;
}

const QRCode: React.FC<QRCodeProps> = ({
  value,
  size = 256,
  bgColor = "#FFFFFF",
  fgColor = "#000000",
  level = "M",
  style,
  includeMargin = true
}) => {
  if (!value) {
    return null;
  }

  const containerStyle = [
    styles.container,
    includeMargin && styles.containerWithMargin,
    {backgroundColor: bgColor},
    style
  ];

  return (
    <View style={containerStyle}>
      <QRCodeGenerator
        value={value}
        size={size}
        bgColor={bgColor}
        fgColor={fgColor}
        level={level}
        style={styles.qrCode}
        viewBox={`0 0 ${size} ${size}`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8
  },
  containerWithMargin: {
    padding: 16
  },
  qrCode: {
    height: "auto",
    maxWidth: "100%",
    width: "100%",
    aspectRatio: 1
  }
});

export default QRCode;
