import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { AppEventCategory, getEventCategoryLabel } from '@/models/api/app-create-event.models';
import styles from '@/styles/components/event-category-selector.style';

interface EventCategorySelectorProps {
  visible: boolean;
  selectedCategory?: AppEventCategory;
  onSelect: (category: AppEventCategory) => void;
  onClose: () => void;
}

interface EventCategoryOption {
  value: AppEventCategory;
  label: string;
  description: string;
  icon: string;
}

const EventCategorySelector: React.FC<EventCategorySelectorProps> = ({
  visible,
  selectedCategory,
  onSelect,
  onClose,
}) => {
  const { t } = useTranslation();
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [tempSelectedCategory, setTempSelectedCategory] = useState<AppEventCategory | undefined>(selectedCategory);

  const screenHeight = Dimensions.get('window').height;

  const eventCategoryOptions: EventCategoryOption[] = [
    {
      value: AppEventCategory.CONFERENCE,
      label: t('eventCategorySelector.conference.label', 'Conferência'),
      description: t('eventCategorySelector.conference.description', 'Evento de grande porte com palestras'),
      icon: '🎤',
    },
    {
      value: AppEventCategory.WORKSHOP,
      label: t('eventCategorySelector.workshop.label', 'Workshop'),
      description: t('eventCategorySelector.workshop.description', 'Atividade prática e interativa'),
      icon: '🛠️',
    },
    {
      value: AppEventCategory.SEMINAR,
      label: t('eventCategorySelector.seminar.label', 'Seminário'),
      description: t('eventCategorySelector.seminar.description', 'Apresentação educativa sobre um tema'),
      icon: '📚',
    },
    {
      value: AppEventCategory.NETWORKING,
      label: t('eventCategorySelector.networking.label', 'Networking'),
      description: t('eventCategorySelector.networking.description', 'Evento para conexões profissionais'),
      icon: '🤝',
    },
    {
      value: AppEventCategory.WEBINAR,
      label: t('eventCategorySelector.webinar.label', 'Webinar'),
      description: t('eventCategorySelector.webinar.description', 'Seminário online interativo'),
      icon: '💻',
    },
    {
      value: AppEventCategory.SOCIAL,
      label: t('eventCategorySelector.social.label', 'Social'),
      description: t('eventCategorySelector.social.description', 'Evento de confraternização'),
      icon: '🎉',
    },
    {
      value: AppEventCategory.TRAINING,
      label: t('eventCategorySelector.training.label', 'Treinamento'),
      description: t('eventCategorySelector.training.description', 'Capacitação e desenvolvimento'),
      icon: '🎯',
    },
    {
      value: AppEventCategory.MEETING,
      label: t('eventCategorySelector.meeting.label', 'Reunião'),
      description: t('eventCategorySelector.meeting.description', 'Encontro para discussões'),
      icon: '👥',
    },
    {
      value: AppEventCategory.OTHER,
      label: t('eventCategorySelector.other.label', 'Outro'),
      description: t('eventCategorySelector.other.description', 'Categoria personalizada'),
      icon: '📋',
    },
  ];

  useEffect(() => {
    if (visible) {
      setTempSelectedCategory(selectedCategory);
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, selectedCategory, slideAnim]);

  const handleOptionSelect = (category: AppEventCategory) => {
    setTempSelectedCategory(category);
  };

  const handleConfirm = () => {
    if (tempSelectedCategory !== undefined) {
      onSelect(tempSelectedCategory);
    }
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedCategory(selectedCategory);
    onClose();
  };

  const handleBackdropPress = () => {
    handleCancel();
  };

  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [screenHeight, 0],
  });

  const backdropOpacity = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5],
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleCancel}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, { opacity: backdropOpacity }]}>
          <TouchableOpacity
            style={styles.backdropTouchable}
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{ translateY }],
            },
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Handle Bar */}
            <View style={styles.handleBar} />

            {/* Title */}
            <Text style={styles.title}>
              {t('eventCategorySelector.title', 'Selecionar categoria')}
            </Text>

            {/* Options List */}
            <ScrollView
              style={styles.optionsContainer}
              showsVerticalScrollIndicator={false}
            >
              {eventCategoryOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionItem,
                    tempSelectedCategory === option.value && styles.selectedOptionItem,
                  ]}
                  onPress={() => handleOptionSelect(option.value)}
                  activeOpacity={0.7}
                >
                  <View style={styles.optionContent}>
                    <View style={styles.optionIconContainer}>
                      <Text style={styles.optionIcon}>{option.icon}</Text>
                    </View>
                    <View style={styles.optionTextContainer}>
                      <Text
                        style={[
                          styles.optionLabel,
                          tempSelectedCategory === option.value && styles.selectedOptionLabel,
                        ]}
                      >
                        {option.label}
                      </Text>
                      <Text
                        style={[
                          styles.optionDescription,
                          tempSelectedCategory === option.value && styles.selectedOptionDescription,
                        ]}
                      >
                        {option.description}
                      </Text>
                    </View>
                    {tempSelectedCategory === option.value && (
                      <View style={styles.checkmarkContainer}>
                        <Text style={styles.checkmark}>✓</Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCancel}
                activeOpacity={0.7}
              >
                <Text style={styles.cancelButtonText}>
                  {t('eventCategorySelector.cancel', 'Cancelar')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.button,
                  styles.confirmButton,
                  tempSelectedCategory === undefined && styles.disabledButton,
                ]}
                onPress={handleConfirm}
                disabled={tempSelectedCategory === undefined}
                activeOpacity={0.7}
              >
                <Text style={styles.confirmButtonText}>
                  {t('eventCategorySelector.confirm', 'Confirmar')}
                </Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default EventCategorySelector;
