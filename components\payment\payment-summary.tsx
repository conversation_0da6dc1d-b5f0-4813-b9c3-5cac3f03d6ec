/**
 * Componente de resumo de pagamento
 * Exibe detalhes do item e valores do pagamento
 */

import React, {useMemo} from "react";
import {View, Text, Image} from "react-native";
import {useTranslation} from "react-i18next";
import {PaymentEntity} from "../../models/api/payments.models";
import styles from "../../styles/components/payment/payment-summary.style";

export interface PaymentSummaryItem {
  id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  price: number;
  quantity?: number;
  entity: PaymentEntity;
}

export interface PaymentSummaryProps {
  items: PaymentSummaryItem[];
  subtotal?: number;
  fees?: number;
  discount?: number;
  total: number;
  currency?: string;
  showItemDetails?: boolean;
}

const PaymentSummary: React.FC<PaymentSummaryProps> = ({
  items,
  subtotal,
  fees = 0,
  discount = 0,
  total,
  currency = "BRL",
  showItemDetails = true
}) => {
  const {t} = useTranslation();

  // Calcula subtotal se não fornecido
  const calculatedSubtotal = useMemo(() => {
    if (subtotal !== undefined) return subtotal;
    return items.reduce(
      (sum, item) => sum + item.price * (item.quantity || 1),
      0
    );
  }, [items, subtotal]);

  // Formata valor monetário
  const formatCurrency = useMemo(() => {
    return (value: number) => {
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: currency
      }).format(value);
    };
  }, [currency]);

  // Renderiza um item
  const renderItem = (item: PaymentSummaryItem) => (
    <View key={item.id} style={styles.itemContainer}>
      <View style={styles.itemHeader}>
        {item.imageUrl && (
          <Image source={{uri: item.imageUrl}} style={styles.itemImage} />
        )}
        <View style={styles.itemInfo}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          {item.description && (
            <Text style={styles.itemDescription}>{item.description}</Text>
          )}
          <Text style={styles.itemEntity}>
            {t(`components.payment.summary.entity.${item.entity}`)}
          </Text>
        </View>
        <View style={styles.itemPricing}>
          {item.quantity && item.quantity > 1 && (
            <Text style={styles.itemQuantity}>
              {item.quantity}x {formatCurrency(item.price)}
            </Text>
          )}
          <Text style={styles.itemTotal}>
            {formatCurrency(item.price * (item.quantity || 1))}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("components.payment.summary.title")}</Text>

      {/* Lista de itens */}
      {showItemDetails && (
        <View style={styles.itemsSection}>{items.map(renderItem)}</View>
      )}

      {/* Resumo financeiro */}
      <View style={styles.summarySection}>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>
            {t("components.payment.summary.subtotal")}
          </Text>
          <Text style={styles.summaryValue}>
            {formatCurrency(calculatedSubtotal)}
          </Text>
        </View>

        {fees > 0 && (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {t("components.payment.summary.fees")}
            </Text>
            <Text style={styles.summaryValue}>{formatCurrency(fees)}</Text>
          </View>
        )}

        {discount > 0 && (
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, styles.discountLabel]}>
              {t("components.payment.summary.discount")}
            </Text>
            <Text style={[styles.summaryValue, styles.discountValue]}>
              -{formatCurrency(discount)}
            </Text>
          </View>
        )}

        <View style={styles.divider} />

        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>
            {t("components.payment.summary.total")}
          </Text>
          <Text style={styles.totalValue}>{formatCurrency(total)}</Text>
        </View>
      </View>

      {/* Informações adicionais */}
      <View style={styles.infoSection}>
        <Text style={styles.infoText}>
          {t("components.payment.summary.securePayment")}
        </Text>
        <Text style={styles.infoText}>
          {t("components.payment.summary.refundPolicy")}
        </Text>
      </View>
    </View>
  );
};

export default PaymentSummary;
