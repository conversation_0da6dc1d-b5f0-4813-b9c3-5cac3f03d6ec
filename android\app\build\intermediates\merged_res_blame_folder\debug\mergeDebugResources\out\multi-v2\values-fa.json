{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da3d785accc181b5d8cd59ecc7f8a711\\transformed\\android-image-cropper-4.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,117,167,243,318,391,443", "endColumns": "61,49,75,74,72,51,72", "endOffsets": "112,162,238,313,386,438,511"}, "to": {"startLines": "74,98,99,100,101,102,170", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7526,10157,10207,10283,10358,10431,15644", "endColumns": "61,49,75,74,72,51,72", "endOffsets": "7583,10202,10278,10353,10426,10478,15712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,76,77,78,103,106,108,109,110,111,112,113,114,115,116,117,118,119,120,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3564,3639,3716,3798,3891,4702,4799,4928,7676,7734,7797,10483,10702,10830,10921,10984,11048,11107,11174,11236,11291,11414,11472,11533,11588,11660,12012,12093,12173,12270,12351,12433,12563,12637,12711,12843,12929,13006,13057,13111,13177,13248,13325,13396,13475,13548,13622,13692,13766,13867,13953,14027,14116,14208,14282,14355,14444,14495,14575,14642,14725,14809,14871,14935,14998,15067,15161,15262,15355,15453,15508,15566,16232,16318,16395", "endLines": "5,38,39,40,41,42,50,51,52,76,77,78,103,106,108,109,110,111,112,113,114,115,116,117,118,119,120,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "304,3634,3711,3793,3886,3973,4794,4923,5007,7729,7792,7882,10547,10757,10916,10979,11043,11102,11169,11231,11286,11409,11467,11528,11583,11655,11792,12088,12168,12265,12346,12428,12558,12632,12706,12838,12924,13001,13052,13106,13172,13243,13320,13391,13470,13543,13617,13687,13761,13862,13948,14022,14111,14203,14277,14350,14439,14490,14570,14637,14720,14804,14866,14930,14993,15062,15156,15257,15350,15448,15503,15561,15639,16313,16390,16464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,16150", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,16227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,113", "endOffsets": "161,275"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3068,3179", "endColumns": "110,113", "endOffsets": "3174,3288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,326,438,526,670,785,894,1011,1139,1262,1412,1529,1653,1750,1900,2026,2160,2298,2413,2536,2636,2763,2852,2968,3069,3197", "endColumns": "148,121,111,87,143,114,108,116,127,122,149,116,123,96,149,125,133,137,114,122,99,126,88,115,100,127,104", "endOffsets": "199,321,433,521,665,780,889,1006,1134,1257,1407,1524,1648,1745,1895,2021,2155,2293,2408,2531,2631,2758,2847,2963,3064,3192,3297"}, "to": {"startLines": "36,37,72,75,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,174,193,194,195,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3293,3442,7315,7588,7887,8031,8457,8566,8683,8811,8934,9084,9201,9325,9422,9572,9698,9832,9970,15948,17491,17591,17718,17807,17923,18024,18152", "endColumns": "148,121,111,87,143,114,108,116,127,122,149,116,123,96,149,125,133,137,114,122,99,126,88,115,100,127,104", "endOffsets": "3437,3559,7422,7671,8026,8141,8561,8678,8806,8929,9079,9196,9320,9417,9567,9693,9827,9965,10080,16066,17586,17713,17802,17918,18019,18147,18252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6068", "endColumns": "154", "endOffsets": "6218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "43,44,45,46,47,48,49,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3978,4077,4179,4278,4378,4479,4585,17095", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "4072,4174,4273,4373,4474,4580,4697,17191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5089,5197,5354,5483,5586,5726,5852,5966,6223,6376,6486,6656,6787,6940,7096,7162,7230", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "5192,5349,5478,5581,5721,5847,5961,6063,6371,6481,6651,6782,6935,7091,7157,7225,7310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "73,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7427,8146,8243,8354", "endColumns": "98,96,110,102", "endOffsets": "7521,8238,8349,8452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,273,342,423,491,557,631,706,787,868,937,1016,1094,1168,1250,1331,1410,1483,1554,1642,1713,1789,1861", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,196,268,337,418,486,552,626,701,782,863,932,1011,1089,1163,1245,1326,1405,1478,1549,1637,1708,1784,1856,1932"}, "to": {"startLines": "33,53,97,104,105,107,121,122,123,171,172,173,175,180,181,182,183,184,185,186,187,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,5012,10085,10552,10621,10762,11797,11863,11937,15717,15798,15879,16071,16469,16547,16621,16703,16784,16863,16936,17007,17196,17267,17343,17415", "endColumns": "68,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "3063,5084,10152,10616,10697,10825,11858,11932,12007,15793,15874,15943,16145,16542,16616,16698,16779,16858,16931,17002,17090,17262,17338,17410,17486"}}]}]}