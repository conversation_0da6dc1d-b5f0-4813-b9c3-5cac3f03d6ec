{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-72:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,425,526,636,724,831,945,1027,1105,1196,1289,1383,1482,1582,1675,1770,1864,1955,2047,2132,2237,2343,2443,2552,2657,2759,2917,15282", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "420,521,631,719,826,940,1022,1100,1191,1284,1378,1477,1577,1670,1765,1859,1950,2042,2127,2232,2338,2438,2547,2652,2754,2912,3018,15361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6096", "endColumns": "161", "endOffsets": "6253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "52,96,97,99,113,165,166", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5003,10153,10222,10363,11388,15602,15677", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "5079,10217,10299,10426,11451,15672,15747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,167", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3984,4085,4187,4290,4394,4495,4600,15752", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "4080,4182,4285,4389,4490,4595,4706,15848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3023,3132", "endColumns": "108,117", "endOffsets": "3127,3245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,372,485,574,726,845,958,1079,1209,1337,1458,1578,1725,1819,1986,2117,2247,2377,2497,2627,2743,2897,2990,3118,3227,3370", "endColumns": "174,141,112,88,151,118,112,120,129,127,120,119,146,93,166,130,129,129,119,129,115,153,92,127,108,142,106", "endOffsets": "225,367,480,569,721,840,953,1074,1204,1332,1453,1573,1720,1814,1981,2112,2242,2372,2492,2622,2738,2892,2985,3113,3222,3365,3472"}, "to": {"startLines": "35,36,71,73,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,160,168,169,170,171,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3250,3425,7330,7539,7847,7999,8433,8546,8667,8797,8925,9046,9166,9313,9407,9574,9705,9835,9965,15152,15853,15969,16123,16216,16344,16453,16596", "endColumns": "174,141,112,88,151,118,112,120,129,127,120,119,146,93,166,130,129,129,119,129,115,153,92,127,108,142,106", "endOffsets": "3420,3562,7438,7623,7994,8113,8541,8662,8792,8920,9041,9161,9308,9402,9569,9700,9830,9960,10080,15277,15964,16118,16211,16339,16448,16591,16698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "72,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7443,8118,8222,8330", "endColumns": "95,103,107,102", "endOffsets": "7534,8217,8325,8428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1035,1099,1193,1261,1320,1415,1478,1542,1601,1668,1731,1785,1900,1958,2020,2074,2145,2277,2361,2440,2532,2616,2696,2830,2906,2982,3111,3195,3274,3331,3382,3448,3518,3596,3667,3747,3817,3893,3971,4042,4140,4226,4309,4402,4495,4568,4640,4734,4788,4872,4939,5023,5111,5175,5240,5304,5374,5476,5580,5676,5777,5838,5893,5973,6060,6135", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "260,348,433,513,598,677,773,889,969,1030,1094,1188,1256,1315,1410,1473,1537,1596,1663,1726,1780,1895,1953,2015,2069,2140,2272,2356,2435,2527,2611,2691,2825,2901,2977,3106,3190,3269,3326,3377,3443,3513,3591,3662,3742,3812,3888,3966,4037,4135,4221,4304,4397,4490,4563,4635,4729,4783,4867,4934,5018,5106,5170,5235,5299,5369,5471,5575,5671,5772,5833,5888,5968,6055,6130,6204"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,74,75,76,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3567,3655,3740,3820,3905,4711,4807,4923,7628,7689,7753,10085,10304,10431,10526,10589,10653,10712,10779,10842,10896,11011,11069,11131,11185,11256,11456,11540,11619,11711,11795,11875,12009,12085,12161,12290,12374,12453,12510,12561,12627,12697,12775,12846,12926,12996,13072,13150,13221,13319,13405,13488,13581,13674,13747,13819,13913,13967,14051,14118,14202,14290,14354,14419,14483,14553,14655,14759,14855,14956,15017,15072,15366,15453,15528", "endLines": "5,37,38,39,40,41,49,50,51,74,75,76,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,162,163,164", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "310,3650,3735,3815,3900,3979,4802,4918,4998,7684,7748,7842,10148,10358,10521,10584,10648,10707,10774,10837,10891,11006,11064,11126,11180,11251,11383,11535,11614,11706,11790,11870,12004,12080,12156,12285,12369,12448,12505,12556,12622,12692,12770,12841,12921,12991,13067,13145,13216,13314,13400,13483,13576,13669,13742,13814,13908,13962,14046,14113,14197,14285,14349,14414,14478,14548,14650,14754,14850,14951,15012,15067,15147,15448,15523,15597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5084,5193,5348,5481,5591,5737,5870,5990,6258,6424,6534,6683,6820,6964,7120,7184,7249", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "5188,5343,5476,5586,5732,5865,5985,6091,6419,6529,6678,6815,6959,7115,7179,7244,7325"}}]}]}