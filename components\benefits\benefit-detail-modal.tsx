import React, {useCallback} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions
} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import {Benefit} from "@/models/api/benefits.models";
import CloseIcon from "@/components/icons/close-icon";
import GiftIcon from "@/components/icons/gift-icon";
import styles from "@/styles/components/benefits/benefit-detail-modal.style";

const {height: screenHeight} = Dimensions.get("window");

export interface BenefitDetailModalProps {
  benefit: Benefit | null;
  visible: boolean;
  onClose: () => void;
  onInteract?: (interactionType: "view" | "click" | "redeem") => void;
}

/**
 * Modal component that displays detailed information about a benefit
 * with interaction capabilities and consistent styling.
 */
const BenefitDetailModal: React.FC<BenefitDetailModalProps> = ({
  benefit,
  visible,
  onClose,
  onInteract
}) => {
  const {t} = useTranslation();

  const handleInteraction = useCallback(
    (type: "view" | "click" | "redeem") => {
      onInteract?.(type);
    },
    [onInteract]
  );

  const handleRedeem = useCallback(() => {
    handleInteraction("redeem");
    onClose();
  }, [handleInteraction, onClose]);

  if (!benefit) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            {t("partners.benefit", "Benefício")}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <CloseIcon width={24} height={24} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Benefit Image/Icon */}
          <View style={styles.imageContainer}>
            {benefit.imageUrl ? (
              <Image
                source={{uri: benefit.imageUrl}}
                style={styles.image}
                contentFit="cover"
              />
            ) : (
              <View style={styles.iconPlaceholder}>
                <GiftIcon width={48} height={48} color="#4A90E2" />
              </View>
            )}
          </View>

          {/* Benefit Info */}
          <View style={styles.infoSection}>
            <Text style={styles.title}>{benefit.title}</Text>
            <Text style={styles.description}>{benefit.description}</Text>

            {benefit.terms && (
              <View style={styles.termsSection}>
                <Text style={styles.termsTitle}>
                  {t("benefits.terms", "Termos e Condições")}
                </Text>
                <Text style={styles.termsText}>{benefit.terms}</Text>
              </View>
            )}

            {benefit.validUntil && (
              <View style={styles.validitySection}>
                <Text style={styles.validityLabel}>
                  {t("benefits.validUntil", "Válido até")}
                </Text>
                <Text style={styles.validityDate}>
                  {new Date(benefit.validUntil).toLocaleDateString("pt-BR")}
                </Text>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Footer Actions */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.redeemButton}
            onPress={handleRedeem}
            activeOpacity={0.8}
          >
            <Text style={styles.redeemButtonText}>
              {t("benefits.redeem", "Resgatar Benefício")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default BenefitDetailModal;
