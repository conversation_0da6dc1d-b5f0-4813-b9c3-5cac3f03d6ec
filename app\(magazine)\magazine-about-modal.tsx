import React, {useState} from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  Alert,
  ActivityIndicator
} from "react-native";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import {useTranslation} from "react-i18next";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import {useFileUrl} from "@/hooks/api/use-storage";
import {apiClient} from "@/services/api/base/api-client";
import styles from "@/styles/magazine/magazine-about-modal.style";

interface MagazineEdition {
  id: string;
  title: string;
  description: string;
  releaseDate: string;
  fileId: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

interface Magazine {
  id: string;
  title: string;
  description: string;
  author?: string;
  coverImage: string;
  category: string;
  publishDate: string;
  isNew: boolean;
  isPremium: boolean;
  fileId?: string;
  editions?: MagazineEdition[];
}

interface MagazineAboutModalProps {
  magazine: Magazine;
}

const MagazineAboutModal: React.FC<MagazineAboutModalProps> = ({magazine}) => {
  const {closeModal} = useBottomModal();
  const {t} = useTranslation();
  const screenHeight = Dimensions.get("window").height;
  const [isReadingMagazine, setIsReadingMagazine] = useState(false);

  // Função para lidar com o download de uma edição específica
  const handleReadEdition = async (edition: MagazineEdition) => {
    closeModal();
    setIsReadingMagazine(true);

    try {
      console.log("📖 [MAGAZINE-ABOUT] Baixando edição para visualização:", {
        magazineId: magazine.id,
        editionId: edition.id,
        editionTitle: edition.title,
        fileId: edition.fileId
      });

      // Obter token de autenticação
      const tokenData = await apiClient.getValidToken();
      if (!tokenData?.accessToken) {
        throw new Error("Token de autenticação não disponível");
      }

      // Construir URL do arquivo
      const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
      if (!baseUrl) {
        throw new Error("EXPO_PUBLIC_API_BASE_URL não está configurado");
      }
      const fileUrl = `${baseUrl}/api/storage/${edition.fileId}`;

      // Criar nome do arquivo temporário
      const fileName = `revista-${magazine.id}-edicao-${
        edition.id
      }-${Date.now()}.pdf`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Baixar o arquivo PDF com headers de autenticação
      const downloadResult = await FileSystem.downloadAsync(fileUrl, fileUri, {
        headers: {
          Authorization: `${tokenData.tokenType} ${tokenData.accessToken}`
        }
      });

      if (downloadResult.status !== 200) {
        throw new Error(`Download falhou com status: ${downloadResult.status}`);
      }

      console.log("📖 [MAGAZINE-ABOUT] Download da edição concluído:", {
        uri: downloadResult.uri,
        status: downloadResult.status,
        editionTitle: edition.title
      });

      // Verificar se o sharing está disponível
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        throw new Error(
          "Compartilhamento não está disponível neste dispositivo"
        );
      }

      // Abrir o PDF no app nativo
      await Sharing.shareAsync(downloadResult.uri, {
        mimeType: "application/pdf",
        dialogTitle: `${magazine.title} - ${edition.title}`,
        UTI: "com.adobe.pdf"
      });

      console.log(
        "📖 [MAGAZINE-ABOUT] Edição aberta com sucesso no visualizador nativo:",
        edition.title
      );
    } catch (error) {
      console.error("📖 [MAGAZINE-ABOUT] Erro ao baixar/abrir edição:", error);

      Alert.alert(
        t("magazine.readError", "Erro"),
        t(
          "magazine.readErrorMessage",
          "Não foi possível baixar ou abrir a edição. Verifique sua conexão e se você tem um aplicativo para visualizar PDFs instalado."
        ),
        [{text: t("common.ok", "OK")}]
      );
    } finally {
      setIsReadingMagazine(false);
    }
  };

  return (
    <View style={[styles.container, {height: screenHeight}]}>
      <View style={styles.contentWrapper}>
        {/* Top Handle */}
        <View style={styles.handleContainer}>
          <View style={styles.handle} />
        </View>

        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={styles.headerTitle}>Sobre a revista</Text>
        </View>

        {/* Magazine Cover */}
        <View style={styles.coverContainer}>
          <Image
            source={{
              uri: "https://static.motiffcontent.com/private/resource/image/197d6b679e1d7bb-535a991d-1b05-4ecd-8ba1-6160cc282740.jpg"
            }}
            style={styles.coverImage}
            resizeMode="cover"
          />
        </View>

        {/* Content */}
        <View style={styles.contentContainer}>
          <Text style={styles.badge}>Club M Apresenta</Text>

          <Text style={styles.title}>
            João Kepler, O Maior Investidor Anjo do Brasil
          </Text>

          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>
              <Text style={styles.descriptionNormal}>
                O homem das mil startups recebeu a equipe da{" "}
              </Text>
              <Text style={styles.descriptionBold}>Club M The Magazine</Text>
              <Text style={styles.descriptionNormal}>
                {" "}
                para um bate papo sobre negócios, empreendedorismo e muitas
                lições para a vida.
              </Text>
            </Text>
          </View>
        </View>
      </View>

      {/* Buttons */}
      <View style={styles.buttonsContainer}>
        {magazine.editions && magazine.editions.length > 0 ? (
          magazine.editions.map((edition, index) => (
            <TouchableOpacity
              key={edition.id}
              style={[
                styles.primaryButton,
                index > 0 && {marginTop: 12},
                isReadingMagazine && {opacity: 0.7}
              ]}
              onPress={() => handleReadEdition(edition)}
              disabled={isReadingMagazine}
            >
              {isReadingMagazine ? (
                <View style={{flexDirection: "row", alignItems: "center"}}>
                  <ActivityIndicator
                    size="small"
                    color="#FFFFFF"
                    style={{marginRight: 8}}
                  />
                  <Text style={styles.primaryButtonText}>Baixando...</Text>
                </View>
              ) : (
                <View style={{alignItems: "center"}}>
                  <Text style={styles.primaryButtonText}>{edition.title}</Text>
                  <Text
                    style={[
                      styles.primaryButtonText,
                      {fontSize: 12, opacity: 0.8, marginTop: 2}
                    ]}
                  >
                    {new Date(edition.releaseDate).toLocaleDateString("pt-BR")}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))
        ) : (
          <View style={[styles.primaryButton, {backgroundColor: "#666"}]}>
            <Text style={styles.primaryButtonText}>
              Nenhuma edição disponível
            </Text>
          </View>
        )}

        <TouchableOpacity style={styles.secondaryButton} onPress={closeModal}>
          <Text style={styles.secondaryButtonText}>Fechar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default MagazineAboutModal;
