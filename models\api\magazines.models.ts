/**
 * Modelos para revistas/magazines baseados na API ClubM
 */

import {z} from "zod";
import {PaginationRequest} from "./common.models";

// Interface para edições de revista
export interface MagazineEdition {
  id: string;
  title: string;
  description: string;
  releaseDate: string;
  fileId: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

// Interface principal para Magazine
export interface Magazine {
  id: string;
  title: string;
  description: string;
  shortDescription?: string;
  coverImage: string;
  publishDate: string;
  category: string;
  readTime: string;
  isNew: boolean;
  isPremium: boolean;
  author?: string;
  authorTitle?: string;
  content?: string;
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  createdAt?: string;
  updatedAt?: string;
  fileId?: string; // ID do arquivo PDF da revista no storage (deprecated - usar editions)
  editions?: MagazineEdition[]; // Edições da revista
  metadata?: Record<string, any>;
}

// Parâmetros para buscar magazines
export interface MagazinesListParams extends PaginationRequest {
  search?: string;
  category?: string;
  isNew?: boolean;
  isPremium?: boolean;
  author?: string;
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  publishedAfter?: string;
  publishedBefore?: string;
}

// Schema de validação para parâmetros de busca
export const MagazinesListParamsSchema = z.object({
  page: z.number().min(1).optional().default(1),
  pageSize: z.number().min(1).max(100).optional().default(20),
  search: z.string().optional(),
  category: z.string().optional(),
  isNew: z.boolean().optional(),
  isPremium: z.boolean().optional(),
  author: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  publishedAfter: z.string().datetime().optional(),
  publishedBefore: z.string().datetime().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc")
});

// Schema de validação para Magazine
export const MagazineSchema = z.object({
  id: z.string().min(1, "ID é obrigatório"),
  title: z.string().min(1, "Título é obrigatório"),
  description: z.string().min(1, "Descrição é obrigatória"),
  shortDescription: z.string().optional(),
  coverImage: z.string().url("URL da imagem de capa inválida"),
  publishDate: z.string().datetime("Data de publicação inválida"),
  category: z.string().min(1, "Categoria é obrigatória"),
  readTime: z.string().min(1, "Tempo de leitura é obrigatório"),
  isNew: z.boolean(),
  isPremium: z.boolean(),
  author: z.string().optional(),
  authorTitle: z.string().optional(),
  content: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
  fileId: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

// Tipos para filtros específicos
export interface MagazineFilters {
  search?: string;
  category?: string;
  isNew?: boolean;
  isPremium?: boolean;
  author?: string;
  tags?: string[];
  publishedAfter?: string;
  publishedBefore?: string;
}

// Tipos para estatísticas de magazines
export interface MagazineStats {
  totalMagazines: number;
  newMagazines: number;
  premiumMagazines: number;
  categoriesCount: Record<string, number>;
  authorsCount: Record<string, number>;
  averageReadTime: number;
  mostPopularCategory: string;
  latestMagazine?: Magazine;
}

// Tipos para categorias de magazines
export interface MagazineCategory {
  id: string;
  name: string;
  description?: string;
  magazineCount: number;
  isActive: boolean;
}

// Tipos para autores
export interface MagazineAuthor {
  id: string;
  name: string;
  title?: string;
  bio?: string;
  avatar?: string;
  magazineCount: number;
  isActive: boolean;
}

// Enums para tipos de magazine
export enum MagazineType {
  ARTICLE = "article",
  INTERVIEW = "interview",
  NEWS = "news",
  TUTORIAL = "tutorial",
  REVIEW = "review",
  OPINION = "opinion"
}

export enum MagazineStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  ARCHIVED = "archived",
  FEATURED = "featured"
}

// Tipos para interações com magazines
export interface MagazineInteraction {
  id: string;
  magazineId: string;
  userId: string;
  type: "view" | "like" | "share" | "bookmark";
  timestamp: string;
  metadata?: Record<string, any>;
}

// Tipos para comentários (se aplicável)
export interface MagazineComment {
  id: string;
  magazineId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
  isApproved: boolean;
  parentCommentId?: string;
  replies?: MagazineComment[];
}
