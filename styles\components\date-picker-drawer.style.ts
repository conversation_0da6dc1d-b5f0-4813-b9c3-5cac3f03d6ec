import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../styles-constants";

const {height: screenHeight} = Dimensions.get("window");

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end"
  },
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.8,
    minHeight: screenHeight * 0.6
  },
  safeArea: {
    flex: 1
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault,
    alignItems: "center"
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    marginTop: 20,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.openSans
  },
  calendarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingHorizontal: 10
  },
  navButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.highlightBackground
  },
  monthYearButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "transparent"
  },
  monthYear: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans
  },
  weekDaysHeader: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
    paddingHorizontal: 5
  },
  weekDayText: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textSecondary,
    textAlign: "center",
    width: 40,
    fontFamily: stylesConstants.fonts.openSans
  },
  calendarGrid: {
    marginBottom: 20
  },
  calendarRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 8,
    paddingHorizontal: 5
  },
  dayCell: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center"
  },
  activeDayCell: {
    // Default styling for current month days
  },
  inactiveDayCell: {
    opacity: 0.3
  },
  selectedDayCell: {
    backgroundColor: stylesConstants.colors.brand.primary
  },
  todayDayCell: {
    borderWidth: 2,
    borderColor: stylesConstants.colors.brand.primary
  },
  dayText: {
    fontSize: 16,
    fontWeight: "400",
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  activeDayText: {
    color: stylesConstants.colors.fullWhite
  },
  inactiveDayText: {
    color: stylesConstants.colors.textSecondary
  },
  selectedDayText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  },
  todayDayText: {
    color: stylesConstants.colors.brand.primary,
    fontWeight: "600"
  },
  clearContainer: {
    alignItems: "center",
    marginBottom: 20
  },
  clearButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  clearButtonText: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans
  },
  buttonContainer: {
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault
  },
  applyButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center"
  },
  disabledButton: {
    backgroundColor: stylesConstants.colors.gray300,
    opacity: 0.6
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans
  },
  disabledButtonText: {
    color: stylesConstants.colors.gray500
  },
  cancelButton: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "400",
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans
  },
  // Year Picker Styles
  yearPickerContainer: {
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    maxHeight: 200
  },
  yearPickerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    marginBottom: 12,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.openSans
  },
  yearPickerScroll: {
    maxHeight: 150
  },
  yearGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between"
  },
  yearButton: {
    width: "30%",
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: "transparent",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  selectedYearButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary
  },
  yearButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans
  },
  selectedYearButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: "600"
  }
});

export default styles;
