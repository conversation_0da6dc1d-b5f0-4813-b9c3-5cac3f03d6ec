{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-73:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "43,44,45,46,47,48,49,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4051,4149,4251,4349,4453,4557,4659,15388", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "4144,4246,4344,4448,4552,4654,4771,15484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e054245bee9bbc8098dc00b5c8db8d90\\transformed\\play-services-basement-18.4.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6071", "endColumns": "140", "endOffsets": "6207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2520,2625,2739,2842,3011,15062", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2515,2620,2734,2837,3006,3102,15144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ebecbd677b4a4fda1fcdc45db910ad7c\\transformed\\credentials-1.2.0-rc01\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3107,3218", "endColumns": "110,121", "endOffsets": "3213,3335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,329,438,531,692,813,929,1052,1184,1315,1443,1573,1707,1808,1969,2090,2225,2358,2481,2608,2705,2842,2943,3084,3185,3326", "endColumns": "156,116,108,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "207,324,433,526,687,808,924,1047,1179,1310,1438,1568,1702,1803,1964,2085,2220,2353,2476,2603,2700,2837,2938,3079,3180,3321,3430"}, "to": {"startLines": "36,37,71,73,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,156,162,163,164,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3340,3497,7272,7485,7798,7959,8394,8510,8633,8765,8896,9024,9154,9288,9389,9550,9671,9806,9939,14935,15489,15586,15723,15824,15965,16066,16207", "endColumns": "156,116,108,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "3492,3609,7376,7573,7954,8075,8505,8628,8760,8891,9019,9149,9283,9384,9545,9666,9801,9934,10057,15057,15581,15718,15819,15960,16061,16202,16311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "72,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7381,8080,8180,8294", "endColumns": "103,99,113,99", "endOffsets": "7480,8175,8289,8389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1535,1595,1661,1717,1788,1848,1902,2021,2078,2140,2194,2269,2393,2481,2558,2652,2736,2819,2964,3049,3135,3268,3356,3434,3488,3542,3608,3682,3760,3831,3913,3985,4062,4135,4205,4314,4407,4479,4571,4667,4741,4817,4913,4966,5048,5115,5202,5289,5351,5415,5478,5547,5655,5760,5861,5964,6022,6080,6160,6246,6322", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1530,1590,1656,1712,1783,1843,1897,2016,2073,2135,2189,2264,2388,2476,2553,2647,2731,2814,2959,3044,3130,3263,3351,3429,3483,3537,3603,3677,3755,3826,3908,3980,4057,4130,4200,4309,4402,4474,4566,4662,4736,4812,4908,4961,5043,5110,5197,5284,5346,5410,5473,5542,5650,5755,5856,5959,6017,6075,6155,6241,6317,6394"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,74,75,76,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3614,3693,3773,3855,3957,4776,4872,4998,7578,7640,7706,10062,10139,10202,10310,10370,10436,10492,10563,10623,10677,10796,10853,10915,10969,11044,11168,11256,11333,11427,11511,11594,11739,11824,11910,12043,12131,12209,12263,12317,12383,12457,12535,12606,12688,12760,12837,12910,12980,13089,13182,13254,13346,13442,13516,13592,13688,13741,13823,13890,13977,14064,14126,14190,14253,14322,14430,14535,14636,14739,14797,14855,15149,15235,15311", "endLines": "6,38,39,40,41,42,50,51,52,74,75,76,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,158,159,160", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "372,3688,3768,3850,3952,4046,4867,4993,5074,7635,7701,7793,10134,10197,10305,10365,10431,10487,10558,10618,10672,10791,10848,10910,10964,11039,11163,11251,11328,11422,11506,11589,11734,11819,11905,12038,12126,12204,12258,12312,12378,12452,12530,12601,12683,12755,12832,12905,12975,13084,13177,13249,13341,13437,13511,13587,13683,13736,13818,13885,13972,14059,14121,14185,14248,14317,14425,14530,14631,14734,14792,14850,14930,15230,15306,15383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04b3c844b3393ff9a6c840f537ca40ca\\transformed\\play-services-base-18.5.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5079,5185,5345,5471,5581,5731,5857,5969,6212,6366,6473,6634,6761,6911,7057,7125,7187", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "5180,5340,5466,5576,5726,5852,5964,6066,6361,6468,6629,6756,6906,7052,7120,7182,7267"}}]}]}