import {useEffect, useState} from "react";
import * as LocalAuthentication from "expo-local-authentication";

export interface BiometricAvailability {
  isAvailable: boolean;
  isEnrolled: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  isLoading: boolean;
}

/**
 * Hook para verificar disponibilidade de autenticação biométrica
 */
export const useBiometricAvailability = (): BiometricAvailability => {
  const [availability, setAvailability] = useState<BiometricAvailability>({
    isAvailable: false,
    isEnrolled: false,
    supportedTypes: [],
    isLoading: true
  });

  useEffect(() => {
    const checkBiometricAvailability = async () => {
      try {
        const hasHardware = await LocalAuthentication.hasHardwareAsync();
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

        setAvailability({
          isAvailable: hasHardware,
          isEnrolled,
          supportedTypes,
          isLoading: false
        });
      } catch (error) {
        console.error("Erro ao verificar disponibilidade biométrica:", error);
        setAvailability({
          isAvailable: false,
          isEnrolled: false,
          supportedTypes: [],
          isLoading: false
        });
      }
    };

    checkBiometricAvailability();
  }, []);

  return availability;
};

export default useBiometricAvailability;
